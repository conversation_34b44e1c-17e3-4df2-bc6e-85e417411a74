/**
 * Complete Token Flow Script
 * 1. Finds deployment request
 * 2. Completes token creation (virtual pool)
 * 3. Simulates initial purchase (gives user tokens)
 * 4. Provides clear status to user
 */

const { SuiClient, getFullnodeUrl } = require('@mysten/sui/client');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { Transaction } = require('@mysten/sui/transactions');

// Configuration
const NETWORK = 'devnet';
const PACKAGE_ID = '0x351ff7bca3c8ff3b9aa34b331daa1ea745ce5e4837e75e323c741c36092a67d3';
const TOKEN_REGISTRY = '0x71170147b6830583a01a9a97d17fa6b7ca88266266891418fafa5faa108dec6a';
const PRIVATE_KEY = 'suiprivkey1qq96u4z3h5ze7ptnm69d45fugmutr499mwddfqt6009jzfgqypge6fmrzws';

async function completeTokenFlow() {
  console.log('🚀 Complete Token Flow');
  console.log('======================');

  const client = new SuiClient({ url: getFullnodeUrl(NETWORK) });
  const keypair = Ed25519Keypair.fromSecretKey(PRIVATE_KEY);

  try {
    // Step 1: Find latest deployment request
    console.log('🔍 Step 1: Finding deployment requests...');
    
    const eventQuery = {
      MoveEventType: `${PACKAGE_ID}::dexsta_token::TokenModuleDeploymentRequested`
    };

    const events = await client.queryEvents({
      query: eventQuery,
      limit: 5,
      order: 'descending'
    });

    if (!events.data || events.data.length === 0) {
      console.log('❌ No deployment requests found');
      return;
    }

    console.log(`📋 Found ${events.data.length} deployment requests:`);
    events.data.forEach((event, index) => {
      const data = event.parsedJson;
      console.log(`  ${index + 1}. ${data.token_symbol} - ${data.token_name}`);
    });

    // Process each pending token
    for (const event of events.data) {
      const eventData = event.parsedJson;
      const tokenSymbol = eventData.token_symbol;
      
      console.log(`\n🎯 Processing token: ${tokenSymbol}`);

      // Check if already completed
      const completionQuery = {
        MoveEventType: `${PACKAGE_ID}::dexsta_token::TokenCreated`
      };

      const completionEvents = await client.queryEvents({
        query: completionQuery,
        limit: 20,
        order: 'descending'
      });

      const alreadyCompleted = completionEvents.data?.find(e => 
        e.parsedJson?.symbol === tokenSymbol
      );

      if (alreadyCompleted) {
        console.log(`✅ Token ${tokenSymbol} already completed`);
        
        // Check if creator got their initial tokens
        const poolAddress = alreadyCompleted.parsedJson.token_pool_address;
        await checkAndMintInitialTokens(client, keypair, tokenSymbol, poolAddress, eventData);
        continue;
      }

      // Step 2: Complete token creation
      console.log(`🏗️ Step 2: Completing token creation for ${tokenSymbol}...`);
      
      const completionTx = new Transaction();
      completionTx.moveCall({
        target: `${PACKAGE_ID}::dexsta_token::complete_token_creation`,
        arguments: [
          completionTx.object(TOKEN_REGISTRY),
          completionTx.pure.string(tokenSymbol),
          completionTx.pure.address(PACKAGE_ID),
          completionTx.pure.string('dexsta_token'),
          completionTx.pure.string(`${PACKAGE_ID}::dexsta_token::DEXSTA`),
          completionTx.pure.id('0x0'),
          completionTx.pure.id('0x0'),
          completionTx.pure.u64('1000000'),
          completionTx.pure.string(''),
          completionTx.pure.string(''),
          completionTx.pure.string(''),
          completionTx.pure.string(''),
          completionTx.pure.u16(250),
          completionTx.pure.u16(250),
          completionTx.pure.u16(1000),
          completionTx.pure.u64(0),
          completionTx.pure.u64(0),
        ],
      });

      const completionResult = await client.signAndExecuteTransaction({
        transaction: completionTx,
        signer: keypair,
        options: { showEffects: true, showEvents: true }
      });

      console.log(`✅ Token creation completed: ${completionResult.digest}`);

      // Find the pool address from events
      const tokenCreatedEvent = completionResult.events?.find(e => 
        e.type.includes('TokenCreated')
      );

      if (tokenCreatedEvent) {
        const poolAddress = tokenCreatedEvent.parsedJson.token_pool_address;
        console.log(`📍 Token pool created: ${poolAddress}`);

        // Step 3: Mint initial tokens to creator
        await mintInitialTokensToCreator(client, keypair, tokenSymbol, poolAddress, eventData);
      }
    }

    console.log('\n🎉 All tokens processed successfully!');
    console.log('💡 Users should now see their tokens and trading should be active.');

  } catch (error) {
    console.error('❌ Token flow failed:', error);
  }
}

async function checkAndMintInitialTokens(client, keypair, tokenSymbol, poolAddress, eventData) {
  console.log(`🔍 Checking if creator got initial tokens for ${tokenSymbol}...`);
  
  // Check for existing swap events for this creator
  const swapQuery = {
    MoveEventType: `${PACKAGE_ID}::dexsta_token::TokenSwapped`
  };

  const swapEvents = await client.queryEvents({
    query: swapQuery,
    limit: 50,
    order: 'descending'
  });

  const creatorSwap = swapEvents.data?.find(e => {
    const data = e.parsedJson;
    return data.token_pool_address === poolAddress && 
           data.trader === eventData.creator &&
           data.is_buy === true;
  });

  if (creatorSwap) {
    console.log(`✅ Creator already received initial tokens for ${tokenSymbol}`);
    return;
  }

  console.log(`🪙 Minting initial tokens for creator of ${tokenSymbol}...`);
  await mintInitialTokensToCreator(client, keypair, tokenSymbol, poolAddress, eventData);
}

async function mintInitialTokensToCreator(client, keypair, tokenSymbol, poolAddress, eventData) {
  try {
    console.log(`🪙 Minting initial tokens for creator of ${tokenSymbol}...`);

    // Use the initial purchase amount from the event
    const initialSuiAmount = eventData.initial_purchase_amount || 100_000_000; // Default 0.1 SUI
    const expectedTokens = (initialSuiAmount * 100) / 1_000_000_000; // 1 SUI = 100 tokens

    console.log(`💰 Initial purchase details:`);
    console.log(`  SUI amount: ${initialSuiAmount / 1_000_000_000} SUI`);
    console.log(`  Expected tokens: ${expectedTokens}`);

    if (initialSuiAmount === 0) {
      console.log('⚠️ No initial purchase amount, skipping token minting');
      return;
    }

    const purchaseTx = new Transaction();
    const [suiCoin] = purchaseTx.splitCoins(purchaseTx.gas, [initialSuiAmount]);

    // Note: We need the TreasuryCap for this to work
    // For now, skip the initial token minting since we don't have the TreasuryCap
    // The user will need to buy tokens manually after deployment
    console.log('⚠️ Skipping initial token minting - user needs to buy tokens manually after deployment');
    return;

    // TODO: Implement proper initial token minting with TreasuryCap
    // purchaseTx.moveCall({
    //   target: `${PACKAGE_ID}::dexsta_token::mint_initial_tokens`,
    //   arguments: [
    //     purchaseTx.object(poolAddress),
    //     purchaseTx.object(treasuryCapId), // Need to get this from deployment
    //     purchaseTx.pure.u64(initialSuiAmount),
    //   ],
    // });

    const result = await client.signAndExecuteTransaction({
      transaction: purchaseTx,
      signer: keypair,
      options: { showEffects: true, showEvents: true }
    });

    console.log(`✅ Initial tokens purchased: ${result.digest}`);

    // Log swap details
    const swapEvent = result.events?.find(e => e.type.includes('TokenSwapped'));
    if (swapEvent) {
      const data = swapEvent.parsedJson;
      console.log(`💰 Creator received ${data.token_amount} tokens for ${data.sui_amount / 1_000_000_000} SUI`);
    }

  } catch (error) {
    console.error(`❌ Failed to mint initial tokens for ${tokenSymbol}:`, error);
  }
}

// Run the complete flow
completeTokenFlow().catch(console.error);
