#!/usr/bin/env node

/**
 * Deploy Main Platform Contracts
 * 
 * This script deploys the core Dexsta platform contracts
 */

const { SuiClient } = require('@mysten/sui/client');
const { Transaction } = require('@mysten/sui/transactions');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const fs = require('fs');
const path = require('path');

// Contract configuration
const SUI_NETWORK = 'https://fullnode.testnet.sui.io:443';

async function loadKeypair() {
  const keystorePath = path.join(process.env.HOME, '.sui', 'sui_config', 'sui.keystore');
  const keystore = JSON.parse(fs.readFileSync(keystorePath, 'utf8'));
  const privateKeyBytes = Buffer.from(keystore[0], 'base64');
  return Ed25519Keypair.fromSecretKey(privateKeyBytes.slice(1));
}

async function deployContracts(client, keypair) {
  console.log('\n🚀 Deploying Platform Contracts...');
  
  try {
    // Read the compiled bytecode
    const contractsDir = path.join(__dirname, '..', 'contracts_sui');
    
    // Check if we have compiled bytecode
    const buildDir = path.join(contractsDir, 'build', 'dexsta');
    if (!fs.existsSync(buildDir)) {
      console.log('❌ No compiled contracts found. Need to build contracts first.');
      console.log('   Run: cd contracts_sui && sui move build');
      return null;
    }
    
    // For now, let's create the shared objects we need for testing
    // In a real deployment, this would publish the actual contract bytecode
    
    console.log('⚠️  Note: This is a mock deployment for testing purposes');
    console.log('   Real deployment requires Sui CLI to compile and publish contracts');
    
    // Create mock shared objects for testing
    const tx = new Transaction();
    
    // Create a simple shared object to represent our token registry
    // This is just for testing - real contracts would be much more complex
    tx.moveCall({
      target: '0x2::object::new',
      arguments: []
    });

    const result = await client.signAndExecuteTransaction({
      signer: keypair,
      transaction: tx,
      options: { showEffects: true, showEvents: true, showObjectChanges: true }
    });

    console.log('✅ Mock deployment completed!');
    console.log('   Transaction:', result.digest);
    
    return result;
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    throw error;
  }
}

async function createTestTokenRegistry(client, keypair) {
  console.log('\n📋 Creating Token Registry...');
  
  try {
    // This would normally be part of the contract deployment
    // For testing, we'll create a simple transaction
    
    const tx = new Transaction();
    
    // In a real implementation, this would call the token module's init function
    // tx.moveCall({
    //   target: `${PACKAGE_ID}::token::create_registry`,
    //   arguments: []
    // });
    
    // For now, just create a simple transaction to test the flow
    const [coin] = tx.splitCoins(tx.gas, [tx.pure.u64(1000000)]); // 0.001 SUI
    tx.transferObjects([coin], tx.pure.address(keypair.getPublicKey().toSuiAddress()));

    const result = await client.signAndExecuteTransaction({
      signer: keypair,
      transaction: tx,
      options: { showEffects: true, showEvents: true }
    });

    console.log('✅ Token registry created (mock)!');
    console.log('   Transaction:', result.digest);
    
    return result;
  } catch (error) {
    console.error('❌ Registry creation failed:', error.message);
    return null;
  }
}

async function createTestPoolRegistry(client, keypair) {
  console.log('\n🏊 Creating Pool Registry...');
  
  try {
    const tx = new Transaction();
    
    // Mock pool registry creation
    const [coin] = tx.splitCoins(tx.gas, [tx.pure.u64(1000000)]); // 0.001 SUI
    tx.transferObjects([coin], tx.pure.address(keypair.getPublicKey().toSuiAddress()));

    const result = await client.signAndExecuteTransaction({
      signer: keypair,
      transaction: tx,
      options: { showEffects: true, showEvents: true }
    });

    console.log('✅ Pool registry created (mock)!');
    console.log('   Transaction:', result.digest);
    
    return result;
  } catch (error) {
    console.error('❌ Pool registry creation failed:', error.message);
    return null;
  }
}

async function main() {
  console.log('🚀 Dexsta Platform Contract Deployment');
  console.log('=====================================');

  const client = new SuiClient({ url: SUI_NETWORK });
  const keypair = await loadKeypair();
  
  console.log('Deployer address:', keypair.getPublicKey().toSuiAddress());

  try {
    // Check if contracts are already compiled
    const contractsDir = path.join(__dirname, '..', 'contracts_sui');
    const sourcesDir = path.join(contractsDir, 'sources');
    
    console.log('\n📁 Checking contract sources...');
    const sourceFiles = fs.readdirSync(sourcesDir);
    console.log('   Found sources:', sourceFiles.join(', '));
    
    // For now, we'll create the infrastructure needed for testing
    // Real deployment would require Sui CLI
    
    console.log('\n⚠️  IMPORTANT: Real contract deployment requires Sui CLI');
    console.log('   Install Sui CLI and run: sui move build && sui move publish');
    console.log('   For now, creating mock infrastructure for testing...');
    
    // Create mock registries for testing
    await createTestTokenRegistry(client, keypair);
    await createTestPoolRegistry(client, keypair);

    console.log('\n🎉 Mock deployment completed!');
    console.log('\nNext steps for real deployment:');
    console.log('1. Install Sui CLI: https://docs.sui.io/build/install');
    console.log('2. Build contracts: cd contracts_sui && sui move build');
    console.log('3. Publish contracts: sui move publish');
    console.log('4. Update contract addresses in constants.ts');
    console.log('\nFor now, you can test with the admin contracts that are already deployed.');

  } catch (error) {
    console.error('\n💥 Deployment failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
