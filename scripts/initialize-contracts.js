#!/usr/bin/env node

/**
 * Initialize Dexsta Admin Contracts
 * 
 * This script initializes both pool and token admin contracts
 * using the deployer's private key from the Sui keystore.
 */

const { SuiClient } = require('@mysten/sui/client');
const { Transaction } = require('@mysten/sui/transactions');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const fs = require('fs');
const path = require('path');

// Contract configuration
const PACKAGE_ID = '0x2db3d095d44f3f384c54bfc0d8dc7c780e6c1396c518fff6d458b3fbd5771e65';
const DEPLOYER_ADDRESS = '0x7e1b89254c8f898b1a835a7819cfb5a4e1de6a186fcfcbd39a07a4d52c060c5e';
const SUI_NETWORK = 'https://fullnode.testnet.sui.io:443';

// Default settings
const DEFAULT_POOL_SETTINGS = {
  platformFeeAddress: DEPLOYER_ADDRESS,
  platformFeeBps: 100,        // 1%
  lpFeeBps: 20,              // 20% of platform fee
  rewardFeeBps: 50,          // 0.5%
  existingTokenImportFee: 100000000,  // 0.1 SUI
  minRewardTradeAmount: 10000000,     // 0.01 SUI
  initialRewardGoal: 1000000000,      // 1 SUI
  rewardGoalIncrease: 500000000,      // 0.5 SUI
  rewardGoalDecreaseAmount: 100000000, // 0.1 SUI
  rewardGoalDecreaseThreshold: 300,    // 5 minutes
  rewardGoalProximityThreshold: 90     // 90%
};

const DEFAULT_TOKEN_SETTINGS = {
  platformFeeAddress: DEPLOYER_ADDRESS,
  platformFeeBps: 100,        // 1%
  rewardFeeBps: 50,          // 0.5%
  mintFee: 100000000,        // 0.1 SUI
  minRewardTradeAmount: 10000000,     // 0.01 SUI
  initialRewardGoal: 1000000000,      // 1 SUI
  bondingCurveGoal: 60000000000,      // 60 SUI
  rewardGoalIncrease: 500000000,      // 0.5 SUI
  rewardGoalDecreaseAmount: 100000000, // 0.1 SUI
  rewardGoalDecreaseThreshold: 300,    // 5 minutes
  rewardGoalProximityThreshold: 90,    // 90%
  migrationFeePercentage: 5,          // 5%
  migrationGasFee: 5000000           // 0.005 SUI
};

async function loadKeypair() {
  try {
    const keystorePath = path.join(process.env.HOME, '.sui', 'sui_config', 'sui.keystore');
    const keystore = JSON.parse(fs.readFileSync(keystorePath, 'utf8'));

    // Use the first key (should be the deployer key)
    const privateKeyBase64 = keystore[0];

    // Convert from base64 to Uint8Array
    const privateKeyBytes = Buffer.from(privateKeyBase64, 'base64');

    // Create keypair from the raw bytes (skip the first byte which is the scheme flag)
    const keypair = Ed25519Keypair.fromSecretKey(privateKeyBytes.slice(1));

    const address = keypair.getPublicKey().toSuiAddress();
    console.log('✅ Loaded keypair for address:', address);

    // Verify this is the deployer address
    if (address !== DEPLOYER_ADDRESS) {
      console.warn('⚠️  Warning: Loaded address does not match deployer address');
      console.log('   Loaded:', address);
      console.log('   Expected:', DEPLOYER_ADDRESS);
    }

    return keypair;
  } catch (error) {
    console.error('❌ Failed to load keypair:', error.message);
    console.error('   Keystore path:', path.join(process.env.HOME, '.sui', 'sui_config', 'sui.keystore'));
    process.exit(1);
  }
}

async function initializePoolContract(client, keypair) {
  console.log('\n🏊 Initializing Pool Admin Contract...');
  
  try {
    const tx = new Transaction();
    tx.moveCall({
      target: `${PACKAGE_ID}::pool_admin::initialize_platform_settings`,
      arguments: [
        tx.pure.address(DEFAULT_POOL_SETTINGS.platformFeeAddress),
        tx.pure.u16(DEFAULT_POOL_SETTINGS.platformFeeBps),
        tx.pure.u16(DEFAULT_POOL_SETTINGS.lpFeeBps),
        tx.pure.u16(DEFAULT_POOL_SETTINGS.rewardFeeBps),
        tx.pure.u64(DEFAULT_POOL_SETTINGS.existingTokenImportFee),
        tx.pure.u64(DEFAULT_POOL_SETTINGS.minRewardTradeAmount),
        tx.pure.u64(DEFAULT_POOL_SETTINGS.initialRewardGoal),
        tx.pure.u64(DEFAULT_POOL_SETTINGS.rewardGoalIncrease),
        tx.pure.u64(DEFAULT_POOL_SETTINGS.rewardGoalDecreaseAmount),
        tx.pure.u64(DEFAULT_POOL_SETTINGS.rewardGoalDecreaseThreshold),
        tx.pure.u64(DEFAULT_POOL_SETTINGS.rewardGoalProximityThreshold),
      ]
    });

    const result = await client.signAndExecuteTransaction({
      signer: keypair,
      transaction: tx,
      options: { showEffects: true, showEvents: true }
    });

    console.log('✅ Pool contract initialized!');
    console.log('   Transaction:', result.digest);
    return result;
  } catch (error) {
    console.error('❌ Pool initialization failed:', error.message);
    throw error;
  }
}

async function initializeTokenContract(client, keypair) {
  console.log('\n🪙 Initializing Token Admin Contract...');
  
  try {
    const tx = new Transaction();
    tx.moveCall({
      target: `${PACKAGE_ID}::token_admin::initialize_platform_settings`,
      arguments: [
        tx.pure.address(DEFAULT_TOKEN_SETTINGS.platformFeeAddress),
        tx.pure.u16(DEFAULT_TOKEN_SETTINGS.platformFeeBps),
        tx.pure.u16(DEFAULT_TOKEN_SETTINGS.rewardFeeBps),
        tx.pure.u64(DEFAULT_TOKEN_SETTINGS.mintFee),
        tx.pure.u64(DEFAULT_TOKEN_SETTINGS.minRewardTradeAmount),
        tx.pure.u64(DEFAULT_TOKEN_SETTINGS.initialRewardGoal),
        tx.pure.u64(DEFAULT_TOKEN_SETTINGS.bondingCurveGoal),
        tx.pure.u64(DEFAULT_TOKEN_SETTINGS.rewardGoalIncrease),
        tx.pure.u64(DEFAULT_TOKEN_SETTINGS.rewardGoalDecreaseAmount),
        tx.pure.u64(DEFAULT_TOKEN_SETTINGS.rewardGoalDecreaseThreshold),
        tx.pure.u64(DEFAULT_TOKEN_SETTINGS.rewardGoalProximityThreshold),
        tx.pure.u8(DEFAULT_TOKEN_SETTINGS.migrationFeePercentage),
        tx.pure.u64(DEFAULT_TOKEN_SETTINGS.migrationGasFee),
      ]
    });

    const result = await client.signAndExecuteTransaction({
      signer: keypair,
      transaction: tx,
      options: { showEffects: true, showEvents: true }
    });

    console.log('✅ Token contract initialized!');
    console.log('   Transaction:', result.digest);
    return result;
  } catch (error) {
    console.error('❌ Token initialization failed:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Dexsta Admin Contract Initialization');
  console.log('=====================================');
  console.log('Package ID:', PACKAGE_ID);
  console.log('Network:', SUI_NETWORK);
  console.log('Deployer:', DEPLOYER_ADDRESS);

  // Initialize client and keypair
  const client = new SuiClient({ url: SUI_NETWORK });
  const keypair = await loadKeypair();

  try {
    // Initialize both contracts
    await initializePoolContract(client, keypair);
    await initializeTokenContract(client, keypair);

    console.log('\n🎉 All contracts initialized successfully!');
    console.log('\nNext steps:');
    console.log('1. Open http://localhost:3000/admin');
    console.log('2. Connect with the deployer wallet');
    console.log('3. Manage platform settings');

  } catch (error) {
    console.error('\n💥 Initialization failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
