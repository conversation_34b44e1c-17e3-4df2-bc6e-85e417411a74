#!/usr/bin/env node

/**
 * Test Admin Settings Updates
 * 
 * This script tests updating platform settings through the admin contracts
 */

const { SuiClient } = require('@mysten/sui/client');
const { Transaction } = require('@mysten/sui/transactions');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const fs = require('fs');
const path = require('path');

// Contract configuration
const PACKAGE_ID = '0x2db3d095d44f3f384c54bfc0d8dc7c780e6c1396c518fff6d458b3fbd5771e65';
const POOL_SETTINGS_ID = '0x33141502987854513b747d08e48e937e0612e470c12f7432f3c9fe1c33530a84';
const TOKEN_SETTINGS_ID = '0xa71568b23842e569da1ba22335b1e2b3ef0ab65820be8c4c317806fd1d0ff894';
const ADMIN_CAP_POOL = '0xe269ceb255f3a01d462b5c94d074e543b757ac3d5070aa50053248141b644856';
const ADMIN_CAP_TOKEN = '0x82d567dcd127a316cd9af1f44acab906c0930fbfea44ad5b6d2d1f1bd877f752';
const SUI_NETWORK = 'https://fullnode.testnet.sui.io:443';

async function loadKeypair() {
  const keystorePath = path.join(process.env.HOME, '.sui', 'sui_config', 'sui.keystore');
  const keystore = JSON.parse(fs.readFileSync(keystorePath, 'utf8'));
  const privateKeyBytes = Buffer.from(keystore[0], 'base64');
  return Ed25519Keypair.fromSecretKey(privateKeyBytes.slice(1));
}

async function testPoolSettingsUpdate(client, keypair) {
  console.log('\n🏊 Testing Pool Settings Update...');
  
  try {
    const tx = new Transaction();
    tx.moveCall({
      target: `${PACKAGE_ID}::pool_admin::update_platform_constants`,
      arguments: [
        tx.object(POOL_SETTINGS_ID),
        tx.object(ADMIN_CAP_POOL),
        tx.pure.address('0x51ba62cb2f1c1c142dac8a8bf4cec1046084c88003b1a8c294e6da478c7258dc'), // platform_fee_address
        tx.pure.u16(150),  // platform_fee_bps (1.5% - increased from 1%)
        tx.pure.u16(25),   // lp_fee_bps (25% - increased from 20%)
        tx.pure.u16(60),   // reward_fee_bps (0.6% - increased from 0.5%)
        tx.pure.u64(150000000),  // existing_token_import_fee (0.15 SUI - increased)
        tx.pure.u64(15000000),   // min_reward_trade_amount (0.015 SUI - increased)
        tx.pure.u64(1500000000), // initial_reward_goal (1.5 SUI - increased)
        tx.pure.u64(600000000),  // reward_goal_increase (0.6 SUI - increased)
        tx.pure.u64(150000000),  // reward_goal_decrease_amount (0.15 SUI - increased)
        tx.pure.u64(240),        // reward_goal_decrease_threshold (4 minutes - decreased)
        tx.pure.u64(85),         // reward_goal_proximity_threshold (85% - decreased)
      ]
    });

    const result = await client.signAndExecuteTransaction({
      signer: keypair,
      transaction: tx,
      options: { showEffects: true, showEvents: true }
    });

    console.log('✅ Pool settings updated successfully!');
    console.log('   Transaction:', result.digest);
    
    // Check for events
    if (result.events && result.events.length > 0) {
      console.log('   Events emitted:', result.events.length);
      result.events.forEach((event, i) => {
        console.log(`   Event ${i + 1}:`, event.type);
      });
    }
    
    return result;
  } catch (error) {
    console.error('❌ Pool settings update failed:', error.message);
    throw error;
  }
}

async function testTokenSettingsUpdate(client, keypair) {
  console.log('\n🪙 Testing Token Settings Update...');
  
  try {
    const tx = new Transaction();
    tx.moveCall({
      target: `${PACKAGE_ID}::token_admin::update_platform_constants`,
      arguments: [
        tx.object(TOKEN_SETTINGS_ID),
        tx.object(ADMIN_CAP_TOKEN),
        tx.pure.address('0x51ba62cb2f1c1c142dac8a8bf4cec1046084c88003b1a8c294e6da478c7258dc'), // platform_fee_address
        tx.pure.u16(120),  // platform_fee_bps (1.2% - increased from 1%)
        tx.pure.u16(60),   // reward_fee_bps (0.6% - increased from 0.5%)
        tx.pure.u64(120000000),  // mint_fee (0.12 SUI - increased)
        tx.pure.u64(12000000),   // min_reward_trade_amount (0.012 SUI - increased)
        tx.pure.u64(1200000000), // initial_reward_goal (1.2 SUI - increased)
        tx.pure.u64(65000000000), // bonding_curve_goal (65 SUI - increased from 60)
        tx.pure.u64(600000000),  // reward_goal_increase (0.6 SUI - increased)
        tx.pure.u64(120000000),  // reward_goal_decrease_amount (0.12 SUI - increased)
        tx.pure.u64(240),        // reward_goal_decrease_threshold (4 minutes - decreased)
        tx.pure.u64(85),         // reward_goal_proximity_threshold (85% - decreased)
        tx.pure.u8(6),           // migration_fee_percentage (6% - increased from 5%)
        tx.pure.u64(6000000),    // migration_gas_fee (0.006 SUI - increased)
      ]
    });

    const result = await client.signAndExecuteTransaction({
      signer: keypair,
      transaction: tx,
      options: { showEffects: true, showEvents: true }
    });

    console.log('✅ Token settings updated successfully!');
    console.log('   Transaction:', result.digest);
    
    // Check for events
    if (result.events && result.events.length > 0) {
      console.log('   Events emitted:', result.events.length);
      result.events.forEach((event, i) => {
        console.log(`   Event ${i + 1}:`, event.type);
      });
    }
    
    return result;
  } catch (error) {
    console.error('❌ Token settings update failed:', error.message);
    throw error;
  }
}

async function testAddAuthorizedAdmin(client, keypair) {
  console.log('\n👥 Testing Add Authorized Admin...');
  
  // Create a test admin address (this would be a real address in production)
  const testAdminAddress = '0x1234567890123456789012345678901234567890123456789012345678901234';
  
  try {
    const tx = new Transaction();
    tx.moveCall({
      target: `${PACKAGE_ID}::pool_admin::add_admin`,
      arguments: [
        tx.object(POOL_SETTINGS_ID),
        tx.object(ADMIN_CAP_POOL),
        tx.pure.address(testAdminAddress),
      ]
    });

    const result = await client.signAndExecuteTransaction({
      signer: keypair,
      transaction: tx,
      options: { showEffects: true, showEvents: true }
    });

    console.log('✅ Admin added successfully!');
    console.log('   Transaction:', result.digest);
    console.log('   New admin:', testAdminAddress);
    
    return result;
  } catch (error) {
    console.error('❌ Add admin failed:', error.message);
    // This might fail if admin already exists, which is okay for testing
    return null;
  }
}

async function main() {
  console.log('🧪 Testing Admin Settings Updates');
  console.log('=================================');

  const client = new SuiClient({ url: SUI_NETWORK });
  const keypair = await loadKeypair();
  
  console.log('Super admin address:', keypair.getPublicKey().toSuiAddress());

  try {
    // Test 1: Update pool settings
    await testPoolSettingsUpdate(client, keypair);
    
    // Test 2: Update token settings  
    await testTokenSettingsUpdate(client, keypair);
    
    // Test 3: Add authorized admin
    await testAddAuthorizedAdmin(client, keypair);

    console.log('\n🎉 All admin tests completed successfully!');
    console.log('\nNext: Test token creation and trading');

  } catch (error) {
    console.error('\n💥 Admin testing failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
