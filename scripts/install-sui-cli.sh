#!/bin/bash

# Install Sui CLI for macOS
# This script downloads and installs the Sui CLI

echo "🚀 Installing Sui CLI for macOS..."

# Create temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

# Download Sui CLI
echo "📥 Downloading Sui CLI..."
curl -fLJO https://github.com/MystenLabs/sui/releases/download/testnet-v1.36.2/sui-testnet-v1.36.2-macos-x86_64.tgz

# Extract
echo "📦 Extracting..."
tar -xzf sui-testnet-v1.36.2-macos-x86_64.tgz

# Install to /usr/local/bin
echo "🔧 Installing to /usr/local/bin..."
sudo mv sui /usr/local/bin/

# Make executable
sudo chmod +x /usr/local/bin/sui

# Verify installation
echo "✅ Verifying installation..."
sui --version

echo "🎉 Sui CLI installed successfully!"
echo ""
echo "Next steps:"
echo "1. cd contracts_sui"
echo "2. sui move build"
echo "3. sui move publish --gas-budget 100000000"

# Cleanup
cd - > /dev/null
rm -rf "$TEMP_DIR"
