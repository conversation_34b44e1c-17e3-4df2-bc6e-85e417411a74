#!/bin/bash

# XFT Simple Contracts Comprehensive Testing Script
# Tests all core functionality: labels, NFTs, operator licenses, marketplace, and bank

set -e

echo "🚀 Starting XFT Simple Contracts Comprehensive Testing"
echo "====================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_AMOUNT="********"  # 0.01 SUI for small tests
LABEL_FEE="*********0"  # 1 SUI for label creation
LICENSE_FEE="********0" # 0.5 SUI for license creation
NFT_MINT_FEE="********" # 0.01 SUI for NFT minting

echo -e "${BLUE}📋 Test Configuration:${NC}"
echo "  - Test Amount: $TEST_AMOUNT MIST (0.01 SUI)"
echo "  - Label Fee: $LABEL_FEE MIST (1 SUI)"
echo "  - License Fee: $LICENSE_FEE MIST (0.5 SUI)"
echo "  - NFT Mint Fee: $NFT_MINT_FEE MIST (0.01 SUI)"
echo ""

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

# Function to get gas coin
get_gas_coin() {
    sui client gas --json | jq -r '.[] | select(.mistBalance | tonumber > *********) | .gasCoinId' | head -1
}

echo -e "${YELLOW}🔧 Step 1: Environment Setup${NC}"
echo "=============================================="

# Check if we're in the right directory
if [ ! -d "contracts_sui/xft_simple" ]; then
    echo -e "${RED}❌ Please run this script from the project root directory${NC}"
    exit 1
fi

# Build contracts
echo "📦 Building XFT Simple contracts..."
cd contracts_sui/xft_simple
sui move build
print_result $? "XFT Simple contracts built successfully"

# Deploy contracts
echo "🚀 Deploying XFT Simple contracts..."
DEPLOY_OUTPUT=$(sui client publish --gas-budget ********* --json)
PACKAGE_ID=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.type == "published") | .packageId')

if [ "$PACKAGE_ID" = "null" ] || [ -z "$PACKAGE_ID" ]; then
    echo -e "${RED}❌ Failed to deploy contracts${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Contracts deployed successfully${NC}"
echo "📦 Package ID: $PACKAGE_ID"

# Get registry IDs from deployment
LABEL_REGISTRY=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("label::LabelRegistry")) | .objectId')
NFT_REGISTRY=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("nft::NFTRegistry")) | .objectId')
OPERATOR_REGISTRY=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("operator::OperatorRegistry")) | .objectId')
MARKETPLACE_ID=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("marketplace::Marketplace")) | .objectId')
BANK_ID=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("bank::Bank")) | .objectId')

echo "🏷️  Label Registry: $LABEL_REGISTRY"
echo "🎨 NFT Registry: $NFT_REGISTRY"
echo "👥 Operator Registry: $OPERATOR_REGISTRY"
echo "🛒 Marketplace: $MARKETPLACE_ID"
echo "🏦 Bank: $BANK_ID"
echo ""

cd ../..

echo -e "${YELLOW}🏷️ Step 2: Label System Testing${NC}"
echo "=============================================="

# Get gas coin for transactions
GAS_COIN=$(get_gas_coin)
echo "💰 Using gas coin: $GAS_COIN"

# Test 2.1: Create Standard Label (Type 1)
echo "📝 Creating standard label 'dexsta-meme'..."
LABEL_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function create_label \
    --args $LABEL_REGISTRY \
        '"dexsta-meme"' \
        '1' \
        'true' \
        '250' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

LABEL_ID=$(echo $LABEL_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("label::Label")) | .objectId')
print_result $? "Standard label created: $LABEL_ID"

# Test 2.2: Create Profile Label (Type 2)
echo "📝 Creating profile label 'creator-profile'..."
GAS_COIN=$(get_gas_coin)
PROFILE_LABEL_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function create_label \
    --args $LABEL_REGISTRY \
        '"creator-profile"' \
        '2' \
        'true' \
        '500' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

PROFILE_LABEL_ID=$(echo $PROFILE_LABEL_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("label::Label")) | .objectId')
print_result $? "Profile label created: $PROFILE_LABEL_ID"

# Test 2.3: Create Gaming Label (Type 3)
echo "📝 Creating gaming label 'gaming-nft'..."
GAS_COIN=$(get_gas_coin)
GAMING_LABEL_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function create_label \
    --args $LABEL_REGISTRY \
        '"gaming-nft"' \
        '3' \
        'false' \
        '100' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

GAMING_LABEL_ID=$(echo $GAMING_LABEL_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("label::Label")) | .objectId')
print_result $? "Gaming label created: $GAMING_LABEL_ID"

# Test 2.4: Deposit SUI into label
echo "💰 Depositing SUI into standard label..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function deposit \
    --args $LABEL_ID \
        $GAS_COIN \
        $TEST_AMOUNT \
    --gas-budget ******** > /dev/null
print_result $? "SUI deposited into label"

# Test 2.5: Renew label
echo "🔄 Renewing standard label for 1 more year..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function renew_label \
    --args $LABEL_ID \
        '1' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** > /dev/null
print_result $? "Label renewed successfully"

echo ""

echo -e "${YELLOW}👥 Step 3: Operator License Testing${NC}"
echo "=============================================="

# Test 3.1: Create Super Operator License (Role 1)
echo "🎫 Creating super operator license for standard label..."
GAS_COIN=$(get_gas_coin)
SUPER_LICENSE_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module operator \
    --function create_license \
    --args $OPERATOR_REGISTRY \
        $LABEL_ID \
        '1' \
        '3' \
        'true' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

SUPER_LICENSE_ID=$(echo $SUPER_LICENSE_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("operator::OperatorLicense")) | .objectId')
print_result $? "Super operator license created: $SUPER_LICENSE_ID"

# Test 3.2: Create Regular Operator License (Role 2)
echo "🎫 Creating regular operator license for profile label..."
GAS_COIN=$(get_gas_coin)
REGULAR_LICENSE_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module operator \
    --function create_license \
    --args $OPERATOR_REGISTRY \
        $PROFILE_LABEL_ID \
        '2' \
        '6' \
        'false' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

REGULAR_LICENSE_ID=$(echo $REGULAR_LICENSE_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("operator::OperatorLicense")) | .objectId')
print_result $? "Regular operator license created: $REGULAR_LICENSE_ID"

# Test 3.3: Create Marketplace License
echo "🎫 Creating marketplace license for gaming label..."
GAS_COIN=$(get_gas_coin)
MARKETPLACE_LICENSE_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module operator \
    --function create_license \
    --args $OPERATOR_REGISTRY \
        $GAMING_LABEL_ID \
        '1' \
        '12' \
        'true' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

MARKETPLACE_LICENSE_ID=$(echo $MARKETPLACE_LICENSE_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("operator::OperatorLicense")) | .objectId')
print_result $? "Marketplace license created: $MARKETPLACE_LICENSE_ID"

# Test 3.4: Renew operator license
echo "🔄 Renewing super operator license for 3 more months..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module operator \
    --function renew_license \
    --args $SUPER_LICENSE_ID \
        '3' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** > /dev/null
print_result $? "Operator license renewed successfully"

echo ""

echo -e "${YELLOW}🎨 Step 4: NFT System Testing${NC}"
echo "=============================================="

# Test 4.1: Mint NFT linked to standard label
echo "🖼️  Minting VIP Access NFT linked to standard label..."
GAS_COIN=$(get_gas_coin)
NFT_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module nft \
    --function mint_nft \
    --args $NFT_REGISTRY \
        '"VIP Access Pass"' \
        '"Exclusive access to premium features"' \
        '"https://xft.red/nft/vip.png"' \
        '100' \
        '500' \
        $LABEL_ID \
        $GAS_COIN \
    --gas-budget ******** \
    --json)

NFT_ID=$(echo $NFT_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("nft::NFT")) | .objectId')
print_result $? "VIP Access NFT minted: $NFT_ID"

# Test 4.2: Mint Gaming NFT linked to gaming label
echo "🖼️  Minting Gaming NFT linked to gaming label..."
GAS_COIN=$(get_gas_coin)
GAMING_NFT_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module nft \
    --function mint_nft \
    --args $NFT_REGISTRY \
        '"Epic Sword"' \
        '"Legendary weapon for brave warriors"' \
        '"https://xft.red/nft/sword.png"' \
        '1' \
        '1000' \
        $GAMING_LABEL_ID \
        $GAS_COIN \
    --gas-budget ******** \
    --json)

GAMING_NFT_ID=$(echo $GAMING_NFT_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("nft::NFT")) | .objectId')
print_result $? "Gaming NFT minted: $GAMING_NFT_ID"

# Test 4.3: Deposit SUI into NFT
echo "💰 Depositing SUI into VIP Access NFT..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module nft \
    --function deposit \
    --args $NFT_ID \
        $GAS_COIN \
        $TEST_AMOUNT \
    --gas-budget ******** > /dev/null
print_result $? "SUI deposited into NFT"

# Test 4.4: Deposit more SUI into Gaming NFT for collateral
echo "💰 Depositing SUI into Gaming NFT for loan collateral..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module nft \
    --function deposit \
    --args $GAMING_NFT_ID \
        $GAS_COIN \
        '*********' \
    --gas-budget ******** > /dev/null
print_result $? "SUI deposited into Gaming NFT for collateral"

echo ""

echo -e "${YELLOW}🏦 Step 5: Bank System Testing${NC}"
echo "=============================================="

# Test 5.1: Deposit funds into bank
echo "🏦 Depositing funds into bank..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module bank \
    --function deposit_to_bank \
    --args $BANK_ID \
        $GAS_COIN \
        '********0' \
    --gas-budget ******** > /dev/null
print_result $? "Funds deposited into bank"

# Test 5.2: Issue loan against Gaming NFT
echo "💳 Issuing loan against Gaming NFT..."
LOAN_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module bank \
    --function issue_loan \
    --args $BANK_ID \
        $GAMING_NFT_ID \
        '0x6' \
    --gas-budget ******** \
    --json)

LOAN_ID=$(echo $LOAN_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("bank::Loan")) | .objectId' | head -1)
print_result $? "Loan issued against Gaming NFT: $LOAN_ID"

echo ""

echo -e "${YELLOW}🛒 Step 6: Marketplace Testing${NC}"
echo "=============================================="

# Test 6.1: Create marketplace listing for VIP Access NFT
echo "📋 Creating marketplace listing for VIP Access NFT..."
GAS_COIN=$(get_gas_coin)
LISTING_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module marketplace \
    --function create_listing \
    --args $MARKETPLACE_ID \
        $NFT_ID \
        '*********' \
        '0x6' \
    --gas-budget ******** \
    --json)

print_result $? "Marketplace listing created for VIP Access NFT"

echo ""

echo -e "${YELLOW}⏰ Step 7: Advanced Testing${NC}"
echo "=============================================="

# Test 7.1: Withdraw from label
echo "💸 Withdrawing SUI from standard label..."
sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function withdraw \
    --args $LABEL_ID \
        '5000000' \
    --gas-budget ******** > /dev/null
print_result $? "SUI withdrawn from label"

# Test 7.2: Withdraw from NFT
echo "💸 Withdrawing SUI from VIP Access NFT..."
sui client call \
    --package $PACKAGE_ID \
    --module nft \
    --function withdraw \
    --args $NFT_ID \
        '5000000' \
    --gas-budget ******** > /dev/null
print_result $? "SUI withdrawn from NFT"

echo ""

echo -e "${GREEN}🎉 ALL XFT SIMPLE TESTS COMPLETED SUCCESSFULLY!${NC}"
echo "=============================================="
echo ""
echo -e "${BLUE}📊 Test Summary:${NC}"
echo "✅ Label creation (3 different types)"
echo "✅ Label asset management (deposit/withdraw/renewal)"
echo "✅ Operator license creation (3 different roles)"
echo "✅ License renewal"
echo "✅ NFT minting with label association (2 NFTs)"
echo "✅ NFT asset storage and withdrawal"
echo "✅ Bank funding and loan issuance"
echo "✅ Marketplace listing creation"
echo ""
echo -e "${BLUE}📝 Created Objects:${NC}"
echo "🏷️  Standard Label: $LABEL_ID"
echo "🏷️  Profile Label: $PROFILE_LABEL_ID"
echo "🏷️  Gaming Label: $GAMING_LABEL_ID"
echo "🎫 Super License: $SUPER_LICENSE_ID"
echo "🎫 Regular License: $REGULAR_LICENSE_ID"
echo "🎫 Marketplace License: $MARKETPLACE_LICENSE_ID"
echo "🎨 VIP Access NFT: $NFT_ID"
echo "🎨 Gaming NFT: $GAMING_NFT_ID"
echo "💳 Loan: $LOAN_ID"
echo ""
echo -e "${YELLOW}💡 Key Features Demonstrated:${NC}"
echo "🔹 Multi-type label system with different royalty rates"
echo "🔹 Role-based operator licensing with expiration"
echo "🔹 NFT-label association for ecosystem organization"
echo "🔹 Asset storage in both labels and NFTs"
echo "🔹 Collateralized lending against NFT assets"
echo "🔹 Marketplace integration with fee distribution"
echo "🔹 Renewal mechanisms for sustainability"
echo ""
echo -e "${GREEN}🚀 XFT Simple Contract System is fully functional!${NC}"
echo ""
echo -e "${BLUE}🔗 Next Steps for Dexsta Integration:${NC}"
echo "1. Integrate XFT labels for token categorization"
echo "2. Use NFTs for private pool access control"
echo "3. Implement fee-earning NFTs for creators"
echo "4. Add collateral-based trading features"
echo "5. Create event ticket NFTs with revenue sharing"
