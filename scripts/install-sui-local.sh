#!/bin/bash

# Install Sui CLI locally (no sudo required)
echo "🚀 Installing Sui CLI locally..."

# Create local bin directory
mkdir -p ~/.local/bin

# Create temporary directory
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

# Download Sui CLI
echo "📥 Downloading Sui CLI..."
curl -fLJO https://github.com/MystenLabs/sui/releases/download/testnet-v1.36.2/sui-testnet-v1.36.2-macos-x86_64.tgz

# Extract
echo "📦 Extracting..."
tar -xzf sui-testnet-v1.36.2-macos-x86_64.tgz

# Install to local bin
echo "🔧 Installing to ~/.local/bin..."
mv sui ~/.local/bin/

# Make executable
chmod +x ~/.local/bin/sui

# Add to PATH for this session
export PATH="$HOME/.local/bin:$PATH"

# Verify installation
echo "✅ Verifying installation..."
~/.local/bin/sui --version

echo "🎉 Sui CLI installed successfully!"
echo ""
echo "To use sui command, run: export PATH=\"\$HOME/.local/bin:\$PATH\""
echo "Or use the full path: ~/.local/bin/sui"

# Cleanup
cd - > /dev/null
rm -rf "$TEMP_DIR"
