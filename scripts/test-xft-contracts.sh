#!/bin/bash

# XFT Contracts Comprehensive Testing Script
# Run this script to test all XFT contract functionality

set -e

echo "🚀 Starting XFT Contracts Comprehensive Testing"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_AMOUNT="10000000"  # 0.01 SUI for small tests
LABEL_FEE="**********"  # 1 SUI for label creation
LICENSE_FEE="********0" # 0.5 SUI for license creation

echo -e "${BLUE}📋 Test Configuration:${NC}"
echo "  - Test Amount: $TEST_AMOUNT MIST (0.01 SUI)"
echo "  - Label Fee: $LABEL_FEE MIST (1 SUI)"
echo "  - License Fee: $LICENSE_FEE MIST (0.5 SUI)"
echo ""

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

# Function to get gas coin
get_gas_coin() {
    sui client gas --json | jq -r '.[] | select(.mistBalance | tonumber > ********) | .gasCoinId' | head -1
}

echo -e "${YELLOW}🔧 Step 1: Environment Setup${NC}"
echo "=============================================="

# Check if we're in the right directory
if [ ! -d "contracts_sui/xft" ]; then
    echo -e "${RED}❌ Please run this script from the project root directory${NC}"
    exit 1
fi

# Build contracts
echo "📦 Building XFT contracts..."
cd contracts_sui/xft
sui move build
print_result $? "XFT contracts built successfully"

# Deploy contracts
echo "🚀 Deploying XFT contracts..."
DEPLOY_OUTPUT=$(sui client publish --gas-budget 200000000 --json)
PACKAGE_ID=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.type == "published") | .packageId')

if [ "$PACKAGE_ID" = "null" ] || [ -z "$PACKAGE_ID" ]; then
    echo -e "${RED}❌ Failed to deploy contracts${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Contracts deployed successfully${NC}"
echo "📦 Package ID: $PACKAGE_ID"

# Get registry IDs from deployment
LABEL_REGISTRY=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("label::LabelRegistry")) | .objectId')
NFT_REGISTRY=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("nft::NFTRegistry")) | .objectId')
OPERATOR_REGISTRY=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("operator::OperatorRegistry")) | .objectId')
MARKETPLACE_ID=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("marketplace::Marketplace")) | .objectId')
BANK_ID=$(echo $DEPLOY_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("bank::Bank")) | .objectId')

echo "🏷️  Label Registry: $LABEL_REGISTRY"
echo "🎨 NFT Registry: $NFT_REGISTRY"
echo "👥 Operator Registry: $OPERATOR_REGISTRY"
echo "🛒 Marketplace: $MARKETPLACE_ID"
echo "🏦 Bank: $BANK_ID"
echo ""

cd ../..

echo -e "${YELLOW}🏷️ Step 2: Label Creation Testing${NC}"
echo "=============================================="

# Get gas coin for transactions
GAS_COIN=$(get_gas_coin)
echo "💰 Using gas coin: $GAS_COIN"

# Test 2.1: Create Standard Label
echo "📝 Creating standard label 'dexsta-meme'..."
LABEL_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function create_label \
    --args $LABEL_REGISTRY \
        '"dexsta-meme"' \
        '[1]' \
        'true' \
        '250' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

LABEL_ID=$(echo $LABEL_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("label::Label")) | .objectId')
print_result $? "Standard label created: $LABEL_ID"

# Test 2.2: Create Profile Label
echo "📝 Creating profile label 'creator-profile'..."
GAS_COIN=$(get_gas_coin)
PROFILE_LABEL_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function create_label \
    --args $LABEL_REGISTRY \
        '"creator-profile"' \
        '[2]' \
        'true' \
        '500' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

PROFILE_LABEL_ID=$(echo $PROFILE_LABEL_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("label::Label")) | .objectId')
print_result $? "Profile label created: $PROFILE_LABEL_ID"

# Test 2.3: Deposit SUI into label
echo "💰 Depositing SUI into label..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function deposit \
    --args $LABEL_ID \
        $GAS_COIN \
        $TEST_AMOUNT \
    --gas-budget 20000000 > /dev/null
print_result $? "SUI deposited into label"

echo ""

echo -e "${YELLOW}👥 Step 3: Operator License Testing${NC}"
echo "=============================================="

# Test 3.1: Create Super Operator License
echo "🎫 Creating super operator license..."
GAS_COIN=$(get_gas_coin)
SUPER_LICENSE_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module operator \
    --function create_license \
    --args $OPERATOR_REGISTRY \
        $LABEL_ID \
        '1' \
        '3' \
        'true' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

SUPER_LICENSE_ID=$(echo $SUPER_LICENSE_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("operator::License")) | .objectId')
print_result $? "Super operator license created: $SUPER_LICENSE_ID"

# Test 3.2: Create Regular Operator License
echo "🎫 Creating regular operator license..."
GAS_COIN=$(get_gas_coin)
REGULAR_LICENSE_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module operator \
    --function create_license \
    --args $OPERATOR_REGISTRY \
        $LABEL_ID \
        '2' \
        '6' \
        'false' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

REGULAR_LICENSE_ID=$(echo $REGULAR_LICENSE_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("operator::License")) | .objectId')
print_result $? "Regular operator license created: $REGULAR_LICENSE_ID"

# Test 3.3: Create Marketplace License
echo "🎫 Creating marketplace license..."
GAS_COIN=$(get_gas_coin)
MARKETPLACE_LICENSE_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module operator \
    --function create_license \
    --args $OPERATOR_REGISTRY \
        $PROFILE_LABEL_ID \
        '1' \
        '12' \
        'true' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

MARKETPLACE_LICENSE_ID=$(echo $MARKETPLACE_LICENSE_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("operator::License")) | .objectId')
print_result $? "Marketplace license created: $MARKETPLACE_LICENSE_ID"

echo ""

echo -e "${YELLOW}🎨 Step 4: NFT Creation Testing${NC}"
echo "=============================================="

# Test 4.1: Mint NFT linked to label
echo "🖼️  Minting NFT linked to label..."
GAS_COIN=$(get_gas_coin)
NFT_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module nft \
    --function mint_nft \
    --args $NFT_REGISTRY \
        '"VIP Access Pass"' \
        '"Exclusive access to premium features"' \
        '"https://xft.red/nft/vip.png"' \
        '100' \
        '[1, **********]' \
        'true' \
        '500' \
        $LABEL_ID \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** \
    --json)

NFT_ID=$(echo $NFT_OUTPUT | jq -r '.objectChanges[] | select(.objectType | contains("nft::NFTData")) | .objectId')
print_result $? "NFT minted and linked to label: $NFT_ID"

# Test 4.2: Deposit SUI into NFT
echo "💰 Depositing SUI into NFT..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module nft \
    --function deposit \
    --args $NFT_ID \
        $GAS_COIN \
        $TEST_AMOUNT \
    --gas-budget 20000000 > /dev/null
print_result $? "SUI deposited into NFT"

echo ""

echo -e "${YELLOW}🛒 Step 5: Marketplace Testing${NC}"
echo "=============================================="

# Test 5.1: Create marketplace listing
echo "📋 Creating marketplace listing..."
GAS_COIN=$(get_gas_coin)
LISTING_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module marketplace \
    --function create_listing \
    --args $MARKETPLACE_ID \
        $NFT_ID \
        '*********' \
        '1' \
        '[0]' \
        $PROFILE_LABEL_ID \
        'false' \
        '0x6' \
    --gas-budget ******** \
    --json)

print_result $? "Marketplace listing created"

echo ""

echo -e "${YELLOW}🏦 Step 6: Bank/Lending Testing${NC}"
echo "=============================================="

# Test 6.1: Issue loan against NFT
echo "💳 Issuing loan against NFT..."
LOAN_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module bank \
    --function issue_loan \
    --args $BANK_ID \
        $NFT_ID \
        '0x6' \
    --gas-budget ******** \
    --json)

print_result $? "Loan issued against NFT collateral"

echo ""

echo -e "${YELLOW}⏰ Step 7: Renewal Testing${NC}"
echo "=============================================="

# Test 7.1: Renew label
echo "🔄 Renewing label for 1 more year..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function renew_label \
    --args $LABEL_REGISTRY \
        $LABEL_ID \
        '1' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** > /dev/null
print_result $? "Label renewed successfully"

# Test 7.2: Renew operator license
echo "🔄 Renewing operator license for 3 more months..."
GAS_COIN=$(get_gas_coin)
sui client call \
    --package $PACKAGE_ID \
    --module operator \
    --function renew_license \
    --args $OPERATOR_REGISTRY \
        $SUPER_LICENSE_ID \
        '3' \
        $GAS_COIN \
        '0x6' \
    --gas-budget ******** > /dev/null
print_result $? "Operator license renewed successfully"

echo ""

echo -e "${GREEN}🎉 ALL TESTS COMPLETED SUCCESSFULLY!${NC}"
echo "=============================================="
echo ""
echo -e "${BLUE}📊 Test Summary:${NC}"
echo "✅ Label creation (multiple types)"
echo "✅ Label asset management (deposit/withdraw)"
echo "✅ Operator license creation (multiple roles)"
echo "✅ NFT minting with label association"
echo "✅ NFT asset storage"
echo "✅ Marketplace listing creation"
echo "✅ Bank loan issuance"
echo "✅ Label renewal"
echo "✅ License renewal"
echo ""
echo -e "${BLUE}📝 Created Objects:${NC}"
echo "🏷️  Standard Label: $LABEL_ID"
echo "🏷️  Profile Label: $PROFILE_LABEL_ID"
echo "🎫 Super License: $SUPER_LICENSE_ID"
echo "🎫 Regular License: $REGULAR_LICENSE_ID"
echo "🎫 Marketplace License: $MARKETPLACE_LICENSE_ID"
echo "🎨 NFT: $NFT_ID"
echo ""
echo -e "${YELLOW}💡 Next Steps:${NC}"
echo "1. Test marketplace purchases"
echo "2. Test loan repayment"
echo "3. Test license expiration scenarios"
echo "4. Test unauthorized access attempts"
echo "5. Test asset withdrawal from NFTs"
echo ""
echo -e "${GREEN}🚀 XFT Contract System is fully functional!${NC}"
