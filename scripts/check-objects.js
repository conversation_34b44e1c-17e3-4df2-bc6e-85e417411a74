#!/usr/bin/env node

/**
 * Check Deployed Objects
 * 
 * This script checks if our deployed objects exist and are accessible
 */

const { SuiClient } = require('@mysten/sui/client');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const fs = require('fs');
const path = require('path');

// Contract configuration
const TOKEN_PLATFORM_SETTINGS_ID = '0xa71568b23842e569da1ba22335b1e2b3ef0ab65820be8c4c317806fd1d0ff894';
const TOKEN_REGISTRY_ID = '0x2d6c39f3689edd6696e3f79f85739b374e69da313e4802c9b4efa61270ee4fd0';
const POOL_REGISTRY_ID = '0x4057d2af41346cf18b3767b60c41e86241aafbfba85f52f7a8986aad4265cd3c';
const SUI_NETWORK = 'https://fullnode.testnet.sui.io:443';

async function loadKeypair() {
  const keystorePath = path.join(process.env.HOME, '.sui', 'sui_config', 'sui.keystore');
  const keystore = JSON.parse(fs.readFileSync(keystorePath, 'utf8'));
  const privateKeyBytes = Buffer.from(keystore[0], 'base64');
  return Ed25519Keypair.fromSecretKey(privateKeyBytes.slice(1));
}

async function checkObject(client, objectId, name) {
  console.log(`\n🔍 Checking ${name}...`);
  console.log(`   Object ID: ${objectId}`);
  
  try {
    const object = await client.getObject({
      id: objectId,
      options: { showContent: true, showType: true }
    });
    
    if (object.data) {
      console.log('✅ Object exists!');
      console.log('   Type:', object.data.type);
      console.log('   Version:', object.data.version);
      console.log('   Digest:', object.data.digest);
      
      if (object.data.content) {
        console.log('   Content type:', object.data.content.type);
        if (object.data.content.fields) {
          console.log('   Fields:', Object.keys(object.data.content.fields));
        }
      }
      
      return object;
    } else {
      console.log('❌ Object not found or not accessible');
      return null;
    }
  } catch (error) {
    console.log('❌ Error checking object:', error.message);
    return null;
  }
}

async function main() {
  console.log('🔍 Checking Deployed Objects');
  console.log('============================');

  const client = new SuiClient({ url: SUI_NETWORK });
  const keypair = await loadKeypair();
  
  console.log('Checker address:', keypair.getPublicKey().toSuiAddress());

  try {
    // Check all our deployed objects
    await checkObject(client, TOKEN_PLATFORM_SETTINGS_ID, 'Token Platform Settings');
    await checkObject(client, TOKEN_REGISTRY_ID, 'Token Registry');
    await checkObject(client, POOL_REGISTRY_ID, 'Pool Registry');

    console.log('\n🎉 Object check completed!');

  } catch (error) {
    console.error('\n💥 Object check failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
