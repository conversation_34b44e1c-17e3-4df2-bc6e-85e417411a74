#!/bin/bash

# Dexsta Platform - Deployment Setup Checker
# Verifies all prerequisites for contract deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Dexsta Platform - Deployment Setup Check${NC}"
echo -e "${BLUE}============================================${NC}"
echo ""

ISSUES=0

# Check 1: Sui CLI
echo -e "${BLUE}1. Checking Sui CLI...${NC}"
if command -v sui &> /dev/null; then
    SUI_VERSION=$(sui --version)
    echo -e "${GREEN}✅ Sui CLI found: $SUI_VERSION${NC}"
else
    echo -e "${RED}❌ Sui CLI not found${NC}"
    echo -e "   Install from: https://docs.sui.io/guides/developer/getting-started/sui-install"
    ISSUES=$((ISSUES + 1))
fi

# Check 2: Wallet Configuration
echo -e "\n${BLUE}2. Checking Wallet Configuration...${NC}"
if sui client active-address &> /dev/null; then
    ACTIVE_ADDRESS=$(sui client active-address)
    echo -e "${GREEN}✅ Active wallet: $ACTIVE_ADDRESS${NC}"
    
    # Check balance
    if command -v jq &> /dev/null; then
        BALANCE=$(sui client balance --json 2>/dev/null | jq -r '.totalBalance // 0' 2>/dev/null || echo "0")
        BALANCE_SUI=$(echo "scale=4; $BALANCE / 1000000000" | bc -l 2>/dev/null || echo "0")
        echo -e "   Balance: ${YELLOW}$BALANCE_SUI SUI${NC}"
        
        if (( $(echo "$BALANCE_SUI < 1" | bc -l) )); then
            echo -e "${YELLOW}⚠️  Low balance. You may need more SUI for deployment${NC}"
            echo -e "   Get testnet SUI from: https://discord.gg/sui"
        fi
    else
        echo -e "${YELLOW}⚠️  jq not found, cannot check balance${NC}"
        echo -e "   Install jq: brew install jq (macOS) or apt install jq (Linux)"
    fi
else
    echo -e "${RED}❌ No active wallet found${NC}"
    echo -e "   Run: sui client new-address ed25519"
    ISSUES=$((ISSUES + 1))
fi

# Check 3: Network Environment
echo -e "\n${BLUE}3. Checking Network Environment...${NC}"
if sui client active-env &> /dev/null; then
    ACTIVE_ENV=$(sui client active-env)
    echo -e "${GREEN}✅ Active environment: $ACTIVE_ENV${NC}"
    
    if [[ "$ACTIVE_ENV" == *"testnet"* ]]; then
        echo -e "${GREEN}✅ Connected to testnet (recommended for testing)${NC}"
    elif [[ "$ACTIVE_ENV" == *"mainnet"* ]]; then
        echo -e "${YELLOW}⚠️  Connected to mainnet (use with caution)${NC}"
    else
        echo -e "${YELLOW}⚠️  Unknown network environment${NC}"
    fi
else
    echo -e "${RED}❌ No active environment found${NC}"
    echo -e "   Run: sui client new-env --alias testnet --rpc https://fullnode.testnet.sui.io:443"
    ISSUES=$((ISSUES + 1))
fi

# Check 4: Contract Files
echo -e "\n${BLUE}4. Checking Contract Files...${NC}"
CONTRACTS_DIR="contracts_sui"
REQUIRED_CONTRACTS=("token.move" "pool.move" "token_admin.move" "pool_admin.move")

if [ -d "$CONTRACTS_DIR" ]; then
    echo -e "${GREEN}✅ Contracts directory found${NC}"
    
    for contract in "${REQUIRED_CONTRACTS[@]}"; do
        if [ -f "$CONTRACTS_DIR/$contract" ]; then
            echo -e "${GREEN}✅ $contract found${NC}"
        else
            echo -e "${RED}❌ $contract missing${NC}"
            ISSUES=$((ISSUES + 1))
        fi
    done
else
    echo -e "${RED}❌ Contracts directory '$CONTRACTS_DIR' not found${NC}"
    ISSUES=$((ISSUES + 1))
fi

# Check 5: Required Tools
echo -e "\n${BLUE}5. Checking Required Tools...${NC}"

# Check jq
if command -v jq &> /dev/null; then
    echo -e "${GREEN}✅ jq found (for JSON parsing)${NC}"
else
    echo -e "${YELLOW}⚠️  jq not found (recommended for deployment script)${NC}"
    echo -e "   Install: brew install jq (macOS) or apt install jq (Linux)"
fi

# Check bc
if command -v bc &> /dev/null; then
    echo -e "${GREEN}✅ bc found (for calculations)${NC}"
else
    echo -e "${YELLOW}⚠️  bc not found (recommended for balance calculations)${NC}"
    echo -e "   Install: brew install bc (macOS) or apt install bc (Linux)"
fi

# Check 6: Node.js and npm (for frontend)
echo -e "\n${BLUE}6. Checking Frontend Prerequisites...${NC}"
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js found: $NODE_VERSION${NC}"
    
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        echo -e "${GREEN}✅ npm found: $NPM_VERSION${NC}"
    else
        echo -e "${YELLOW}⚠️  npm not found${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Node.js not found (needed for frontend)${NC}"
    echo -e "   Install from: https://nodejs.org/"
fi

# Check if package.json exists
if [ -f "package.json" ]; then
    echo -e "${GREEN}✅ package.json found${NC}"
    
    # Check if dependencies are installed
    if [ -d "node_modules" ]; then
        echo -e "${GREEN}✅ node_modules found${NC}"
    else
        echo -e "${YELLOW}⚠️  node_modules not found${NC}"
        echo -e "   Run: npm install"
    fi
else
    echo -e "${YELLOW}⚠️  package.json not found${NC}"
fi

# Summary
echo -e "\n${BLUE}📊 Setup Summary${NC}"
echo -e "${BLUE}=================${NC}"

if [ $ISSUES -eq 0 ]; then
    echo -e "${GREEN}🎉 All checks passed! You're ready to deploy.${NC}"
    echo -e "\n${BLUE}Next Steps:${NC}"
    echo -e "1. Run: ${YELLOW}./deploy-contracts.sh testnet${NC}"
    echo -e "2. Follow the deployment guide: ${YELLOW}CONTRACT_DEPLOYMENT_GUIDE.md${NC}"
    echo -e "3. Initialize contracts through admin interface"
else
    echo -e "${RED}❌ Found $ISSUES issue(s) that need to be resolved.${NC}"
    echo -e "\n${BLUE}Please fix the issues above before deploying.${NC}"
fi

echo -e "\n${BLUE}📚 Helpful Resources:${NC}"
echo -e "• Sui Documentation: https://docs.sui.io"
echo -e "• Sui Discord: https://discord.gg/sui"
echo -e "• Deployment Guide: CONTRACT_DEPLOYMENT_GUIDE.md"
echo -e "• Testnet Faucet: Use !faucet [address] in #testnet-faucet on Discord"

exit $ISSUES
