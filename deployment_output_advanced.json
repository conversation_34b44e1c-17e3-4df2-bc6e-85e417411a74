[warning] Client/Server api version mismatch, client api version : 1.50.1, server api version : 1.51.0
[Note]: Dependency sources are no longer verified automatically during publication and upgrade. You can pass the `--verify-deps` option if you would like to verify them as part of publication or upgrade.
INCLUDING DEP<PERSON><PERSON>NCY Bridge
INCLUDING DEPENDENCY SuiSystem
INCLUDING DEPENDENCY Sui
INCLUDING DEPENDENCY MoveStdlib
BUILDING dexsta
Skipping dependency verification

thread 'main' panicked at /private/tmp/sui-20250610-8506-6ujoqu/sui-testnet-v1.50.1/external-crates/move/crates/move-bytecode-utils/src/lib.rs:29:17:
Duplicate module found: 0x0000000000000000000000000000000000000000000000000000000000000002::groth16
note: run with `RUST_BACKTRACE=1` environment variable to display a backtrace
2025-06-20T23:03:34.235434Z ERROR telemetry_subscribers: panicked at /private/tmp/sui-20250610-8506-6ujoqu/sui-testnet-v1.50.1/external-crates/move/crates/move-bytecode-utils/src/lib.rs:29:17:
Duplicate module found: 0x0000000000000000000000000000000000000000000000000000000000000002::groth16 panic.file="/private/tmp/sui-20250610-8506-6ujoqu/sui-testnet-v1.50.1/external-crates/move/crates/move-bytecode-utils/src/lib.rs" panic.line=29 panic.column=17
