# XFT Contracts Deployment Guide

## Prerequisites

### 1. Install Sui CLI

**For macOS:**
```bash
# Download and install Sui CLI
curl -fLJO https://github.com/MystenLabs/sui/releases/download/devnet-v1.36.2/sui-devnet-v1.36.2-macos-x86_64.tgz
tar -xzf sui-devnet-v1.36.2-macos-x86_64.tgz
sudo mv sui /usr/local/bin/
sudo chmod +x /usr/local/bin/sui
```

**For Linux:**
```bash
curl -fLJO https://github.com/MystenLabs/sui/releases/download/devnet-v1.36.2/sui-devnet-v1.36.2-ubuntu-x86_64.tgz
tar -xzf sui-devnet-v1.36.2-ubuntu-x86_64.tgz
sudo mv sui /usr/local/bin/
sudo chmod +x /usr/local/bin/sui
```

**Verify installation:**
```bash
sui --version
```

### 2. Setup Sui Wallet

```bash
# Create new wallet
sui client new-address ed25519

# Switch to devnet
sui client switch --env devnet

# Get devnet SUI from faucet
sui client faucet

# Check balance
sui client balance
```

## Deployment Steps

### Step 1: Build XFT Contracts

```bash
cd contracts/xft
sui move build
```

### Step 2: Deploy Contracts

```bash
# Deploy all XFT contracts
sui client publish --gas-budget 200000000 --json > deployment_output.json

# Extract package ID
PACKAGE_ID=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.type == "published") | .packageId')
echo "Package ID: $PACKAGE_ID"
```

### Step 3: Extract Contract Object IDs

```bash
# Extract registry and contract IDs
FIRE_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("fire::FireRegistry")) | .objectId')
GLOBAL_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("registry::GlobalRegistry")) | .objectId')
LABEL_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("label::LabelRegistry")) | .objectId')
XFT_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("xft::XFTRegistry")) | .objectId')
OPERATOR_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("operator::OperatorRegistry")) | .objectId')
MARKETPLACE=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("marketplace::Marketplace")) | .objectId')

echo "🔥 Fire Registry: $FIRE_REGISTRY"
echo "🌐 Global Registry: $GLOBAL_REGISTRY"
echo "🏷️  Label Registry: $LABEL_REGISTRY"
echo "🎨 XFT Registry: $XFT_REGISTRY"
echo "👥 Operator Registry: $OPERATOR_REGISTRY"
echo "🛒 Marketplace: $MARKETPLACE"
```

### Step 4: Initialize Fire Registry

```bash
# Initialize Fire Registry with all contract addresses
sui client call \
    --package $PACKAGE_ID \
    --module fire \
    --function initialize_contracts \
    --args $FIRE_REGISTRY \
        $GLOBAL_REGISTRY \
        $LABEL_REGISTRY \
        $XFT_REGISTRY \
        $OPERATOR_REGISTRY \
        $MARKETPLACE \
    --gas-budget 50000000
```

### Step 5: Test Basic Functionality

```bash
# Test 1: Create a label
sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function create_label \
    --args $LABEL_REGISTRY \
        $GLOBAL_REGISTRY \
        $FIRE_REGISTRY \
        '"test-label"' \
        '[1, 1000000000, 1, 1, 0, 0, 1, 0, 0, 1, 0, 250, 0, 0]' \
        'true' \
        '0x6' \
    --gas-budget 50000000 \
    --json > label_creation.json

# Extract label ID
LABEL_ID=$(cat label_creation.json | jq -r '.objectChanges[] | select(.objectType | contains("label::Label")) | .objectId')
echo "✅ Label created: $LABEL_ID"
```

## Environment Configuration

Create a `.env.deployment` file with the deployed contract addresses:

```bash
cat > .env.deployment << EOF
# Dexsta XFT Contracts - Devnet Deployment
NEXT_PUBLIC_SUI_NETWORK=devnet
NEXT_PUBLIC_PACKAGE_ID=$PACKAGE_ID
NEXT_PUBLIC_FIRE_REGISTRY_ID=$FIRE_REGISTRY
NEXT_PUBLIC_GLOBAL_REGISTRY_ID=$GLOBAL_REGISTRY
NEXT_PUBLIC_LABEL_REGISTRY_ID=$LABEL_REGISTRY
NEXT_PUBLIC_XFT_REGISTRY_ID=$XFT_REGISTRY
NEXT_PUBLIC_OPERATOR_REGISTRY_ID=$OPERATOR_REGISTRY
NEXT_PUBLIC_MARKETPLACE_ID=$MARKETPLACE
EOF
```

## Testing Account

Use this testing account for development:
- **Address**: `0x107850bf3302019d71a699210d8fcf1789065700d3a53f2d6bd7322733c4d7fe`
- **Private Key**: `suiprivkey1qq96u4z3h5ze7ptnm69d45fugmutr499mwddfqt6009jzfgqypge6fmrzws`

```bash
# Import testing account
sui keytool import suiprivkey1qq96u4z3h5ze7ptnm69d45fugmutr499mwddfqt6009jzfgqypge6fmrzws ed25519

# Switch to testing account
sui client switch --address 0x107850bf3302019d71a699210d8fcf1789065700d3a53f2d6bd7322733c4d7fe
```

## Next Steps

1. **Copy contract addresses** from `.env.deployment` to your `.env.local`
2. **Update frontend constants** with new contract addresses
3. **Test contract functionality** using the provided test scripts
4. **Initialize platform settings** through admin interface

## Troubleshooting

### Common Issues:

1. **Gas budget too low**: Increase `--gas-budget` to *********
2. **Insufficient balance**: Get more SUI from faucet: `sui client faucet`
3. **Build errors**: Check Move.toml dependencies and addresses
4. **Network issues**: Ensure you're on devnet: `sui client active-env`

### Useful Commands:

```bash
# Check active address
sui client active-address

# Check balance
sui client balance

# Get gas coins
sui client gas

# Check object details
sui client object <OBJECT_ID>
```
