/**
 * Live Token Deployment Script
 * Actually publishes the token to Sui devnet
 */

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");
const { SuiClient, getFullnodeUrl } = require('@mysten/sui/client');

// Configuration
const NETWORK = 'devnet';
const PACKAGE_ID = '0x75da1bbe12c141aa2269396420b17355847063e55f0211fe97b3969c49618bcf';

async function deployTokenLive() {
  console.log('🚀 Live Token Deployment');
  console.log('========================');

  // Initialize Sui client
  const client = new SuiClient({ url: getFullnodeUrl(NETWORK) });

  try {
    // Step 1: Check for deployment events
    console.log('🔍 Step 1: Checking for deployment events...');
    
    const eventQuery = {
      MoveEventType: `${PACKAGE_ID}::dexsta_token::TokenModuleDeploymentRequested`
    };

    const events = await client.queryEvents({
      query: eventQuery,
      limit: 10,
      order: 'descending'
    });

    console.log(`Found ${events.data?.length || 0} deployment events`);

    if (!events.data || events.data.length === 0) {
      console.log('⚠️ No deployment events found. Create a token first using the frontend.');
      return;
    }

    // Check if we have a target token symbol from environment
    const targetTokenSymbol = process.env.TARGET_TOKEN_SYMBOL;

    let targetEvent;
    if (targetTokenSymbol) {
      // Find specific token event
      targetEvent = events.data.find(event =>
        event.parsedJson?.token_symbol === targetTokenSymbol
      );

      if (!targetEvent) {
        console.log(`⚠️ No deployment event found for target token: ${targetTokenSymbol}`);
        return;
      }

      console.log(`🎯 Found target token event: ${targetTokenSymbol}`);
    } else {
      // Use the latest event
      targetEvent = events.data[0];
      console.log('📋 Using latest deployment event');
    }

    const eventData = targetEvent.parsedJson;
    
    console.log('📋 Latest deployment event:');
    console.log('  Symbol:', eventData.token_symbol);
    console.log('  Name:', eventData.token_name);
    console.log('  Creator:', eventData.creator);

    // Step 2: Generate token module
    console.log('\n🔧 Step 2: Generating token module...');

    const inputs = {
      tokenName: eventData.token_name,
      symbol: eventData.token_symbol,
      description: "Token created via Dexsta platform",
      iconURL: "https://example.com/icon.png",
      decimals: 9,
      structName: `${eventData.token_symbol.replace(/[^A-Za-z0-9]/g, '')}Coin`,
      moduleName: eventData.token_symbol.toLowerCase().replace(/[^a-z0-9_]/g, '_'),
      publishAddress: "0x0"
    };

    console.log('📋 Token inputs:', inputs);

    // Generate Move module content
    const coinTemplate = `
module ${inputs.publishAddress}::${inputs.moduleName} {
    use sui::coin::{Self, TreasuryCap, Coin};
    use sui::object::{Self, UID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use std::option;

    struct ${inputs.structName} has store, drop {}

    fun init(ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<${inputs.structName}>(
            ${inputs.structName} {},
            ${inputs.decimals}, // decimals
            b"${inputs.symbol}", // symbol
            b"${inputs.tokenName}", // name
            b"${inputs.description}", // description
            option::none(), // icon_url
            ctx
        );

        // Transfer the treasury cap to the deployer
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));

        // Share the metadata object
        transfer::public_share_object(metadata);
    }

    /// Mint tokens to a recipient
    public entry fun mint(
        treasury_cap: &mut TreasuryCap<${inputs.structName}>,
        amount: u64,
        recipient: address,
        ctx: &mut TxContext
    ) {
        let coin = coin::mint(treasury_cap, amount, ctx);
        transfer::public_transfer(coin, recipient);
    }

    /// Get total supply
    public fun total_supply(treasury_cap: &TreasuryCap<${inputs.structName}>): u64 {
        coin::total_supply(treasury_cap)
    }
}`.trim();

    // Generate Move.toml (using local Sui installation)
    const moveToml = `
[package]
name = "${inputs.moduleName}"
version = "0.0.1"

[dependencies]
Sui = { local = "~/.sui/sui_config/sui_framework" }

[addresses]
${inputs.moduleName} = "0x0"
`.trim();

    // Step 3: Create project directory
    console.log('\n📁 Step 3: Creating project directory...');
    
    const projectDir = path.join(__dirname, "generated_tokens", inputs.moduleName);
    const sourcesDir = path.join(projectDir, "sources");

    fs.mkdirSync(sourcesDir, { recursive: true });
    fs.writeFileSync(path.join(projectDir, "Move.toml"), moveToml);
    fs.writeFileSync(path.join(sourcesDir, "coin.move"), coinTemplate);

    console.log("✅ Move package generated at:", projectDir);

    // Step 4: Build the module
    console.log('\n🔨 Step 4: Building module...');
    
    try {
      execSync(`cd ${projectDir} && sui move build`, { stdio: "inherit" });
      console.log("✅ Build successful.");
    } catch (err) {
      console.error("❌ Build failed:", err.message);
      return;
    }

    // Step 5: Publish the module
    console.log('\n🚀 Step 5: Publishing module to Sui devnet...');
    
    try {
      const publishResult = execSync(`cd ${projectDir} && sui client publish --gas-budget 100000000 --json`, { encoding: 'utf8' });
      const publishData = JSON.parse(publishResult);
      
      console.log("✅ Module published successfully!");
      console.log("📦 Transaction digest:", publishData.digest);
      
      // Extract package ID and object IDs
      const packageChange = publishData.objectChanges?.find(change => change.type === 'published');
      let packageId = '';
      let treasuryCapId = '';
      let coinMetadataId = '';
      
      if (packageChange) {
        packageId = packageChange.packageId;
        console.log("📦 Package ID:", packageId);
      }
      
      // Find TreasuryCap and CoinMetadata
      publishData.objectChanges?.forEach(change => {
        if (change.type === 'created') {
          if (change.objectType?.includes('TreasuryCap')) {
            treasuryCapId = change.objectId;
            console.log("🏛️ Treasury Cap ID:", treasuryCapId);
          } else if (change.objectType?.includes('CoinMetadata')) {
            coinMetadataId = change.objectId;
            console.log("📋 Coin Metadata ID:", coinMetadataId);
          }
        }
      });

      // Step 6: Complete token creation in the main contract
      console.log('\n🎯 Step 6: Completing token creation in main contract...');
      console.log('⚠️ This step requires calling complete_token_creation() with:');
      console.log('  - Token Symbol:', inputs.symbol);
      console.log('  - Package ID:', packageId);
      console.log('  - Treasury Cap ID:', treasuryCapId);
      console.log('  - Coin Metadata ID:', coinMetadataId);
      
      console.log('\n🎉 Token deployment completed successfully!');
      console.log('📁 Generated files are in:', projectDir);
      console.log('🌐 View transaction:', `https://suiexplorer.com/txblock/${publishData.digest}?network=devnet`);
      
    } catch (err) {
      console.error("❌ Publishing failed:", err.message);
      console.log("📁 Generated files are still available in:", projectDir);
    }

  } catch (error) {
    console.error('❌ Deployment failed:', error);
  }
}

// Run the deployment
deployTokenLive().catch(console.error);
