#!/bin/bash

# Dexsta XFT Contracts Deployment Script
# Deploy all XFT contracts to Sui devnet

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Dexsta XFT Contracts Deployment${NC}"
echo -e "${BLUE}===================================${NC}"
echo ""

# Check prerequisites
echo -e "${BLUE}📋 Checking Prerequisites...${NC}"

# Check if Sui CLI is installed
if ! command -v sui &> /dev/null; then
    echo -e "${RED}❌ Sui CLI not found. Please install Sui CLI first.${NC}"
    echo -e "Run: ${YELLOW}scripts/install-sui-cli.sh${NC}"
    exit 1
fi

# Check if contracts directory exists
if [ ! -d "contracts/xft" ]; then
    echo -e "${RED}❌ XFT contracts directory not found.${NC}"
    exit 1
fi

# Check if wallet is configured
if ! sui client active-address &> /dev/null; then
    echo -e "${RED}❌ No active Sui wallet found. Please configure your wallet first.${NC}"
    echo -e "Run: ${YELLOW}sui client new-address ed25519${NC}"
    exit 1
fi

# Get active address and network
ACTIVE_ADDRESS=$(sui client active-address)
ACTIVE_ENV=$(sui client active-env)

echo -e "${GREEN}✅ Sui CLI found${NC}"
echo -e "${GREEN}✅ XFT contracts directory found${NC}"
echo -e "${GREEN}✅ Active wallet: $ACTIVE_ADDRESS${NC}"
echo -e "${GREEN}✅ Active environment: $ACTIVE_ENV${NC}"

# Check if we're on devnet
if [[ "$ACTIVE_ENV" != *"devnet"* ]]; then
    echo -e "${YELLOW}⚠️  Warning: Not on devnet. Switching to devnet...${NC}"
    sui client switch --env devnet
fi

# Check wallet balance
echo -e "\n${BLUE}💰 Checking Wallet Balance...${NC}"
BALANCE=$(sui client balance --json | jq -r '.totalBalance // 0')
echo -e "SUI Balance: ${YELLOW}$BALANCE MIST${NC}"

if [ "$BALANCE" -lt 2000000000 ]; then  # Less than 2 SUI
    echo -e "${YELLOW}⚠️  Low balance detected. Getting SUI from faucet...${NC}"
    sui client faucet
    sleep 5
    BALANCE=$(sui client balance --json | jq -r '.totalBalance // 0')
    echo -e "New SUI Balance: ${YELLOW}$BALANCE MIST${NC}"
fi

# Build contracts
echo -e "\n${BLUE}🔨 Building XFT Contracts...${NC}"
cd contracts/xft
sui move build
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to build contracts${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Contracts built successfully${NC}"

# Deploy contracts
echo -e "\n${BLUE}🚀 Deploying XFT Contracts...${NC}"
DEPLOY_OUTPUT=$(sui client publish --gas-budget 300000000 --json)
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to deploy contracts${NC}"
    exit 1
fi

# Save deployment output
echo "$DEPLOY_OUTPUT" > ../../deployment_output.json
echo -e "${GREEN}✅ Contracts deployed successfully${NC}"

# Extract package ID
PACKAGE_ID=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.type == "published") | .packageId')
if [ "$PACKAGE_ID" = "null" ] || [ -z "$PACKAGE_ID" ]; then
    echo -e "${RED}❌ Failed to extract package ID${NC}"
    exit 1
fi

echo -e "${GREEN}📦 Package ID: $PACKAGE_ID${NC}"

# Extract contract object IDs
echo -e "\n${BLUE}📋 Extracting Contract Object IDs...${NC}"

FIRE_REGISTRY=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("fire::FireRegistry")) | .objectId')
GLOBAL_REGISTRY=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("registry::GlobalRegistry")) | .objectId')
LABEL_REGISTRY=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("label::LabelRegistry")) | .objectId')
XFT_REGISTRY=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("xft::XFTRegistry")) | .objectId')
OPERATOR_REGISTRY=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("operator::OperatorRegistry")) | .objectId')
MARKETPLACE=$(echo "$DEPLOY_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("marketplace::Marketplace")) | .objectId')

echo -e "🔥 Fire Registry: ${YELLOW}$FIRE_REGISTRY${NC}"
echo -e "🌐 Global Registry: ${YELLOW}$GLOBAL_REGISTRY${NC}"
echo -e "🏷️  Label Registry: ${YELLOW}$LABEL_REGISTRY${NC}"
echo -e "🎨 XFT Registry: ${YELLOW}$XFT_REGISTRY${NC}"
echo -e "👥 Operator Registry: ${YELLOW}$OPERATOR_REGISTRY${NC}"
echo -e "🛒 Marketplace: ${YELLOW}$MARKETPLACE${NC}"

# Initialize Fire Registry
echo -e "\n${BLUE}🔧 Initializing Fire Registry...${NC}"
sui client call \
    --package $PACKAGE_ID \
    --module fire \
    --function initialize_contracts \
    --args $FIRE_REGISTRY \
        $GLOBAL_REGISTRY \
        $LABEL_REGISTRY \
        $XFT_REGISTRY \
        $OPERATOR_REGISTRY \
        $MARKETPLACE \
    --gas-budget 100000000 > /dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Fire Registry initialized${NC}"
else
    echo -e "${YELLOW}⚠️  Fire Registry initialization may have failed${NC}"
fi

# Create environment file
echo -e "\n${BLUE}📝 Creating Environment File...${NC}"
cd ../..

cat > .env.deployment << EOF
# Dexsta XFT Contracts - Devnet Deployment
# Generated on $(date)
NEXT_PUBLIC_SUI_NETWORK=devnet
NEXT_PUBLIC_PACKAGE_ID=$PACKAGE_ID
NEXT_PUBLIC_FIRE_REGISTRY_ID=$FIRE_REGISTRY
NEXT_PUBLIC_GLOBAL_REGISTRY_ID=$GLOBAL_REGISTRY
NEXT_PUBLIC_LABEL_REGISTRY_ID=$LABEL_REGISTRY
NEXT_PUBLIC_XFT_REGISTRY_ID=$XFT_REGISTRY
NEXT_PUBLIC_OPERATOR_REGISTRY_ID=$OPERATOR_REGISTRY
NEXT_PUBLIC_MARKETPLACE_ID=$MARKETPLACE
EOF

echo -e "${GREEN}✅ Environment file created: .env.deployment${NC}"

# Test basic functionality
echo -e "\n${BLUE}🧪 Testing Basic Functionality...${NC}"

# Test label creation
echo -e "📝 Creating test label..."
LABEL_OUTPUT=$(sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function create_label \
    --args $LABEL_REGISTRY \
        $GLOBAL_REGISTRY \
        $FIRE_REGISTRY \
        '"test-label"' \
        '[1, 1000000000, 1, 1, 0, 0, 1, 0, 0, 1, 0, 250, 0, 0]' \
        'true' \
        '0x6' \
    --gas-budget 100000000 \
    --json 2>/dev/null)

if [ $? -eq 0 ]; then
    LABEL_ID=$(echo "$LABEL_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("label::Label")) | .objectId')
    echo -e "${GREEN}✅ Test label created: $LABEL_ID${NC}"
else
    echo -e "${YELLOW}⚠️  Test label creation failed (this is normal for initial deployment)${NC}"
fi

# Summary
echo -e "\n${GREEN}🎉 XFT Contracts Deployment Complete!${NC}"
echo -e "${BLUE}====================================${NC}"
echo ""
echo -e "${BLUE}📊 Deployment Summary:${NC}"
echo -e "✅ Package deployed: $PACKAGE_ID"
echo -e "✅ Fire Registry initialized"
echo -e "✅ Environment file created"
echo -e "✅ Basic functionality tested"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "1. Copy contract addresses from ${YELLOW}.env.deployment${NC} to your ${YELLOW}.env.local${NC}"
echo -e "2. Update frontend constants with new contract addresses"
echo -e "3. Test contract functionality using provided test scripts"
echo -e "4. Initialize platform settings through admin interface"
echo ""
echo -e "${BLUE}🔧 Contract Addresses:${NC}"
cat .env.deployment
echo ""
echo -e "${GREEN}🚀 Ready to use XFT contracts on Sui devnet!${NC}"
