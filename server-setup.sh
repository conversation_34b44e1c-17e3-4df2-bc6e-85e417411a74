#!/bin/bash

# Dexsta Server Setup Script
# Run this on a fresh Ubuntu 22.04 server

echo "🚀 Setting up Dexsta deployment server..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install basic dependencies
sudo apt install -y curl build-essential git nodejs npm nginx

# Install Node.js 18 (latest LTS)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Rust
echo "📦 Installing Rust..."
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source ~/.cargo/env

# Add Rust to PATH permanently
echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.bashrc

# Install Sui CLI
echo "⚡ Installing Sui CLI..."
cargo install --locked --git https://github.com/MystenLabs/sui.git --branch devnet sui

# Install PM2 for process management
sudo npm install -g pm2

# Create app directory
sudo mkdir -p /var/www/dexsta
sudo chown $USER:$USER /var/www/dexsta

# Clone your repository (replace with your repo)
echo "📥 Cloning Dexsta repository..."
cd /var/www/dexsta
# git clone https://github.com/yourusername/dexsta.git .

echo "✅ Server setup complete!"
echo ""
echo "Next steps:"
echo "1. Clone your Dexsta repository to /var/www/dexsta"
echo "2. Run: cd /var/www/dexsta && npm install"
echo "3. Create .env.local with your environment variables"
echo "4. Run: npm run build"
echo "5. Start with: pm2 start npm --name 'dexsta' -- start"
echo "6. Setup nginx reverse proxy (optional)"
echo ""
echo "🎉 Your server is ready for Dexsta deployment!"
