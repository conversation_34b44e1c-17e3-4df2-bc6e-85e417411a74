# 🧪 Dexsta Contract Testing Checklist
======================================
### 1. **Contract Deployment**
- [ ] All contracts deployed successfully
- [ ] Contract addresses saved to `.env.deployment`
- [ ] No deployment errors in log file
- [ ] Test script runs without errors

### 2. **Frontend Configuration**
- [ ] Frontend environment updated with contract addresses
- [ ] Constants file updated with new addresses
- [ ] Admin interface accessible
- [ ] Pool contract initialized
- [ ] Token contract initialized
- [ ] No frontend errors

Admin Interface:
Initlize Pool Contract:
- [ ] Pool contract initialized
- [ ] Pool settings updated
- [ ] Pool admin added
- [ ] Pool admin removed
- [ ] Pool paused trading
- [ ] Pool unpaused trading
- [ ] Pool settings updated
- [ ] Set dexsta token address (can only be set once)
- [ ] Set community vault address (bank contract)
- [ ] Link Pool contract to platform label xft

Fee Distribution: Pool contract
- [ ] 50% of platform fees on all token trades are sent to the community vault contract (the bank to fund loans). 
- [ ] If a token is linked to a label, the creator buy/sell fees are sent to the label xft address, otherwise send fees to the payout wallet address set by the token owner
- [ ] 50% of creator fees (buy/sell) on all token trades are sent to the reward pot for the trading game

Initlize Token Contract:
- [ ] Token contract initialized
- [ ] Token settings updated
- [ ] Token admin added
- [ ] Token admin removed
- [ ] Trading paused
- [ ] Trading unpaused
- [ ] Set dexsta token address (can only be set once)
- [ ] Set community vault address (bank contract)
- [ ] Link Token contract to platform label xft

Fee Distribution: token contract
- [ ] 50% of platform fees on all token trades are sent to the community vault contract (the bank to fund loans). 
- [ ] If a token is linked to a label, the creator buy/sell fees are sent to the label xft address, otherwise send fees to the payout wallet address set by the token owner
- [ ] 50% of creator fees (buy/sell) on all token trades are sent to the reward pot for the trading game

XFT Admin Interface:
- [ ] Set fees, (xft label per year registration fee, marketplace fee)
- [ ] Set platform xft label id (to receive fees from marketplace sales)
- [ ] Set community vault address


Trades
- [ ] Buy tokens with SUI
- [ ] Sell tokens for SUI
- [ ] Handle slippage settings
- [ ] Display price impact
- [ ] Update balances after trades
- [ ] Trading game mechanics work
- [ ] Trading game rewards work
- [ ] all events are emitted correctly
- [ ] all events include correct data (token address, trader address, pair address (if migrated), amount, price, etc)

Migration
- [ ] Migration from token virtual pool to main pool
- [ ] migration fees are collected correctly
- [ ] migration fees are sent to the correct address (label xft address if linked, otherwise token creator address, platform xft address)
- [ ] migration events are emitted correctly
- [ ] migration events include correct data (token address, pool address, amounts, token price, etc)

XFT Labels
- [ ] Create label
- [ ] Create sub label linked to main label
- [ ] Create xft linked to label
- [ ] Create xft linked to sub label
- [ ] Create operator license linked to main label (with experation date and role permissions), set transfer able to false... none transferable xft can only be transferred once, from creator to new owner... after than the new owner can only transfer it back to the creator or to the burn address
- [ ] Transfer operator license to operator
- [ ] Test that operator canot transfer the license to another address other than the creator or the burn address
- [ ] Mint xft as operator (check if the operator license is valid and not expired, check if the operator owns this license (license must be in their wallet), license is linked to the label the operator is trying to mint for)
- [ ] Burn xft as operator
- [ ] super operators can set withdraw permissions for operators (how much sui and how often can operator license #3 withdraw from the main label wallet, set token (addresses) they can withdraw, nft id's they can withdraw)
- [ ] withdraw funds from mail label wallet as operator (check if the operator license is valid and not expired, check if the operator owns this license (license must be in their wallet), license is linked to the label the operator is trying to withdraw from, check if the operator has withdraw permissions for this label, check if the operator has withdraw permissions for this token, check if the operator has withdraw permissions for this nft, check if the operator has not reached the withdraw limit for this token, check if the operator has not reached the withdraw limit for this nft, check if the operator has not reached the withdraw limit for this label, check if the operator has not reached the withdraw limit for all tokens, check if the operator has not reached the withdraw limit for all nfts, check if the operator has not reached the withdraw limit for all labels)

- [ ] Operators with role 1 (super operator), can mint operator licenses and transfer them to other operators.
- [ ] label owner is always a super operator
- [ ] if label is transferred, the new owner is the super operator
- [ ] if label is transferred, the old owner is no longer a super operator
- [ ] if label is transferred, the old owner can no longer mint operator licenses
- [ ] if label is transferred, the old owner can no longer transfer operator licenses
- [ ] if label is transferred, the old owner can no longer mint xfts
- [ ] if label is transferred, add function allowing new label owner to remove all operators linked to label (new label owner will have to add new operators)

- [ ] check front end nft/{id} page has the interface for label owners and operators to perform the above actions (add operator, remove operator, set withdraw permissions, withdraw funds, etc)

- [ ] check front end nft/{id} page has the interface for nft owners to transfer nfts

Marketplace license:
- [ ] Create marketplace license linked to a label (1 of 1) as the label owner or operator (super operator)
- [ ] transfer marketplace license to another address
- [ ] list marketplace license for sale (as the label owner or operator) (all xft's listed for sale are transferred to the marketplace contract for escrow)
- [ ] buy marketplace license (as regular user)
- [ ] cancel marketplace license from sale (as label owner or operator)
- [ ] use marketplace license to list nfts for sale under the label the marketplace license is linked to (check marketplace license isn't expired, label isn't expired, etc) "for example, if you have a marketplace license linked to the label #4 'dexsta-meme', you can list nfts for sale under that label, but not under the label #8 'nike-shoes-2' or any other label"

However if you buy an item from the marketplace for label #4, you can list it for sale under the same label you bought it from, even if you don't have a marketing license for that label. In the case the item is transfered to another address, the new owner can list it for sale under the same label, even if they don't have a marketplace license for that label. In short the item can be listed for sale in the same marketplace it was last bought from, without a marketplace license.

Marketplace:
- [ ] List xft for sale
- [ ] Buy xft
- [ ] Cancel listing
- [ ] Update balances after trades
- [ ] Fee distribution works (platform fee (50% to community vault, 50% to platform xft address), amount after fees sent to seller (or main label xft address if the seller used an operator license to sell (included if it's the label owner that listed the item for sale under that label)))
- [ ] none transferrable xfts cannot be listed for sale
- [ ] platform admins can remove items from sale at any time (send xfts back to seller)

- [ ] check front end nft/{id} page has the interface for nft owners to list nfts for sale, cancel listings, etc
- [ ] check front end marketplace page has the interface for users to buy nfts, see listings, history of sales, labels the user has marketplace licenses for, labels the user has bought items from, etc

Admin
- [ ] user managament (message user, ban user, etc)
- [ ] pause/unpause platform
- [ ] update platform fees
- [ ] update platform xft address
- [ ] update community vault address
- [ ] update platform addresses
- [ ] add/remove admin addresses
- [ ] view admin status

