# 🧪 XFT Contracts Comprehensive Testing Plan

## 📋 Overview

This document outlines comprehensive testing procedures for the XFT (NFT 2.0) contract system, covering all modules: Label, NFT, Operator, Bank, and Marketplace.

## 🔧 Test Environment Setup

### Prerequisites
```bash
# 1. Deploy XFT contracts
cd contracts_sui/xft
sui move build
sui client publish --gas-budget *********

# 2. <PERSON>reate test wallets
sui client new-address ed25519 creator
sui client new-address ed25519 operator1
sui client new-address ed25519 operator2
sui client new-address ed25519 marketplace_owner
sui client new-address ed25519 buyer
sui client new-address ed25519 borrower

# 3. Fund test wallets (1 SUI each for testing)
sui client gas --address <address>

# 4. Set test environment variables
export LABEL_REGISTRY="<label_registry_id>"
export NFT_REGISTRY="<nft_registry_id>"
export OPERATOR_REGISTRY="<operator_registry_id>"
export BANK_ID="<bank_id>"
export MARKETPLACE_ID="<marketplace_id>"
export PACKAGE_ID="<package_id>"
```

## 🏷️ Phase 1: Label System Testing

### Test 1.1: Create Basic Label
```bash
# Create a standard label (Type 1)
sui client call --package $PACKAGE_ID \
  --module label \
  --function create_label \
  --args $LABEL_REGISTRY \
    "\"dexsta-meme\"" \
    "[1]" \
    true \
    250 \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********
```

**Expected Results:**
- ✅ Label created with 1-year expiration
- ✅ Creator becomes owner and super operator
- ✅ 1 SUI fee deducted
- ✅ Label indexed by type and owner
- ✅ Search prefixes created

### Test 1.2: Create Different Label Types
```bash
# Profile Label (Type 2)
sui client call --package $PACKAGE_ID \
  --module label \
  --function create_label \
  --args $LABEL_REGISTRY \
    "\"creator-profile\"" \
    "[2]" \
    true \
    500 \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********

# Tag Label (Type 3)
sui client call --package $PACKAGE_ID \
  --module label \
  --function create_label \
  --args $LABEL_REGISTRY \
    "\"gaming\"" \
    "[3]" \
    false \
    100 \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********

# Collection Label (Type 5)
sui client call --package $PACKAGE_ID \
  --module label \
  --function create_label \
  --args $LABEL_REGISTRY \
    "\"nft-collection\"" \
    "[5]" \
    true \
    750 \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********
```

### Test 1.3: Label Asset Management
```bash
# Deposit SUI into label
sui client call --package $PACKAGE_ID \
  --module label \
  --function deposit \
  --args $LABEL_ID \
    $SUI_COIN_ID \
    ********0 \
  --gas-budget 20000000

# Check label balance
sui client call --package $PACKAGE_ID \
  --module label \
  --function balance \
  --args $LABEL_ID \
  --gas-budget ********

# Withdraw SUI from label (owner only)
sui client call --package $PACKAGE_ID \
  --module label \
  --function withdraw \
  --args $LABEL_ID \
    ******** \
  --gas-budget 20000000
```

### Test 1.4: Label Renewal
```bash
# Renew label for 2 years
sui client call --package $PACKAGE_ID \
  --module label \
  --function renew_label \
  --args $LABEL_REGISTRY \
    $LABEL_ID \
    2 \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********
```

**Expected Results:**
- ✅ 2 SUI fee deducted (1 SUI per year)
- ✅ Expiration time extended by 2 years
- ✅ LabelRenewed event emitted

## 👥 Phase 2: Operator License Testing

### Test 2.1: Create Operator Licenses
```bash
# Switch to label owner
sui client switch --address $CREATOR_ADDRESS

# Create super operator license (Role 1)
sui client call --package $PACKAGE_ID \
  --module operator \
  --function create_license \
  --args $OPERATOR_REGISTRY \
    $LABEL_ID \
    1 \
    3 \
    true \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********

# Create regular operator license (Role 2)
sui client call --package $PACKAGE_ID \
  --module operator \
  --function create_license \
  --args $OPERATOR_REGISTRY \
    $LABEL_ID \
    2 \
    6 \
    false \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********
```

**Expected Results:**
- ✅ Super operator license: 1.5 SUI fee (0.5 SUI × 3 months)
- ✅ Regular operator license: 3 SUI fee (0.5 SUI × 6 months)
- ✅ Licenses transferred to creator
- ✅ LicenseCreated events emitted

### Test 2.2: Marketplace License
```bash
# Create marketplace operator license
sui client call --package $PACKAGE_ID \
  --module operator \
  --function create_license \
  --args $OPERATOR_REGISTRY \
    $LABEL_ID \
    1 \
    12 \
    true \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********
```

### Test 2.3: License Transfer
```bash
# Transfer operator license to another address
sui client call --package $PACKAGE_ID \
  --module operator \
  --function transfer_license \
  --args $OPERATOR_LICENSE_ID \
    $OPERATOR1_ADDRESS \
  --gas-budget 20000000
```

### Test 2.4: License Renewal
```bash
# Switch to operator1
sui client switch --address $OPERATOR1_ADDRESS

# Renew license for 6 more months
sui client call --package $PACKAGE_ID \
  --module operator \
  --function renew_license \
  --args $OPERATOR_REGISTRY \
    $OPERATOR_LICENSE_ID \
    6 \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********
```

## 🎨 Phase 3: NFT Creation and Management

### Test 3.1: Mint NFT as Operator
```bash
# Switch to operator with valid license
sui client switch --address $OPERATOR1_ADDRESS

# Mint NFT linked to label
sui client call --package $PACKAGE_ID \
  --module nft \
  --function mint_nft \
  --args $NFT_REGISTRY \
    "\"VIP Access Pass\"" \
    "\"Exclusive access to premium features\"" \
    "\"https://xft.red/nft/vip.png\"" \
    100 \
    "[1, **********]" \
    true \
    500 \
    $LABEL_ID \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********
```

**Expected Results:**
- ✅ NFT minted with label association
- ✅ Operator can mint (has valid license)
- ✅ NFT has 5% royalty fee
- ✅ NFT is transferable

### Test 3.2: NFT Asset Storage
```bash
# Deposit SUI into NFT
sui client call --package $PACKAGE_ID \
  --module nft \
  --function deposit \
  --args $NFT_ID \
    $SUI_COIN_ID \
    ******** \
  --gas-budget 20000000

# Check NFT balance
sui client call --package $PACKAGE_ID \
  --module nft \
  --function balance \
  --args $NFT_ID \
  --gas-budget ********

# Withdraw from NFT (owner only)
sui client call --package $PACKAGE_ID \
  --module nft \
  --function withdraw \
  --args $NFT_ID \
    25000000 \
  --gas-budget 20000000
```

### Test 3.3: NFT Operator Management
```bash
# Add operator to NFT
sui client call --package $PACKAGE_ID \
  --module nft \
  --function add_operator \
  --args $NFT_ID \
    $OPERATOR2_ADDRESS \
    2 \
    30 \
    $CLOCK_ID \
  --gas-budget 20000000

# Remove operator from NFT
sui client call --package $PACKAGE_ID \
  --module nft \
  --function remove_operator \
  --args $NFT_ID \
    $OPERATOR2_ADDRESS \
  --gas-budget 20000000
```

## 🛒 Phase 4: Marketplace Testing

### Test 4.1: Create Marketplace Listing
```bash
# Switch to marketplace license holder
sui client switch --address $MARKETPLACE_OWNER_ADDRESS

# List NFT for sale
sui client call --package $PACKAGE_ID \
  --module marketplace \
  --function create_listing \
  --args $MARKETPLACE_ID \
    $NFT_ID \
    ********** \
    1 \
    "[0]" \
    $LABEL_ID \
    false \
    $CLOCK_ID \
  --gas-budget ********
```

**Expected Results:**
- ✅ NFT escrowed in marketplace
- ✅ Listing created with 1 SUI price
- ✅ 90-day expiration set
- ✅ ListingCreated event emitted

### Test 4.2: Buy NFT from Marketplace
```bash
# Switch to buyer
sui client switch --address $BUYER_ADDRESS

# Buy NFT
sui client call --package $PACKAGE_ID \
  --module marketplace \
  --function buy \
  --args $MARKETPLACE_ID \
    $NFT_ID \
    $LABEL_ID \
    $LISTING_ID \
    $SUI_COIN_ID \
    1 \
    $CLOCK_ID \
  --gas-budget ********
```

**Expected Results:**
- ✅ NFT transferred to buyer
- ✅ Platform fee (3%) to marketplace
- ✅ Royalty fee (5%) to NFT creator
- ✅ Label fee (2.5%) to label
- ✅ Remaining SUI to seller
- ✅ Sale event emitted

## 🏦 Phase 5: Bank/Lending Testing

### Test 5.1: Issue Loan Against NFT
```bash
# Switch to NFT owner
sui client switch --address $BUYER_ADDRESS

# Issue loan using NFT as collateral
sui client call --package $PACKAGE_ID \
  --module bank \
  --function issue_loan \
  --args $BANK_ID \
    $NFT_ID \
    $CLOCK_ID \
  --gas-budget ********
```

**Expected Results:**
- ✅ Loan issued based on NFT's SUI balance
- ✅ LTV starts at 45% for new borrower
- ✅ Loan SUI transferred to borrower
- ✅ NFT marked as collateral

### Test 5.2: Repay Loan
```bash
# Repay loan
sui client call --package $PACKAGE_ID \
  --module bank \
  --function repay_loan \
  --args $BANK_ID \
    $NFT_ID \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********
```

**Expected Results:**
- ✅ Loan repaid with interest
- ✅ NFT released from collateral
- ✅ User's LTV improves for future loans
- ✅ LoanRepaid event emitted

### Test 5.3: Default Handling
```bash
# Wait for loan to default (or simulate time passage)
# Another user can purchase defaulted NFT

sui client call --package $PACKAGE_ID \
  --module bank \
  --function purchase_defaulted_asset \
  --args $BANK_ID \
    $NFT_ID \
    $SUI_COIN_ID \
  --gas-budget ********
```

## ⏰ Phase 6: Expiration and Edge Case Testing

### Test 6.1: License Expiration
```bash
# Check if license is expired
sui client call --package $PACKAGE_ID \
  --module operator \
  --function is_expired \
  --args $OPERATOR_LICENSE_ID \
    $CLOCK_ID \
  --gas-budget ********

# Try to mint NFT with expired license (should fail)
sui client call --package $PACKAGE_ID \
  --module nft \
  --function mint_nft \
  --args $NFT_REGISTRY \
    "\"Should Fail\"" \
    "\"This should fail due to expired license\"" \
    "\"https://fail.png\"" \
    1 \
    "[1]" \
    true \
    0 \
    $LABEL_ID \
    $SUI_COIN_ID \
    $CLOCK_ID \
  --gas-budget ********
```

### Test 6.2: Label Expiration
```bash
# Check label expiration
sui client call --package $PACKAGE_ID \
  --module label \
  --function is_expired \
  --args $LABEL_ID \
    $CLOCK_ID \
  --gas-budget ********

# Try operations on expired label (should fail)
```

### Test 6.3: Unauthorized Access Tests
```bash
# Try to withdraw from NFT as non-owner (should fail)
sui client switch --address $UNAUTHORIZED_ADDRESS

sui client call --package $PACKAGE_ID \
  --module nft \
  --function withdraw \
  --args $NFT_ID \
    1000000 \
  --gas-budget 20000000
```

## ✅ Validation Checklist

### Label System
- [ ] Label creation with different types
- [ ] Label renewal and expiration
- [ ] Label transfer (if transferable)
- [ ] SUI deposit/withdrawal
- [ ] Operator management
- [ ] Search functionality

### Operator Licenses
- [ ] License creation with different roles
- [ ] License renewal and expiration
- [ ] License transfer (if transferable)
- [ ] Permission validation
- [ ] Fee payment

### NFT System
- [ ] NFT minting with label association
- [ ] Asset storage (SUI deposit/withdrawal)
- [ ] Operator management
- [ ] Transfer controls
- [ ] Royalty collection

### Marketplace
- [ ] Listing creation and management
- [ ] Purchase with fee distribution
- [ ] Listing cancellation
- [ ] Search and filtering
- [ ] Incremental pricing

### Bank System
- [ ] Loan issuance against NFT collateral
- [ ] Loan repayment and LTV improvement
- [ ] Default handling
- [ ] Love system rewards
- [ ] Gas refunds

### Edge Cases
- [ ] Expired license operations
- [ ] Expired label operations
- [ ] Unauthorized access attempts
- [ ] Insufficient balance scenarios
- [ ] Invalid parameter handling

## 🚀 Quick Start Testing

### Automated Test Scripts

We've created comprehensive test scripts to validate all XFT functionality:

#### 1. Basic Functionality Test
```bash
# Run comprehensive basic tests
./scripts/test-xft-contracts.sh
```

**Tests Covered:**
- ✅ Label creation (all types)
- ✅ Operator license creation
- ✅ NFT minting with label association
- ✅ Asset storage in NFTs and labels
- ✅ Marketplace listing creation
- ✅ Bank loan issuance
- ✅ Renewal processes

#### 2. Advanced Edge Case Testing
```bash
# Run advanced edge case tests
./scripts/test-xft-advanced.sh
```

**Tests Covered:**
- ✅ Unauthorized access prevention
- ✅ Asset management edge cases
- ✅ License expiration scenarios
- ✅ Marketplace stress testing
- ✅ Bank loan stress testing
- ✅ Search functionality
- ✅ Complex operator hierarchies

### Manual Testing Checklist

#### Phase 1: Foundation ✅
- [ ] Deploy all XFT contracts
- [ ] Verify registry initialization
- [ ] Check contract addresses

#### Phase 2: Label System ✅
- [ ] Create standard label (Type 1)
- [ ] Create profile label (Type 2)
- [ ] Create tag label (Type 3)
- [ ] Create collection label (Type 5)
- [ ] Test label asset deposit/withdrawal
- [ ] Test label renewal
- [ ] Test label search functionality

#### Phase 3: Operator Licenses ✅
- [ ] Create super operator license (Role 1)
- [ ] Create regular operator license (Role 2)
- [ ] Test license transfer
- [ ] Test license renewal
- [ ] Test license expiration
- [ ] Test unauthorized license usage

#### Phase 4: NFT System ✅
- [ ] Mint NFT as operator
- [ ] Test NFT-label association
- [ ] Test NFT asset storage
- [ ] Test NFT operator management
- [ ] Test NFT transfer controls
- [ ] Test unauthorized NFT access

#### Phase 5: Marketplace ✅
- [ ] Create marketplace listings
- [ ] Test purchase with fee distribution
- [ ] Test listing cancellation
- [ ] Test incremental pricing
- [ ] Test search and filtering

#### Phase 6: Banking System ✅
- [ ] Issue loans against NFT collateral
- [ ] Test LTV calculations
- [ ] Test loan repayment
- [ ] Test default handling
- [ ] Test love system rewards

#### Phase 7: Integration Testing ✅
- [ ] End-to-end user journeys
- [ ] Cross-module interactions
- [ ] Performance under load
- [ ] Gas optimization validation

## 📊 Expected Test Results

### Success Metrics
- **All transactions complete** without errors
- **Proper fee distribution** across platform/creator/label
- **Security controls** prevent unauthorized access
- **Asset safety** prevents excessive withdrawals
- **Expiration handling** works correctly
- **Search functionality** returns correct results

### Performance Benchmarks
- **Label Creation**: ~0.02 SUI gas cost
- **NFT Minting**: ~0.03 SUI gas cost
- **License Creation**: ~0.015 SUI gas cost
- **Marketplace Listing**: ~0.025 SUI gas cost
- **Loan Issuance**: ~0.02 SUI gas cost

This comprehensive testing suite ensures all XFT contract functionality works correctly and securely! 🧪✨
