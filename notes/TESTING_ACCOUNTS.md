# Testing Accounts for Dexsta Platform

## Browser Testing Account (Admin Controls)
**Use this account for contract deployment and testing in browser**

- **Address:** `0x107850bf3302019d71a699210d8fcf1789065700d3a53f2d6bd7322733c4d7fe`
- **Private Key:** `suiprivkey1qq96u4z3h5ze7ptnm69d45fugmutr499mwddfqt6009jzfgqypge6fmrzws`
- **Purpose:** Contract deployment, admin controls, browser testing
- **Network:** Sui Devnet

## Development Accounts (Keep for Devnet Tokens)
**Preserve these accounts as they have devnet SUI tokens**

### Account 1
- **Address:** `[Previous dev address 1]`
- **Private Key:** `[Previous dev key 1]`
- **Purpose:** Development testing, token creation

### Account 2  
- **Address:** `[Previous dev address 2]`
- **Private Key:** `[Previous dev key 2]`
- **Purpose:** Development testing, token trading

## Usage Guidelines

1. **Contract Deployment:** Use browser testing account for all contract deployments
2. **Admin Functions:** Browser testing account has admin controls for platform management
3. **Token Testing:** Use development accounts for creating and trading test tokens
4. **Preserve Tokens:** Keep existing dev accounts to maintain devnet SUI balances

## Security Notes

- These are **DEVNET ONLY** accounts
- Never use these keys on mainnet
- Store securely and don't share publicly
- Rotate keys if compromised

## Contract Deployment Checklist

- [ ] Deploy Fire Registry with browser testing account
- [ ] Deploy XFT contracts with browser testing account  
- [ ] Deploy Token contracts with browser testing account
- [ ] Configure admin addresses in contracts
- [ ] Test admin functions work correctly
- [ ] Verify fee collection addresses are set properly
