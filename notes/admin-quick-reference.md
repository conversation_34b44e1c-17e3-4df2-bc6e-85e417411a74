# 🎛️ Admin Quick Reference Card

## 🚀 Bonding Curve Aggressiveness Settings

### 🟢 Conservative (Encourage Trading)
```
300 = 0.3x - Very Gentle (High volume tokens)
500 = 0.5x - Gentle (Established tokens)  
700 = 0.7x - Slightly Gentle (Growing tokens)
```

### 🟡 Normal (Balanced)
```
1000 = 1.0x - Standard (DEFAULT - Most tokens)
1200 = 1.2x - Slightly Aggressive (New tokens)
```

### 🟠 Aggressive (Protection Mode)
```
1500 = 1.5x - Moderate Protection (Low liquidity)
2000 = 2.0x - Strong Protection (Manipulation risk)
2500 = 2.5x - Very Strong Protection (High risk)
```

### 🔴 Extreme (Emergency Only)
```
3000 = 3.0x - Extreme Protection (Crisis mode)
4000 = 4.0x - Maximum Protection (Emergency)
5000 = 5.0x - Emergency Mode (Halt large trades)
```

## 💧 Virtual Liquidity Buffer

### Low Responsiveness
```
0.0001 SUI (100,000 MIST) - Minimal buffer
0.001 SUI (1,000,000 MIST) - Low buffer
```

### Normal Stability
```
0.01 SUI (10,000,000 MIST) - DEFAULT
0.02 SUI (20,000,000 MIST) - Stable
```

### High Stability
```
0.05 SUI (50,000,000 MIST) - High stability
0.1 SUI (100,000,000 MIST) - Maximum stability
```

## 🎯 Common Scenarios

### New Token Launch
```
Aggressiveness: 3000 (3.0x)
Buffer: 50,000,000 MIST (0.05 SUI)
Duration: First 24 hours
```

### Mature Token
```
Aggressiveness: 1000 (1.0x)  
Buffer: 10,000,000 MIST (0.01 SUI)
Duration: Ongoing
```

### Manipulation Detected
```
Aggressiveness: 4000 (4.0x)
Buffer: 100,000,000 MIST (0.1 SUI)
Duration: Until resolved
```

### High Activity Period
```
Aggressiveness: 700 (0.7x)
Buffer: 5,000,000 MIST (0.005 SUI)
Duration: During high volume
```

## ⚠️ Important Warnings

- **Gradual Changes**: Adjust by ±500 aggressiveness max
- **Monitor Impact**: Watch volume after changes
- **Document Changes**: Record all adjustments
- **Revert Ready**: Be prepared to quickly revert

## 📞 Emergency Contacts

- **Technical Issues**: Check testing-procedures.md
- **Security Concerns**: Review security-analysis.md  
- **Parameter Questions**: See admin-interface-guide.md
- **Platform Overview**: Read platform-overview.md
