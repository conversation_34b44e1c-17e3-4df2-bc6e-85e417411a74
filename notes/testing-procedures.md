# 🧪 Dexsta Testing Procedures

## 📋 Overview

Comprehensive testing procedures for the Dexsta platform, covering all contracts, features, and edge cases. This document provides step-by-step testing protocols to ensure platform reliability and security.

## 🔧 Test Environment Setup

### Prerequisites
```bash
# 1. Deploy all contracts
sui move build
sui client publish --gas-budget 100000000

# 2. Create test wallets
sui client new-address ed25519 creator
sui client new-address ed25519 trader1  
sui client new-address ed25519 trader2
sui client new-address ed25519 admin

# 3. Fund test wallets (0.1 SUI each)
sui client gas --address <address>

# 4. Set conservative test values
export TOKEN_REGISTRY="<registry_id>"
export POOL_REGISTRY="<pool_registry_id>"
export NFT_REGISTRY="<nft_registry_id>"
```

### Test Constants
- **Max Trade Amount**: 0.01 SUI (conserve funds)
- **Migration Goal**: 0.1 SUI (quick testing)
- **Reward Goal**: 5 trades (fast cycles)
- **Test Duration**: 30 minutes per full cycle

## 🎯 Phase 1: Token Contract Testing

### Test 1.1: Token Creation with Social Links
```bash
# Create token with all social links
sui client call --package $PACKAGE_ID \
  --module simple_token \
  --function create_token_simple \
  --args $TOKEN_REGISTRY \
    "\"Test Token\"" \
    "\"TEST\"" \
    "\"A comprehensive test token\"" \
    "\"https://test.com/icon.png\"" \
    "\"https://testtoken.com\"" \
    "\"https://x.com/testtoken\"" \
    "\"https://t.me/testtoken\"" \
    "\"https://tiktok.com/@testtoken\"" \
    1000000000000000 \
    9 \
    250 \
    250 \
  --gas-budget 100000000
```

**Expected Results:**
- ✅ Token created successfully
- ✅ Social links stored correctly
- ✅ Creator = Owner initially
- ✅ Fees set to 2.5% buy/sell
- ✅ Virtual pool initialized

### Test 1.2: Bonding Curve Parameter Testing
```bash
# Test different aggressiveness levels
sui client call --package $PACKAGE_ID \
  --module simple_token \
  --function update_curve_aggressiveness \
  --args $TOKEN_REGISTRY 2000 \
  --gas-budget 10000000

# Test virtual liquidity buffer
sui client call --package $PACKAGE_ID \
  --module simple_token \
  --function update_virtual_liquidity_buffer \
  --args $TOKEN_REGISTRY 5000000 \
  --gas-budget 10000000
```

**Expected Results:**
- ✅ Parameters updated successfully
- ✅ Only admin can update
- ✅ Parameters within valid ranges

### Test 1.3: Trading Mechanics
```bash
# Buy tokens (Trade 1)
sui client call --package $PACKAGE_ID \
  --module simple_token \
  --function swap_sui_for_tokens \
  --args $TOKEN_REGISTRY $TOKEN_INFO_ID $SUI_COIN_ID 0 \
  --gas-budget 100000000

# Sell tokens (Trade 2) 
sui client call --package $PACKAGE_ID \
  --module simple_token \
  --function swap_tokens_for_sui \
  --args $TOKEN_REGISTRY $TOKEN_INFO_ID 1000000000 0 \
  --gas-budget 100000000
```

**Expected Results:**
- ✅ Tokens received/sent correctly
- ✅ Fees deducted properly (2.5%)
- ✅ 50% of fees go to reward pot
- ✅ Trade count increments
- ✅ Price impact reflects aggressiveness setting

### Test 1.4: Trade Game Progression
```bash
# Continue trading until goal reached (5 trades)
# Trade 3, 4, 5...
# Monitor reward pot growth and goal achievement
```

**Expected Results:**
- ✅ Trade count increases with each trade
- ✅ Reward pot grows with each trade
- ✅ Goal achievement triggers payout
- ✅ Trade count resets, goal increases 50%

### Test 1.5: Migration Testing
```bash
# Continue buying until virtual liquidity reaches 0.1 SUI
# Monitor migration trigger
```

**Expected Results:**
- ✅ Migration triggered at 0.1 SUI
- ✅ has_migrated = true
- ✅ Fee distribution: 1% creator, 1% reward pot, 1% platform
- ✅ Post-migration trading blocked in token contract

## 🏊 Phase 2: Pool Contract Testing

### Test 2.1: Pool Creation (Post-Migration)
```bash
# Create pool for migrated token
sui client call --package $PACKAGE_ID \
  --module pool \
  --function create_pool \
  --args $POOL_REGISTRY \
    "\"TEST\"" \
    $TOKEN_ADDRESS \
    1000000000000 \
    $SUI_COIN_ID \
    250 \
    250 \
  --gas-budget 100000000
```

**Expected Results:**
- ✅ Pool created successfully
- ✅ Initial liquidity added
- ✅ LP tokens minted and burned
- ✅ Creator = Owner initially

### Test 2.2: Pool Trading
```bash
# Buy from pool
sui client call --package $PACKAGE_ID \
  --module pool \
  --function swap_sui_for_tokens \
  --args $POOL_ID $SUI_COIN_ID 0 \
  --gas-budget 100000000

# Sell to pool
sui client call --package $PACKAGE_ID \
  --module pool \
  --function swap_tokens_for_sui \
  --args $POOL_ID 1000000000 0 \
  --gas-budget 100000000
```

**Expected Results:**
- ✅ Trading works correctly
- ✅ Fees collected properly
- ✅ Trade game continues (post-migration logic)
- ✅ Only buys count until first goal hit

### Test 2.3: Open Liquidity Feature
```bash
# Send SUI directly to pool address
sui client transfer-sui --to $POOL_ADDRESS --amount 10000000

# Execute trade and verify price impact
```

**Expected Results:**
- ✅ Direct SUI affects pricing
- ✅ Price becomes more favorable for buyers
- ✅ Pool balance reflects additional SUI

## 🎨 Phase 3: NFT Contract Testing

### Test 3.1: NFT Minting
```bash
# Mint 1-of-1 NFT
sui client call --package $PACKAGE_ID \
  --module dexsta_nft \
  --function mint_one_of_one \
  --args $NFT_REGISTRY \
    "\"VIP Access Pass\"" \
    "\"Exclusive trading access\"" \
    "\"https://nft.com/vip.png\"" \
    $SUI_COIN_ID \
  --gas-budget 100000000
```

**Expected Results:**
- ✅ NFT minted successfully
- ✅ Asset storage enabled
- ✅ NFT ID assigned

### Test 3.2: Private Token Creation
```bash
# Create NFT-gated token
sui client call --package $PACKAGE_ID \
  --module simple_token \
  --function create_token_with_nft_gating \
  --args $TOKEN_REGISTRY \
    "\"VIP Token\"" \
    "\"VIP\"" \
    "\"NFT-gated exclusive token\"" \
    "\"https://vip.com/icon.png\"" \
    "\"https://viptoken.com\"" \
    "\"\"" "\"\"" "\"\"" \
    1000000000000000 \
    9 \
    250 \
    250 \
    $NFT_ID \
    $SUI_COIN_ID \
  --gas-budget 100000000
```

**Expected Results:**
- ✅ Private token created
- ✅ NFT gating enabled
- ✅ Only NFT holders can trade

## 🔐 Phase 4: Ownership & Admin Testing

### Test 4.1: Ownership Transfer
```bash
# Transfer token ownership
sui client call --package $PACKAGE_ID \
  --module simple_token \
  --function transfer_ownership \
  --args $TOKEN_REGISTRY $TOKEN_INFO_ID $NEW_OWNER_ADDRESS \
  --gas-budget 100000000
```

**Expected Results:**
- ✅ Ownership transferred
- ✅ Fee payout address updated
- ✅ Original creator loses control

### Test 4.2: Fee Updates by New Owner
```bash
# Switch to new owner wallet
sui client switch --address $NEW_OWNER_ADDRESS

# Update fees
sui client call --package $PACKAGE_ID \
  --module simple_token \
  --function update_fees \
  --args $TOKEN_INFO_ID 300 300 \
  --gas-budget 100000000
```

**Expected Results:**
- ✅ Fees updated to 3%
- ✅ New owner has control
- ✅ Original creator cannot update

### Test 4.3: Platform Admin Override
```bash
# Switch to platform admin
sui client switch --address $PLATFORM_ADMIN_ADDRESS

# Admin transfers ownership
sui client call --package $PACKAGE_ID \
  --module simple_token \
  --function transfer_ownership \
  --args $TOKEN_REGISTRY $TOKEN_INFO_ID $ADMIN_CHOSEN_ADDRESS \
  --gas-budget 100000000
```

**Expected Results:**
- ✅ Admin can override ownership
- ✅ Platform maintains ultimate control

## 🛡️ Phase 5: Security & Edge Case Testing

### Test 5.1: MEV Protection
```bash
# Test transaction size limits
# Try to trade more than 10 SUI (should fail)

# Test percentage limits  
# Try to trade more than 10% of pool (should fail)

# Test slippage protection
# Set min_tokens_out higher than possible (should fail)
```

### Test 5.2: Parameter Validation
```bash
# Test invalid aggressiveness (should fail)
update_curve_aggressiveness(registry, 10000) # > 5000

# Test invalid buffer (should fail)  
update_virtual_liquidity_buffer(registry, 50000) # < 100000

# Test invalid fees (should fail)
update_fees(token_info, 600, 600) # > 500 (5%)
```

### Test 5.3: Access Control
```bash
# Test unauthorized access (should fail)
# Non-owner tries to update fees
# Non-admin tries to update curve parameters
# Non-NFT-holder tries to trade private token
```

## 📊 Phase 6: Performance & Integration Testing

### Test 6.1: High-Frequency Trading
```bash
# Execute rapid trades to test:
# - Gas efficiency
# - State consistency  
# - Event emission
# - Error handling
```

### Test 6.2: Cross-Contract Integration
```bash
# Test complete user journey:
# 1. Create token
# 2. Trade to migration
# 3. Create pool
# 4. Continue trading
# 5. Transfer ownership
# 6. Update parameters
```

## ✅ Test Validation Checklist

### Token Contract
- [ ] Token creation with social links
- [ ] Fee collection and distribution (50/50 split)
- [ ] Trade game mechanics (pre/post migration)
- [ ] Migration trigger and fee distribution
- [ ] Ownership transfer restrictions
- [ ] Owner-only functions (fees, socials)
- [ ] Platform admin override
- [ ] Bonding curve parameter effects

### Pool Contract
- [ ] Pool creation and liquidity
- [ ] Trading with fee collection
- [ ] Trade counting logic (post-migration)
- [ ] Ownership transfer
- [ ] Fee updates by owner
- [ ] Open liquidity feature
- [ ] LP token burning

### NFT Contract
- [ ] 1-of-1 and collection minting
- [ ] Asset storage capabilities
- [ ] Time-based locking
- [ ] Ownership validation
- [ ] NFT-gated pool access

### Security
- [ ] MEV protection mechanisms
- [ ] Access control enforcement
- [ ] Parameter validation
- [ ] Error handling
- [ ] Edge case resilience

### Integration
- [ ] Cross-contract interactions
- [ ] Event emissions
- [ ] State consistency
- [ ] Gas efficiency
- [ ] User experience flow

## 🚨 Emergency Procedures

### Test Failure Response
1. **Document Issue**: Record exact steps and error messages
2. **Isolate Problem**: Identify specific contract/function
3. **Check Parameters**: Verify all inputs are correct
4. **Review Logs**: Analyze transaction events and errors
5. **Fix & Retest**: Implement fix and repeat test

### Critical Issues
- **Contract Deployment Failures**: Check gas limits and dependencies
- **Migration Issues**: Verify liquidity calculations and thresholds
- **Access Control Bypasses**: Immediate security review required
- **Fee Distribution Errors**: Check arithmetic and balance handling

This comprehensive testing suite ensures the Dexsta platform operates reliably and securely across all features and edge cases! 🧪
