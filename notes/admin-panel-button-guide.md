# 🎛️ Admin Panel Button Guide - What Updates What

## 📋 Overview

The admin panel has many settings but now has **4 main update buttons** in the Platform Settings tab. This guide clarifies exactly which button updates which settings to avoid confusion.

## 🔧 **Platform Settings Tab - 4 Update Buttons**

### 💰 **Button 1: "Update Token Platform Settings"**
**Color**: Green gradient  
**Updates**: Token contract platform settings via `updateTokenPlatformSettings()`

#### ✅ **Settings Updated:**
- **Platform Fee Address** - Where token trading fees go
- **Platform Fee (BPS)** - Token trading platform fee percentage
- **Reward Fee (BPS)** - Token trading reward fee percentage
- **Token Mint Fee** - Cost to create new tokens
- **Migration Goal** - SUI amount to trigger pool migration ⭐
- **Migration Fee Percentage** - Fee taken during migration ⭐
- **Migration Gas Fee** - Gas cost for migration ⭐

#### 🎯 **Key Features:**
- **Migration Controls** ⭐ - Only this button updates migration settings
- **Token-specific Fees** - Mint fees and token trading fees
- **Platform Fee Settings** - Core fee configuration

---

### 🏊 **Button 2: "Update Pool Platform Settings"**  
**Color**: Blue gradient  
**Updates**: Pool contract platform settings via `updatePoolPlatformSettings()`

#### ✅ **Settings Updated:**
- **Platform Fee Address** - Where pool trading fees go
- **Platform Fee (BPS)** - Pool trading platform fee percentage
- **LP Fee (BPS)** - Liquidity provider fee percentage
- **Reward Fee (BPS)** - Pool trading reward fee percentage
- **Existing Token Import Fee** - Cost to import existing tokens to pools

#### 🎯 **Key Features:**
- **Pool-specific Fees** - LP fees and pool trading fees
- **Import Functionality** - Existing token import costs
- **Pool Platform Settings** - Core pool configuration

---

### ⚡ **Button 3: "Update Bonding Curve Settings"**
**Color**: Indigo gradient  
**Updates**: Bonding curve parameters via `handleUpdateBondingCurveSettings()`

#### ✅ **Settings Updated:**
- **Token Curve Aggressiveness** - Token pricing curve steepness (0.1x-5.0x)
- **Token Virtual Liquidity Buffer** - Token price stability buffer
- **Pool Curve Aggressiveness** - Pool pricing curve steepness (0.1x-5.0x)  
- **Pool Virtual Liquidity Buffer** - Pool price stability buffer

#### 🎯 **Key Features:**
- **Pricing Control** ⭐ - How aggressive/gentle pricing curves are
- **Stability Buffers** ⭐ - Virtual liquidity for price smoothing
- **Separate Controls** - Independent token and pool curve settings

---

### 🏆 **Button 4: "Update Reward Game Settings"**
**Color**: Orange gradient
**Updates**: Reward game parameters for both contracts via `handleUpdateRewardGameSettings()`

#### ✅ **Settings Updated:**
- **Min Reward Trade Amount** - Minimum trade for reward eligibility (both contracts)
- **Initial Reward Goal** - Starting reward pot target (both contracts)
- **Reward Goal Increase** - How much goal increases after win (both contracts)
- **Reward Goal Decrease Amount** - How much goal decreases on timeout (both contracts)
- **Reward Goal Decrease Threshold** - Time before goal decreases (both contracts)
- **Reward Goal Proximity Threshold** - Percentage for goal adjustment (both contracts)

#### 🎯 **Key Features:**
- **Dual Contract Update** ⭐ - Updates both token and pool reward settings
- **Trading Game Focus** ⭐ - Only reward game parameters
- **Batch Operation** - Single transaction for both contracts

---

## 🚫 **Settings NOT Updated by Any Button**

### 📝 **Display-Only Settings:**
These are shown in the UI but updated by other buttons or not implemented yet:

#### **Platform Label ID**
- **Display**: Platform Settings section
- **Update Method**: Not implemented yet (future feature)
- **Purpose**: Route fees to XFT label instead of wallet

#### **Fire Registry Contract Addresses**
- **Display**: Fire Registry tab
- **Update Method**: Separate "Initialize Fire Registry" and "Update Contract Addresses" buttons
- **Purpose**: Inter-contract communication setup

#### **XFT Settings**
- **Display**: XFT Settings tab  
- **Update Method**: Separate "Update Label Settings" button
- **Purpose**: Annual label mint price and marketplace fees

---

## 🎯 **Button Mapping Summary**

| Setting Category | Button | Color | Contract |
|------------------|--------|-------|----------|
| **Token Fees & Migration** | Update Token Platform Settings | 🟢 Green | Token Admin |
| **Pool Fees & LP Settings** | Update Pool Platform Settings | 🔵 Blue | Pool Admin |
| **Pricing Curves & Buffers** | Update Bonding Curve Settings | 🟣 Indigo | Token & Pool Registries |
| **Reward Game Parameters** | Update Reward Game Settings | 🟠 Orange | Token & Pool Admin |
| **Fire Registry** | Initialize/Update Fire Registry | 🟠 Orange | Fire Registry |
| **XFT Settings** | Update Label Settings | ⚙️ Gray | XFT Admin |

---

## 🎮 **Reward Game Settings - Which Button?**

### 🏆 **Both Token & Pool Reward Game** → **Orange Button** (Update Reward Game Settings) ⭐ **RECOMMENDED**
- Updates both contracts in one transaction
- All reward game parameters for both token and pool trading
- Most efficient way to update reward settings

### 🎯 **Token Reward Game Only** → **Green Button** (Update Token Platform Settings)
- Updates only token contract reward settings
- Also updates other token platform settings
- Use when you only want to change token reward game

### 🏊 **Pool Reward Game Only** → **Blue Button** (Update Pool Platform Settings)
- Updates only pool contract reward settings
- Also updates other pool platform settings
- Use when you only want to change pool reward game

---

## ⭐ **Migration Settings - Which Button?**

### 🚀 **All Migration Settings** → **Green Button** (Update Token Platform Settings)
- **Migration Goal** - SUI threshold to trigger migration (60 SUI default)
- **Migration Fee Percentage** - Fee taken during migration (5% default)
- **Migration Gas Fee** - Gas cost for migration (0.005 SUI default)

**Note**: Migration only applies to token contract, not pools

---

## 🔧 **Bonding Curve Settings - Which Button?**

### ⚡ **All Curve Settings** → **Indigo Button** (Update Bonding Curve Settings)
- **Token Curve Aggressiveness** - How steep token pricing is (1.0x default)
- **Token Virtual Liquidity** - Price stability for tokens (0.001 SUI default)
- **Pool Curve Aggressiveness** - How steep pool pricing is (1.0x default)
- **Pool Virtual Liquidity** - Price stability for pools (0.01 SUI default)

---

## 💡 **Why This Design?**

### 🎯 **Logical Grouping:**
- **Token Button**: Everything related to token contract
- **Pool Button**: Everything related to pool contract  
- **Curve Button**: Pricing mechanics for both contracts

### 🔐 **Security:**
- Each button calls specific admin functions
- Prevents accidental cross-contract updates
- Clear separation of concerns

### 🚀 **Efficiency:**
- Batch updates within each contract
- Single transaction per button
- Reduced gas costs

---

## 📝 **Quick Reference**

### 🤔 **"I want to update..."**

**Migration threshold** → 🟢 Green Button (Token Platform Settings)
**Reward game rules** → 🟠 Orange Button (Reward Game Settings) ⭐ **RECOMMENDED**
**How aggressive pricing is** → 🟣 Indigo Button (Bonding Curve Settings)
**Platform fee percentage** → 🟢 Green Button (Token) or 🔵 Blue Button (Pool)
**Where fees go** → 🟢 Green Button (Token) or 🔵 Blue Button (Pool)
**Price stability** → 🟣 Indigo Button (Bonding Curve Settings)
**Contract addresses** → 🟠 Orange Button (Fire Registry tab)
**XFT label pricing** → ⚙️ Gray Button (XFT Settings tab)

### ⚠️ **Important Notes:**
- **Platform Label ID**: Not implemented yet (display only)
- **Reward Game**: Use Orange button for both contracts, or individual buttons for single contract
- **Migration**: Only applies to token contract
- **Curve Settings**: Affect both token and pool pricing

This design ensures clear separation between different types of platform settings! 🎯
