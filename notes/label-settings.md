// 0 link to label
// 1 registration in years
// 2 minter license
// 3 nftea type
// 4 if type is license, license term
// 5 0 false, 1 true // formerly mint pass
// 6 quantity
// 7 label registration expire
// 8 unused.. use to be redeem days
// 9 transferable
// 10 wrapto
// 11 label split for marketplace license
// 12 unused

let settings_:number[] = []
settings_.push(parseInt(_mintData.linkto)) //0 link to label
settings_.push(parseInt(_mintData.years)) // 1 registration in years
settings_.push(parseInt(_userLicense[0])) //2 minter license
settings_.push(parseInt(_nfteaType)) //3 nftea type
settings_.push(parseInt(_mintData.licenseterm)) //4 if type is license, license term
settings_.push(0) //5 0 false, 1 true // formerly mint pass
settings_.push(parseInt(_mintData.supply)) //6 quantity
settings_.push(0) //7 label registration expire
settings_.push(0) //8 unused.. use to be redeem days
settings_.push(transfer_) //9 transferable
settings_.push(0) // 10 wrapto
settings_.push(parseInt(_mintData.labelsplit)) //11 label split for marketplace license
settings_.push(0) // 12 open