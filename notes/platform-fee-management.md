# 💰 Platform Fee Management System

## 📋 Overview

The Dexsta platform now has a comprehensive fee management system that allows dynamic control over fee destinations and rates. Platform fees can be routed to either wallet addresses or XFT labels for advanced fee distribution.

## 🎯 Fee Routing System

### 💳 Wallet-Based Fees (Default)
```typescript
// Current configuration
platformFeeAddress: "0x107850bf3302019d71a699210d8fcf1789065700d3a53f2d6bd7322733c4d7fe"
platformLabelId: "" // Empty = fees go to wallet
```

### 🏷️ Label-Based Fees (Advanced)
```typescript
// XFT label integration
platformFeeAddress: "0x107850bf..." // Fallback wallet
platformLabelId: "0x..." // Platform label object ID
```

**Fee Priority Logic:**
```
If platformLabelId is set:
  ✅ All fees → Platform Label Object
Else:
  ✅ All fees → Platform Wallet Address
```

## 💸 Fee Structure

### 🪙 Token Contract Fees
- **Platform Fee**: 3% (300 BPS) - Goes to platform
- **Reward Fee**: 1% (100 BPS) - Goes to trading game pot
- **Mint Fee**: 0.1 SUI - One-time token creation cost
- **Migration Fee**: 5% of virtual liquidity + 0.005 SUI gas

### 🏊 Pool Contract Fees  
- **Platform Fee**: 3% (300 BPS) - Goes to platform
- **LP Fee**: 0.6% (20% of platform fee) - Goes to liquidity providers
- **Reward Fee**: 1% (100 BPS) - Goes to trading game pot
- **Import Fee**: 0.1 SUI - Existing token import cost

## 🔧 Admin Controls

### 📊 Platform Settings Updates
```typescript
// Update token platform settings
updateTokenPlatformSettings({
  platformFeeAddress: "0x...",
  platformFeeBps: 300,        // 3%
  rewardFeeBps: 100,         // 1%
  mintFee: "100000000",      // 0.1 SUI
  bondingCurveGoal: "60000000000", // 60 SUI migration
  migrationFeePercentage: 5,  // 5%
  // ... other settings
})

// Update pool platform settings  
updatePoolPlatformSettings({
  platformFeeAddress: "0x...",
  platformFeeBps: 300,        // 3%
  lpFeeBps: 20,              // 20% of platform fee
  rewardFeeBps: 100,         // 1%
  existingTokenImportFee: "100000000", // 0.1 SUI
  // ... other settings
})
```

### 🎛️ Bonding Curve Controls
```typescript
// Adjust pricing aggressiveness
updateTokenCurveAggressiveness(1500) // 1.5x steepness
updatePoolCurveAggressiveness(800)   // 0.8x steepness

// Adjust virtual liquidity for stability
updateTokenVirtualLiquidityBuffer("2000000")  // 0.002 SUI
updatePoolVirtualLiquidityBuffer("20000000")  // 0.02 SUI
```

## 🏷️ Platform Label Integration

### 🎯 Benefits of Label-Based Fees
- **Advanced Distribution**: Use XFT system for fee management
- **Operator Control**: Delegate fee management to operators
- **Marketplace Integration**: Fees can be traded as XFTs
- **Transparency**: On-chain fee tracking and distribution

### 🔧 Setup Process
1. **Create Platform Label**: Mint a label for the platform
2. **Configure Admin Panel**: Set `platformLabelId` in settings
3. **Update Contracts**: Call platform settings update functions
4. **Verify Routing**: Test that fees go to label instead of wallet

### 📝 Label Requirements
- **Label Type**: Any valid XFT label type
- **Ownership**: Platform admin should own or operate the label
- **Permissions**: Label must allow fee deposits
- **Expiration**: Consider label expiration dates

## 📊 Fee Collection Mechanics

### ⚡ Automatic Collection
- **Real-time**: Fees collected during every trade
- **No Manual Action**: Contracts handle distribution automatically
- **Immediate**: Fees appear in destination immediately
- **Transparent**: All fee events are emitted on-chain

### 🔍 Fee Tracking
```move
// Fee collection events
public struct PlatformFeeCollected has copy, drop {
    token_address: address,
    fee_amount: u64,
    destination: address, // Wallet or label
    timestamp: u64,
}
```

## 🎮 Trading Game Integration

### 🎯 Reward Pot Management
- **50% Creator Fees**: Go to trader reward pot
- **Auto-Adjustment**: Goal increases/decreases based on activity
- **Community Contributions**: Users can add to pot
- **Winner Selection**: Xth trade wins entire pot

### ⚙️ Reward Settings
```typescript
// Configurable reward parameters
initialRewardGoal: "1000000000",      // 1 SUI
rewardGoalIncrease: "500000000",      // 0.5 SUI
rewardGoalDecreaseAmount: "100000000", // 0.1 SUI
rewardGoalDecreaseThreshold: 300,      // 5 minutes
rewardGoalProximityThreshold: 90,      // 90%
```

## 🔐 Security & Access Control

### 👑 Admin-Only Functions
- **Platform Fee Updates**: Only platform admin
- **Bonding Curve Changes**: Only platform admin  
- **Label Configuration**: Only platform admin
- **Migration Settings**: Only platform admin

### 🛡️ Safety Measures
- **Input Validation**: Range checks on all parameters
- **Transaction Atomicity**: All-or-nothing updates
- **Error Recovery**: Clear error messages and rollback
- **Rate Limiting**: Prevent excessive updates

## 📈 Migration & Bonding Curves

### 🎯 Migration Goals
- **Token Contract**: Configurable SUI threshold (default: 60 SUI)
- **Automatic Trigger**: Migration when virtual pool reaches goal
- **Fee Distribution**: 3% split (1% creator, 1% reward, 1% platform)
- **LP Token Burning**: Liquidity permanently locked

### 📊 Curve Aggressiveness
- **Range**: 0.1x to 5.0x multiplier
- **Default**: 1.0x (normal curve)
- **Effect**: Controls price impact and slippage
- **Separate Controls**: Independent for tokens and pools

## 🚀 Current Status

### ✅ Production Ready
- **Fee Collection**: Active and working
- **Platform Settings**: Fully configurable
- **Admin Controls**: All functions implemented
- **Label Support**: Ready for XFT integration

### 📊 Current Configuration
- **Platform Fee Address**: `0x107850bf...`
- **Platform Fee Rate**: 3% (300 BPS)
- **Reward Fee Rate**: 1% (100 BPS)
- **Migration Goal**: 60 SUI
- **Curve Aggressiveness**: 1.0x (normal)

### 🎯 Next Steps
1. **Optional**: Create platform label for advanced fee management
2. **Test**: Verify fee collection and routing
3. **Monitor**: Track fee accumulation and distribution
4. **Optimize**: Adjust rates based on platform performance
