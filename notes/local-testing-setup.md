# Local Contract Testing Setup Guide
**Dexsta Platform - Complete Testing Environment**

## Prerequisites Installation

### 1. Rust Installation
```bash
# Install Rust (required for Solana and Anchor)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Verify installation
rustc --version
cargo --version
```

### 2. Solana CLI Installation
```bash
# Install Solana CLI
sh -c "$(curl -sSfL https://release.solana.com/v1.17.0/install)"

# Add to PATH (add to ~/.bashrc or ~/.zshrc)
export PATH="$HOME/.local/share/solana/install/active_release/bin:$PATH"

# Verify installation
solana --version
```

### 3. Anchor Framework Installation
```bash
# Install Anchor CLI
cargo install --git https://github.com/coral-xyz/anchor avm --locked --force

# Install latest Anchor version
avm install latest
avm use latest

# Verify installation
anchor --version
```

### 4. Node.js Dependencies
```bash
# Install additional Solana dependencies
npm install -g @solana/web3.js @coral-xyz/anchor
```

## Local Validator Setup

### 1. Configure Solana for Local Development
```bash
# Set config to localhost
solana config set --url localhost

# Set keypair for testing
solana-keygen new --outfile ~/.config/solana/id.json

# Verify configuration
solana config get
```

### 2. Start Local Validator
```bash
# Start validator with required programs
solana-test-validator \
  --bpf-program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA BPFLoader2111111111111111111111111111111111 \
  --bpf-program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL BPFLoader2111111111111111111111111111111111 \
  --reset

# In another terminal, verify validator is running
solana cluster-version
```

### 3. Fund Test Accounts
```bash
# Airdrop SOL to your account
solana airdrop 100

# Verify balance
solana balance
```

## Anchor Project Setup

### 1. Initialize Anchor Workspace
```bash
# Create new Anchor project (if not exists)
anchor init dexsta-contracts --template multiple

# Navigate to project
cd dexsta-contracts
```

### 2. Project Structure
```
dexsta-contracts/
├── Anchor.toml
├── Cargo.toml
├── package.json
├── programs/
│   ├── token-program/
│   │   ├── Cargo.toml
│   │   └── src/
│   │       └── lib.rs
│   └── pool-program/
│       ├── Cargo.toml
│       └── src/
│           └── lib.rs
├── tests/
│   ├── token-program.ts
│   └── pool-program.ts
└── target/
```

### 3. Configure Anchor.toml
```toml
[features]
seeds = false
skip-lint = false

[programs.localnet]
token_program = "TokenProgram111111111111111111111111111111111"
pool_program = "PoolProgram1111111111111111111111111111111111"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "Localnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"

[test]
startup_wait = 5000
shutdown_wait = 2000
upgradeable = false
```

## Contract Development

### 1. Token Program Structure
```rust
// programs/token-program/src/lib.rs
use anchor_lang::prelude::*;

declare_id!("TokenProgram111111111111111111111111111111111");

#[program]
pub mod token_program {
    use super::*;

    pub fn create_token(
        ctx: Context<CreateToken>,
        name: String,
        symbol: String,
        description: String,
        image_url: String,
        buy_fee_bps: u16,
        sell_fee_bps: u16,
    ) -> Result<()> {
        // Implementation
        Ok(())
    }

    pub fn buy_tokens(
        ctx: Context<BuyTokens>,
        sol_amount: u64,
        minimum_tokens_out: u64,
    ) -> Result<()> {
        // Implementation
        Ok(())
    }

    pub fn sell_tokens(
        ctx: Context<SellTokens>,
        token_amount: u64,
        minimum_sol_out: u64,
    ) -> Result<()> {
        // Implementation
        Ok(())
    }
}

#[derive(Accounts)]
pub struct CreateToken<'info> {
    // Account definitions
}

#[derive(Accounts)]
pub struct BuyTokens<'info> {
    // Account definitions
}

#[derive(Accounts)]
pub struct SellTokens<'info> {
    // Account definitions
}
```

### 2. Build and Deploy
```bash
# Build programs
anchor build

# Deploy to local validator
anchor deploy

# Get program IDs
anchor keys list
```

## Frontend Integration Testing

### 1. Update Constants File
```typescript
// src/constants/contracts.ts
// Update with deployed program IDs from anchor keys list
export const TOKEN_PROGRAM_ID_DEXSTA = new PublicKey('DEPLOYED_TOKEN_PROGRAM_ID')
export const POOL_PROGRAM_ID_DEXSTA = new PublicKey('DEPLOYED_POOL_PROGRAM_ID')
export const TOKEN_ADMIN_PROGRAM_ID = new PublicKey('DEPLOYED_TOKEN_ADMIN_ID')
export const POOL_ADMIN_PROGRAM_ID = new PublicKey('DEPLOYED_POOL_ADMIN_ID')
```

### 2. Environment Configuration
```bash
# .env.local
NEXT_PUBLIC_SOLANA_NETWORK=localnet
NEXT_PUBLIC_RPC_ENDPOINT=http://localhost:8899
NEXT_PUBLIC_ENABLE_DEVTOOLS=true
```

### 3. Test Connection
```typescript
// Test local connection
const connection = new Connection('http://localhost:8899', 'confirmed')
const version = await connection.getVersion()
console.log('Local validator version:', version)
```

## Testing Scenarios

### 1. Admin Contract Initialization
```bash
# Test admin initialization
anchor test tests/admin-initialization.ts
```

### 2. Token Creation Flow
```bash
# Test token creation
anchor test tests/token-creation.ts
```

### 3. Trading Operations
```bash
# Test buy/sell operations
anchor test tests/trading-operations.ts
```

### 4. Migration Testing
```bash
# Test migration threshold
anchor test tests/migration-testing.ts
```

### 5. Trading Game Testing
```bash
# Test game mechanics
anchor test tests/trading-game.ts
```

## Debugging Tools

### 1. Solana Logs
```bash
# Monitor transaction logs
solana logs

# Monitor specific program
solana logs --program TokenProgram111111111111111111111111111111111
```

### 2. Account Inspection
```bash
# Inspect account data
solana account <ACCOUNT_ADDRESS>

# Decode program data
anchor account <ACCOUNT_TYPE> <ACCOUNT_ADDRESS>
```

### 3. Transaction Analysis
```bash
# Get transaction details
solana transaction <TRANSACTION_SIGNATURE>
```

## Common Issues and Solutions

### 1. Program Deployment Issues
```bash
# Clear program cache
rm -rf target/

# Rebuild and redeploy
anchor build
anchor deploy --program-name token-program
```

### 2. Account Size Issues
```rust
// Increase account space if needed
#[account(init, payer = user, space = 8 + 1000)]
```

### 3. PDA Derivation Issues
```rust
// Ensure consistent seed derivation
let (pda, bump) = Pubkey::find_program_address(
    &[b"token_data", mint.key().as_ref()],
    program_id
);
```

## Testing Checklist

### Pre-Testing Setup
- [ ] Rust installed and updated
- [ ] Solana CLI installed (v1.17.0+)
- [ ] Anchor CLI installed (latest)
- [ ] Local validator running
- [ ] Test accounts funded with SOL
- [ ] Programs built and deployed
- [ ] Frontend constants updated

### Contract Testing
- [ ] Admin contract initialization
- [ ] Token creation with all parameters
- [ ] Buy operations with slippage protection
- [ ] Sell operations with balance checks
- [ ] Migration threshold testing
- [ ] Trading game pot accumulation
- [ ] Winner selection mechanism
- [ ] Error handling and edge cases

### Frontend Integration
- [ ] Wallet connection to localnet
- [ ] Token creation form submission
- [ ] Real-time price calculations
- [ ] Swap interface functionality
- [ ] Migration progress display
- [ ] Trading game stats updates
- [ ] Error handling and user feedback

### Performance Testing
- [ ] Multiple concurrent transactions
- [ ] Large trade amounts
- [ ] Rapid successive trades
- [ ] UI responsiveness under load
- [ ] Database synchronization
- [ ] Real-time updates accuracy

This setup provides a complete local testing environment for the Dexsta platform contracts and frontend integration.
