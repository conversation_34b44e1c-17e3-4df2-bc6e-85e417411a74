"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Upload, ImagePlus, Info, Sparkles, Gem, Wallet, Search, Tag, User, FileText } from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"

export function NFTMintDesktop() {
  const [mintType, setMintType] = useState<"label" | "nft">("nft")
  const { toast } = useToast()

  return (
    <div className="container py-10">
      <div className="mx-auto max-w-5xl">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-violet-600 font-heading">
            Create Your NFT
          </h1>
          <p className="mt-2 text-lg text-muted-foreground">
            Mint a unique NFT or label on the Sui blockchain
          </p>
        </div>

        <Tabs defaultValue="nft" onValueChange={(value) => setMintType(value as "label" | "nft")} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="nft" className="text-lg py-3">
              <ImagePlus className="h-5 w-5 mr-2" />
              Mint NFT
            </TabsTrigger>
            <TabsTrigger value="label" className="text-lg py-3">
              <Tag className="h-5 w-5 mr-2" />
              Mint Label
            </TabsTrigger>
          </TabsList>

          <TabsContent value="nft">
            <NFTMintForm />
          </TabsContent>

          <TabsContent value="label">
            <LabelMintForm />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

function NFTMintForm() {
  const [nftName, setNftName] = useState("")
  const [nftDescription, setNftDescription] = useState("")
  const [nftImage, setNftImage] = useState<File | null>(null)
  const [nftImagePreview, setNftImagePreview] = useState<string | null>(null)
  const [royaltyFee, setRoyaltyFee] = useState(5) // 5%
  const [isTransferable, setIsTransferable] = useState(true)
  const [quantity, setQuantity] = useState(1)
  const [labelId, setLabelId] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [mediaFile, setMediaFile] = useState<File | null>(null)
  const [mediaType, setMediaType] = useState<string | null>(null)
  const [attributes, setAttributes] = useState<any>({})
  const { toast } = useToast()

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]

      if (file.type.startsWith('image/')) {
        setNftImage(file)

        // Create a preview URL
        const reader = new FileReader()
        reader.onload = (event) => {
          setNftImagePreview(event.target?.result as string)
        }
        reader.readAsDataURL(file)

        toast({
          title: "Image uploaded",
          description: "Your NFT image has been uploaded successfully."
        })
      } else if (file.type.startsWith('audio/') || file.type.startsWith('video/')) {
        setMediaFile(file)
        setMediaType(file.type)

        toast({
          title: "Media file uploaded",
          description: "Your media file has been uploaded successfully."
        })
      } else {
        toast({
          title: "Invalid file type",
          description: "Please upload an image, audio, or video file.",
          variant: "destructive"
        })
      }
    }
  }

  const handleAttributesUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      const reader = new FileReader()

      reader.onload = (event) => {
        try {
          const data = JSON.parse(event.target?.result as string)
          setAttributes(data)

          toast({
            title: "Attributes uploaded",
            description: "Your NFT attributes have been uploaded successfully."
          })
        } catch (error) {
          toast({
            title: "Invalid JSON file",
            description: "Please upload a valid JSON file for attributes.",
            variant: "destructive"
          })
        }
      }

      reader.readAsText(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      console.log({
        name: nftName,
        description: nftDescription,
        image: nftImage,
        media: mediaFile,
        mediaType: mediaType,
        royaltyFee,
        isTransferable,
        quantity,
        labelId: labelId === "none" ? null : labelId,
        attributes: attributes
      })

      toast({
        title: "NFT Minted Successfully",
        description: "Your NFT is being minted and will appear in your collection shortly."
      })

      setIsSubmitting(false)
      // Reset form or redirect
    }, 2000)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
      {/* Left column - Preview */}
      <div className="lg:col-span-2">
        <Card className="sticky top-24 border-gray-800 bg-gray-900/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>NFT Preview</CardTitle>
            <CardDescription>See how your NFT will look</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center">
            <div className="relative w-full aspect-square rounded-lg overflow-hidden border-2 border-dashed border-gray-700 bg-gray-800/50 flex items-center justify-center mb-4">
              {nftImagePreview ? (
                <img
                  src={nftImagePreview}
                  alt="NFT Preview"
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="text-center p-6">
                  <ImagePlus className="h-12 w-12 mx-auto text-gray-500 mb-2" />
                  <p className="text-sm text-gray-500">NFT preview will appear here</p>
                </div>
              )}

              {mediaFile && (
                <Badge className="absolute top-2 right-2 bg-purple-600">
                  {mediaType?.startsWith('audio/') ? 'Audio' : 'Video'} Attached
                </Badge>
              )}
            </div>

            <div className="w-full space-y-2">
              <h3 className="text-xl font-bold truncate">
                {nftName || "Untitled NFT"}
              </h3>
              <p className="text-sm text-gray-400 line-clamp-3">
                {nftDescription || "No description provided"}
              </p>

              <div className="flex items-center justify-between mt-4 text-sm">
                <div className="flex items-center">
                  <Gem className="h-4 w-4 mr-1 text-purple-400" />
                  <span>Royalty: {royaltyFee}%</span>
                </div>
                <div className="flex items-center">
                  <Wallet className="h-4 w-4 mr-1 text-purple-400" />
                  <span>Qty: {quantity}</span>
                </div>
              </div>

              {Object.keys(attributes).length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-800">
                  <h4 className="text-sm font-medium mb-2">Attributes</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(attributes).map(([key, value]) => (
                      <div key={key} className="bg-gray-800/50 rounded-md p-2 text-xs">
                        <p className="text-gray-400">{key}</p>
                        <p className="font-medium truncate">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right column - Form */}
      <div className="lg:col-span-3">
        <Card className="border-gray-800 bg-gray-900/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle>NFT Details</CardTitle>
            <CardDescription>Fill in the details for your new NFT</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="nft-name">NFT Name</Label>
                  <Input
                    id="nft-name"
                    placeholder="Enter NFT name"
                    value={nftName}
                    onChange={(e) => setNftName(e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="nft-description">Description</Label>
                  <Textarea
                    id="nft-description"
                    placeholder="Describe your NFT"
                    value={nftDescription}
                    onChange={(e) => setNftDescription(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>

                <div>
                  <Label htmlFor="nft-image">Upload Image</Label>
                  <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed border-gray-700 rounded-md">
                    <div className="space-y-1 text-center">
                      <Upload className="mx-auto h-12 w-12 text-gray-500" />
                      <div className="flex text-sm text-gray-400">
                        <label
                          htmlFor="nft-image"
                          className="relative cursor-pointer rounded-md font-medium text-purple-400 hover:text-purple-300"
                        >
                          <span>Upload a file</span>
                          <input
                            id="nft-image"
                            name="nft-image"
                            type="file"
                            className="sr-only"
                            accept="image/*, audio/*, video/*"
                            onChange={handleImageChange}
                            required
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, GIF, MP3, MP4 up to 10MB
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="label-id">Label ID</Label>
                  <Input
                    id="label-id"
                    type="number"
                    placeholder="Enter Label ID (optional)"
                    value={labelId}
                    onChange={(e) => setLabelId(e.target.value)}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Link this NFT to a specific label ID
                  </p>
                </div>

                <div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="royalty-fee">
                      Royalty Fee ({royaltyFee}%)
                    </Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-gray-500" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="w-[200px] text-xs">
                            You'll receive this percentage of the sale price each time your NFT is sold on the marketplace.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Slider
                    id="royalty-fee"
                    min={0}
                    max={15}
                    step={1}
                    value={[royaltyFee]}
                    onValueChange={(value) => setRoyaltyFee(value[0])}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min={1}
                    max={100}
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value))}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="attributes" className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    Attributes (JSON File)
                  </Label>
                  <div className="mt-1">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => document.getElementById('attributes-upload')?.click()}
                    >
                      Upload Attributes JSON
                    </Button>
                    <input
                      id="attributes-upload"
                      type="file"
                      accept="application/json"
                      className="hidden"
                      onChange={handleAttributesUpload}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Upload a JSON file with your NFT attributes
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="transferable"
                    checked={isTransferable}
                    onCheckedChange={setIsTransferable}
                  />
                  <Label htmlFor="transferable">Transferable</Label>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Sparkles className="mr-2 h-4 w-4 animate-spin" />
                    Minting...
                  </>
                ) : (
                  "Mint NFT"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

function LabelMintForm() {
  const [labelName, setLabelName] = useState("")
  const [labelDescription, setLabelDescription] = useState("")
  const [labelImage, setLabelImage] = useState<string | null>(null)
  const [labelType, setLabelType] = useState<number>(1) // Default to Lead Label
  const [labelTypeName, setLabelTypeName] = useState("Lead Label")
  const [registrationYears, setRegistrationYears] = useState(1)
  const [quantity, setQuantity] = useState(1)
  const [linkToLabelId, setLinkToLabelId] = useState("")
  const [royaltyFee, setRoyaltyFee] = useState(10) // Default 10%
  const [isSearching, setIsSearching] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResult, setSearchResult] = useState<"available" | "taken" | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showLabelForm, setShowLabelForm] = useState(false)
  const { toast } = useToast()

  // Label types
  const labelTypes = [
    { id: 1, name: "Lead Label", description: "Your lead label is a 1 of 1 smart NFT. Think of it as your collection or album that NFTs you mint are linked to. Funds from sales are held in the wallet of your lead label." },
    { id: 2, name: "Profile Label", description: "Your profile label is a lead label, just for you. For example, create a profile label for your student profile, store all your NFT grades under this label." },
    { id: 3, name: "Tags Label", description: "Tag labels are limited transfer labels. After you send it to the recipient they will not be able to send it to anyone else. This is great if you are a teacher, consultant or general professional." },
    { id: 4, name: "Chapters Label", description: "Limited Edition prints, that are text-based NFTs. These are perfect for literature, poems, stories, recipes and more." },
    { id: 5, name: "Label Operator License", description: "If you have already minted your lead label, you can mint operator licenses for your team members. An operators license allows team members to mint and or sell NFTs under your label." },
    { id: 6, name: "Label Marketplace License", description: "If you have already minted your lead label, you can mint marketplace licenses, allowing artists and creators to sell their NFTs under your label." }
  ]
  
  // Generate a text-based image for the label
  const generateLabelImage = (text: string) => {
    // This is a simplified version - in a real app, you'd use a library like UltimateTextToImage
    // or a backend service to generate the image
    
    // For now, we'll just create a colored div with the text
    const colors = [
      '#34326E', '#68316E', '#316E69', '#6E3C31', '#6E3161',
      '#BF0B99', '#D426AF', '#8026D4', '#9A4FBD', '#497B8C',
      '#8B8C49', '#2F709E', '#2F5A9E', '#2A52DE', '#2A78DE',
      '#E3204A', '#26060D', '#1A0207', '#1A0106', '#038250'
    ]
    
    const bgColor = colors[Math.floor(Math.random() * colors.length)]

    // In a real implementation, this would be a base64 image URL
    // For now, we'll just set a placeholder
    setLabelImage(`data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400"><rect width="400" height="400" fill="${bgColor.replace('#', '%23')}" /><text x="50%" y="50%" font-family="Arial" font-size="32" fill="white" text-anchor="middle" dominant-baseline="middle">${text}</text></svg>`)
    
    toast({
      title: "Label preview generated",
      description: "Your label preview has been generated."
    })
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSearching(true)

    // Simulate API call to check if label is available
    setTimeout(() => {
      // For demo purposes, let's say labels with "taken" in the name are already taken
      const isTaken = searchQuery.toLowerCase().includes("taken")

      setSearchResult(isTaken ? "taken" : "available")
      setIsSearching(false)

      if (!isTaken) {
        setLabelName(searchQuery)
        generateLabelImage(searchQuery)
      }

      toast({
        title: isTaken ? "Label is taken" : "Label is available",
        description: isTaken
          ? "This label is already taken. Please try another one."
          : "This label is available for minting.",
        variant: isTaken ? "destructive" : "default"
      })
    }, 1500)
  }

  const handleSelectLabelType = (id: number) => {
    const selectedType = labelTypes.find(type => type.id === id)
    if (selectedType) {
      setLabelType(id)
      setLabelTypeName(selectedType.name)
      setShowLabelForm(true)

      // Set default values based on label type
      if (id === 1 || id === 2) {
        setQuantity(1) // Lead and Profile labels are 1 of 1
      }
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      console.log({
        name: labelName,
        description: labelDescription,
        type: labelType,
        typeName: labelTypeName,
        registrationYears,
        quantity,
        linkToLabelId: linkToLabelId || null,
        royaltyFee: labelType === 1 ? royaltyFee : 0, // Only lead labels have royalty
      })

      toast({
        title: "Label Minted Successfully",
        description: "Your label is being minted and will appear in your collection shortly."
      })

      setIsSubmitting(false)
    }, 2000)
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Left column - Search and Preview */}
      <div>
        <Card className="border-gray-800 bg-gray-900/50 backdrop-blur-sm mb-6">
          <CardHeader>
            <CardTitle>Search for Label</CardTitle>
            <CardDescription>Check if your desired label is available</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="flex items-center space-x-2">
                <Input
                  placeholder="Enter label name (e.g., 'Best Pizza NY')"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  required
                />
                <Button type="submit" disabled={isSearching}>
                  {isSearching ? (
                    <Sparkles className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>

              <div className="text-xs text-gray-400">
                <p>Labels starting with special characters have specific meanings:</p>
                <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-1">
                  <div>* - identity/profile</div>
                  <div>[ - label operator</div>
                  <div># - social/marketplace</div>
                  <div>! - male</div>
                  <div>@ - contract</div>
                  <div>$ - business</div>
                  <div>^ - travel</div>
                  <div>& - tickets/gaming</div>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>

        {searchResult === "available" && (
          <Card className="border-gray-800 bg-gray-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center text-green-400">
                <div className="w-2 h-2 rounded-full bg-green-400 mr-2"></div>
                Label Available
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col items-center">
              {labelImage ? (
                <div className="w-full max-w-[300px] aspect-square rounded-lg overflow-hidden mb-4">
                  <img
                    src={labelImage}
                    alt={labelName}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-full max-w-[300px] aspect-square rounded-lg bg-gray-800 flex items-center justify-center mb-4">
                  <Tag className="h-16 w-16 text-gray-600" />
                </div>
              )}

              <h3 className="text-xl font-bold">{labelName}</h3>

              {!showLabelForm && (
                <div className="w-full mt-4">
                  <h4 className="text-sm font-medium mb-2">Select Label Type</h4>
                  <div className="space-y-2">
                    {labelTypes.map((type) => (
                      <Button
                        key={type.id}
                        variant="outline"
                        className="w-full justify-start h-auto py-2 px-3"
                        onClick={() => handleSelectLabelType(type.id)}
                      >
                        <div className="text-left">
                          <div className="font-medium">{type.name}</div>
                          <div className="text-xs text-gray-400 line-clamp-1">{type.description}</div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {searchResult === "taken" && (
          <Card className="border-gray-800 bg-gray-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center text-red-400">
                <div className="w-2 h-2 rounded-full bg-red-400 mr-2"></div>
                Label Taken
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-gray-400">
                This label is already taken. Please try searching for a different label name.
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Right column - Form (only shown when a label type is selected) */}
      {showLabelForm && (
        <div>
          <Card className="border-gray-800 bg-gray-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Mint {labelTypeName}</CardTitle>
              <CardDescription>Fill in the details for your new label</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="label-title">Label Title</Label>
                    <Input
                      id="label-title"
                      placeholder="Enter a title for your label"
                      value={labelName}
                      onChange={(e) => setLabelName(e.target.value)}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="label-description">Description</Label>
                    <Textarea
                      id="label-description"
                      placeholder="Tell a cool story about why this label is important to you"
                      value={labelDescription}
                      onChange={(e) => setLabelDescription(e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>

                  <div>
                    <Label htmlFor="registration-years">Registration Years</Label>
                    <Input
                      id="registration-years"
                      type="number"
                      min={1}
                      max={10}
                      value={registrationYears}
                      onChange={(e) => setRegistrationYears(parseInt(e.target.value))}
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Number of years to register this label
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="quantity">Quantity</Label>
                    <Input
                      id="quantity"
                      type="number"
                      min={1}
                      max={labelType === 1 || labelType === 2 ? 1 : 100}
                      value={quantity}
                      onChange={(e) => setQuantity(parseInt(e.target.value))}
                      required
                      disabled={labelType === 1 || labelType === 2}
                    />
                    {(labelType === 1 || labelType === 2) && (
                      <p className="text-xs text-gray-500 mt-1">
                        {labelTypeName} must be a 1 of 1 NFT
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="link-to-label">Link To Label ID (Optional)</Label>
                    <Input
                      id="link-to-label"
                      type="number"
                      placeholder="0"
                      value={linkToLabelId}
                      onChange={(e) => setLinkToLabelId(e.target.value)}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Link this label to an existing label ID
                    </p>
                  </div>

                  {labelType === 1 && (
                    <div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="royalty-fee">
                          Label Royalty ({royaltyFee}%)
                        </Label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Info className="h-4 w-4 text-gray-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="w-[200px] text-xs">
                                Percentage of each sale that goes to the label wallet
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <Slider
                        id="royalty-fee"
                        min={0}
                        max={30}
                        step={1}
                        value={[royaltyFee]}
                        onValueChange={(value) => setRoyaltyFee(value[0])}
                        className="mt-2"
                      />
                    </div>
                  )}
                </div>

                
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Sparkles className="mr-2 h-4 w-4 animate-spin" />
                      Minting...
                    </>
                  ) : (
                    "Mint Label"
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
