module your_platform::token_launcher {

    use sui::balance;
    use sui::coin::{Self, Coin};
    use sui::object::{Self, UID};
    use sui::table::{Self, Table};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use sui::vec;
    use sui::string::{Self, String};
    use sui::option::{Self, Option};
    use sui::token::metadata::{Self, CoinMetadata};

    /// Shared phantom type for all meme tokens
    struct YourPlatformCoin has store, drop {}

    /// Basic info for each launched token
    struct TokenInfo has store, drop {
        id: u64,
        name: String,
        symbol: String,
        decimals: u8,
        total_supply: u64,
        creator: address,
    }

    /// Global token launcher & registry object
    struct TokenRegistry has key {
        id: UID,
        next_id: u64,
        registry: Table<u64, TokenInfo>,
        metadata_registry: Table<u64, CoinMetadata>,
        treasury: balance::Supply<YourPlatformCoin>,
    }

    /// Initialize registry and treasury
    public entry fun init(ctx: &mut TxContext): TokenRegistry {
        let id = object::new(ctx);
        let registry = table::new<u64, TokenInfo>(ctx);
        let metadata_registry = table::new<u64, CoinMetadata>(ctx);
        let treasury = balance::create_supply<YourPlatformCoin>(ctx);
        TokenRegistry {
            id,
            next_id: 0,
            registry,
            metadata_registry,
            treasury,
        }
    }

    /// Mint a new token and register its metadata
    public entry fun launch_token(
        launcher: &mut TokenRegistry,
        name: String,
        symbol: String,
        decimals: u8,
        total_supply: u64,
        ctx: &mut TxContext
    ) {
        let creator = tx_context::sender(ctx);
        let id = launcher.next_id;

        // Store metadata
        let token_info = TokenInfo {
            id,
            name: string::clone(&name),
            symbol: string::clone(&symbol),
            decimals,
            total_supply,
            creator,
        };
        table::add(&mut launcher.registry, id, token_info);

        // Optional: customize these fields from frontend input later
        let metadata = CoinMetadata {
            name: string::clone(&name),
            symbol: string::clone(&symbol),
            description: string::utf8(b"Created with YourPlatform Meme Launcher"),
            icon_url: string::utf8(b""), // Could be user-submitted in future
            project_url: string::utf8(b""),
            decimals,
        };
        table::add(&mut launcher.metadata_registry, id, metadata);

        // Mint and send coins
        let coins = coin::mint<YourPlatformCoin>(&mut launcher.treasury, total_supply, ctx);
        transfer::transfer(coins, creator);

        launcher.next_id = launcher.next_id + 1;
    }

    /// View token info by ID
    public fun get_token_info(launcher: &TokenRegistry, id: u64): &TokenInfo {
        table::borrow(&launcher.registry, &id)
    }

    /// View metadata by ID
    public fun get_token_metadata(launcher: &TokenRegistry, id: u64): &CoinMetadata {
        table::borrow(&launcher.metadata_registry, &id)
    }
}
