const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

const inputs = {
  tokenName: "<PERSON>ba Wali",
  symbol: "WALI",
  description: "The dog of dignity. Launched on-chain.",
  iconURL: "https://example.com/icon.png",
  decimals: 9,
  structName: "ShibaWaliCoin",
  moduleName: "shibawali",
  publishAddress: "0xYourAddressHere"
};

const coinTemplate = `
module ${inputs.publishAddress}::${inputs.moduleName} {
    use sui::coin::{Self, TreasuryCap, Coin, create_currency};
    use sui::tx_context::TxContext;

    struct ${inputs.structName} has store, drop {}

    public entry fun init(
        ctx: &mut TxContext
    ): TreasuryCap<${inputs.structName}> {
        create_currency<${inputs.structName}>(
            b"${inputs.tokenName}",
            b"${inputs.symbol}",
            b"${inputs.description}",
            b"${inputs.iconURL}",
            ${inputs.decimals},
            ctx
        )
    }
}
`;

const moveToml = `
[package]
name = "${inputs.moduleName}"
version = "0.0.1"
published-at = "${inputs.publishAddress}"

[dependencies]
Sui = { local = "../../sui-framework" }

[addresses]
${inputs.moduleName} = "${inputs.publishAddress}"
`;

const projectDir = path.join(__dirname, "generated_tokens", inputs.moduleName);
const sourcesDir = path.join(projectDir, "sources");

fs.mkdirSync(sourcesDir, { recursive: true });
fs.writeFileSync(path.join(projectDir, "Move.toml"), moveToml.trim());
fs.writeFileSync(path.join(sourcesDir, "coin.move"), coinTemplate.trim());

console.log("✅ Move package generated.");

// Optional: build it
try {
  execSync(`cd ${projectDir} && sui move build`, { stdio: "inherit" });
  console.log("✅ Build successful.");

  // Optional: publish it
  // execSync(`cd ${projectDir} && sui client publish --gas-budget 50000000`, { stdio: "inherit" });
  // console.log("✅ Module published.");
} catch (err) {
  console.error("❌ Error building or publishing:", err.message);
}
