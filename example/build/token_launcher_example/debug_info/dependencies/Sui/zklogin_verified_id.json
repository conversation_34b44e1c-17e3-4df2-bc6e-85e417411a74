{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/crypto/zklogin_verified_id.move", "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 127, "end": 146}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "zklogin_verified_id"], "struct_map": {"0": {"definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 338, "end": 348}, "type_parameters": [], "fields": [{"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 397, "end": 399}, {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 465, "end": 470}, {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 519, "end": 533}, {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 582, "end": 597}, {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 630, "end": 636}, {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 680, "end": 688}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 762, "end": 839}, "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 773, "end": 778}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 779, "end": 790}]], "returns": [{"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 806, "end": 813}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 820, "end": 831}, "1": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 820, "end": 837}}, "is_native": false}, "1": {"location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 916, "end": 1012}, "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 927, "end": 941}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 942, "end": 953}]], "returns": [{"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 969, "end": 976}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 984, "end": 995}, "1": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 983, "end": 1010}}, "is_native": false}, "2": {"location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1090, "end": 1188}, "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1101, "end": 1116}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1117, "end": 1128}]], "returns": [{"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1144, "end": 1151}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1159, "end": 1170}, "1": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1158, "end": 1186}}, "is_native": false}, "3": {"location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1250, "end": 1330}, "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1261, "end": 1267}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1268, "end": 1279}]], "returns": [{"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1295, "end": 1302}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1310, "end": 1321}, "1": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1309, "end": 1328}}, "is_native": false}, "4": {"location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1403, "end": 1487}, "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1414, "end": 1422}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1423, "end": 1434}]], "returns": [{"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1450, "end": 1457}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1465, "end": 1476}, "1": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1464, "end": 1485}}, "is_native": false}, "5": {"location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1513, "end": 1698}, "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1524, "end": 1530}, "type_parameters": [], "parameters": [["verified_id#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1531, "end": 1542}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1667, "end": 1678}, "1": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1566, "end": 1656}, "2": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1653, "end": 1654}, "3": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1640, "end": 1641}, "4": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1629, "end": 1630}, "5": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1609, "end": 1610}, "6": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1590, "end": 1591}, "7": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1684, "end": 1695}, "8": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1695, "end": 1696}}, "is_native": false}, "6": {"location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1737, "end": 1961}, "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1748, "end": 1765}, "type_parameters": [], "parameters": [["_key_claim_name#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1771, "end": 1786}], ["_key_claim_value#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1800, "end": 1816}], ["_issuer#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1830, "end": 1837}], ["_audience#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1851, "end": 1860}], ["_pin_hash#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1874, "end": 1883}], ["_ctx#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1895, "end": 1899}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1940, "end": 1957}, "1": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 1925, "end": 1958}}, "is_native": false}, "7": {"location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2000, "end": 2240}, "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2011, "end": 2027}, "type_parameters": [], "parameters": [["_address#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2033, "end": 2041}], ["_key_claim_name#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2056, "end": 2071}], ["_key_claim_value#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2086, "end": 2102}], ["_issuer#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2117, "end": 2124}], ["_audience#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2139, "end": 2148}], ["_pin_hash#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2163, "end": 2172}]], "returns": [{"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2183, "end": 2187}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2209, "end": 2226}, "1": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2194, "end": 2227}}, "is_native": false}, "8": {"location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2640, "end": 2847}, "definition_location": {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2651, "end": 2676}, "type_parameters": [], "parameters": [["address#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2682, "end": 2689}], ["key_claim_name#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2704, "end": 2718}], ["key_claim_value#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2737, "end": 2752}], ["issuer#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2771, "end": 2777}], ["audience#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2796, "end": 2804}], ["pin_hash#0#0", {"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2823, "end": 2831}]], "returns": [{"file_hash": [14, 226, 78, 222, 205, 92, 118, 240, 108, 12, 39, 91, 193, 252, 178, 101, 115, 171, 141, 253, 24, 181, 226, 195, 183, 248, 53, 159, 39, 3, 155, 96], "start": 2842, "end": 2846}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EFunctionDisabled": 0}}