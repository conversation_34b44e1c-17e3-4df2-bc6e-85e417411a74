{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/bag.move", "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1151, "end": 1154}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "bag"], "struct_map": {"0": {"definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1275, "end": 1278}, "type_parameters": [], "fields": [{"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1327, "end": 1329}, {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1389, "end": 1393}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1432, "end": 1539}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1443, "end": 1446}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1447, "end": 1450}]], "returns": [{"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1469, "end": 1472}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1509, "end": 1512}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1497, "end": 1513}, "2": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1529, "end": 1530}, "3": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1479, "end": 1537}}, "is_native": false}, "1": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1710, "end": 1853}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1721, "end": 1724}, "type_parameters": [["K", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1725, "end": 1726}], ["V", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1749, "end": 1750}]], "parameters": [["bag#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1759, "end": 1762}], ["k#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1774, "end": 1775}], ["v#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1780, "end": 1781}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1808, "end": 1811}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1803, "end": 1814}, "2": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1816, "end": 1817}, "3": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1819, "end": 1820}, "4": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1792, "end": 1821}, "5": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1838, "end": 1841}, "6": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1838, "end": 1846}, "8": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1849, "end": 1850}, "9": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1847, "end": 1848}, "10": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1827, "end": 1830}, "11": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1827, "end": 1835}, "12": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1827, "end": 1850}, "13": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 1850, "end": 1851}}, "is_native": false}, "2": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2215, "end": 2321}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2226, "end": 2232}, "type_parameters": [["K", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2233, "end": 2234}], ["V", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2257, "end": 2258}]], "parameters": [["bag#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2267, "end": 2270}], ["k#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2278, "end": 2279}]], "returns": [{"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2285, "end": 2287}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2309, "end": 2312}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2308, "end": 2315}, "2": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2317, "end": 2318}, "3": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2294, "end": 2319}}, "is_native": false}, "3": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2685, "end": 2811}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2696, "end": 2706}, "type_parameters": [["K", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2707, "end": 2708}], ["V", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2731, "end": 2732}]], "parameters": [["bag#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2741, "end": 2744}], ["k#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2756, "end": 2757}]], "returns": [{"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2763, "end": 2769}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2799, "end": 2802}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2794, "end": 2805}, "2": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2807, "end": 2808}, "3": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 2776, "end": 2809}}, "is_native": false}, "4": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3165, "end": 3322}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3176, "end": 3182}, "type_parameters": [["K", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3183, "end": 3184}], ["V", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3207, "end": 3208}]], "parameters": [["bag#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3217, "end": 3220}], ["k#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3232, "end": 3233}]], "returns": [{"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3239, "end": 3240}], "locals": [["v#1#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3251, "end": 3252}]], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3274, "end": 3277}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3269, "end": 3280}, "2": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3282, "end": 3283}, "3": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3255, "end": 3284}, "4": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3251, "end": 3252}, "5": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3301, "end": 3304}, "6": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3301, "end": 3309}, "8": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3312, "end": 3313}, "9": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3310, "end": 3311}, "10": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3290, "end": 3293}, "11": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3290, "end": 3298}, "12": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3290, "end": 3313}, "13": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3319, "end": 3320}}, "is_native": false}, "5": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3417, "end": 3521}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3428, "end": 3436}, "type_parameters": [["K", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3437, "end": 3438}]], "parameters": [["bag#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3461, "end": 3464}], ["k#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3472, "end": 3473}]], "returns": [{"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3479, "end": 3483}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3509, "end": 3512}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3508, "end": 3515}, "2": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3517, "end": 3518}, "3": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3490, "end": 3519}}, "is_native": false}, "6": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3655, "end": 3791}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3666, "end": 3684}, "type_parameters": [["K", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3685, "end": 3686}], ["V", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3709, "end": 3710}]], "parameters": [["bag#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3719, "end": 3722}], ["k#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3730, "end": 3731}]], "returns": [{"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3737, "end": 3741}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3779, "end": 3782}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3778, "end": 3785}, "2": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3787, "end": 3788}, "3": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3748, "end": 3789}}, "is_native": false}, "7": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3856, "end": 3906}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3867, "end": 3873}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3874, "end": 3877}]], "returns": [{"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3886, "end": 3889}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3896, "end": 3899}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3896, "end": 3904}}, "is_native": false}, "8": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3972, "end": 4030}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3983, "end": 3991}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 3992, "end": 3995}]], "returns": [{"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4004, "end": 4008}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4015, "end": 4018}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4015, "end": 4023}, "3": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4027, "end": 4028}, "4": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4024, "end": 4026}, "5": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4015, "end": 4028}}, "is_native": false}, "9": {"location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4122, "end": 4246}, "definition_location": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4133, "end": 4146}, "type_parameters": [], "parameters": [["bag#0#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4147, "end": 4150}]], "returns": [], "locals": [["id#1#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4173, "end": 4175}], ["size#1#0", {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4177, "end": 4181}]], "nops": {}, "code_map": {"0": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4186, "end": 4189}, "1": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4167, "end": 4183}, "2": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4177, "end": 4181}, "3": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4173, "end": 4175}, "4": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4203, "end": 4207}, "5": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4211, "end": 4212}, "6": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4208, "end": 4210}, "7": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4195, "end": 4227}, "9": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4214, "end": 4226}, "10": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4195, "end": 4227}, "11": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4233, "end": 4235}, "12": {"file_hash": [49, 137, 240, 249, 165, 41, 149, 242, 64, 25, 120, 91, 64, 228, 178, 159, 31, 225, 146, 114, 52, 101, 138, 242, 89, 236, 244, 251, 252, 134, 114, 86], "start": 4233, "end": 4244}}, "is_native": false}}, "constant_map": {"EBagNotEmpty": 0}}