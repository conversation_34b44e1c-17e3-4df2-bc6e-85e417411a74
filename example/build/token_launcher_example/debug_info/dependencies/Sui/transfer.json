{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/transfer.move", "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 110, "end": 118}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "transfer"], "struct_map": {"0": {"definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 598, "end": 607}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 616, "end": 617}]], "fields": [{"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 639, "end": 641}, {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 651, "end": 658}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2349, "end": 2442}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2360, "end": 2368}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2369, "end": 2370}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2377, "end": 2380}], ["recipient#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2385, "end": 2394}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2425, "end": 2428}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2430, "end": 2439}, "2": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2411, "end": 2440}}, "is_native": false}, "1": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2871, "end": 2979}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2882, "end": 2897}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2898, "end": 2899}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2914, "end": 2917}], ["recipient#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2922, "end": 2931}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2962, "end": 2965}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2967, "end": 2976}, "2": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 2948, "end": 2977}}, "is_native": false}, "2": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 3984, "end": 4246}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 3995, "end": 4009}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4010, "end": 4011}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4018, "end": 4021}], ["party#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4026, "end": 4031}]], "returns": [], "locals": [["addresses#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4136, "end": 4145}], ["default#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4127, "end": 4134}], ["permissions#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4147, "end": 4158}]], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4066, "end": 4071}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4066, "end": 4089}, "2": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4058, "end": 4116}, "4": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4091, "end": 4115}, "5": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4058, "end": 4116}, "6": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4162, "end": 4167}, "7": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4162, "end": 4181}, "8": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4147, "end": 4158}, "9": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4136, "end": 4145}, "10": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4127, "end": 4134}, "11": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4207, "end": 4210}, "12": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4212, "end": 4219}, "13": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4221, "end": 4230}, "14": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4232, "end": 4243}, "15": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 4187, "end": 4244}}, "is_native": false}, "3": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5066, "end": 5343}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5077, "end": 5098}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5099, "end": 5100}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5115, "end": 5118}], ["party#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5123, "end": 5128}]], "returns": [], "locals": [["addresses#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5233, "end": 5242}], ["default#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5224, "end": 5231}], ["permissions#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5244, "end": 5255}]], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5163, "end": 5168}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5163, "end": 5186}, "2": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5155, "end": 5213}, "4": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5188, "end": 5212}, "5": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5155, "end": 5213}, "6": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5259, "end": 5264}, "7": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5259, "end": 5278}, "8": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5244, "end": 5255}, "9": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5233, "end": 5242}, "10": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5224, "end": 5231}, "11": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5304, "end": 5307}, "12": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5309, "end": 5316}, "13": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5318, "end": 5327}, "14": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5329, "end": 5340}, "15": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5284, "end": 5341}}, "is_native": false}, "4": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5712, "end": 5784}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5723, "end": 5736}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5737, "end": 5738}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5745, "end": 5748}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5778, "end": 5781}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5759, "end": 5782}}, "is_native": false}, "5": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5961, "end": 6048}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5972, "end": 5992}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 5993, "end": 5994}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 6009, "end": 6012}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 6042, "end": 6045}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 6023, "end": 6046}}, "is_native": false}, "6": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 6639, "end": 6709}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 6650, "end": 6662}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 6663, "end": 6664}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 6671, "end": 6674}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 6703, "end": 6706}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 6685, "end": 6707}}, "is_native": false}, "7": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7111, "end": 7196}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7122, "end": 7141}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7142, "end": 7143}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7158, "end": 7161}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7190, "end": 7193}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7172, "end": 7194}}, "is_native": false}, "8": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7685, "end": 7861}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7696, "end": 7703}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7704, "end": 7705}]], "parameters": [["parent#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7712, "end": 7718}], ["to_receive#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7730, "end": 7740}]], "returns": [{"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7757, "end": 7758}], "locals": [["id#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7781, "end": 7783}], ["version#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7785, "end": 7792}]], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7797, "end": 7807}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7769, "end": 7794}, "2": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7785, "end": 7792}, "3": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7781, "end": 7783}, "4": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7826, "end": 7832}, "6": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7826, "end": 7845}, "7": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7847, "end": 7849}, "8": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7851, "end": 7858}, "9": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 7813, "end": 7859}}, "is_native": false}, "9": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8179, "end": 8370}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8190, "end": 8204}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8205, "end": 8206}]], "parameters": [["parent#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8221, "end": 8227}], ["to_receive#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8239, "end": 8249}]], "returns": [{"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8266, "end": 8267}], "locals": [["id#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8290, "end": 8292}], ["version#1#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8294, "end": 8301}]], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8306, "end": 8316}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8278, "end": 8303}, "2": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8294, "end": 8301}, "3": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8290, "end": 8292}, "4": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8335, "end": 8341}, "6": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8335, "end": 8354}, "7": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8356, "end": 8358}, "8": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8360, "end": 8367}, "9": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8322, "end": 8368}}, "is_native": false}, "10": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8445, "end": 8534}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8456, "end": 8475}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8476, "end": 8477}]], "parameters": [["receiving#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8484, "end": 8493}]], "returns": [{"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8511, "end": 8513}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8520, "end": 8529}, "1": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8520, "end": 8532}}, "is_native": false}, "11": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8536, "end": 8598}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8563, "end": 8581}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8582, "end": 8583}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8590, "end": 8593}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "12": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8600, "end": 8661}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8627, "end": 8644}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8645, "end": 8646}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8653, "end": 8656}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "13": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8663, "end": 8825}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8690, "end": 8709}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8710, "end": 8711}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8723, "end": 8726}], ["default_permissions#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8735, "end": 8754}], ["addresses#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8765, "end": 8774}], ["permissions#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8797, "end": 8808}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "14": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8827, "end": 8904}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8854, "end": 8867}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8868, "end": 8869}]], "parameters": [["obj#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8876, "end": 8879}], ["recipient#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8884, "end": 8893}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "15": {"location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8906, "end": 8988}, "definition_location": {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8917, "end": 8929}, "type_parameters": [["T", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8930, "end": 8931}]], "parameters": [["parent#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8938, "end": 8944}], ["to_receive#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8955, "end": 8965}], ["version#0#0", {"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8971, "end": 8978}]], "returns": [{"file_hash": [104, 65, 5, 13, 216, 83, 115, 194, 111, 76, 67, 27, 250, 172, 193, 99, 131, 152, 109, 161, 16, 96, 48, 71, 57, 146, 77, 148, 198, 75, 156, 230], "start": 8986, "end": 8987}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EBCSSerializationFailure": 1, "EInvalidPartyPermissions": 7, "ENotSupported": 5, "EReceivingObjectTypeMismatch": 2, "ESharedNonNewObject": 0, "ESharedObjectOperationNotSupported": 4, "EUnableToReceiveObject": 3}}