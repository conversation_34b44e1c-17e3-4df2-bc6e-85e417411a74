{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/sui.move", "definition_location": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 213, "end": 216}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "sui"], "struct_map": {"0": {"definition_location": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 835, "end": 838}, "type_parameters": [], "fields": [{"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 835, "end": 838}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 992, "end": 1574}, "definition_location": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 996, "end": 999}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1000, "end": 1003}]], "returns": [{"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1022, "end": 1034}], "locals": [["metadata#1#0", {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1158, "end": 1166}], ["supply#1#0", {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1425, "end": 1431}], ["total_sui#1#0", {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1475, "end": 1484}], ["treasury#1#0", {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1148, "end": 1156}]], "nops": {}, "code_map": {"0": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1049, "end": 1052}, "2": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1049, "end": 1061}, "3": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1065, "end": 1069}, "4": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1062, "end": 1064}, "5": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1041, "end": 1089}, "9": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1071, "end": 1088}, "10": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1041, "end": 1089}, "11": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1103, "end": 1106}, "13": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1103, "end": 1114}, "14": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1118, "end": 1119}, "15": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1115, "end": 1117}, "16": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1095, "end": 1136}, "20": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1121, "end": 1135}, "21": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1095, "end": 1136}, "22": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1201, "end": 1207}, "24": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1217, "end": 1218}, "25": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1228, "end": 1234}, "26": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1244, "end": 1250}, "27": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1318, "end": 1321}, "28": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1331, "end": 1345}, "29": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1355, "end": 1358}, "30": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1170, "end": 1365}, "31": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1158, "end": 1166}, "32": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1148, "end": 1156}, "33": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1402, "end": 1410}, "34": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1371, "end": 1411}, "35": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1434, "end": 1442}, "36": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1434, "end": 1465}, "37": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1421, "end": 1431}, "38": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1487, "end": 1493}, "39": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1510, "end": 1527}, "40": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1487, "end": 1528}, "41": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1475, "end": 1484}, "42": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1534, "end": 1540}, "43": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1534, "end": 1557}, "45": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1563, "end": 1572}}, "is_native": false}, "1": {"location": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1576, "end": 1689}, "definition_location": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1593, "end": 1601}, "type_parameters": [], "parameters": [["c#0#0", {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1602, "end": 1603}], ["recipient#0#0", {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1622, "end": 1631}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1674, "end": 1675}, "1": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1677, "end": 1686}, "2": {"file_hash": [200, 88, 22, 88, 21, 130, 13, 201, 189, 44, 223, 254, 64, 70, 59, 244, 38, 49, 190, 94, 242, 248, 114, 84, 247, 240, 234, 175, 4, 205, 108, 219], "start": 1648, "end": 1687}}, "is_native": false}}, "constant_map": {"EAlreadyMinted": 0, "ENotSystemAddress": 1, "MIST_PER_SUI": 2, "TOTAL_SUPPLY_MIST": 4, "TOTAL_SUPPLY_SUI": 3}}