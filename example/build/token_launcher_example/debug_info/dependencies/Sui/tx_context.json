{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/tx_context.move", "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 87, "end": 97}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "tx_context"], "struct_map": {"0": {"definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 603, "end": 612}, "type_parameters": [], "fields": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 696, "end": 702}, {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 757, "end": 764}, {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 815, "end": 820}, {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 875, "end": 893}, {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1044, "end": 1055}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1140, "end": 1209}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1151, "end": 1157}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1158, "end": 1163}]], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1178, "end": 1185}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1192, "end": 1207}}, "is_native": false}, "1": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1210, "end": 1246}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1221, "end": 1234}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1238, "end": 1245}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "2": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1361, "end": 1431}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1372, "end": 1378}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1379, "end": 1383}]], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1398, "end": 1409}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1417, "end": 1421}, "1": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1416, "end": 1429}}, "is_native": false}, "3": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1462, "end": 1525}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1473, "end": 1478}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1479, "end": 1484}]], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1499, "end": 1502}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1509, "end": 1523}}, "is_native": false}, "4": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1526, "end": 1557}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1537, "end": 1549}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1553, "end": 1556}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "5": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1628, "end": 1717}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1639, "end": 1657}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1658, "end": 1663}]], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1678, "end": 1681}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1688, "end": 1715}}, "is_native": false}, "6": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1718, "end": 1762}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1729, "end": 1754}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1758, "end": 1761}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "7": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1848, "end": 1927}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1859, "end": 1866}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1867, "end": 1872}]], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1887, "end": 1902}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 1909, "end": 1925}}, "is_native": false}, "8": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2130, "end": 2211}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2141, "end": 2161}, "type_parameters": [], "parameters": [["_ctx#0#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2162, "end": 2166}]], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2185, "end": 2192}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2199, "end": 2209}}, "is_native": false}, "9": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2212, "end": 2243}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2223, "end": 2231}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2235, "end": 2242}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "10": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2378, "end": 2446}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2382, "end": 2393}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2394, "end": 2399}]], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2414, "end": 2417}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2424, "end": 2444}}, "is_native": false}, "11": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2447, "end": 2484}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2458, "end": 2476}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2480, "end": 2483}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "12": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2576, "end": 2611}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2587, "end": 2603}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2607, "end": 2610}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "13": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2704, "end": 2740}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2715, "end": 2732}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 2736, "end": 2739}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "14": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5340, "end": 5493}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5344, "end": 5358}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5362, "end": 5377}], "locals": [["%#1", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5420, "end": 5491}], ["sponsor#1#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5388, "end": 5395}]], "nops": {}, "code_map": {"0": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5398, "end": 5414}, "1": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5388, "end": 5395}, "2": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5424, "end": 5431}, "3": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5424, "end": 5440}, "4": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5444, "end": 5445}, "5": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5441, "end": 5443}, "6": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5420, "end": 5491}, "7": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5447, "end": 5461}, "8": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5420, "end": 5491}, "10": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5480, "end": 5490}, "11": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5488, "end": 5489}, "12": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5480, "end": 5490}, "14": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5467, "end": 5491}, "15": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5420, "end": 5491}}, "is_native": false}, "15": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5494, "end": 5539}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5505, "end": 5519}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5523, "end": 5538}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "16": {"location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5860, "end": 5929}, "definition_location": {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5871, "end": 5880}, "type_parameters": [], "parameters": [["tx_hash#0#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5881, "end": 5888}], ["ids_created#0#0", {"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5902, "end": 5913}]], "returns": [{"file_hash": [167, 87, 177, 80, 201, 95, 19, 195, 52, 101, 45, 80, 221, 92, 78, 228, 224, 252, 241, 190, 48, 252, 154, 13, 186, 147, 120, 253, 33, 155, 28, 169], "start": 5921, "end": 5928}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}