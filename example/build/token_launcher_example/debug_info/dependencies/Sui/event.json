{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/event.move", "definition_location": {"file_hash": [248, 12, 179, 88, 238, 241, 183, 79, 123, 236, 52, 235, 230, 247, 34, 8, 9, 104, 210, 170, 48, 70, 138, 165, 149, 218, 15, 84, 187, 119, 32, 181], "start": 778, "end": 783}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "event"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [248, 12, 179, 88, 238, 241, 183, 79, 123, 236, 52, 235, 230, 247, 34, 8, 9, 104, 210, 170, 48, 70, 138, 165, 149, 218, 15, 84, 187, 119, 32, 181], "start": 1099, "end": 1148}, "definition_location": {"file_hash": [248, 12, 179, 88, 238, 241, 183, 79, 123, 236, 52, 235, 230, 247, 34, 8, 9, 104, 210, 170, 48, 70, 138, 165, 149, 218, 15, 84, 187, 119, 32, 181], "start": 1117, "end": 1121}, "type_parameters": [["T", {"file_hash": [248, 12, 179, 88, 238, 241, 183, 79, 123, 236, 52, 235, 230, 247, 34, 8, 9, 104, 210, 170, 48, 70, 138, 165, 149, 218, 15, 84, 187, 119, 32, 181], "start": 1122, "end": 1123}]], "parameters": [["event#0#0", {"file_hash": [248, 12, 179, 88, 238, 241, 183, 79, 123, 236, 52, 235, 230, 247, 34, 8, 9, 104, 210, 170, 48, 70, 138, 165, 149, 218, 15, 84, 187, 119, 32, 181], "start": 1138, "end": 1143}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}