module your_platform::token_launcher {

    use sui::balance::{Self, Balance};
    use sui::coin::{Self, Coin, TreasuryCap};
    use sui::object::{Self, UID, ID};
    use sui::table::{Self, Table};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use sui::string::{Self, String};
    use sui::option::{Self, Option};
    use sui::url::{Self, Url};
    use sui::sui::SUI;
    use sui::event;

    // Error codes
    const ETokenAlreadyExists: u64 = 1;
    const EInsufficientPayment: u64 = 2;
    const ETokenNotFound: u64 = 3;

    // Constants
    const MINT_FEE: u64 = 100_000_000; // 0.1 SUI

    /// One-time witness for the platform
    public struct TOKEN_LAUNCHER has drop {}

    /// Pre-deployed token types for real Sui tokens
    public struct PLATFORM_TOKEN_001 has drop {}
    public struct PLATFORM_TOKEN_002 has drop {}
    public struct PLATFORM_TOKEN_003 has drop {}
    // Add more as needed...

    /// Metadata for each created token
    public struct TokenInfo has key, store {
        id: UID,
        token_id: u64,
        name: String,
        symbol: String,
        decimals: u8,
        total_supply: u64,
        creator: address,
        description: String,
        icon_url: Option<Url>,
        // Treasury info for real tokens
        treasury_cap_id: Option<ID>,
        metadata_id: Option<ID>,
        // Virtual balance tracking for non-real tokens
        is_real_token: bool,
    }

    /// The main launcher registry
    public struct TokenRegistry has key {
        id: UID,
        next_token_id: u64,
        tokens: Table<String, address>, // symbol -> TokenInfo address
        next_real_token_type: u64, // Next available real token type (1-3)
        platform_admin: address,
    }

    /// Events
    public struct TokenCreated has copy, drop {
        token_id: u64,
        name: String,
        symbol: String,
        creator: address,
        total_supply: u64,
        is_real_token: bool,
        token_info_address: address,
    }

    /// Initialize the registry
    fun init(_witness: TOKEN_LAUNCHER, ctx: &mut TxContext) {
        let registry = TokenRegistry {
            id: object::new(ctx),
            next_token_id: 1,
            tokens: table::new(ctx),
            next_real_token_type: 1,
            platform_admin: tx_context::sender(ctx),
        };
        transfer::share_object(registry);
    }

    /// Create a real Sui token (limited to pre-deployed types)
    public entry fun launch_real_token(
        registry: &mut TokenRegistry,
        name: vector<u8>,
        symbol: vector<u8>,
        description: vector<u8>,
        icon_url: vector<u8>,
        decimals: u8,
        total_supply: u64,
        payment: Coin<SUI>,
        ctx: &mut TxContext
    ) {
        let creator = tx_context::sender(ctx);
        let name_str = string::utf8(name);
        let symbol_str = string::utf8(symbol);
        let description_str = string::utf8(description);

        // Check payment
        assert!(coin::value(&payment) >= MINT_FEE, EInsufficientPayment);

        // Check if symbol already exists
        assert!(!table::contains(&registry.tokens, symbol_str), ETokenAlreadyExists);

        // Check if we have available real token types
        assert!(registry.next_real_token_type <= 3, ETokenNotFound);

        // Create icon URL if provided
        let icon_url_option = if (vector::length(&icon_url) > 0) {
            option::some(url::new_unsafe_from_bytes(icon_url))
        } else {
            option::none()
        };

        // Create real token based on available type
        let (treasury_cap_id, metadata_id) = if (registry.next_real_token_type == 1) {
            create_real_token_type_1(name, symbol, description, icon_url_option, decimals, ctx)
        } else if (registry.next_real_token_type == 2) {
            create_real_token_type_2(name, symbol, description, icon_url_option, decimals, ctx)
        } else {
            create_real_token_type_3(name, symbol, description, icon_url_option, decimals, ctx)
        };

        // Create token info
        let token_info = TokenInfo {
            id: object::new(ctx),
            token_id: registry.next_token_id,
            name: name_str,
            symbol: symbol_str,
            decimals,
            total_supply,
            creator,
            description: description_str,
            icon_url: icon_url_option,
            treasury_cap_id: option::some(treasury_cap_id),
            metadata_id: option::some(metadata_id),
            is_real_token: true,
        };

        let token_info_address = object::id_address(&token_info);

        // Add to registry
        table::add(&mut registry.tokens, symbol_str, token_info_address);

        // Transfer payment to admin
        transfer::public_transfer(payment, registry.platform_admin);

        // Share token info
        transfer::share_object(token_info);

        // Update counters
        registry.next_token_id = registry.next_token_id + 1;
        registry.next_real_token_type = registry.next_real_token_type + 1;

        // Emit event
        event::emit(TokenCreated {
            token_id: registry.next_token_id - 1,
            name: name_str,
            symbol: symbol_str,
            creator,
            total_supply,
            is_real_token: true,
            token_info_address,
        });
    }

    /// Create virtual token (unlimited, but not wallet-native)
    public entry fun launch_virtual_token(
        registry: &mut TokenRegistry,
        name: vector<u8>,
        symbol: vector<u8>,
        description: vector<u8>,
        icon_url: vector<u8>,
        decimals: u8,
        total_supply: u64,
        ctx: &mut TxContext
    ) {
        let creator = tx_context::sender(ctx);
        let name_str = string::utf8(name);
        let symbol_str = string::utf8(symbol);
        let description_str = string::utf8(description);

        // Check if symbol already exists
        assert!(!table::contains(&registry.tokens, symbol_str), ETokenAlreadyExists);

        // Create icon URL if provided
        let icon_url_option = if (vector::length(&icon_url) > 0) {
            option::some(url::new_unsafe_from_bytes(icon_url))
        } else {
            option::none()
        };

        // Create token info (virtual token)
        let token_info = TokenInfo {
            id: object::new(ctx),
            token_id: registry.next_token_id,
            name: name_str,
            symbol: symbol_str,
            decimals,
            total_supply,
            creator,
            description: description_str,
            icon_url: icon_url_option,
            treasury_cap_id: option::none(),
            metadata_id: option::none(),
            is_real_token: false,
        };

        let token_info_address = object::id_address(&token_info);

        // Add to registry
        table::add(&mut registry.tokens, symbol_str, token_info_address);

        // Share token info
        transfer::share_object(token_info);

        // Update counter
        registry.next_token_id = registry.next_token_id + 1;

        // Emit event
        event::emit(TokenCreated {
            token_id: registry.next_token_id - 1,
            name: name_str,
            symbol: symbol_str,
            creator,
            total_supply,
            is_real_token: false,
            token_info_address,
        });
    }

    /// Helper functions for creating real tokens
    fun create_real_token_type_1(
        name: vector<u8>,
        symbol: vector<u8>,
        description: vector<u8>,
        icon_url: Option<Url>,
        decimals: u8,
        ctx: &mut TxContext
    ): (ID, ID) {
        let (treasury_cap, metadata) = coin::create_currency(
            PLATFORM_TOKEN_001 {},
            decimals,
            symbol,
            name,
            description,
            icon_url,
            ctx
        );

        let treasury_id = object::id(&treasury_cap);
        let metadata_id = object::id(&metadata);

        // Transfer treasury to creator
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));
        // Share metadata for wallet recognition
        transfer::public_share_object(metadata);

        (treasury_id, metadata_id)
    }

    fun create_real_token_type_2(
        name: vector<u8>,
        symbol: vector<u8>,
        description: vector<u8>,
        icon_url: Option<Url>,
        decimals: u8,
        ctx: &mut TxContext
    ): (ID, ID) {
        let (treasury_cap, metadata) = coin::create_currency(
            PLATFORM_TOKEN_002 {},
            decimals,
            symbol,
            name,
            description,
            icon_url,
            ctx
        );

        let treasury_id = object::id(&treasury_cap);
        let metadata_id = object::id(&metadata);

        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));
        transfer::public_share_object(metadata);

        (treasury_id, metadata_id)
    }

    fun create_real_token_type_3(
        name: vector<u8>,
        symbol: vector<u8>,
        description: vector<u8>,
        icon_url: Option<Url>,
        decimals: u8,
        ctx: &mut TxContext
    ): (ID, ID) {
        let (treasury_cap, metadata) = coin::create_currency(
            PLATFORM_TOKEN_003 {},
            decimals,
            symbol,
            name,
            description,
            icon_url,
            ctx
        );

        let treasury_id = object::id(&treasury_cap);
        let metadata_id = object::id(&metadata);

        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));
        transfer::public_share_object(metadata);

        (treasury_id, metadata_id)
    }

    /// View functions
    public fun get_token_info_by_symbol(registry: &TokenRegistry, symbol: String): address {
        *table::borrow(&registry.tokens, symbol)
    }

    public fun get_registry_info(registry: &TokenRegistry): (u64, u64, address) {
        (registry.next_token_id, registry.next_real_token_type, registry.platform_admin)
    }
}
