module {{token_module_address}}::{{token_module_name}} {

    use sui::coin::{Self, TreasuryCap, Coin};
    use sui::object::{Self, UID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use std::option;
    
    /// Unique token type
    struct {{token_struct_name}} has store, drop {}

    /// Initializes this token: creates metadata and treasury cap
    fun init(ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<{{token_struct_name}}>(
            {{token_struct_name}} {},
            9, // decimals
            b"{{token_symbol}}", // symbol
            b"{{token_name}}", // name
            b"{{token_description}}", // description
            option::none(), // icon_url (will be set later)
            ctx
        );

        // Transfer the treasury cap to the deployer
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));

        // Share the metadata object
        transfer::public_share_object(metadata);
    }

    /// Mint tokens to a recipient
    public entry fun mint(
        treasury_cap: &mut TreasuryCap<{{token_struct_name}}>,
        amount: u64,
        recipient: address,
        ctx: &mut TxContext
    ) {
        let coin = coin::mint(treasury_cap, amount, ctx);
        transfer::public_transfer(coin, recipient);
    }

    /// Get total supply
    public fun total_supply(treasury_cap: &TreasuryCap<{{token_struct_name}}>): u64 {
        coin::total_supply(treasury_cap)
    }
}
