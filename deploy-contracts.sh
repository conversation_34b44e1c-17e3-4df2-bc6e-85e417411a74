#!/bin/bash

# Dexsta Platform - Sui Contract Deployment Script
# This script deploys all Move contracts to Sui network

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NETWORK=${1:-testnet}  # Default to testnet, can override with argument
CONTRACTS_DIR="contracts_sui"
DEPLOYMENT_LOG="deployment_${NETWORK}_$(date +%Y%m%d_%H%M%S).log"

echo -e "${BLUE}🚀 Dexsta Platform - Sui Contract Deployment${NC}"
echo -e "${BLUE}================================================${NC}"
echo -e "Network: ${YELLOW}$NETWORK${NC}"
echo -e "Contracts Directory: ${YELLOW}$CONTRACTS_DIR${NC}"
echo -e "Deployment Log: ${YELLOW}$DEPLOYMENT_LOG${NC}"
echo ""

# Check prerequisites
echo -e "${BLUE}📋 Checking Prerequisites...${NC}"

# Check if Sui CLI is installed
if ! command -v sui &> /dev/null; then
    echo -e "${RED}❌ Sui CLI not found. Please install Sui CLI first.${NC}"
    echo -e "Visit: https://docs.sui.io/guides/developer/getting-started/sui-install"
    exit 1
fi

# Check if contracts directory exists
if [ ! -d "$CONTRACTS_DIR" ]; then
    echo -e "${RED}❌ Contracts directory '$CONTRACTS_DIR' not found.${NC}"
    exit 1
fi

# Check if wallet is configured
if ! sui client active-address &> /dev/null; then
    echo -e "${RED}❌ No active Sui wallet found. Please configure your wallet first.${NC}"
    echo -e "Run: ${YELLOW}sui client new-address ed25519${NC}"
    exit 1
fi

# Get active address and network
ACTIVE_ADDRESS=$(sui client active-address)
ACTIVE_ENV=$(sui client active-env)

echo -e "${GREEN}✅ Sui CLI found${NC}"
echo -e "${GREEN}✅ Contracts directory found${NC}"
echo -e "${GREEN}✅ Active wallet: $ACTIVE_ADDRESS${NC}"
echo -e "${GREEN}✅ Active environment: $ACTIVE_ENV${NC}"

# Check if we're on the right network
if [[ "$ACTIVE_ENV" != *"$NETWORK"* ]]; then
    echo -e "${YELLOW}⚠️  Warning: Active environment ($ACTIVE_ENV) doesn't match target network ($NETWORK)${NC}"
    echo -e "Do you want to continue? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo -e "${RED}❌ Deployment cancelled${NC}"
        exit 1
    fi
fi

# Check wallet balance
echo -e "\n${BLUE}💰 Checking Wallet Balance...${NC}"
BALANCE=$(sui client balance --json | jq -r '.totalBalance // 0')
echo -e "SUI Balance: ${YELLOW}$BALANCE MIST${NC}"

if [ "$BALANCE" -lt 1000000000 ]; then  # Less than 1 SUI
    echo -e "${YELLOW}⚠️  Low balance detected. You may need more SUI for deployment.${NC}"
    if [ "$NETWORK" = "testnet" ]; then
        echo -e "Get testnet SUI from: ${BLUE}https://discord.gg/sui${NC}"
    fi
fi

# Create Move.toml if it doesn't exist
echo -e "\n${BLUE}📝 Setting up Move.toml...${NC}"
if [ ! -f "$CONTRACTS_DIR/Move.toml" ]; then
    cat > "$CONTRACTS_DIR/Move.toml" << EOF
[package]
name = "dexsta"
version = "1.0.0"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/testnet" }

[addresses]
maji = "0x0"
EOF
    echo -e "${GREEN}✅ Created Move.toml${NC}"
else
    echo -e "${GREEN}✅ Move.toml already exists${NC}"
fi

# Start deployment
echo -e "\n${BLUE}🚀 Starting Contract Deployment...${NC}"
echo "Deployment started at $(date)" > "$DEPLOYMENT_LOG"

# Function to deploy a single contract
deploy_contract() {
    local contract_name=$1
    local contract_file="$CONTRACTS_DIR/$contract_name.move"
    
    echo -e "\n${BLUE}📦 Deploying $contract_name...${NC}"
    
    if [ ! -f "$contract_file" ]; then
        echo -e "${RED}❌ Contract file not found: $contract_file${NC}"
        return 1
    fi
    
    echo "Deploying $contract_name at $(date)" >> "$DEPLOYMENT_LOG"
    
    # Deploy the contract
    local output
    if output=$(sui client publish "$CONTRACTS_DIR" --gas-budget 100000000 --json 2>&1); then
        echo -e "${GREEN}✅ $contract_name deployed successfully${NC}"
        
        # Parse and save deployment info
        local package_id=$(echo "$output" | jq -r '.objectChanges[] | select(.type == "published") | .packageId')
        
        if [ "$package_id" != "null" ] && [ -n "$package_id" ]; then
            echo "Package ID: $package_id" >> "$DEPLOYMENT_LOG"
            echo -e "Package ID: ${YELLOW}$package_id${NC}"
            
            # Save to environment file
            echo "NEXT_PUBLIC_${contract_name^^}_PACKAGE_ID=$package_id" >> ".env.deployment"
        else
            echo -e "${YELLOW}⚠️  Could not extract package ID from deployment output${NC}"
        fi
        
        echo "$output" >> "$DEPLOYMENT_LOG"
        echo "---" >> "$DEPLOYMENT_LOG"
        
        return 0
    else
        echo -e "${RED}❌ Failed to deploy $contract_name${NC}"
        echo "Error: $output" >> "$DEPLOYMENT_LOG"
        echo -e "${RED}$output${NC}"
        return 1
    fi
}

# Initialize deployment environment file
echo "# Dexsta Contract Deployment - $NETWORK" > ".env.deployment"
echo "# Generated on $(date)" >> ".env.deployment"
echo "NEXT_PUBLIC_SUI_NETWORK=$NETWORK" >> ".env.deployment"
echo "" >> ".env.deployment"

# Deploy contracts in order (dependencies first)
CONTRACTS=("token_admin" "pool_admin" "token" "pool")
FAILED_CONTRACTS=()

for contract in "${CONTRACTS[@]}"; do
    if ! deploy_contract "$contract"; then
        FAILED_CONTRACTS+=("$contract")
    fi
    
    # Wait a bit between deployments
    sleep 2
done

# Summary
echo -e "\n${BLUE}📊 Deployment Summary${NC}"
echo -e "${BLUE}=====================${NC}"

if [ ${#FAILED_CONTRACTS[@]} -eq 0 ]; then
    echo -e "${GREEN}🎉 All contracts deployed successfully!${NC}"
    
    echo -e "\n${BLUE}📋 Next Steps:${NC}"
    echo -e "1. Copy contract addresses from ${YELLOW}.env.deployment${NC} to your ${YELLOW}.env.local${NC}"
    echo -e "2. Update ${YELLOW}src/constants/contracts.ts${NC} with the new addresses"
    echo -e "3. Initialize contracts through the admin interface"
    echo -e "4. Test the deployment with the provided test script"
    
    echo -e "\n${BLUE}🔧 Contract Addresses:${NC}"
    cat ".env.deployment"
    
else
    echo -e "${RED}❌ Some contracts failed to deploy:${NC}"
    for contract in "${FAILED_CONTRACTS[@]}"; do
        echo -e "${RED}  - $contract${NC}"
    done
    echo -e "\nCheck ${YELLOW}$DEPLOYMENT_LOG${NC} for details"
fi

echo -e "\n${BLUE}📄 Full deployment log saved to: ${YELLOW}$DEPLOYMENT_LOG${NC}"

# Create test script
echo -e "\n${BLUE}🧪 Creating test script...${NC}"
cat > "test-deployment.sh" << 'EOF'
#!/bin/bash

# Test deployed contracts
echo "🧪 Testing Dexsta Contract Deployment"
echo "====================================="

# Source deployment environment
if [ -f ".env.deployment" ]; then
    source ".env.deployment"
    echo "✅ Loaded deployment environment"
else
    echo "❌ .env.deployment not found"
    exit 1
fi

# Test contract calls
echo ""
echo "📋 Testing contract accessibility..."

# Test token admin contract
if [ -n "$NEXT_PUBLIC_TOKEN_ADMIN_PACKAGE_ID" ]; then
    echo "Testing Token Admin Contract: $NEXT_PUBLIC_TOKEN_ADMIN_PACKAGE_ID"
    # Add specific test calls here
else
    echo "❌ Token Admin Package ID not found"
fi

# Test pool admin contract  
if [ -n "$NEXT_PUBLIC_POOL_ADMIN_PACKAGE_ID" ]; then
    echo "Testing Pool Admin Contract: $NEXT_PUBLIC_POOL_ADMIN_PACKAGE_ID"
    # Add specific test calls here
else
    echo "❌ Pool Admin Package ID not found"
fi

# Test token contract
if [ -n "$NEXT_PUBLIC_TOKEN_PACKAGE_ID" ]; then
    echo "Testing Token Contract: $NEXT_PUBLIC_TOKEN_PACKAGE_ID"
    # Add specific test calls here
else
    echo "❌ Token Package ID not found"
fi

# Test pool contract
if [ -n "$NEXT_PUBLIC_POOL_PACKAGE_ID" ]; then
    echo "Testing Pool Contract: $NEXT_PUBLIC_POOL_PACKAGE_ID"
    # Add specific test calls here
else
    echo "❌ Pool Package ID not found"
fi

echo ""
echo "🎉 Basic deployment test completed"
echo "Next: Initialize contracts through admin interface"
EOF

chmod +x "test-deployment.sh"
echo -e "${GREEN}✅ Test script created: test-deployment.sh${NC}"

echo -e "\n${GREEN}🎉 Deployment process completed!${NC}"
