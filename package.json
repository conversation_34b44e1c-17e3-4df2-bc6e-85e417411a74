{"name": "dexsta", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@mysten/dapp-kit": "^0.16.12", "@mysten/sui": "^1.33.0", "@mysten/sui.js": "^0.54.1", "@suiet/wallet-kit": "^0.4.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.80.10", "framer-motion": "^12.18.1", "lucide-react": "^0.516.0", "next": "15.3.3", "pinata": "^2.4.8", "pinata-web3": "^0.5.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}