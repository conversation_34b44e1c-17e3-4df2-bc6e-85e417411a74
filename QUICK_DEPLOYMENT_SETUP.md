# 🚀 Quick XFT Contracts Deployment Setup

## Overview
This guide will help you deploy the complete Dexsta XFT contract system to Sui devnet.

## 📋 Prerequisites Checklist

### 1. Install Sui CLI
```bash
# For macOS
curl -fLJO https://github.com/MystenLabs/sui/releases/download/devnet-v1.36.2/sui-devnet-v1.36.2-macos-x86_64.tgz
tar -xzf sui-devnet-v1.36.2-macos-x86_64.tgz
sudo mv sui /usr/local/bin/
sudo chmod +x /usr/local/bin/sui

# Verify installation
sui --version
```

### 2. Setup Wallet
```bash
# Create new wallet
sui client new-address ed25519

# Switch to devnet
sui client switch --env devnet

# Get devnet SUI
sui client faucet

# Check balance (should have at least 2 SUI)
sui client balance
```

### 3. Import Testing Account (Optional)
```bash
# Import the testing account
sui keytool import suiprivkey1qq96u4z3h5ze7ptnm69d45fugmutr499mwddfqt6009jzfgqypge6fmrzws ed25519

# Switch to testing account
sui client switch --address 0x107850bf3302019d71a699210d8fcf1789065700d3a53f2d6bd7322733c4d7fe
```

## 🚀 Deployment Steps

### Step 1: Make Scripts Executable
```bash
chmod +x deploy-xft-contracts.sh
chmod +x test-xft-deployment.sh
```

### Step 2: Deploy Contracts
```bash
./deploy-xft-contracts.sh
```

This script will:
- ✅ Check prerequisites
- ✅ Build XFT contracts
- ✅ Deploy to devnet
- ✅ Initialize Fire Registry
- ✅ Create environment file
- ✅ Test basic functionality

### Step 3: Test Deployment
```bash
./test-xft-deployment.sh
```

This script will:
- ✅ Test label creation
- ✅ Test XFT minting
- ✅ Test operator licenses
- ✅ Test marketplace listings
- ✅ Verify contract queries

## 📝 Expected Output

### Successful Deployment
```
🚀 Dexsta XFT Contracts Deployment
===================================

📋 Checking Prerequisites...
✅ Sui CLI found
✅ XFT contracts directory found
✅ Active wallet: 0x107850bf...
✅ Active environment: devnet

💰 Checking Wallet Balance...
SUI Balance: ********** MIST

🔨 Building XFT Contracts...
✅ Contracts built successfully

🚀 Deploying XFT Contracts...
✅ Contracts deployed successfully
📦 Package ID: 0x1234567890abcdef...

📋 Extracting Contract Object IDs...
🔥 Fire Registry: 0xabcdef1234567890...
🌐 Global Registry: 0x1111222233334444...
🏷️  Label Registry: 0x5555666677778888...
🎨 XFT Registry: 0x9999aaaabbbbcccc...
👥 Operator Registry: 0xddddeeeeffffgggg...
🛒 Marketplace: 0xhhhhiiiijjjjkkkk...

🔧 Initializing Fire Registry...
✅ Fire Registry initialized

📝 Creating Environment File...
✅ Environment file created: .env.deployment

🧪 Testing Basic Functionality...
📝 Creating test label...
✅ Test label created: 0xllllmmmmnnnnooo...

🎉 XFT Contracts Deployment Complete!
```

### Generated Files
- `.env.deployment` - Contract addresses for frontend
- `deployment_output.json` - Full deployment details

## 🔧 Contract Architecture

### Core Contracts
1. **Fire Registry** - Central contract registry
2. **Global Registry** - Global ID management
3. **Label Registry** - Label/brand management
4. **XFT Registry** - NFT/XFT management
5. **Operator Registry** - Permission management
6. **Marketplace** - Trading platform

### Key Features
- ✅ **Label System** - Brand management with fees
- ✅ **XFT System** - Advanced NFTs with asset storage
- ✅ **Operator Licenses** - Delegated permissions
- ✅ **Marketplace** - Complete trading platform
- ✅ **Purchase Tracking** - Customer relationship mapping
- ✅ **Fee Distribution** - Automated revenue sharing

## 📊 Contract Addresses

After deployment, you'll have these contract addresses in `.env.deployment`:

```bash
NEXT_PUBLIC_SUI_NETWORK=devnet
NEXT_PUBLIC_PACKAGE_ID=0x...
NEXT_PUBLIC_FIRE_REGISTRY_ID=0x...
NEXT_PUBLIC_GLOBAL_REGISTRY_ID=0x...
NEXT_PUBLIC_LABEL_REGISTRY_ID=0x...
NEXT_PUBLIC_XFT_REGISTRY_ID=0x...
NEXT_PUBLIC_OPERATOR_REGISTRY_ID=0x...
NEXT_PUBLIC_MARKETPLACE_ID=0x...
```

## 🧪 Testing Commands

### Manual Testing
```bash
# Test label creation
sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function create_label \
    --args $LABEL_REGISTRY $GLOBAL_REGISTRY $FIRE_REGISTRY \
        '"my-label"' \
        '[1, **********, 1, 1, 0, 0, 1, 0, 0, 1, 0, 250, 0, 0]' \
        'true' \
        '0x6' \
    --gas-budget 100000000

# Test XFT minting
sui client call \
    --package $PACKAGE_ID \
    --module xft \
    --function mint_xft \
    --args $XFT_REGISTRY $GLOBAL_REGISTRY $FIRE_REGISTRY \
        '"My XFT"' \
        '"Description"' \
        '"https://example.com/image.png"' \
        '[1, **********, 1, 1, 0, 0, 1, 0, 0, 1, 0, 250, 0, 0]' \
        'true' \
        '0x6' \
    --gas-budget 100000000
```

## 🔍 Troubleshooting

### Common Issues

1. **"Sui CLI not found"**
   - Install Sui CLI using the commands above
   - Verify with `sui --version`

2. **"Insufficient balance"**
   - Get more SUI: `sui client faucet`
   - Wait 60 seconds between faucet requests

3. **"Build failed"**
   - Check Move.toml in contracts/xft/
   - Ensure all dependencies are correct

4. **"Deployment failed"**
   - Increase gas budget to 300000000
   - Check network connection
   - Verify you're on devnet: `sui client active-env`

### Useful Commands
```bash
# Check active environment
sui client active-env

# Check active address
sui client active-address

# Check balance
sui client balance

# Get gas coins
sui client gas

# Switch to devnet
sui client switch --env devnet

# Get more SUI
sui client faucet
```

## 🎯 Next Steps

1. **Copy contract addresses** from `.env.deployment` to `.env.local`
2. **Update frontend** with new contract addresses
3. **Test frontend integration** with deployed contracts
4. **Initialize platform settings** through admin interface
5. **Run comprehensive tests** to verify all functionality

## 🚀 Ready to Launch!

Once deployment is complete, your XFT contract system will be fully functional on Sui devnet with:
- Complete label and XFT management
- Operator permission system
- Marketplace with fee distribution
- Purchase tracking and customer relationships
- Asset storage and lending capabilities

The contracts are now ready for frontend integration and production use!
