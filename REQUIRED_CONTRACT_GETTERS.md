# Required Contract Getter Functions for Token Creation Validation

## Label Contract Getters (Already Exist)
```move
// In simple_label.move
public fun owner(label: &Label): address
public fun expiration_time(label: &Label): u64  
public fun is_expired(label: &Label, clock: &Clock): bool
public fun label_exists(registry: &LabelRegistry, name: String): bool
```

## XFT Contract Getters (Need to Add)
```move
// In simple_nft.move - ADD THESE FUNCTIONS

/// Get the label global ID that an NFT is linked to
public fun label_global_id(nft: &NFTData): Option<u64> {
    nft.label_global_id
}

/// Check if an NFT exists in the registry by global ID
public fun nft_exists(registry: &NFTRegistry, global_id: u64): bool {
    table::contains(&registry.nfts_by_global_id, global_id)
}

/// Get NFT by global ID from registry
public fun get_nft_by_global_id(registry: &NFTRegistry, global_id: u64): Option<&NFTData> {
    if (table::contains(&registry.nfts_by_global_id, global_id)) {
        option::some(table::borrow(&registry.nfts_by_global_id, global_id))
    } else {
        option::none()
    }
}
```

## Label Registry Getters (Need to Add)
```move
// In simple_label.move - ADD THESE FUNCTIONS

/// Get label by global ID from registry
public fun get_label_by_global_id(registry: &LabelRegistry, global_id: u64): Option<&Label> {
    if (table::contains(&registry.labels_by_global_id, global_id)) {
        option::some(table::borrow(&registry.labels_by_global_id, global_id))
    } else {
        option::none()
    }
}

/// Check if a label exists by global ID
public fun label_exists_by_id(registry: &LabelRegistry, global_id: u64): bool {
    table::contains(&registry.labels_by_global_id, global_id)
}
```

## Validation Service Functions (Frontend)
```typescript
// In useContracts.ts - ADD THESE FUNCTIONS

const validateLabelOwnership = async (labelId: string): Promise<{
  exists: boolean;
  owned: boolean;
  expired: boolean;
}> => {
  // Call XFT_FUNCTIONS.LABEL.LABEL_EXISTS
  // Call XFT_FUNCTIONS.LABEL.GET_LABEL_OWNER  
  // Call XFT_FUNCTIONS.LABEL.IS_EXPIRED
}

const validateNFTLinking = async (nftId: string, labelId: string): Promise<{
  exists: boolean;
  linkedToLabel: boolean;
}> => {
  // Call XFT_FUNCTIONS.XFT.NFT_EXISTS
  // Call XFT_FUNCTIONS.XFT.GET_NFT_LABEL_ID
  // Compare returned label ID with expected label ID
}
```

## Usage in Token Creation
1. **User enters Label ID** → Validate label exists and is owned by user
2. **User toggles Private Pool** → Only enabled if label is valid
3. **User enters NFT ID** → Validate NFT exists and is linked to the label
4. **Form submission** → All validations must pass before token creation

## Benefits
- **🔒 Security** - Prevents invalid label/NFT linking
- **✨ UX** - Real-time feedback prevents submission errors  
- **🎯 Accuracy** - Ensures private pools work correctly
- **⚡ Performance** - Validates before expensive token creation
