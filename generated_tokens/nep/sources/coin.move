module 0x0::nep {
    use sui::coin::{Self, TreasuryCap, Coin};
    use sui::object::{Self, UID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use std::option;

    struct NEPCoin has store, drop {}

    fun init(ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<NEPCoin>(
            NEPCoin {},
            9, // decimals
            b"NEP", // symbol
            b"Nep", // name
            b"this will be dope when it's finally working", // description
            option::none(), // icon_url (will be set later)
            ctx
        );

        // Transfer the treasury cap to the deployer
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));

        // Share the metadata object
        transfer::public_share_object(metadata);
    }

    /// Mint tokens to a recipient
    public entry fun mint(
        treasury_cap: &mut TreasuryCap<NEPCoin>,
        amount: u64,
        recipient: address,
        ctx: &mut TxContext
    ) {
        let coin = coin::mint(treasury_cap, amount, ctx);
        transfer::public_transfer(coin, recipient);
    }

    /// Get total supply
    public fun total_supply(treasury_cap: &TreasuryCap<NEPCoin>): u64 {
        coin::total_supply(treasury_cap)
    }
}