{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/url.move", "definition_location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 137, "end": 140}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "url"], "struct_map": {"0": {"definition_location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 234, "end": 237}, "type_parameters": [], "fields": [{"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 299, "end": 302}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 354, "end": 413}, "definition_location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 365, "end": 375}, "type_parameters": [], "parameters": [["url#0#0", {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 376, "end": 379}]], "returns": [{"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 390, "end": 393}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 406, "end": 409}, "1": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 400, "end": 411}}, "is_native": false}, "1": {"location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 520, "end": 635}, "definition_location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 531, "end": 552}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 553, "end": 558}]], "returns": [{"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 573, "end": 576}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 593, "end": 598}, "1": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 593, "end": 616}, "2": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 622, "end": 633}}, "is_native": false}, "2": {"location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 655, "end": 712}, "definition_location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 666, "end": 675}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 676, "end": 680}]], "returns": [{"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 689, "end": 695}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 702, "end": 706}, "1": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 702, "end": 710}}, "is_native": false}, "3": {"location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 739, "end": 809}, "definition_location": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 750, "end": 756}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 757, "end": 761}], ["url#0#0", {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 773, "end": 776}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 803, "end": 806}, "1": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 792, "end": 796}, "2": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 792, "end": 800}, "3": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 792, "end": 806}, "4": {"file_hash": [113, 5, 65, 81, 183, 80, 213, 209, 104, 13, 112, 230, 155, 40, 131, 121, 153, 15, 139, 116, 173, 14, 59, 21, 182, 73, 24, 65, 52, 241, 168, 227], "start": 806, "end": 807}}, "is_native": false}}, "constant_map": {}}