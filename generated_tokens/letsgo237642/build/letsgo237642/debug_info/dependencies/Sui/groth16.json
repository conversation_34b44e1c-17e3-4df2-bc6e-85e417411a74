{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/crypto/groth16.move", "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 87, "end": 94}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "groth16"], "struct_map": {"0": {"definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 954, "end": 959}, "type_parameters": [], "fields": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 988, "end": 990}]}, "1": {"definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1401, "end": 1421}, "type_parameters": [], "fields": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1450, "end": 1471}, {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1489, "end": 1511}, {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1529, "end": 1550}, {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1568, "end": 1589}]}, "2": {"definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2410, "end": 2427}, "type_parameters": [], "fields": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2456, "end": 2461}]}, "3": {"definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3035, "end": 3046}, "type_parameters": [], "fields": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3075, "end": 3080}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1107, "end": 1155}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1118, "end": 1126}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1130, "end": 1135}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1150, "end": 1151}, "1": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1138, "end": 1153}}, "is_native": false}, "1": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1261, "end": 1306}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1272, "end": 1277}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1281, "end": 1286}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1301, "end": 1302}, "1": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1289, "end": 1304}}, "is_native": false}, "2": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1655, "end": 2024}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1666, "end": 1680}, "type_parameters": [], "parameters": [["vk_gamma_abc_g1_bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1686, "end": 1707}], ["alpha_g1_beta_g2_bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1725, "end": 1747}], ["gamma_g2_neg_pc_bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1765, "end": 1786}], ["delta_g2_neg_pc_bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1804, "end": 1825}]], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1842, "end": 1862}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1900, "end": 1921}, "1": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1931, "end": 1953}, "2": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1963, "end": 1984}, "3": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1994, "end": 2015}, "4": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 1869, "end": 2022}}, "is_native": false}, "3": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2098, "end": 2331}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2109, "end": 2121}, "type_parameters": [], "parameters": [["pvk#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2122, "end": 2125}]], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2150, "end": 2168}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2191, "end": 2216}, "3": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2226, "end": 2252}, "6": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2262, "end": 2287}, "9": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2297, "end": 2322}, "12": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2175, "end": 2329}}, "is_native": false}, "4": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2696, "end": 2941}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2707, "end": 2737}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2738, "end": 2743}]], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2758, "end": 2775}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2790, "end": 2795}, "1": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2790, "end": 2804}, "2": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2807, "end": 2809}, "3": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2805, "end": 2806}, "4": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2813, "end": 2814}, "5": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2810, "end": 2812}, "6": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2782, "end": 2831}, "8": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2816, "end": 2830}, "9": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2782, "end": 2831}, "10": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2845, "end": 2850}, "11": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2845, "end": 2859}, "12": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2862, "end": 2864}, "13": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2860, "end": 2861}, "14": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2868, "end": 2883}, "15": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2865, "end": 2867}, "16": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2837, "end": 2906}, "18": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2885, "end": 2905}, "19": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2837, "end": 2906}, "20": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2932, "end": 2937}, "21": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 2912, "end": 2939}}, "is_native": false}, "5": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3145, "end": 3241}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3156, "end": 3179}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3180, "end": 3185}]], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3200, "end": 3211}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3232, "end": 3237}, "1": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3218, "end": 3239}}, "is_native": false}, "6": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3701, "end": 3862}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3712, "end": 3733}, "type_parameters": [], "parameters": [["curve#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3734, "end": 3739}], ["verifying_key#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3749, "end": 3762}]], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3778, "end": 3798}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3836, "end": 3841}, "1": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3836, "end": 3844}, "3": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3846, "end": 3859}, "4": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 3805, "end": 3860}}, "is_native": false}, "7": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4022, "end": 4136}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4033, "end": 4063}, "type_parameters": [], "parameters": [["curve#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4069, "end": 4074}], ["verifying_key#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4084, "end": 4097}]], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4115, "end": 4135}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "8": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4556, "end": 5092}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4567, "end": 4587}, "type_parameters": [], "parameters": [["curve#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4593, "end": 4598}], ["prepared_verifying_key#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4612, "end": 4634}], ["public_proof_inputs#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4663, "end": 4682}], ["proof_points#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4708, "end": 4720}]], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4739, "end": 4743}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4789, "end": 4794}, "1": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4789, "end": 4797}, "3": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4808, "end": 4830}, "4": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4807, "end": 4852}, "5": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4863, "end": 4885}, "6": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4862, "end": 4908}, "7": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4919, "end": 4941}, "8": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4918, "end": 4963}, "9": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4974, "end": 4996}, "10": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4973, "end": 5018}, "11": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5029, "end": 5048}, "12": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5028, "end": 5054}, "13": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5065, "end": 5077}, "14": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5064, "end": 5083}, "15": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 4750, "end": 5090}}, "is_native": false}, "9": {"location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5261, "end": 5556}, "definition_location": {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5272, "end": 5301}, "type_parameters": [], "parameters": [["curve#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5307, "end": 5312}], ["vk_gamma_abc_g1_bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5322, "end": 5343}], ["alpha_g1_beta_g2_bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5362, "end": 5384}], ["gamma_g2_neg_pc_bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5403, "end": 5424}], ["delta_g2_neg_pc_bytes#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5443, "end": 5464}], ["public_proof_inputs#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5483, "end": 5502}], ["proof_points#0#0", {"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5521, "end": 5533}]], "returns": [{"file_hash": [113, 181, 19, 86, 142, 174, 206, 165, 152, 46, 140, 126, 124, 52, 100, 100, 179, 160, 135, 198, 48, 17, 162, 236, 19, 103, 53, 141, 0, 192, 255, 195], "start": 5551, "end": 5555}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidCurve": 1, "EInvalidScalar": 3, "EInvalidVerifyingKey": 0, "ETooManyPublicInputs": 2, "MaxPublicInputs": 4}}