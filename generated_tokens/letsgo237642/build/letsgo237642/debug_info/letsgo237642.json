{"version": 2, "from_file_path": "/Users/<USER>/Documents/NIT/dexsta/generated_tokens/letsgo237642/sources/coin.move", "definition_location": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 12, "end": 24}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000000", "letsgo237642"], "struct_map": {"0": {"definition_location": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 207, "end": 223}, "type_parameters": [], "fields": [{"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 207, "end": 223}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 248, "end": 845}, "definition_location": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 252, "end": 256}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 257, "end": 260}]], "returns": [], "locals": [["metadata#1#0", {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 307, "end": 315}]], "nops": {}, "code_map": {"0": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 372, "end": 391}, "2": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 405, "end": 406}, "3": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 432, "end": 447}, "4": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 471, "end": 483}, "5": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 505, "end": 541}, "6": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 570, "end": 584}, "7": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 610, "end": 613}, "8": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 319, "end": 623}, "9": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 307, "end": 315}, "10": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 746, "end": 749}, "12": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 727, "end": 750}, "13": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 687, "end": 751}, "14": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 829, "end": 837}, "15": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 799, "end": 838}, "16": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 838, "end": 839}}, "is_native": false}, "1": {"location": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 886, "end": 1167}, "definition_location": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 903, "end": 907}, "type_parameters": [], "parameters": [["treasury_cap#0#0", {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 917, "end": 929}], ["amount#0#0", {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 975, "end": 981}], ["recipient#0#0", {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 996, "end": 1005}], ["ctx#0#0", {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1024, "end": 1027}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1082, "end": 1094}, "1": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1096, "end": 1102}, "2": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1104, "end": 1107}, "3": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1071, "end": 1108}, "4": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1150, "end": 1159}, "5": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1118, "end": 1160}, "6": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1160, "end": 1161}}, "is_native": false}, "2": {"location": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1198, "end": 1321}, "definition_location": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1209, "end": 1221}, "type_parameters": [], "parameters": [["treasury_cap#0#0", {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1222, "end": 1234}]], "returns": [{"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1269, "end": 1272}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1302, "end": 1314}, "1": {"file_hash": [20, 220, 152, 7, 40, 234, 146, 168, 224, 224, 162, 144, 41, 159, 165, 228, 122, 7, 22, 113, 241, 14, 140, 73, 12, 207, 209, 153, 177, 201, 67, 67], "start": 1283, "end": 1315}}, "is_native": false}}, "constant_map": {}}