module 0x0::letsgo237642 {
    use sui::coin::{Self, TreasuryCap, Coin};
    use sui::object::{Self, UID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use std::option;

    struct LETSGO237642<PERSON>oi<PERSON> has store, drop {}

    fun init(ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<LETSGO237642Coin>(
            LETSGO237642Coin {},
            9, // decimals
            b"LETSGO237642", // symbol
            b"Lets Go!!", // name
            b"Token created via Dexsta platform", // description
            option::none(), // icon_url
            ctx
        );

        // Transfer the treasury cap to the deployer
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));

        // Share the metadata object
        transfer::public_share_object(metadata);
    }

    /// Mint tokens to a recipient
    public entry fun mint(
        treasury_cap: &mut TreasuryCap<LETSGO237642Coin>,
        amount: u64,
        recipient: address,
        ctx: &mut TxContext
    ) {
        let coin = coin::mint(treasury_cap, amount, ctx);
        transfer::public_transfer(coin, recipient);
    }

    /// Get total supply
    public fun total_supply(treasury_cap: &TreasuryCap<LETSGO237642Coin>): u64 {
        coin::total_supply(treasury_cap)
    }
}