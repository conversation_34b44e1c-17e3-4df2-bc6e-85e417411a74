---
compiled_package_info:
  package_name: letsgo237642
  address_alias_instantiation:
    letsgo237642: "0000000000000000000000000000000000000000000000000000000000000000"
    std: "0000000000000000000000000000000000000000000000000000000000000001"
    sui: "0000000000000000000000000000000000000000000000000000000000000002"
  source_digest: A3B0D79B08907D98DFFB24F9CE12E2E6D6740EA3DEF89C7DB3A94F7215557E2A
  build_flags:
    dev_mode: false
    test_mode: false
    generate_docs: false
    save_disassembly: false
    install_dir: ~
    force_recompilation: false
    lock_file: "./Move.lock"
    fetch_deps_only: false
    skip_fetch_latest_git_deps: false
    default_flavor: sui
    default_edition: ~
    deps_as_root: false
    silence_warnings: false
    warnings_are_errors: false
    json_errors: false
    additional_named_addresses: {}
    lint_flag:
      no_lint: false
      lint: false
    implicit_dependencies:
      Bridge:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: af5297b292c3
              subdir: crates/sui-framework/packages/bridge
          subst: ~
          digest: ~
          dep_override: true
      MoveStdlib:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: af5297b292c3
              subdir: crates/sui-framework/packages/move-stdlib
          subst: ~
          digest: ~
          dep_override: true
      Sui:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: af5297b292c3
              subdir: crates/sui-framework/packages/sui-framework
          subst: ~
          digest: ~
          dep_override: true
      SuiSystem:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: af5297b292c3
              subdir: crates/sui-framework/packages/sui-system
          subst: ~
          digest: ~
          dep_override: true
    force_lock_file: false
dependencies:
  - MoveStdlib
  - Sui
