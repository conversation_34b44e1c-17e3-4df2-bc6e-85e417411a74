module 0x107850bf3302019d71a699210d8fcf1789065700d3a53f2d6bd7322733c4d7fe::dexsta540651_______540651 {
    use sui::coin::{Self, TreasuryCap, Coin};
    use sui::object::{Self, UID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use std::option;

    struct DEXSTA540651Coin has store, drop {}

    fun init(ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<DEXSTA540651Coin>(
            DEXSTA540651Coin {},
            9, // decimals
            b"DEXSTA540651", // symbol
            b"Dexsta", // name
            b"this is going to be dope!!!", // description
            option::none(), // icon_url (will be set later)
            ctx
        );

        // Transfer the treasury cap to the deployer
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));

        // Share the metadata object
        transfer::public_share_object(metadata);
    }

    /// Mint tokens to a recipient
    public entry fun mint(
        treasury_cap: &mut TreasuryCap<DEXSTA540651Coin>,
        amount: u64,
        recipient: address,
        ctx: &mut TxContext
    ) {
        let coin = coin::mint(treasury_cap, amount, ctx);
        transfer::public_transfer(coin, recipient);
    }

    /// Get total supply
    public fun total_supply(treasury_cap: &TreasuryCap<DEXSTA540651Coin>): u64 {
        coin::total_supply(treasury_cap)
    }
}