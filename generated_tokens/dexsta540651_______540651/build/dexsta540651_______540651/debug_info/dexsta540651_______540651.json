{"version": 2, "from_file_path": "/Users/<USER>/Documents/NIT/dexsta/generated_tokens/dexsta540651_______540651/sources/coin.move", "definition_location": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 75, "end": 100}, "module_name": ["107850bf3302019d71a699210d8fcf1789065700d3a53f2d6bd7322733c4d7fe", "dexsta540651_______540651"], "struct_map": {"0": {"definition_location": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 283, "end": 299}, "type_parameters": [], "fields": [{"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 283, "end": 299}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 324, "end": 932}, "definition_location": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 328, "end": 332}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 333, "end": 336}]], "returns": [], "locals": [["metadata#1#0", {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 383, "end": 391}]], "nops": {}, "code_map": {"0": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 448, "end": 467}, "2": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 481, "end": 482}, "3": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 508, "end": 523}, "4": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 547, "end": 556}, "5": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 578, "end": 608}, "6": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 637, "end": 651}, "7": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 697, "end": 700}, "8": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 395, "end": 710}, "9": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 383, "end": 391}, "10": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 833, "end": 836}, "12": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 814, "end": 837}, "13": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 774, "end": 838}, "14": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 916, "end": 924}, "15": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 886, "end": 925}, "16": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 925, "end": 926}}, "is_native": false}, "1": {"location": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 973, "end": 1254}, "definition_location": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 990, "end": 994}, "type_parameters": [], "parameters": [["treasury_cap#0#0", {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1004, "end": 1016}], ["amount#0#0", {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1062, "end": 1068}], ["recipient#0#0", {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1083, "end": 1092}], ["ctx#0#0", {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1111, "end": 1114}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1169, "end": 1181}, "1": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1183, "end": 1189}, "2": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1191, "end": 1194}, "3": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1158, "end": 1195}, "4": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1237, "end": 1246}, "5": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1205, "end": 1247}, "6": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1247, "end": 1248}}, "is_native": false}, "2": {"location": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1285, "end": 1408}, "definition_location": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1296, "end": 1308}, "type_parameters": [], "parameters": [["treasury_cap#0#0", {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1309, "end": 1321}]], "returns": [{"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1356, "end": 1359}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1389, "end": 1401}, "1": {"file_hash": [22, 89, 135, 58, 190, 37, 70, 189, 111, 185, 97, 156, 174, 20, 112, 121, 64, 59, 132, 231, 227, 71, 112, 40, 12, 208, 151, 198, 80, 253, 131, 198], "start": 1370, "end": 1402}}, "is_native": false}}, "constant_map": {}}