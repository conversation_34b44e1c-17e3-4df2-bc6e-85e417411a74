{"version": 2, "from_file_path": "/Users/<USER>/Documents/NIT/dexsta/generated_tokens/dexsta540651/sources/coin.move", "definition_location": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 12, "end": 24}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000000", "dexsta540651"], "struct_map": {"0": {"definition_location": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 207, "end": 223}, "type_parameters": [], "fields": [{"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 207, "end": 223}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 248, "end": 842}, "definition_location": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 252, "end": 256}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 257, "end": 260}]], "returns": [], "locals": [["metadata#1#0", {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 307, "end": 315}]], "nops": {}, "code_map": {"0": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 372, "end": 391}, "2": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 405, "end": 406}, "3": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 432, "end": 447}, "4": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 471, "end": 480}, "5": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 502, "end": 538}, "6": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 567, "end": 581}, "7": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 607, "end": 610}, "8": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 319, "end": 620}, "9": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 307, "end": 315}, "10": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 743, "end": 746}, "12": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 724, "end": 747}, "13": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 684, "end": 748}, "14": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 826, "end": 834}, "15": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 796, "end": 835}, "16": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 835, "end": 836}}, "is_native": false}, "1": {"location": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 883, "end": 1164}, "definition_location": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 900, "end": 904}, "type_parameters": [], "parameters": [["treasury_cap#0#0", {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 914, "end": 926}], ["amount#0#0", {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 972, "end": 978}], ["recipient#0#0", {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 993, "end": 1002}], ["ctx#0#0", {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1021, "end": 1024}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1079, "end": 1091}, "1": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1093, "end": 1099}, "2": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1101, "end": 1104}, "3": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1068, "end": 1105}, "4": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1147, "end": 1156}, "5": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1115, "end": 1157}, "6": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1157, "end": 1158}}, "is_native": false}, "2": {"location": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1195, "end": 1318}, "definition_location": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1206, "end": 1218}, "type_parameters": [], "parameters": [["treasury_cap#0#0", {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1219, "end": 1231}]], "returns": [{"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1266, "end": 1269}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1299, "end": 1311}, "1": {"file_hash": [249, 62, 183, 17, 149, 106, 71, 17, 134, 245, 155, 78, 81, 250, 101, 58, 16, 132, 72, 7, 153, 165, 22, 138, 124, 173, 112, 147, 186, 177, 36, 29], "start": 1280, "end": 1312}}, "is_native": false}}, "constant_map": {}}