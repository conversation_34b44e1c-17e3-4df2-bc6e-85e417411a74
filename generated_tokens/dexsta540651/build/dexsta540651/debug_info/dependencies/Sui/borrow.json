{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/borrow.move", "definition_location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 383, "end": 389}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "borrow"], "struct_map": {"0": {"definition_location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 653, "end": 661}, "type_parameters": [["T", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 662, "end": 663}]], "fields": [{"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 694, "end": 696}, {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 711, "end": 716}]}, "1": {"definition_location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 813, "end": 819}, "type_parameters": [], "fields": [{"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 822, "end": 825}, {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 836, "end": 839}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 882, "end": 1068}, "definition_location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 893, "end": 896}, "type_parameters": [["T", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 897, "end": 898}]], "parameters": [["value#0#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 913, "end": 918}], ["ctx#0#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 923, "end": 926}]], "returns": [{"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 945, "end": 956}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1019, "end": 1022}, "1": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 986, "end": 1023}, "2": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1053, "end": 1058}, "3": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1040, "end": 1059}, "4": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 963, "end": 1066}}, "is_native": false}, "1": {"location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1158, "end": 1406}, "definition_location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1169, "end": 1175}, "type_parameters": [["T", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1176, "end": 1177}]], "parameters": [["self#0#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1192, "end": 1196}]], "returns": [{"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1218, "end": 1219}, {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1221, "end": 1227}], "locals": [["id#1#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1277, "end": 1279}], ["value#1#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1239, "end": 1244}]], "nops": {}, "code_map": {"0": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1247, "end": 1251}, "1": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1247, "end": 1257}, "2": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1247, "end": 1267}, "3": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1239, "end": 1244}, "4": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1293, "end": 1299}, "5": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1282, "end": 1300}, "6": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1277, "end": 1279}, "7": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1317, "end": 1322}, "8": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1358, "end": 1362}, "9": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1358, "end": 1365}, "11": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1384, "end": 1386}, "12": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1332, "end": 1397}, "13": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1307, "end": 1404}}, "is_native": false}, "2": {"location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1460, "end": 1712}, "definition_location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1471, "end": 1479}, "type_parameters": [["T", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1480, "end": 1481}]], "parameters": [["self#0#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1496, "end": 1500}], ["value#0#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1520, "end": 1525}], ["borrow#0#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1530, "end": 1536}]], "returns": [], "locals": [["obj#1#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1570, "end": 1573}], ["ref#1#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1565, "end": 1568}]], "nops": {}, "code_map": {"0": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1578, "end": 1584}, "1": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1556, "end": 1575}, "2": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1570, "end": 1573}, "3": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1565, "end": 1568}, "4": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1610, "end": 1616}, "5": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1599, "end": 1617}, "6": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1621, "end": 1624}, "7": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1618, "end": 1620}, "8": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1591, "end": 1638}, "12": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1626, "end": 1637}, "13": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1591, "end": 1638}, "14": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1652, "end": 1656}, "15": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1652, "end": 1659}, "17": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1663, "end": 1666}, "18": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1660, "end": 1662}, "19": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1644, "end": 1681}, "23": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1668, "end": 1680}, "24": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1644, "end": 1681}, "25": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1687, "end": 1691}, "26": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1687, "end": 1697}, "27": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1703, "end": 1708}, "28": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1687, "end": 1709}, "29": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1709, "end": 1710}}, "is_native": false}, "3": {"location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1769, "end": 1896}, "definition_location": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1780, "end": 1787}, "type_parameters": [["T", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1788, "end": 1789}]], "parameters": [["self#0#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1804, "end": 1808}]], "returns": [{"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1824, "end": 1825}], "locals": [["value#1#0", {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1854, "end": 1859}]], "nops": {}, "code_map": {"0": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1864, "end": 1868}, "1": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1836, "end": 1861}, "2": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1854, "end": 1859}, "3": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1851, "end": 1852}, "4": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1874, "end": 1879}, "5": {"file_hash": [90, 228, 132, 198, 111, 232, 224, 42, 174, 87, 68, 232, 161, 89, 76, 125, 24, 92, 24, 83, 35, 46, 254, 40, 157, 201, 43, 103, 127, 164, 23, 26], "start": 1874, "end": 1894}}, "is_native": false}}, "constant_map": {"EWrongBorrow": 0, "EWrongValue": 1}}