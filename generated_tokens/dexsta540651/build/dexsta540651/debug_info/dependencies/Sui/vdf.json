{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/crypto/vdf.move", "definition_location": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 87, "end": 90}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "vdf"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 308, "end": 406}, "definition_location": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 319, "end": 332}, "type_parameters": [], "parameters": [["message#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 333, "end": 340}]], "returns": [{"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 356, "end": 366}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 396, "end": 403}, "1": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 373, "end": 404}}, "is_native": false}, "1": {"location": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 456, "end": 524}, "definition_location": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 467, "end": 489}, "type_parameters": [], "parameters": [["message#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 490, "end": 497}]], "returns": [{"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 513, "end": 523}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "2": {"location": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1460, "end": 1646}, "definition_location": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1471, "end": 1481}, "type_parameters": [], "parameters": [["input#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1487, "end": 1492}], ["output#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1511, "end": 1517}], ["proof#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1536, "end": 1541}], ["iterations#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1560, "end": 1570}]], "returns": [{"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1580, "end": 1584}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1611, "end": 1616}, "1": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1618, "end": 1624}, "2": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1626, "end": 1631}, "3": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1633, "end": 1643}, "4": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1591, "end": 1644}}, "is_native": false}, "3": {"location": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1702, "end": 1836}, "definition_location": {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1713, "end": 1732}, "type_parameters": [], "parameters": [["input#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1738, "end": 1743}], ["output#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1762, "end": 1768}], ["proof#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1787, "end": 1792}], ["iterations#0#0", {"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1811, "end": 1821}]], "returns": [{"file_hash": [212, 20, 249, 29, 35, 30, 200, 87, 176, 238, 154, 153, 122, 119, 100, 123, 120, 176, 210, 142, 247, 36, 159, 67, 244, 211, 88, 6, 180, 122, 2, 157], "start": 1831, "end": 1835}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidInput": 0}}