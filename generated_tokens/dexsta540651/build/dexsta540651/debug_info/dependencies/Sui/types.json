{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/types.move", "definition_location": {"file_hash": [63, 13, 213, 43, 72, 142, 57, 16, 144, 212, 163, 115, 244, 38, 177, 212, 153, 153, 70, 69, 151, 247, 9, 20, 2, 216, 173, 237, 18, 38, 238, 48], "start": 123, "end": 128}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "types"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [63, 13, 213, 43, 72, 142, 57, 16, 144, 212, 163, 115, 244, 38, 177, 212, 153, 153, 70, 69, 151, 247, 9, 20, 2, 216, 173, 237, 18, 38, 238, 48], "start": 290, "end": 350}, "definition_location": {"file_hash": [63, 13, 213, 43, 72, 142, 57, 16, 144, 212, 163, 115, 244, 38, 177, 212, 153, 153, 70, 69, 151, 247, 9, 20, 2, 216, 173, 237, 18, 38, 238, 48], "start": 308, "end": 327}, "type_parameters": [["T", {"file_hash": [63, 13, 213, 43, 72, 142, 57, 16, 144, 212, 163, 115, 244, 38, 177, 212, 153, 153, 70, 69, 151, 247, 9, 20, 2, 216, 173, 237, 18, 38, 238, 48], "start": 328, "end": 329}]], "parameters": [["_#0#0", {"file_hash": [63, 13, 213, 43, 72, 142, 57, 16, 144, 212, 163, 115, 244, 38, 177, 212, 153, 153, 70, 69, 151, 247, 9, 20, 2, 216, 173, 237, 18, 38, 238, 48], "start": 337, "end": 338}]], "returns": [{"file_hash": [63, 13, 213, 43, 72, 142, 57, 16, 144, 212, 163, 115, 244, 38, 177, 212, 153, 153, 70, 69, 151, 247, 9, 20, 2, 216, 173, 237, 18, 38, 238, 48], "start": 345, "end": 349}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}