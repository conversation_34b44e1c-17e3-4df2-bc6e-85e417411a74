{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/crypto/nitro_attestation.move", "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 87, "end": 104}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "nitro_attestation"], "struct_map": {"0": {"definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 634, "end": 642}, "type_parameters": [], "fields": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 658, "end": 663}, {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 673, "end": 678}]}, "1": {"definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 757, "end": 781}, "type_parameters": [], "fields": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 841, "end": 850}, {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 946, "end": 955}, {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1036, "end": 1042}, {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1217, "end": 1221}, {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1337, "end": 1347}, {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1431, "end": 1440}, {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1579, "end": 1584}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1831, "end": 2011}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1841, "end": 1863}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1864, "end": 1875}], ["clock#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1889, "end": 1894}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1905, "end": 1929}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1968, "end": 1980}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2002, "end": 2007}, "2": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1982, "end": 2008}, "3": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 1936, "end": 2009}}, "is_native": false}, "1": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2013, "end": 2117}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2024, "end": 2033}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2034, "end": 2045}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2075, "end": 2086}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2094, "end": 2105}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2093, "end": 2115}}, "is_native": false}, "2": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2119, "end": 2216}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2130, "end": 2139}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2140, "end": 2151}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2181, "end": 2185}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2193, "end": 2204}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2192, "end": 2214}}, "is_native": false}, "3": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2218, "end": 2316}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2229, "end": 2235}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2236, "end": 2247}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2277, "end": 2288}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2296, "end": 2307}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2295, "end": 2314}}, "is_native": false}, "4": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2458, "end": 2558}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2469, "end": 2473}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2474, "end": 2485}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2515, "end": 2532}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2540, "end": 2551}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2539, "end": 2556}}, "is_native": false}, "5": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2560, "end": 2674}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2571, "end": 2581}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2582, "end": 2593}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2623, "end": 2642}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2650, "end": 2661}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2649, "end": 2672}}, "is_native": false}, "6": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2676, "end": 2788}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2687, "end": 2696}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2697, "end": 2708}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2738, "end": 2757}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2765, "end": 2776}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2764, "end": 2786}}, "is_native": false}, "7": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2790, "end": 2894}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2801, "end": 2806}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2807, "end": 2818}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2848, "end": 2867}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2875, "end": 2886}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2874, "end": 2892}}, "is_native": false}, "8": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2896, "end": 2954}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2907, "end": 2912}, "type_parameters": [], "parameters": [["entry#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2913, "end": 2918}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2932, "end": 2934}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2941, "end": 2946}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2941, "end": 2952}}, "is_native": false}, "9": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2956, "end": 3024}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2967, "end": 2972}, "type_parameters": [], "parameters": [["entry#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2973, "end": 2978}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 2992, "end": 3003}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 3011, "end": 3016}, "1": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 3010, "end": 3022}}, "is_native": false}, "10": {"location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 3055, "end": 3185}, "definition_location": {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 3066, "end": 3097}, "type_parameters": [], "parameters": [["attestation#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 3103, "end": 3114}], ["current_timestamp#0#0", {"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 3133, "end": 3150}]], "returns": [{"file_hash": [163, 44, 187, 59, 153, 233, 67, 47, 215, 21, 131, 14, 236, 173, 148, 154, 217, 158, 118, 199, 6, 176, 81, 115, 170, 205, 245, 43, 8, 82, 231, 162], "start": 3160, "end": 3184}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidPCRsError": 3, "ENotSupportedError": 0, "EParseError": 1, "EVerifyError": 2}}