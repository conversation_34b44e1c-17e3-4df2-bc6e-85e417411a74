{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/crypto/ecdsa_r1.move", "definition_location": {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 87, "end": 95}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "ecdsa_r1"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 1297, "end": 1414}, "definition_location": {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 1315, "end": 1334}, "type_parameters": [], "parameters": [["signature#0#0", {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 1340, "end": 1349}], ["msg#0#0", {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 1368, "end": 1371}], ["hash#0#0", {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 1390, "end": 1394}]], "returns": [{"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 1403, "end": 1413}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "1": {"location": {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 2127, "end": 2264}, "definition_location": {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 2145, "end": 2161}, "type_parameters": [], "parameters": [["signature#0#0", {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 2167, "end": 2176}], ["public_key#0#0", {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 2195, "end": 2205}], ["msg#0#0", {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 2224, "end": 2227}], ["hash#0#0", {"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 2246, "end": 2250}]], "returns": [{"file_hash": [115, 243, 150, 208, 233, 175, 71, 95, 163, 192, 168, 236, 60, 32, 181, 66, 249, 189, 71, 140, 187, 10, 135, 254, 209, 65, 61, 183, 165, 12, 246, 66], "start": 2259, "end": 2263}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EFailToRecoverPubKey": 0, "EInvalidSignature": 1, "KECCAK256": 2, "SHA256": 3}}