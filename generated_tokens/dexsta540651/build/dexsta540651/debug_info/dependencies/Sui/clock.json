{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/clock.move", "definition_location": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 215, "end": 220}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "clock"], "struct_map": {"0": {"definition_location": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 763, "end": 768}, "type_parameters": [], "fields": [{"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 783, "end": 785}, {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1001, "end": 1013}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1136, "end": 1206}, "definition_location": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1147, "end": 1159}, "type_parameters": [], "parameters": [["clock#0#0", {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1160, "end": 1165}]], "returns": [{"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1176, "end": 1179}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1186, "end": 1191}, "1": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1186, "end": 1204}}, "is_native": false}, "1": {"location": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1336, "end": 1667}, "definition_location": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1340, "end": 1346}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1347, "end": 1350}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1378, "end": 1381}, "1": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1378, "end": 1390}, "2": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1394, "end": 1398}, "3": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1391, "end": 1393}, "4": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1370, "end": 1418}, "6": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1400, "end": 1417}, "7": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1370, "end": 1418}, "8": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1468, "end": 1483}, "9": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1656, "end": 1657}, "10": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1448, "end": 1664}, "11": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1425, "end": 1665}}, "is_native": false}, "2": {"location": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1695, "end": 1949}, "definition_location": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1699, "end": 1724}, "type_parameters": [], "parameters": [["clock#0#0", {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1725, "end": 1730}], ["timestamp_ms#0#0", {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1744, "end": 1756}], ["ctx#0#0", {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1763, "end": 1766}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1867, "end": 1870}, "1": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1867, "end": 1879}, "2": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1883, "end": 1887}, "3": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1880, "end": 1882}, "4": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1859, "end": 1907}, "8": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1889, "end": 1906}, "9": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1859, "end": 1907}, "10": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1935, "end": 1947}, "11": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1914, "end": 1919}, "12": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1914, "end": 1932}, "13": {"file_hash": [127, 207, 14, 233, 4, 247, 38, 157, 135, 14, 185, 217, 17, 23, 85, 86, 165, 99, 125, 161, 99, 102, 97, 57, 2, 15, 13, 126, 68, 117, 200, 53], "start": 1914, "end": 1947}}, "is_native": false}}, "constant_map": {"ENotSystemAddress": 0}}