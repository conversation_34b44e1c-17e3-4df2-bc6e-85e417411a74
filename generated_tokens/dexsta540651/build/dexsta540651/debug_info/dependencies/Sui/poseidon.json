{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_framework__devnet/crates/sui-framework/packages/sui-framework/sources/crypto/poseidon.move", "definition_location": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 180, "end": 188}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "poseidon"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 973, "end": 1408}, "definition_location": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 984, "end": 998}, "type_parameters": [], "parameters": [["data#0#0", {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 999, "end": 1003}]], "returns": [{"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1021, "end": 1025}], "locals": [["%#1", {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1371, "end": 1394}], ["b#1#0", {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1048, "end": 1049}], ["i#1#0", {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1041, "end": 1042}], ["l#1#0", {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1051, "end": 1052}]], "nops": {}, "code_map": {"0": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1057, "end": 1058}, "1": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1060, "end": 1068}, "2": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1070, "end": 1074}, "3": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1070, "end": 1083}, "4": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1051, "end": 1052}, "5": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1044, "end": 1049}, "6": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1037, "end": 1042}, "7": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1098, "end": 1099}, "8": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1102, "end": 1103}, "9": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1100, "end": 1101}, "10": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1090, "end": 1117}, "14": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1105, "end": 1116}, "15": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1090, "end": 1117}, "16": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1130, "end": 1131}, "17": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1134, "end": 1135}, "18": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1132, "end": 1133}, "19": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1123, "end": 1312}, "20": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1168, "end": 1172}, "21": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1173, "end": 1174}, "22": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1167, "end": 1175}, "23": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1193, "end": 1207}, "24": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1210, "end": 1219}, "25": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1208, "end": 1209}, "26": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1185, "end": 1240}, "30": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1221, "end": 1239}, "31": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1185, "end": 1240}, "32": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1250, "end": 1251}, "33": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1277, "end": 1281}, "34": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1282, "end": 1283}, "35": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1276, "end": 1284}, "36": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1262, "end": 1285}, "37": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1250, "end": 1286}, "38": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1300, "end": 1301}, "39": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1304, "end": 1305}, "40": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1302, "end": 1303}, "41": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1296, "end": 1297}, "42": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1123, "end": 1312}, "43": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1318, "end": 1365}, "45": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1362, "end": 1364}, "46": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1338, "end": 1365}, "47": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1371, "end": 1394}, "50": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1371, "end": 1406}}, "is_native": false}, "1": {"location": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1603, "end": 1677}, "definition_location": {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1614, "end": 1637}, "type_parameters": [], "parameters": [["data#0#0", {"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1638, "end": 1642}]], "returns": [{"file_hash": [61, 169, 109, 254, 42, 160, 191, 89, 57, 88, 132, 125, 179, 223, 53, 150, 86, 212, 220, 155, 141, 201, 103, 117, 136, 84, 252, 228, 165, 99, 31, 70], "start": 1666, "end": 1676}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"BN254_MAX": 2, "EEmptyInput": 1, "ENonCanonicalInput": 0}}