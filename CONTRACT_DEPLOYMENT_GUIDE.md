# Dexsta Platform - Contract Deployment Guide

This guide walks you through deploying the Dexsta Move contracts to Sui blockchain.

## 📋 Prerequisites

### 1. Install Sui CLI
```bash
# Install Sui CLI (choose your platform)
# macOS
brew install sui

# Linux/WSL
curl -fLJO https://github.com/MystenLabs/sui/releases/latest/download/sui-ubuntu-x86_64.tgz
tar -xf sui-ubuntu-x86_64.tgz
sudo mv sui /usr/local/bin/

# Verify installation
sui --version
```

### 2. Set Up Sui Wallet
```bash
# Create new wallet
sui client new-address ed25519

# Or import existing wallet
sui client import [private-key] ed25519

# Check active address
sui client active-address

# Check balance
sui client balance
```

### 3. Configure Network
```bash
# Add testnet environment (if not already added)
sui client new-env --alias testnet --rpc https://fullnode.testnet.sui.io:443

# Switch to testnet
sui client switch --env testnet

# Verify current environment
sui client active-env
```

### 4. Get Testnet SUI
- Join Sui Discord: https://discord.gg/sui
- Use the `#testnet-faucet` channel
- Request SUI with: `!faucet [your-address]`
- You need at least 1 SUI for deployment

## 🚀 Deployment Process

### Step 1: Prepare for Deployment
```bash
# Make deployment script executable
chmod +x deploy-contracts.sh

# Check your setup
sui client active-address
sui client balance
sui client active-env
```

### Step 2: Deploy to Testnet
```bash
# Deploy all contracts to testnet
./deploy-contracts.sh testnet
```

The script will:
1. ✅ Check prerequisites (Sui CLI, wallet, balance)
2. ✅ Create Move.toml configuration
3. ✅ Deploy contracts in dependency order:
   - `token_admin` (platform settings)
   - `pool_admin` (pool settings)  
   - `token` (token creation and trading)
   - `pool` (AMM pools and liquidity)
4. ✅ Generate `.env.deployment` with contract addresses
5. ✅ Create test script for verification

### Step 3: Verify Deployment
```bash
# Run the generated test script
./test-deployment.sh

# Check deployment log
cat deployment_testnet_[timestamp].log
```

### Step 4: Update Frontend Configuration

1. **Copy contract addresses** from `.env.deployment`:
```bash
cat .env.deployment
```

2. **Update your environment file**:
```bash
# Copy addresses to your .env.local
cp .env.deployment .env.local

# Or manually add to existing .env.local
echo "" >> .env.local
cat .env.deployment >> .env.local
```

3. **Update constants file** (`src/constants/contracts.ts`):
```typescript
// Update with your deployed addresses
export const TESTNET_CONTRACTS = {
  TOKEN_PACKAGE_ID: '0x[your-token-package-id]',
  POOL_PACKAGE_ID: '0x[your-pool-package-id]',
  TOKEN_ADMIN_PACKAGE_ID: '0x[your-token-admin-package-id]',
  POOL_ADMIN_PACKAGE_ID: '0x[your-pool-admin-package-id]'
}
```

## 🔧 Contract Initialization

After deployment, initialize the contracts through the admin interface:

### Step 1: Start Frontend
```bash
npm run dev
```

### Step 2: Connect Admin Wallet
1. Open http://localhost:3000/admin
2. Connect the same wallet used for deployment
3. Verify admin status shows "Authorized"

### Step 3: Initialize Pool Contract
1. Go to "Pool" tab in admin interface
2. Review default settings:
   - Platform Fee: 1% (100 bps)
   - LP Fee: 0.3% (30 bps)
   - Mint Fee: 0.1 SUI
   - Migration Threshold: 60 SUI
3. Click "Initialize Pool Contract"
4. Approve transaction in wallet
5. Wait for confirmation

### Step 4: Initialize Token Contract
1. Go to "Token" tab in admin interface
2. Review default settings:
   - Platform Fee: 1% (100 bps)
   - Reward Fee: 0.5% (50 bps)
   - Mint Fee: 0.1 SUI
   - Initial Reward Goal: 1 SUI
3. Click "Initialize Token Contract"
4. Approve transaction in wallet
5. Wait for confirmation

## 🧪 Testing the Deployment

### Test 1: Create a Token
1. Go to http://localhost:3000/create
2. Fill in token details:
   - Name: "Test Token"
   - Symbol: "TEST"
   - Description: "A test token for Dexsta"
   - Upload image
   - Set fees: 1% buy, 1% sell
   - Initial SUI: 1.0
3. Click "Create Token"
4. Approve transaction
5. Verify token appears in dashboard

### Test 2: Trade Tokens
1. Go to token detail page
2. Test buying tokens:
   - Enter 0.1 SUI
   - Check estimated tokens
   - Execute trade
3. Test selling tokens:
   - Select 25% to sell
   - Check estimated SUI
   - Execute trade
4. Verify balances update

### Test 3: Pool Creation (After Migration)
1. Trade enough to reach 60 SUI threshold
2. Token should auto-migrate to AMM
3. Test adding liquidity
4. Test removing liquidity

## 🔍 Troubleshooting

### Common Issues

**1. "Insufficient gas" error**
```bash
# Check balance
sui client balance

# Get more testnet SUI if needed
# Use Discord faucet: !faucet [address]
```

**2. "Package not found" error**
```bash
# Verify contract addresses in constants file
# Check .env.local has correct addresses
# Restart development server
```

**3. "Transaction failed" error**
```bash
# Check transaction details
sui client tx-block [transaction-digest]

# Verify wallet has enough SUI
# Check if contracts are initialized
```

**4. "Admin not authorized" error**
```bash
# Verify you're using the deployment wallet
sui client active-address

# Check if contracts are properly initialized
# Try refreshing admin status
```

### Debug Commands

```bash
# Check object details
sui client object [object-id]

# View transaction details
sui client tx-block [tx-digest]

# List owned objects
sui client objects

# Check gas usage
sui client gas
```

## 📊 Deployment Checklist

### Pre-Deployment
- [ ] Sui CLI installed and working
- [ ] Wallet configured with sufficient SUI (>1 SUI)
- [ ] Connected to correct network (testnet)
- [ ] Contracts directory exists with all 4 Move files

### Deployment
- [ ] All 4 contracts deployed successfully
- [ ] Contract addresses saved to `.env.deployment`
- [ ] No deployment errors in log file
- [ ] Test script runs without errors

### Post-Deployment
- [ ] Frontend environment updated with contract addresses
- [ ] Constants file updated with new addresses
- [ ] Admin interface accessible
- [ ] Pool contract initialized
- [ ] Token contract initialized

### Testing
- [ ] Token creation works
- [ ] Token trading works
- [ ] Admin functions work
- [ ] Real-time updates work
- [ ] Error handling works

## 🚀 Deploy to Mainnet

Once testnet deployment is working:

1. **Switch to mainnet**:
```bash
sui client switch --env mainnet
```

2. **Get mainnet SUI** (real SUI required)

3. **Deploy to mainnet**:
```bash
./deploy-contracts.sh mainnet
```

4. **Update production environment**:
```bash
# Update .env.production with mainnet addresses
cp .env.deployment .env.production
```

5. **Deploy frontend** to production with mainnet configuration

## 📞 Support

If you encounter issues:

1. Check the deployment log file
2. Verify all prerequisites are met
3. Test with a fresh wallet if needed
4. Review Sui documentation: https://docs.sui.io
5. Join Sui Discord for community support

## 🎉 Success!

Once deployment is complete, you'll have:
- ✅ All 4 contracts deployed and initialized
- ✅ Frontend connected to live contracts
- ✅ Full token creation and trading functionality
- ✅ Admin interface for platform management
- ✅ Real-time event processing

Your Dexsta platform is now live on Sui! 🚀
