/**
 * React hooks for token data management
 */

import { useState, useEffect, useCallback } from 'react'
import { 
  getTokens, 
  getToken, 
  getTrendingTokens, 
  getFeaturedTokens, 
  getTokensByCreator,
  searchTokens,
  createTokenRecord,
  type Token 
} from '@/lib/database'

export interface UseTokensOptions {
  limit?: number
  offset?: number
  sortBy?: 'created_at' | 'market_cap' | 'volume_24h' | 'price_change_24h'
  sortOrder?: 'asc' | 'desc'
  category?: string
  search?: string
  autoRefresh?: boolean
  refreshInterval?: number
}

export interface UseTokensResult {
  tokens: Token[]
  total: number
  loading: boolean
  error: string | null
  refresh: () => Promise<void>
  loadMore: () => Promise<void>
  hasMore: boolean
}

/**
 * Hook for fetching and managing tokens list
 */
export function useTokens(options: UseTokensOptions = {}): UseTokensResult {
  const [tokens, setTokens] = useState<Token[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [offset, setOffset] = useState(0)

  const {
    limit = 20,
    sortBy = 'created_at',
    sortOrder = 'desc',
    category,
    search,
    autoRefresh = false,
    refreshInterval = 30000 // 30 seconds
  } = options

  const fetchTokens = useCallback(async (isLoadMore = false) => {
    try {
      if (!isLoadMore) {
        setLoading(true)
        setError(null)
      }

      const currentOffset = isLoadMore ? offset : 0
      const result = await getTokens({
        limit,
        offset: currentOffset,
        sortBy,
        sortOrder,
        category,
        search
      })

      if (isLoadMore) {
        setTokens(prev => [...prev, ...result.tokens])
        setOffset(prev => prev + limit)
      } else {
        setTokens(result.tokens)
        setOffset(limit)
      }
      
      setTotal(result.total)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tokens')
    } finally {
      setLoading(false)
    }
  }, [limit, sortBy, sortOrder, category, search, offset])

  const refresh = useCallback(async () => {
    setOffset(0)
    await fetchTokens(false)
  }, [fetchTokens])

  const loadMore = useCallback(async () => {
    if (tokens.length < total) {
      await fetchTokens(true)
    }
  }, [fetchTokens, tokens.length, total])

  // Initial load and dependency changes
  useEffect(() => {
    setOffset(0)
    fetchTokens(false)
  }, [sortBy, sortOrder, category, search])

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(refresh, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, refresh])

  const hasMore = tokens.length < total

  return {
    tokens,
    total,
    loading,
    error,
    refresh,
    loadMore,
    hasMore
  }
}

/**
 * Hook for fetching a single token
 */
export function useToken(tokenId: string | null) {
  const [token, setToken] = useState<Token | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!tokenId) {
      setToken(null)
      return
    }

    const fetchToken = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const result = await getToken(tokenId)
        setToken(result)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch token')
      } finally {
        setLoading(false)
      }
    }

    fetchToken()
  }, [tokenId])

  return { token, loading, error }
}

/**
 * Hook for trending tokens
 */
export function useTrendingTokens(limit: number = 10) {
  const [tokens, setTokens] = useState<Token[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchTrending = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await getTrendingTokens(limit)
      setTokens(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch trending tokens')
    } finally {
      setLoading(false)
    }
  }, [limit])

  useEffect(() => {
    fetchTrending()
  }, [fetchTrending])

  return { tokens, loading, error, refresh: fetchTrending }
}

/**
 * Hook for featured tokens
 */
export function useFeaturedTokens(limit: number = 5) {
  const [tokens, setTokens] = useState<Token[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchFeatured = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await getFeaturedTokens(limit)
      setTokens(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch featured tokens')
    } finally {
      setLoading(false)
    }
  }, [limit])

  useEffect(() => {
    fetchFeatured()
  }, [fetchFeatured])

  return { tokens, loading, error, refresh: fetchFeatured }
}

/**
 * Hook for creator's tokens
 */
export function useCreatorTokens(creatorAddress: string | null) {
  const [tokens, setTokens] = useState<Token[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!creatorAddress) {
      setTokens([])
      return
    }

    const fetchCreatorTokens = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const result = await getTokensByCreator(creatorAddress)
        setTokens(result)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch creator tokens')
      } finally {
        setLoading(false)
      }
    }

    fetchCreatorTokens()
  }, [creatorAddress])

  return { tokens, loading, error }
}

/**
 * Hook for token search
 */
export function useTokenSearch() {
  const [results, setResults] = useState<Token[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (query: string, limit: number = 20) => {
    if (!query.trim()) {
      setResults([])
      return
    }

    setLoading(true)
    setError(null)

    try {
      const result = await searchTokens(query, limit)
      setResults(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed')
    } finally {
      setLoading(false)
    }
  }, [])

  const clearResults = useCallback(() => {
    setResults([])
    setError(null)
  }, [])

  return { results, loading, error, search, clearResults }
}

/**
 * Hook for creating tokens
 */
export function useCreateToken() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createToken = useCallback(async (tokenData: Omit<Token, 'id' | 'created_at' | 'updated_at'>) => {
    setLoading(true)
    setError(null)

    try {
      const result = await createTokenRecord(tokenData)
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create token'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  return { createToken, loading, error }
}
