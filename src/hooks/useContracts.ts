import { useState, useCallback } from 'react';
import { useWallet } from '@suiet/wallet-kit';
import { SuiClient } from '@mysten/sui/client';
import { Transaction } from '@mysten/sui/transactions';
import { createTokenRecord } from '@/lib/database';
import {
  DEXSTA_PACKAGE_ID,
  TOKEN_REGISTRY_ID,
  POOL_REGISTRY_ID,
  NFT_REGISTRY_ID,
  DEXSTA_FUNCTIONS,
  XFT_PACKAGE_ID,
  XFT_LABEL_REGISTRY_ID,
  XFT_NFT_REGISTRY_ID,
  XFT_FUNCTIONS,
  XFT_CONSTANTS,
  PLATFORM_ADMIN_ADDRESS,
  FIRE_REGISTRY_ID,
  TOKEN_PLATFORM_SETTINGS,
  POOL_PLATFORM_SETTINGS,
  TOKEN_ADMIN_CAP,
  POOL_ADMIN_CAP
} from '@/constants/contracts';

// Types
interface TokenCreationParams {
  name: string;
  symbol: string;
  description: string;
  iconUrl: string;
  metadataUrl?: string;
  website: string;
  twitter: string;
  telegram: string;
  tiktok: string;
  totalSupply: string;
  buyFeeBps: number;
  sellFeeBps: number;
  suiAmount: bigint;
  // Advanced validation parameters
  linkToLabel?: string | null;
  useOperatorLicense?: boolean;
  operatorLicenseId?: string | null;
  requiredNftId?: string | null;
}

interface PrivateTokenCreationParams extends TokenCreationParams {
  requiredNftId: string;
}

// Pools are automatically created during token migration
// No manual pool creation needed

interface NFTCreationParams {
  name: string;
  description: string;
  imageUrl: string;
  suiAmount: string;
}

// XFT-specific interfaces
interface LabelCreationParams {
  name: string;
  title: string;
  description: string;
  imageUrl: string;
  labelType: number;
  supply: number;
  durationMs: number;
  suiAmount: string;
}

interface XFTCreationParams {
  name: string;
  description: string;
  imageUrl: string;
  supply: number;
  labelGlobalId?: number;
  operatorLicenseGlobalId?: number;
  attributes: string[];
  ipfsHash: string;
  suiAmount: string;
}

interface WrapXFTParams {
  xftId: string;
}

// Hook
export const useContracts = () => {
  const wallet = useWallet();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Helper to execute transactions
  const executeTransaction = useCallback(async (tx: Transaction) => {
    if (!wallet.connected || !wallet.signAndExecuteTransactionBlock) {
      throw new Error('Wallet not connected');
    }

    setLoading(true);
    setError(null);

    try {
      const result = await wallet.signAndExecuteTransaction({
        transaction: tx,
        options: {
          showEffects: true,
          showEvents: true,
          showObjectChanges: true,
        },
      });

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Transaction failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [wallet]);

  // ========== ADMIN FUNCTIONS ==========

  // Initialize token admin contract
  const initializeTokenAdmin = useCallback(async () => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    // Check if already initialized by looking at the constants
    // TOKEN_PLATFORM_SETTINGS indicates it's already initialized
    if (process.env.NODE_ENV === 'development') {
      console.log('Token Admin Platform Settings ID:', TOKEN_PLATFORM_SETTINGS);
      if (TOKEN_PLATFORM_SETTINGS !== '0x0000000000000000000000000000000000000000000000000000000000000000') {
        throw new Error('Token Admin is already initialized. Platform Settings ID: ' + TOKEN_PLATFORM_SETTINGS);
      }
    }

    const tx = new Transaction();

    // Call the actual contract function with all required parameters
    tx.moveCall({
      target: `${DEXSTA_PACKAGE_ID}::token_admin::initialize_platform_settings`,
      arguments: [
        tx.pure.address(PLATFORM_ADMIN_ADDRESS), // platform_fee_address
        tx.pure.u16(300), // platform_fee_bps (3%)
        tx.pure.u16(100), // reward_fee_bps (1%)
        tx.pure.u64(*********), // mint_fee (0.1 SUI)
        tx.pure.u64(********), // min_reward_trade_amount (0.01 SUI)
        tx.pure.u64(*********0), // initial_reward_goal (1 SUI)
        tx.pure.u64(60000000000), // bonding_curve_goal (60 SUI)
        tx.pure.u64(*********), // reward_goal_increase (0.5 SUI)
        tx.pure.u64(*********), // reward_goal_decrease_amount (0.1 SUI)
        tx.pure.u64(300), // reward_goal_decrease_threshold (5 minutes)
        tx.pure.u64(90), // reward_goal_proximity_threshold (90%)
        tx.pure.u8(5), // migration_fee_percentage (5%)
        tx.pure.u64(5000000), // migration_gas_fee (0.005 SUI)
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Initialize pool admin contract
  const initializePoolAdmin = useCallback(async () => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    // Check if already initialized by looking at the constants
    // POOL_PLATFORM_SETTINGS indicates it's already initialized
    if (process.env.NODE_ENV === 'development') {
      console.log('Pool Admin Platform Settings ID:', POOL_PLATFORM_SETTINGS);
      if (POOL_PLATFORM_SETTINGS !== '0x0000000000000000000000000000000000000000000000000000000000000000') {
        throw new Error('Pool Admin is already initialized. Platform Settings ID: ' + POOL_PLATFORM_SETTINGS);
      }
    }

    const tx = new Transaction();

    // Call the actual contract function with all required parameters
    tx.moveCall({
      target: `${DEXSTA_PACKAGE_ID}::pool_admin::initialize_platform_settings`,
      arguments: [
        tx.pure.address(PLATFORM_ADMIN_ADDRESS), // platform_fee_address
        tx.pure.u16(300), // platform_fee_bps (3%)
        tx.pure.u16(20), // lp_fee_bps (20% of platform fee)
        tx.pure.u16(100), // reward_fee_bps (1%)
        tx.pure.u64(*********), // existing_token_import_fee (0.1 SUI)
        tx.pure.u64(********), // min_reward_trade_amount (0.01 SUI)
        tx.pure.u64(*********0), // initial_reward_goal (1 SUI)
        tx.pure.u64(*********), // reward_goal_increase (0.5 SUI)
        tx.pure.u64(*********), // reward_goal_decrease_amount (0.1 SUI)
        tx.pure.u64(300), // reward_goal_decrease_threshold (5 minutes)
        tx.pure.u64(90), // reward_goal_proximity_threshold (90%)
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Initialize Fire Registry by registering contracts
  const initializeFireRegistry = useCallback(async (contractAddresses: {
    tokenContract: string;
    poolContract: string;
    labelContract: string;
    operatorContract: string;
    xftContract: string;
    marketplaceContract: string;
    bankContract: string;
  }) => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    const tx = new Transaction();

    // Note: Fire Registry is deployed separately and creates its own shared object
    // We register each contract with the Fire Registry
    // Assuming FIRE_REGISTRY_ID is the shared object ID of the Fire Registry

    // Register token contract
    tx.moveCall({
      target: `fire_registry::fire::register_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID), // Fire Registry shared object
        tx.pure.string("token"),
        tx.pure.address(contractAddresses.tokenContract), // package address
        tx.pure.address(TOKEN_REGISTRY_ID), // registry address (shared object)
      ],
    });

    // Register pool contract
    tx.moveCall({
      target: `fire_registry::fire::register_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("pool"),
        tx.pure.address(contractAddresses.poolContract),
        tx.pure.address(POOL_REGISTRY_ID),
      ],
    });

    // Register label contract
    tx.moveCall({
      target: `fire_registry::fire::register_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("label"),
        tx.pure.address(contractAddresses.labelContract),
        tx.pure.address(XFT_LABEL_REGISTRY_ID),
      ],
    });

    // Register operator contract
    tx.moveCall({
      target: `fire_registry::fire::register_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("operator"),
        tx.pure.address(contractAddresses.operatorContract),
        tx.pure.address(XFT_NFT_REGISTRY_ID),
      ],
    });

    // Register XFT contract (NFT 2.0)
    tx.moveCall({
      target: `fire_registry::fire::register_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("xft"),
        tx.pure.address(contractAddresses.xftContract),
        tx.pure.address(XFT_NFT_REGISTRY_ID), // Same registry as operator
      ],
    });

    // Register marketplace contract
    tx.moveCall({
      target: `fire_registry::fire::register_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("marketplace"),
        tx.pure.address(contractAddresses.marketplaceContract),
        tx.pure.address(contractAddresses.marketplaceContract), // Marketplace is its own registry
      ],
    });

    // Register bank contract (All Vault)
    tx.moveCall({
      target: `fire_registry::fire::register_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("bank"),
        tx.pure.address(contractAddresses.bankContract),
        tx.pure.address(contractAddresses.bankContract), // Bank is its own registry
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Update contract addresses in Fire Registry
  const updateContractAddresses = useCallback(async (contractAddresses: {
    tokenContract: string;
    poolContract: string;
    labelContract: string;
    operatorContract: string;
    xftContract: string;
    marketplaceContract: string;
    bankContract: string;
  }) => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    const tx = new Transaction();

    // Update each contract individually using update_contract function
    // Note: This assumes Fire Registry has an update_contract function
    // If not, we would need to use register_contract to overwrite

    // Update token contract
    tx.moveCall({
      target: `fire_registry::fire::update_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("token"),
        tx.pure.address(contractAddresses.tokenContract),
        tx.pure.option("address", TOKEN_REGISTRY_ID),
      ],
    });

    // Update pool contract
    tx.moveCall({
      target: `fire_registry::fire::update_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("pool"),
        tx.pure.address(contractAddresses.poolContract),
        tx.pure.option("address", POOL_REGISTRY_ID),
      ],
    });

    // Update label contract
    tx.moveCall({
      target: `fire_registry::fire::update_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("label"),
        tx.pure.address(contractAddresses.labelContract),
        tx.pure.option("address", XFT_LABEL_REGISTRY_ID),
      ],
    });

    // Update operator contract
    tx.moveCall({
      target: `fire_registry::fire::update_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("operator"),
        tx.pure.address(contractAddresses.operatorContract),
        tx.pure.option("address", XFT_NFT_REGISTRY_ID),
      ],
    });

    // Update XFT contract
    tx.moveCall({
      target: `fire_registry::fire::update_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("xft"),
        tx.pure.address(contractAddresses.xftContract),
        tx.pure.option("address", XFT_NFT_REGISTRY_ID),
      ],
    });

    // Update marketplace contract
    tx.moveCall({
      target: `fire_registry::fire::update_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("marketplace"),
        tx.pure.address(contractAddresses.marketplaceContract),
        tx.pure.option("address", contractAddresses.marketplaceContract),
      ],
    });

    // Update bank contract
    tx.moveCall({
      target: `fire_registry::fire::update_contract`,
      arguments: [
        tx.object(FIRE_REGISTRY_ID),
        tx.pure.string("bank"),
        tx.pure.address(contractAddresses.bankContract),
        tx.pure.option("address", contractAddresses.bankContract),
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Update platform settings for Token Admin
  const updateTokenPlatformSettings = useCallback(async (settings: {
    platformFeeAddress: string;
    platformFeeBps: number;
    rewardFeeBps: number;
    mintFee: string;
    minRewardTradeAmount: string;
    initialRewardGoal: string;
    bondingCurveGoal: string;
    rewardGoalIncrease: string;
    rewardGoalDecreaseAmount: string;
    rewardGoalDecreaseThreshold: number;
    rewardGoalProximityThreshold: number;
    migrationFeePercentage: number;
    migrationGasFee: string;
  }) => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    const tx = new Transaction();

    // Update token admin platform settings
    tx.moveCall({
      target: `${DEXSTA_PACKAGE_ID}::token_admin::update_platform_constants`,
      arguments: [
        tx.object(TOKEN_PLATFORM_SETTINGS), // platform_settings
        tx.object(TOKEN_ADMIN_CAP), // admin_cap
        tx.pure.address(settings.platformFeeAddress),
        tx.pure.u16(settings.platformFeeBps),
        tx.pure.u16(settings.rewardFeeBps),
        tx.pure.u64(settings.mintFee),
        tx.pure.u64(settings.minRewardTradeAmount),
        tx.pure.u64(settings.initialRewardGoal),
        tx.pure.u64(settings.bondingCurveGoal),
        tx.pure.u64(settings.rewardGoalIncrease),
        tx.pure.u64(settings.rewardGoalDecreaseAmount),
        tx.pure.u64(settings.rewardGoalDecreaseThreshold),
        tx.pure.u64(settings.rewardGoalProximityThreshold),
        tx.pure.u8(settings.migrationFeePercentage),
        tx.pure.u64(settings.migrationGasFee),
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Update platform settings for Pool Admin
  const updatePoolPlatformSettings = useCallback(async (settings: {
    platformFeeAddress: string;
    platformFeeBps: number;
    lpFeeBps: number;
    rewardFeeBps: number;
    existingTokenImportFee: string;
    minRewardTradeAmount: string;
    initialRewardGoal: string;
    rewardGoalIncrease: string;
    rewardGoalDecreaseAmount: string;
    rewardGoalDecreaseThreshold: number;
    rewardGoalProximityThreshold: number;
  }) => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    const tx = new Transaction();

    // Update pool admin platform settings
    tx.moveCall({
      target: `${DEXSTA_PACKAGE_ID}::pool_admin::update_platform_constants`,
      arguments: [
        tx.object(POOL_PLATFORM_SETTINGS), // platform_settings
        tx.object(POOL_ADMIN_CAP), // admin_cap
        tx.pure.address(settings.platformFeeAddress),
        tx.pure.u16(settings.platformFeeBps),
        tx.pure.u16(settings.lpFeeBps),
        tx.pure.u16(settings.rewardFeeBps),
        tx.pure.u64(settings.existingTokenImportFee),
        tx.pure.u64(settings.minRewardTradeAmount),
        tx.pure.u64(settings.initialRewardGoal),
        tx.pure.u64(settings.rewardGoalIncrease),
        tx.pure.u64(settings.rewardGoalDecreaseAmount),
        tx.pure.u64(settings.rewardGoalDecreaseThreshold),
        tx.pure.u64(settings.rewardGoalProximityThreshold),
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Update bonding curve aggressiveness for Token Registry
  const updateTokenCurveAggressiveness = useCallback(async (aggressiveness: number) => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    const tx = new Transaction();

    // Update curve aggressiveness in token registry
    tx.moveCall({
      target: `${DEXSTA_PACKAGE_ID}::simple_token::update_curve_aggressiveness`,
      arguments: [
        tx.object(TOKEN_REGISTRY_ID), // token_registry
        tx.pure.u64(aggressiveness), // new_aggressiveness (100-5000)
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Update virtual liquidity buffer for Token Registry
  const updateTokenVirtualLiquidityBuffer = useCallback(async (buffer: string) => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    const tx = new Transaction();

    // Update virtual liquidity buffer in token registry
    tx.moveCall({
      target: `${DEXSTA_PACKAGE_ID}::simple_token::update_virtual_liquidity_buffer`,
      arguments: [
        tx.object(TOKEN_REGISTRY_ID), // token_registry
        tx.pure.u64(buffer), // new_buffer (in MIST)
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Update bonding curve aggressiveness for Pool Registry
  const updatePoolCurveAggressiveness = useCallback(async (aggressiveness: number) => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    const tx = new Transaction();

    // Update curve aggressiveness in pool registry
    tx.moveCall({
      target: `${DEXSTA_PACKAGE_ID}::pool::update_curve_aggressiveness`,
      arguments: [
        tx.object(POOL_REGISTRY_ID), // pool_registry
        tx.pure.u64(aggressiveness), // new_aggressiveness (100-5000)
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Update virtual liquidity buffer for Pool Registry
  const updatePoolVirtualLiquidityBuffer = useCallback(async (buffer: string) => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    const tx = new Transaction();

    // Update virtual liquidity buffer in pool registry
    tx.moveCall({
      target: `${DEXSTA_PACKAGE_ID}::pool::update_virtual_liquidity_buffer`,
      arguments: [
        tx.object(POOL_REGISTRY_ID), // pool_registry
        tx.pure.u64(buffer), // new_buffer (in MIST)
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Update label settings
  const updateLabelSettings = useCallback(async (settings: {
    annualMintPrice: string;
    marketplaceFee: string;
  }) => {
    if (!wallet.account?.address) {
      throw new Error('Wallet not connected');
    }

    const tx = new Transaction();

    // Update annual label mint price
    tx.moveCall({
      target: `${XFT_PACKAGE_ID}::label_admin::update_annual_mint_price`,
      arguments: [
        tx.pure.u64(settings.annualMintPrice), // price in MIST
      ],
    });

    // Update marketplace fee
    tx.moveCall({
      target: `${XFT_PACKAGE_ID}::label_admin::update_marketplace_fee`,
      arguments: [
        tx.pure.u16(settings.marketplaceFee), // fee in BPS
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // ========== TOKEN FUNCTIONS ==========

  const createToken = useCallback(async (params: TokenCreationParams) => {
    const tx = new Transaction();

    // Template-based token creation system
    // Step 1: Request token creation (triggers module deployment)

    // Split coins for payment (minimum 0.1 SUI for token creation)
    const [paymentCoin] = tx.splitCoins(tx.gas, [tx.pure.u64(params.suiAmount)]);

    tx.moveCall({
      target: DEXSTA_FUNCTIONS.TOKEN.REQUEST_CREATION,
      arguments: [
        tx.object(TOKEN_REGISTRY_ID),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.name))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.symbol))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.description))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.iconUrl))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.website || ''))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.twitter || ''))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.telegram || ''))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.tiktok || ''))),
        tx.pure.u64(params.totalSupply),
        tx.pure.u8(9), // decimals
        tx.pure.u16(params.buyFeeBps),
        tx.pure.u16(params.sellFeeBps),
        tx.pure.u16(0), // max_wallet_percentage (0 = no limit)
        tx.pure.u64(params.operatorLicenseId ? parseInt(params.operatorLicenseId) : 0), // operator_id
        tx.pure.u64(params.linkToLabel ? parseInt(params.linkToLabel) : 0), // link_to_label
        tx.pure.u64(0), // xft_id (0 = not used)
        paymentCoin, // payment coin
      ],
    });

    // Note: This triggers TokenModuleDeploymentRequested event
    // Frontend should listen for this event and handle module deployment
    // Then call completeTokenCreation() to finish the process
    return await executeTransaction(tx);
  }, [executeTransaction, wallet.account?.address]);

  // Step 2: Complete token creation after module deployment
  const completeTokenCreation = useCallback(async (params: {
    tokenId: string;
    deployedPackageId: string;
    treasuryCapId: string;
    coinMetadataId: string;
  }) => {
    const tx = new Transaction();

    tx.moveCall({
      target: DEXSTA_FUNCTIONS.TOKEN.COMPLETE_CREATION,
      arguments: [
        tx.object(TOKEN_REGISTRY_ID),
        tx.pure.u64(params.tokenId), // token_id from step 1
        tx.pure.address(params.deployedPackageId), // deployed module package ID
        tx.object(params.treasuryCapId), // TreasuryCap object
        tx.object(params.coinMetadataId), // CoinMetadata object
      ],
    });

    // Note: This completes the token creation and enables trading
    return await executeTransaction(tx);
  }, [executeTransaction]);

  const createPrivateToken = useCallback(async (params: PrivateTokenCreationParams) => {
    const tx = new Transaction();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure.u64(params.suiAmount)]);
    
    tx.moveCall({
      target: DEXSTA_FUNCTIONS.TOKEN.CREATE_WITH_NFT,
      arguments: [
        tx.object(TOKEN_REGISTRY_ID),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.name))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.symbol))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.description))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.iconUrl))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.website))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.twitter))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.telegram))),
        tx.pure.vector('u8', Array.from(new TextEncoder().encode(params.tiktok))),
        tx.pure.u64(params.totalSupply),
        tx.pure.u8(9),
        tx.pure.u16(params.buyFeeBps),
        tx.pure.u16(params.sellFeeBps),
        tx.pure.u16(0), // max_wallet_percentage (0 = no limit)
        params.requiredNftId ? tx.pure.option('u64', params.requiredNftId) : tx.pure.option('u64', null),
        tx.pure.vector('u8', []), // linked_label_id (empty for now)
        coin,
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const buyTokens = useCallback(async (tokenInfoId: string, suiAmount: string) => {
    const tx = new Transaction();

    const [coin] = tx.splitCoins(tx.gas, [tx.pure(suiAmount)]);

    tx.moveCall({
      target: DEXSTA_FUNCTIONS.TOKEN.BUY,
      arguments: [
        tx.object(TOKEN_REGISTRY_ID), // registry
        tx.object(tokenInfoId), // token_info
        coin, // sui_payment
        tx.pure('0'), // min_tokens_out
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const sellTokens = useCallback(async (tokenInfoId: string, tokenCoinId: string, minSuiOut: string = '0') => {
    const tx = new Transaction();

    tx.moveCall({
      target: DEXSTA_FUNCTIONS.TOKEN.SELL,
      arguments: [
        tx.object(TOKEN_REGISTRY_ID), // registry
        tx.object(tokenInfoId), // token_info
        tx.object(tokenCoinId), // tokens (actual coin object)
        tx.pure(minSuiOut), // min_sui_out
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const transferTokenOwnership = useCallback(async (tokenInfoId: string, newOwner: string) => {
    const tx = new Transaction();
    
    tx.moveCall({
      target: DEXSTA_FUNCTIONS.TOKEN.TRANSFER_OWNERSHIP,
      arguments: [
        tx.object(TOKEN_REGISTRY_ID),
        tx.object(tokenInfoId),
        tx.pure(newOwner),
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const updateTokenFees = useCallback(async (tokenInfoId: string, buyFeeBps: number, sellFeeBps: number) => {
    const tx = new Transaction();
    
    tx.moveCall({
      target: DEXSTA_FUNCTIONS.TOKEN.UPDATE_FEES,
      arguments: [
        tx.object(tokenInfoId),
        tx.pure(buyFeeBps),
        tx.pure(sellFeeBps),
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const updateTokenSocials = useCallback(async (tokenInfoId: string, socials: {
    website: string;
    twitter: string;
    telegram: string;
    tiktok: string;
  }) => {
    const tx = new Transaction();
    
    tx.moveCall({
      target: DEXSTA_FUNCTIONS.TOKEN.UPDATE_SOCIALS,
      arguments: [
        tx.object(tokenInfoId),
        tx.pure(socials.website),
        tx.pure(socials.twitter),
        tx.pure(socials.telegram),
        tx.pure(socials.tiktok),
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const donateToTokenRewardPot = useCallback(async (tokenInfoId: string, suiAmount: string) => {
    const tx = new Transaction();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(suiAmount)]);
    
    tx.moveCall({
      target: DEXSTA_FUNCTIONS.TOKEN.DONATE_REWARD,
      arguments: [
        tx.object(tokenInfoId),
        coin,
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  // ========== POOL FUNCTIONS ==========
  // Note: Pools are automatically created during token migration
  // Post-migration: ALL trading happens in pools (token contract blocks trading)
  // Pre-migration: Use token contract functions instead

  const buyFromPool = useCallback(async (poolId: string, suiAmount: string) => {
    const tx = new Transaction();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(suiAmount)]);
    
    tx.moveCall({
      target: DEXSTA_FUNCTIONS.POOL.BUY,
      arguments: [
        tx.object(poolId),
        coin,
        tx.pure('0'),
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const sellToPool = useCallback(async (poolId: string, tokenAmount: string, minSuiOut: string = '0') => {
    const tx = new Transaction();
    
    tx.moveCall({
      target: DEXSTA_FUNCTIONS.POOL.SELL,
      arguments: [
        tx.object(poolId),
        tx.pure(tokenAmount),
        tx.pure(minSuiOut),
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const donateToPoolRewardPot = useCallback(async (poolId: string, suiAmount: string) => {
    const tx = new Transaction();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(suiAmount)]);
    
    tx.moveCall({
      target: DEXSTA_FUNCTIONS.POOL.DONATE_REWARD,
      arguments: [
        tx.object(poolId),
        coin,
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  // ========== NFT FUNCTIONS ==========

  const mintOneOfOneNFT = useCallback(async (params: NFTCreationParams) => {
    const tx = new Transaction();

    const [coin] = tx.splitCoins(tx.gas, [tx.pure(params.suiAmount)]);

    tx.moveCall({
      target: DEXSTA_FUNCTIONS.NFT.MINT_ONE_OF_ONE,
      arguments: [
        tx.object(NFT_REGISTRY_ID),
        tx.pure(params.name),
        tx.pure(params.description),
        tx.pure(params.imageUrl),
        coin,
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  // ========== XFT FUNCTIONS ==========

  const createLabel = useCallback(async (params: LabelCreationParams) => {
    const tx = new Transaction();

    const [coin] = tx.splitCoins(tx.gas, [tx.pure.u64(params.suiAmount)]);

    tx.moveCall({
      target: XFT_FUNCTIONS.LABEL.MINT_LABEL,
      arguments: [
        tx.object(XFT_LABEL_REGISTRY_ID),
        tx.pure.string(params.name),
        tx.pure.string(params.title),
        tx.pure.string(params.description),
        tx.pure.string(params.imageUrl),
        tx.pure.u64(params.supply),
        tx.pure.u64(params.durationMs),
        coin,
        tx.object('0x6'), // Clock object
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const mintXFT = useCallback(async (params: XFTCreationParams) => {
    const tx = new Transaction();

    const [coin] = tx.splitCoins(tx.gas, [tx.pure.u64(params.suiAmount)]);

    tx.moveCall({
      target: XFT_FUNCTIONS.XFT.MINT_XFT,
      arguments: [
        tx.object(XFT_NFT_REGISTRY_ID),
        tx.pure.string(params.name),
        tx.pure.string(params.description),
        tx.pure.string(params.imageUrl),
        tx.pure.u64(params.supply),
        params.labelGlobalId ? [tx.pure.u64(params.labelGlobalId)] : [],
        params.operatorLicenseGlobalId ? [tx.pure.u64(params.operatorLicenseGlobalId)] : [],
        tx.pure.vector('string', params.attributes),
        tx.pure.string(params.ipfsHash),
        coin,
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  const wrapXFT = useCallback(async (params: WrapXFTParams) => {
    const tx = new Transaction();

    tx.moveCall({
      target: XFT_FUNCTIONS.XFT.WRAP_XFT,
      arguments: [
        tx.object(params.xftId),
      ],
    });

    return await executeTransaction(tx);
  }, [executeTransaction]);

  // ========== SMART TRADING FUNCTIONS ==========
  // These functions automatically route to the correct contract based on migration status

  const smartBuy = useCallback(async (tokenInfoId: string, suiAmount: string, poolId?: string) => {
    // If poolId is provided, token has migrated - use pool
    if (poolId) {
      return await buyFromPool(poolId, suiAmount);
    } else {
      // Pre-migration - use token contract
      return await buyTokens(tokenInfoId, suiAmount);
    }
  }, [buyTokens, buyFromPool]);

  const smartSell = useCallback(async (tokenInfoId: string, tokenAmount: string, minSuiOut: string = '0', poolId?: string) => {
    // If poolId is provided, token has migrated - use pool
    if (poolId) {
      return await sellToPool(poolId, tokenAmount, minSuiOut);
    } else {
      // Pre-migration - use token contract
      return await sellTokens(tokenInfoId, tokenAmount, minSuiOut);
    }
  }, [sellTokens, sellToPool]);

  const smartDonateRewardPot = useCallback(async (tokenInfoId: string, suiAmount: string, poolId?: string) => {
    // If poolId is provided, token has migrated - use pool
    if (poolId) {
      return await donateToPoolRewardPot(poolId, suiAmount);
    } else {
      // Pre-migration - use token contract
      return await donateToTokenRewardPot(tokenInfoId, suiAmount);
    }
  }, [donateToTokenRewardPot, donateToPoolRewardPot]);

  return {
    // State
    loading,
    error,

    // Admin functions
    initializeTokenAdmin,
    initializePoolAdmin,
    initializeFireRegistry,
    updateContractAddresses,
    updateTokenPlatformSettings,
    updatePoolPlatformSettings,
    updateTokenCurveAggressiveness,
    updateTokenVirtualLiquidityBuffer,
    updatePoolCurveAggressiveness,
    updatePoolVirtualLiquidityBuffer,
    updateLabelSettings,

    // Token functions (creation & management)
    createToken, // Step 1: Request token creation
    completeTokenCreation, // Step 2: Complete token creation
    createPrivateToken,
    transferTokenOwnership,
    updateTokenFees,
    updateTokenSocials,

    // Smart trading functions (auto-route based on migration)
    smartBuy,
    smartSell,
    smartDonateRewardPot,

    // Direct contract functions (for advanced use)
    buyTokens, // Pre-migration only
    sellTokens, // Pre-migration only
    donateToTokenRewardPot, // Pre-migration only
    buyFromPool, // Post-migration only
    sellToPool, // Post-migration only
    donateToPoolRewardPot, // Post-migration only

    // NFT functions
    mintOneOfOneNFT,

    // XFT functions
    createLabel,
    mintXFT,
    wrapXFT,
  };
};
