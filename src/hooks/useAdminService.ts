import { useState, useEffect, useCallback } from 'react'
import { useWallet } from '@suiet/wallet-kit'
import {
  getAdminService,
  AdminService,
  AdminStatus,
  TransactionResult,
  PoolSettingsForm,
  TokenSettingsForm,
  AdminError
} from '@/services/admin'

interface UseAdminServiceReturn {
  // Service instance
  adminService: AdminService | null
  
  // Status
  adminStatus: AdminStatus
  isLoading: boolean
  error: string | null
  
  // Actions
  initializePoolContract: (settings: PoolSettingsForm) => Promise<TransactionResult>
  initializeTokenContract: (settings: TokenSettingsForm) => Promise<TransactionResult>
  updatePoolSettings: (settings: PoolSettingsForm) => Promise<TransactionResult>
  updateTokenSettings: (settings: TokenSettingsForm) => Promise<TransactionResult>
  refreshStatus: () => Promise<void>
  clearError: () => void
  
  // Utilities
  formatSUI: (mist: number) => string
  formatBPS: (bps: number) => string
  isValidAddress: (address: string) => boolean
}

export const useAdminService = (): UseAdminServiceReturn => {
  const { connected, account } = useWallet()
  
  const [adminService, setAdminService] = useState<AdminService | null>(null)
  const [adminStatus, setAdminStatus] = useState<AdminStatus>({
    walletConnected: false,
    isAdmin: false,
    isSuperAdmin: false,
    poolInitialized: false,
    tokenInitialized: false
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize admin service when wallet connects
  useEffect(() => {
    const initializeService = async () => {
      if (wallet && publicKey && signTransaction && signAllTransactions) {
        try {
          setIsLoading(true)
          setError(null)

          // Create wallet provider
          const provider = {
            publicKey,
            signTransaction,
            signAllTransactions,
          }

          // Initialize admin service
          const service = getAdminService(connection)
          await service.initialize(provider)
          setAdminService(service)

          // Get initial status
          const status = await service.getAdminStatus()
          setAdminStatus(status)

        } catch (err) {
          console.error('Error initializing admin service:', err)
          setError('Failed to initialize admin service')
        } finally {
          setIsLoading(false)
        }
      } else {
        // Wallet disconnected
        setAdminService(null)
        setAdminStatus({
          walletConnected: false,
          isAdmin: false,
          isSuperAdmin: false,
          poolInitialized: false,
          tokenInitialized: false
        })
      }
    }

    initializeService()
  }, [wallet, publicKey, signTransaction, signAllTransactions, connection])

  // Refresh admin status
  const refreshStatus = useCallback(async () => {
    if (!adminService) return
    
    try {
      setIsLoading(true)
      const status = await adminService.getAdminStatus()
      setAdminStatus(status)
    } catch (err) {
      console.error('Error refreshing admin status:', err)
      setError('Failed to refresh admin status')
    } finally {
      setIsLoading(false)
    }
  }, [adminService])

  // Initialize pool contract
  const initializePoolContract = useCallback(async (settings: PoolSettingsForm): Promise<TransactionResult> => {
    if (!adminService) {
      return { success: false, error: 'Admin service not initialized' }
    }
    
    try {
      setIsLoading(true)
      setError(null)
      
      const result = await adminService.initializePoolContract(settings)
      
      if (result.success) {
        // Refresh status after successful initialization
        await refreshStatus()
      } else {
        setError(result.error || 'Pool initialization failed')
      }
      
      return result
    } catch (err) {
      const errorMessage = 'Pool initialization failed'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setIsLoading(false)
    }
  }, [adminService, refreshStatus])

  // Initialize token contract
  const initializeTokenContract = useCallback(async (settings: TokenSettingsForm): Promise<TransactionResult> => {
    if (!adminService) {
      return { success: false, error: 'Admin service not initialized' }
    }
    
    try {
      setIsLoading(true)
      setError(null)
      
      const result = await adminService.initializeTokenContract(settings)
      
      if (result.success) {
        // Refresh status after successful initialization
        await refreshStatus()
      } else {
        setError(result.error || 'Token initialization failed')
      }
      
      return result
    } catch (err) {
      const errorMessage = 'Token initialization failed'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setIsLoading(false)
    }
  }, [adminService, refreshStatus])

  // Update pool settings
  const updatePoolSettings = useCallback(async (settings: PoolSettingsForm): Promise<TransactionResult> => {
    if (!adminService) {
      return { success: false, error: 'Admin service not initialized' }
    }
    
    try {
      setIsLoading(true)
      setError(null)
      
      const result = await adminService.updatePoolSettings(settings)
      
      if (!result.success) {
        setError(result.error || 'Pool settings update failed')
      }
      
      return result
    } catch (err) {
      const errorMessage = 'Pool settings update failed'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setIsLoading(false)
    }
  }, [adminService])

  // Update token settings
  const updateTokenSettings = useCallback(async (settings: TokenSettingsForm): Promise<TransactionResult> => {
    if (!adminService) {
      return { success: false, error: 'Admin service not initialized' }
    }
    
    try {
      setIsLoading(true)
      setError(null)
      
      const result = await adminService.updateTokenSettings(settings)
      
      if (!result.success) {
        setError(result.error || 'Token settings update failed')
      }
      
      return result
    } catch (err) {
      const errorMessage = 'Token settings update failed'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setIsLoading(false)
    }
  }, [adminService])

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Utility functions
  const formatSOL = useCallback((lamports: number): string => {
    return AdminService.formatSOL(lamports)
  }, [])

  const formatBPS = useCallback((bps: number): string => {
    return AdminService.formatBPS(bps)
  }, [])

  const isValidAddress = useCallback((address: string): boolean => {
    return AdminService.isValidSolanaAddress(address)
  }, [])

  return {
    adminService,
    adminStatus,
    isLoading,
    error,
    initializePoolContract,
    initializeTokenContract,
    updatePoolSettings,
    updateTokenSettings,
    refreshStatus,
    clearError,
    formatSOL,
    formatBPS,
    isValidAddress
  }
}
