import { useState, useEffect, useCallback } from 'react'
import { 
  supabase, 
  TokenEvent, 
  TradingEvent, 
  RewardEvent,
  getTokenEvents,
  getTradingEvents,
  getRewardEvents,
  subscribeToTokenEvents,
  subscribeToTradingEvents,
  subscribeToRewardEvents
} from '@/lib/supabase'

// Hook for real-time token events
export function useTokenEvents(tokenAddress?: string, limit = 50) {
  const [events, setEvents] = useState<TokenEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load initial events
  useEffect(() => {
    const loadEvents = async () => {
      try {
        setLoading(true)
        const data = await getTokenEvents(tokenAddress, limit)
        setEvents(data || [])
        setError(null)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load events')
      } finally {
        setLoading(false)
      }
    }

    loadEvents()
  }, [tokenAddress, limit])

  // Subscribe to real-time updates
  useEffect(() => {
    const handleNewEvent = (payload: any) => {
      const newEvent = payload.new as TokenEvent
      
      // Filter by token address if specified
      if (tokenAddress && newEvent.token_address !== tokenAddress) {
        return
      }

      setEvents(prev => [newEvent, ...prev.slice(0, limit - 1)])
    }

    const subscription = subscribeToTokenEvents(handleNewEvent)

    return () => {
      subscription.unsubscribe()
    }
  }, [tokenAddress, limit])

  return { events, loading, error }
}

// Hook for real-time trading events
export function useTradingEvents(tokenAddress?: string, traderAddress?: string, limit = 50) {
  const [events, setEvents] = useState<TradingEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load initial events
  useEffect(() => {
    const loadEvents = async () => {
      try {
        setLoading(true)
        const data = await getTradingEvents(tokenAddress, traderAddress, limit)
        setEvents(data || [])
        setError(null)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load trading events')
      } finally {
        setLoading(false)
      }
    }

    loadEvents()
  }, [tokenAddress, traderAddress, limit])

  // Subscribe to real-time updates
  useEffect(() => {
    const handleNewEvent = (payload: any) => {
      const newEvent = payload.new as TradingEvent
      
      // Filter by token address if specified
      if (tokenAddress && newEvent.token_address !== tokenAddress) {
        return
      }

      // Filter by trader address if specified
      if (traderAddress && newEvent.trader_address !== traderAddress) {
        return
      }

      setEvents(prev => [newEvent, ...prev.slice(0, limit - 1)])
    }

    const subscription = subscribeToTradingEvents(handleNewEvent)

    return () => {
      subscription.unsubscribe()
    }
  }, [tokenAddress, traderAddress, limit])

  return { events, loading, error }
}

// Hook for real-time reward events
export function useRewardEvents(tokenAddress?: string, limit = 50) {
  const [events, setEvents] = useState<RewardEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load initial events
  useEffect(() => {
    const loadEvents = async () => {
      try {
        setLoading(true)
        const data = await getRewardEvents(tokenAddress, limit)
        setEvents(data || [])
        setError(null)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load reward events')
      } finally {
        setLoading(false)
      }
    }

    loadEvents()
  }, [tokenAddress, limit])

  // Subscribe to real-time updates
  useEffect(() => {
    const handleNewEvent = (payload: any) => {
      const newEvent = payload.new as RewardEvent
      
      // Filter by token address if specified
      if (tokenAddress && newEvent.token_address !== tokenAddress) {
        return
      }

      setEvents(prev => [newEvent, ...prev.slice(0, limit - 1)])
    }

    const subscription = subscribeToRewardEvents(handleNewEvent)

    return () => {
      subscription.unsubscribe()
    }
  }, [tokenAddress, limit])

  return { events, loading, error }
}

// Hook for combined real-time token data
export function useTokenData(tokenAddress: string) {
  const { events: tokenEvents, loading: tokenLoading } = useTokenEvents(tokenAddress, 10)
  const { events: tradingEvents, loading: tradingLoading } = useTradingEvents(tokenAddress, undefined, 20)
  const { events: rewardEvents, loading: rewardLoading } = useRewardEvents(tokenAddress, 10)

  const [tokenInfo, setTokenInfo] = useState<{
    name?: string
    symbol?: string
    creator?: string
    currentPrice?: number
    totalTrades?: number
    totalVolume?: number
    linkedLabelId?: number
  }>({})

  // Extract token info from events
  useEffect(() => {
    const creationEvent = tokenEvents.find(e => e.event_type === 'token_created')
    if (creationEvent) {
      setTokenInfo(prev => ({
        ...prev,
        name: creationEvent.token_name,
        symbol: creationEvent.token_symbol,
        creator: creationEvent.creator_address,
        linkedLabelId: creationEvent.linked_label_global_id
      }))
    }
  }, [tokenEvents])

  // Calculate trading statistics
  useEffect(() => {
    if (tradingEvents.length > 0) {
      const totalTrades = tradingEvents.length
      const totalVolume = tradingEvents.reduce((sum, event) => sum + (event.sui_amount || 0), 0)
      const latestEvent = tradingEvents[0]
      const currentPrice = latestEvent?.price_scaled || 0

      setTokenInfo(prev => ({
        ...prev,
        currentPrice: currentPrice / 1000000, // Convert from scaled price
        totalTrades,
        totalVolume: totalVolume / 1000000000 // Convert from MIST to SUI
      }))
    }
  }, [tradingEvents])

  const loading = tokenLoading || tradingLoading || rewardLoading

  return {
    tokenInfo,
    tokenEvents,
    tradingEvents,
    rewardEvents,
    loading
  }
}

// Hook for live price updates
export function useLivePrice(tokenAddress: string) {
  const [price, setPrice] = useState<number>(0)
  const [priceChange24h, setPriceChange24h] = useState<number>(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadInitialPrice = async () => {
      try {
        const events = await getTradingEvents(tokenAddress, undefined, 1)
        if (events && events.length > 0) {
          const latestPrice = events[0].price_scaled / 1000000 // Convert from scaled
          setPrice(latestPrice)
        }
        setLoading(false)
      } catch (error) {
        console.error('Error loading initial price:', error)
        setLoading(false)
      }
    }

    loadInitialPrice()
  }, [tokenAddress])

  // Subscribe to real-time price updates
  useEffect(() => {
    const handlePriceUpdate = (payload: any) => {
      const newEvent = payload.new as TradingEvent
      
      if (newEvent.token_address === tokenAddress) {
        const newPrice = newEvent.price_scaled / 1000000
        setPrice(prevPrice => {
          // Calculate 24h change (simplified - in production, you'd want proper 24h tracking)
          const change = prevPrice > 0 ? ((newPrice - prevPrice) / prevPrice) * 100 : 0
          setPriceChange24h(change)
          return newPrice
        })
      }
    }

    const subscription = subscribeToTradingEvents(handlePriceUpdate)

    return () => {
      subscription.unsubscribe()
    }
  }, [tokenAddress])

  return { price, priceChange24h, loading }
}

// Hook for event listener management
export function useEventListener() {
  const [isListening, setIsListening] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const startListener = useCallback(async () => {
    try {
      // Import the event listener dynamically to avoid SSR issues
      const { startEventListener } = await import('@/lib/eventListener')
      await startEventListener()
      setIsListening(true)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start event listener')
    }
  }, [])

  const stopListener = useCallback(async () => {
    try {
      // Event listener runs in background, no explicit stop needed
      setIsListening(false)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to stop event listener')
    }
  }, [])

  // Auto-start listener on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      startListener()
    }

    return () => {
      if (typeof window !== 'undefined') {
        stopListener()
      }
    }
  }, [startListener, stopListener])

  return { isListening, error, startListener, stopListener }
}
