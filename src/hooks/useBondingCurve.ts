import { useState, useEffect, useCallback, useMemo } from 'react'
import {
  calculateCurrentPrice,
  calculateTokensForSui,
  calculateSuiForTokens,
  calculatePriceImpact,
  mistToSui,
  suiToMist,
  tokensToHuman,
  humanToTokens,
  formatPrice,
  MIST_PER_SUI,
  TOKENS_PER_UNIT,
  // Legacy imports for backward compatibility
  calculateTokensForSol,
  calculateSolForTokens,
  lamportsToSol,
  solToLamports
} from '@/services/pricing/bondingCurve'

export interface TokenInfo {
  mint: string
  totalSupply: bigint
  circulatingSupply: bigint
  liquiditySolAmount: bigint // Keep for backward compatibility
  liquiditySuiAmount?: bigint // New SUI field
  currentPrice?: bigint
}

export interface PriceQuote {
  solAmount: number // Keep for backward compatibility
  suiAmount?: number // New SUI field
  tokenAmount: number
  pricePerToken: number
  priceImpact: number
  slippage: number
  minimumReceived: number
}

export interface UseBondingCurveReturn {
  // Current token state
  tokenInfo: TokenInfo | null
  currentPrice: number
  marketCap: number

  // Quote calculations (SUI)
  getBuyQuote: (suiAmount: number) => PriceQuote
  getSellQuote: (tokenAmount: number) => PriceQuote

  // Legacy quote calculations (SOL) - for backward compatibility
  getBuyQuoteSol?: (solAmount: number) => PriceQuote
  getSellQuoteSol?: (tokenAmount: number) => PriceQuote

  // Real-time calculations (SUI)
  calculateTokensForSui: (suiAmount: number) => number
  calculateSuiForTokens: (tokenAmount: number) => number

  // Legacy calculations (SOL) - for backward compatibility
  calculateTokensForSol: (solAmount: number) => number
  calculateSolForTokens: (tokenAmount: number) => number

  // Price information
  priceImpact: (suiAmount: number) => number
  nextPrice: (suiAmount: number) => number

  // Validation
  isValidBuyAmount: (suiAmount: number) => boolean
  isValidSellAmount: (tokenAmount: number) => boolean

  // Formatting helpers
  formatSui: (amount: number) => string
  formatSol: (amount: number) => string // Legacy
  formatTokens: (amount: number) => string
  formatPrice: (price: number) => string
}

export const useBondingCurve = (
  tokenInfo: TokenInfo | null,
  slippageTolerance: number = 0.5 // Default 0.5% slippage
): UseBondingCurveReturn => {
  const [currentPrice, setCurrentPrice] = useState<number>(0)
  const [marketCap, setMarketCap] = useState<number>(0)

  // Calculate current price when token info changes
  useEffect(() => {
    if (!tokenInfo) {
      setCurrentPrice(0)
      setMarketCap(0)
      return
    }

    const priceScaled = calculateCurrentPrice(
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )
    
    const price = formatPrice(priceScaled)
    setCurrentPrice(price)
    
    // Market cap = circulating supply * current price
    const circulatingTokens = tokensToHuman(tokenInfo.circulatingSupply)
    setMarketCap(circulatingTokens * price)
  }, [tokenInfo])

  // Get buy quote for SUI amount
  const getBuyQuote = useCallback((suiAmount: number): PriceQuote => {
    if (!tokenInfo || suiAmount <= 0) {
      return {
        solAmount: 0,
        suiAmount: 0,
        tokenAmount: 0,
        pricePerToken: 0,
        priceImpact: 0,
        slippage: 0,
        minimumReceived: 0
      }
    }

    const suiMist = suiToMist(suiAmount)
    const currentPriceScaled = calculateCurrentPrice(
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )

    const tokensOut = calculateTokensForSui(
      suiMist,
      currentPriceScaled,
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )

    const tokenAmount = tokensToHuman(tokensOut)
    const pricePerToken = suiAmount / tokenAmount

    const priceImpactPercent = calculatePriceImpact(
      suiMist,
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )

    const minimumReceived = tokenAmount * (1 - slippageTolerance / 100)

    return {
      solAmount: suiAmount, // For backward compatibility
      suiAmount,
      tokenAmount,
      pricePerToken,
      priceImpact: priceImpactPercent,
      slippage: slippageTolerance,
      minimumReceived
    }
  }, [tokenInfo, slippageTolerance])

  // Get sell quote for token amount
  const getSellQuote = useCallback((tokenAmount: number): PriceQuote => {
    if (!tokenInfo || tokenAmount <= 0) {
      return {
        solAmount: 0,
        suiAmount: 0,
        tokenAmount: 0,
        pricePerToken: 0,
        priceImpact: 0,
        slippage: 0,
        minimumReceived: 0
      }
    }

    const tokens = humanToTokens(tokenAmount)
    const currentPriceScaled = calculateCurrentPrice(
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )

    // For selling, we need to calculate price after reducing supply
    const newSupply = tokenInfo.circulatingSupply - tokens
    const suiOut = calculateSuiForTokens(
      tokens,
      currentPriceScaled,
      newSupply, // Use reduced supply for sell calculation
      tokenInfo.totalSupply
    )

    const suiAmount = mistToSui(suiOut)
    const pricePerToken = suiAmount / tokenAmount

    // Calculate price impact (negative for sells)
    const newPrice = calculateCurrentPrice(newSupply, tokenInfo.totalSupply)
    const priceImpactPercent = (Number(currentPriceScaled - newPrice) / Number(currentPriceScaled)) * 100

    const minimumReceived = suiAmount * (1 - slippageTolerance / 100)

    return {
      solAmount: suiAmount, // For backward compatibility
      suiAmount,
      tokenAmount,
      pricePerToken,
      priceImpact: -priceImpactPercent, // Negative for sells
      slippage: slippageTolerance,
      minimumReceived
    }
  }, [tokenInfo, slippageTolerance])

  // Calculate tokens for SOL (simple calculation)
  const calculateTokensForSolAmount = useCallback((solAmount: number): number => {
    if (!tokenInfo || solAmount <= 0) return 0
    
    const solLamports = solToLamports(solAmount)
    const currentPriceScaled = calculateCurrentPrice(
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )
    
    const tokensOut = calculateTokensForSol(
      solLamports,
      currentPriceScaled,
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )
    
    return tokensToHuman(tokensOut)
  }, [tokenInfo])

  // Calculate SOL for tokens (simple calculation)
  const calculateSolForTokensAmount = useCallback((tokenAmount: number): number => {
    if (!tokenInfo || tokenAmount <= 0) return 0
    
    const tokens = humanToTokens(tokenAmount)
    const currentPriceScaled = calculateCurrentPrice(
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )
    
    const newSupply = tokenInfo.circulatingSupply - tokens
    const solOut = calculateSolForTokens(
      tokens,
      currentPriceScaled,
      newSupply,
      tokenInfo.totalSupply
    )
    
    return lamportsToSol(solOut)
  }, [tokenInfo])

  // Calculate price impact for a given SOL amount
  const priceImpactForAmount = useCallback((solAmount: number): number => {
    if (!tokenInfo || solAmount <= 0) return 0
    
    const solLamports = solToLamports(solAmount)
    return calculatePriceImpact(
      solLamports,
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )
  }, [tokenInfo])

  // Calculate next price after a purchase
  const nextPriceAfterPurchase = useCallback((solAmount: number): number => {
    if (!tokenInfo || solAmount <= 0) return currentPrice
    
    const solLamports = solToLamports(solAmount)
    const currentPriceScaled = calculateCurrentPrice(
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )
    
    const tokensOut = calculateTokensForSol(
      solLamports,
      currentPriceScaled,
      tokenInfo.circulatingSupply,
      tokenInfo.totalSupply
    )
    
    const newSupply = tokenInfo.circulatingSupply + tokensOut
    const newPriceScaled = calculateCurrentPrice(newSupply, tokenInfo.totalSupply)
    
    return formatPrice(newPriceScaled)
  }, [tokenInfo, currentPrice])

  // Validation functions
  const isValidBuyAmount = useCallback((solAmount: number): boolean => {
    if (!tokenInfo || solAmount <= 0) return false
    
    // Check if amount is reasonable (not too large to cause overflow)
    const maxSol = 1000 // 1000 SOL max per transaction
    return solAmount <= maxSol
  }, [tokenInfo])

  const isValidSellAmount = useCallback((tokenAmount: number): boolean => {
    if (!tokenInfo || tokenAmount <= 0) return false
    
    // Check if user has enough tokens (this would need user balance)
    // For now, just check if amount is reasonable
    const tokens = humanToTokens(tokenAmount)
    return tokens <= tokenInfo.circulatingSupply
  }, [tokenInfo])

  // Formatting helpers
  const formatSolAmount = useCallback((amount: number): string => {
    if (amount < 0.001) {
      return `${(amount * 1000).toFixed(3)} mSOL`
    }
    return `${amount.toFixed(6)} SOL`
  }, [])

  const formatTokenAmount = useCallback((amount: number): string => {
    if (amount >= 1_000_000) {
      return `${(amount / 1_000_000).toFixed(2)}M`
    }
    if (amount >= 1_000) {
      return `${(amount / 1_000).toFixed(2)}K`
    }
    return amount.toFixed(2)
  }, [])

  const formatPriceAmount = useCallback((price: number): string => {
    if (price < 0.000001) {
      return `${(price * 1_000_000_000).toFixed(2)} nSOL`
    }
    if (price < 0.001) {
      return `${(price * 1_000_000).toFixed(2)} μSOL`
    }
    return `${price.toFixed(8)} SOL`
  }, [])

  return {
    tokenInfo,
    currentPrice,
    marketCap,
    getBuyQuote,
    getSellQuote,
    calculateTokensForSol: calculateTokensForSolAmount,
    calculateSolForTokens: calculateSolForTokensAmount,
    priceImpact: priceImpactForAmount,
    nextPrice: nextPriceAfterPurchase,
    isValidBuyAmount,
    isValidSellAmount,
    formatSol: formatSolAmount,
    formatTokens: formatTokenAmount,
    formatPrice: formatPriceAmount
  }
}
