import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  getSuiEventService, 
  DexstaEvent, 
  TokenEvent, 
  PoolEvent, 
  AdminEvent 
} from '@/services/events/suiEventService'

export interface UseSuiEventsOptions {
  eventTypes?: string[]
  autoStart?: boolean
  tokenAddress?: string
  poolAddress?: string
}

export interface UseSuiEventsReturn {
  events: DexstaEvent[]
  tokenEvents: TokenEvent[]
  poolEvents: PoolEvent[]
  adminEvents: AdminEvent[]
  isListening: boolean
  startListening: () => void
  stopListening: () => void
  clearEvents: () => void
  subscriptionId: string | null
}

export function useSuiEvents(options: UseSuiEventsOptions = {}): UseSuiEventsReturn {
  const {
    eventTypes = [],
    autoStart = true,
    tokenAddress,
    poolAddress
  } = options

  const [events, setEvents] = useState<DexstaEvent[]>([])
  const [isListening, setIsListening] = useState(false)
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null)
  
  const eventServiceRef = useRef(getSuiEventService())
  const eventsRef = useRef<DexstaEvent[]>([])

  // Update events ref when events change
  useEffect(() => {
    eventsRef.current = events
  }, [events])

  // Event handler
  const handleEvent = useCallback((event: DexstaEvent) => {
    // Filter by token address if specified
    if (tokenAddress && 'tokenAddress' in event && event.tokenAddress !== tokenAddress) {
      return
    }

    // Filter by pool address if specified
    if (poolAddress && 'poolAddress' in event && event.poolAddress !== poolAddress) {
      return
    }

    setEvents(prevEvents => {
      // Avoid duplicates based on transaction digest and event type
      const isDuplicate = prevEvents.some(existingEvent => 
        existingEvent.transactionDigest === event.transactionDigest &&
        existingEvent.type === event.type
      )

      if (isDuplicate) {
        return prevEvents
      }

      // Add new event and sort by timestamp (newest first)
      const newEvents = [event, ...prevEvents]
      return newEvents.sort((a, b) => b.timestamp - a.timestamp).slice(0, 100) // Keep last 100 events
    })
  }, [tokenAddress, poolAddress])

  // Start listening for events
  const startListening = useCallback(async () => {
    if (isListening) return

    try {
      const eventService = eventServiceRef.current
      
      // Subscribe to events
      const subId = eventService.subscribe(eventTypes, handleEvent)
      setSubscriptionId(subId)
      
      // Start the event service if not already started
      await eventService.startEventListening()
      
      setIsListening(true)
    } catch (error) {
      console.error('Error starting event listening:', error)
    }
  }, [isListening, eventTypes, handleEvent])

  // Stop listening for events
  const stopListening = useCallback(() => {
    if (!isListening || !subscriptionId) return

    try {
      const eventService = eventServiceRef.current
      eventService.unsubscribe(subscriptionId)
      setSubscriptionId(null)
      setIsListening(false)
    } catch (error) {
      console.error('Error stopping event listening:', error)
    }
  }, [isListening, subscriptionId])

  // Clear events
  const clearEvents = useCallback(() => {
    setEvents([])
  }, [])

  // Auto-start if enabled
  useEffect(() => {
    if (autoStart) {
      startListening()
    }

    // Cleanup on unmount
    return () => {
      if (subscriptionId) {
        stopListening()
      }
    }
  }, [autoStart, startListening, stopListening, subscriptionId])

  // Load historical events for specific token/pool
  useEffect(() => {
    const loadHistoricalEvents = async () => {
      const eventService = eventServiceRef.current

      try {
        if (tokenAddress) {
          const tokenEvents = await eventService.getTokenEvents(tokenAddress, 20)
          setEvents(prevEvents => {
            const combined = [...tokenEvents, ...prevEvents]
            const unique = combined.filter((event, index, self) => 
              index === self.findIndex(e => 
                e.transactionDigest === event.transactionDigest && e.type === event.type
              )
            )
            return unique.sort((a, b) => b.timestamp - a.timestamp).slice(0, 100)
          })
        }

        if (poolAddress) {
          const poolEvents = await eventService.getPoolEvents(poolAddress, 20)
          setEvents(prevEvents => {
            const combined = [...poolEvents, ...prevEvents]
            const unique = combined.filter((event, index, self) => 
              index === self.findIndex(e => 
                e.transactionDigest === event.transactionDigest && e.type === event.type
              )
            )
            return unique.sort((a, b) => b.timestamp - a.timestamp).slice(0, 100)
          })
        }
      } catch (error) {
        console.error('Error loading historical events:', error)
      }
    }

    if (tokenAddress || poolAddress) {
      loadHistoricalEvents()
    }
  }, [tokenAddress, poolAddress])

  // Separate events by type for convenience
  const tokenEvents = events.filter((event): event is TokenEvent => 
    'tokenAddress' in event
  )

  const poolEvents = events.filter((event): event is PoolEvent => 
    'poolAddress' in event
  )

  const adminEvents = events.filter((event): event is AdminEvent => 
    'contractType' in event
  )

  return {
    events,
    tokenEvents,
    poolEvents,
    adminEvents,
    isListening,
    startListening,
    stopListening,
    clearEvents,
    subscriptionId
  }
}

// Hook for token-specific events
export function useTokenEvents(tokenAddress: string) {
  return useSuiEvents({
    eventTypes: ['TokenCreated', 'TokenSwapped', 'TokenMigrated', 'RewardClaimed'],
    tokenAddress,
    autoStart: true
  })
}

// Hook for pool-specific events
export function usePoolEvents(poolAddress: string) {
  return useSuiEvents({
    eventTypes: ['PoolCreated', 'LiquidityAdded', 'LiquidityRemoved', 'PoolSwap'],
    poolAddress,
    autoStart: true
  })
}

// Hook for admin events
export function useAdminEvents() {
  return useSuiEvents({
    eventTypes: ['PlatformSettingsUpdated', 'AdminAdded', 'AdminRemoved', 'PlatformPaused'],
    autoStart: true
  })
}

// Hook for real-time trading activity
export function useTradingActivity() {
  const { tokenEvents } = useSuiEvents({
    eventTypes: ['TokenSwapped', 'RewardClaimed'],
    autoStart: true
  })

  const swapEvents = tokenEvents.filter(event => event.type === 'TokenSwapped')
  const rewardEvents = tokenEvents.filter(event => event.type === 'RewardClaimed')

  return {
    swapEvents,
    rewardEvents,
    allTradingEvents: tokenEvents
  }
}
