/**
 * Event listener service for Dexsta platform
 * 
 * Listens to blockchain events and updates the database accordingly
 */

import { SuiClient } from '@mysten/sui/client'
import { createTokenRecord, updateTokenMarketData } from '@/lib/database'
import { DEXSTA_PACKAGE_ID } from '@/constants/contracts'
import { getDeploymentClient } from '@/services/deploymentClient'

import { getFullnodeUrl } from '@mysten/sui/client'

const network = (process.env.NEXT_PUBLIC_SUI_NETWORK || 'devnet') as 'devnet' | 'testnet' | 'mainnet'
const suiClient = new SuiClient({
  url: getFullnodeUrl(network)
})

console.log(`Event listener using ${network} network`)

// Initialize deployment client service
const deploymentClient = getDeploymentClient()

interface TokenModuleDeploymentRequestedEvent {
  id: {
    txDigest: string
    eventSeq: string
  }
  packageId: string
  transactionModule: string
  sender: string
  type: string
  parsedJson: {
    token_id: string
    name: string
    symbol: string
    description: string
    icon_url: string
    website: string
    twitter: string
    telegram: string
    tiktok: string
    total_supply: string
    decimals: number
    buy_fee_bps: number
    sell_fee_bps: number
    creator: string
  }
  timestampMs: string
}

interface TokenCreatedEvent {
  id: {
    txDigest: string
    eventSeq: string
  }
  packageId: string
  transactionModule: string
  sender: string
  type: string
  parsedJson: {
    token_id: string
    name: string
    symbol: string
    description: string
    icon_url: string
    metadata_url?: string
    website: string
    twitter: string
    telegram: string
    tiktok: string
    total_supply: string
    buy_fee_bps: number
    sell_fee_bps: number
    creator: string
    linked_label_id?: string
    is_private_pool: boolean
    required_nft_id?: string
  }
  timestampMs: string
}

interface TokenSwappedEvent {
  id: {
    txDigest: string
    eventSeq: string
  }
  packageId: string
  transactionModule: string
  sender: string
  type: string
  parsedJson: {
    token_id: string
    trader: string
    is_buy: boolean
    sui_amount: string
    token_amount: string
    new_price: string
    trade_count: number
  }
  timestampMs: string
}

/**
 * Process TokenModuleDeploymentRequested events and trigger Node.js deployment
 */
async function handleTokenModuleDeploymentRequestedEvent(event: TokenModuleDeploymentRequestedEvent) {
  try {
    console.log('🎯 Processing TokenModuleDeploymentRequested event:', event.id.eventSeq)

    const eventData = event.parsedJson
    console.log('📋 Deployment request for token:', eventData.token_symbol)

    // Step 1: Validate that the original transaction succeeded
    console.log('🔍 Validating original transaction:', event.id.txDigest)

    try {
      const txDetails = await suiClient.getTransactionBlock({
        digest: event.id.txDigest,
        options: {
          showEffects: true,
          showEvents: true,
        }
      })

      // Check if transaction was successful
      if (txDetails.effects?.status?.status !== 'success') {
        console.log('❌ Original transaction failed, skipping deployment for:', eventData.token_symbol)
        console.log('Transaction status:', txDetails.effects?.status)
        return
      }

      console.log('✅ Original transaction succeeded, proceeding with deployment')

    } catch (txError) {
      console.error('❌ Could not validate original transaction:', txError)
      console.log('⚠️ Skipping deployment due to validation failure')
      return
    }

    // Step 2: Check if token was already completed
    console.log('🔍 Checking if token already completed:', eventData.token_symbol)

    try {
      const completionEventQuery = {
        MoveEventType: `${DEXSTA_PACKAGE_ID}::dexsta_token::TokenCreated`
      }

      const completionEvents = await suiClient.queryEvents({
        query: completionEventQuery,
        limit: 50,
        order: 'descending'
      })

      const alreadyCompleted = completionEvents.data?.find(e =>
        e.parsedJson?.symbol === eventData.token_symbol
      )

      if (alreadyCompleted) {
        console.log('✅ Token already completed, skipping deployment for:', eventData.token_symbol)
        return
      }

      console.log('🚀 Token not yet completed, proceeding with deployment')

    } catch (completionError) {
      console.warn('⚠️ Could not check completion status:', completionError)
      // Continue with deployment if check fails
    }

    // Option 1: Use RPC deployment API (no CLI required)
    console.log('🚀 Starting RPC-based module deployment for token:', eventData.token_symbol)

    try {
      const rpcResponse = await fetch('/api/deploy-rpc', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenSymbol: eventData.token_symbol,
          tokenName: eventData.token_name,
          tokenDescription: eventData.token_description || 'Token created via Dexsta platform',
          iconUrl: eventData.icon_url || '',
          decimals: 9,
          creator: eventData.creator
        })
      })

      if (rpcResponse.ok) {
        const rpcResult = await rpcResponse.json()
        console.log('✅ RPC deployment completed successfully!')
        console.log('📦 Package ID:', rpcResult.packageId)
        console.log('🏛️ Treasury Cap:', rpcResult.treasuryCapId)
        console.log('📋 Coin Metadata:', rpcResult.coinMetadataId)

        // Update database with deployment results
        await updateTokenDeploymentStatus(eventData.token_symbol, {
          status: 'completed',
          packageId: rpcResult.packageId,
          treasuryCapId: rpcResult.treasuryCapId,
          coinMetadataId: rpcResult.coinMetadataId,
          deploymentMethod: 'rpc'
        })
        return // Success, no need for fallbacks

      } else {
        console.error('❌ RPC deployment failed, status:', rpcResponse.status)
        const errorData = await rpcResponse.json()
        console.error('RPC error details:', errorData)
      }
    } catch (rpcError) {
      console.error('❌ RPC deployment error:', rpcError)
    }

    // Option 2: Fallback to API deployment (requires Sui CLI on server)
    console.log('🔄 Falling back to API-based deployment...')
    const result = await deploymentClient.deployToken(eventData.token_symbol)

    if (result.success) {
      console.log('✅ API deployment completed successfully!')
      console.log('📦 Package ID:', result.packageId)
      console.log('🏛️ Treasury Cap:', result.treasuryCapId)
      console.log('📋 Coin Metadata:', result.coinMetadataId)

      // Update database with deployment results
      await updateTokenDeploymentStatus(eventData.token_symbol, {
        status: 'completed',
        packageId: result.packageId,
        treasuryCapId: result.treasuryCapId,
        coinMetadataId: result.coinMetadataId,
        deploymentMethod: 'api'
      })

    } else {
      console.error('❌ API deployment failed:', result.error)

      // Update database with failure
      await updateTokenDeploymentStatus(eventData.token_symbol, {
        status: 'failed',
        error: result.error
      })

      // Option 3: Final fallback to Node.js script
      console.log('🔄 Final fallback to Node.js deployment script...')
      await triggerNodeJSDeployment(eventData.token_symbol)
    }

  } catch (error) {
    console.error('❌ Failed to process TokenModuleDeploymentRequested event:', error)

    // Only update database with error if we have eventData
    if (eventData?.token_symbol) {
      await updateTokenDeploymentStatus(eventData.token_symbol, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
}

/**
 * Trigger Node.js deployment script as fallback
 */
async function triggerNodeJSDeployment(tokenSymbol: string) {
  try {
    console.log('📜 Triggering Node.js deployment script for:', tokenSymbol)

    // Call the Node.js deployment API endpoint
    const response = await fetch('/api/deploy-nodejs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ tokenSymbol }),
    })

    const result = await response.json()

    if (response.ok && result.success) {
      console.log('✅ Node.js deployment completed successfully!')

      await updateTokenDeploymentStatus(tokenSymbol, {
        status: 'completed',
        packageId: result.packageId,
        treasuryCapId: result.treasuryCapId,
        coinMetadataId: result.coinMetadataId,
        deploymentMethod: 'nodejs'
      })
    } else {
      console.error('❌ Node.js deployment failed:', result.error)

      await updateTokenDeploymentStatus(tokenSymbol, {
        status: 'failed',
        error: result.error,
        deploymentMethod: 'nodejs'
      })
    }

  } catch (error) {
    console.error('❌ Node.js deployment script failed:', error)
  }
}

/**
 * Update token deployment status in database
 */
async function updateTokenDeploymentStatus(tokenSymbol: string, status: {
  status: 'pending' | 'completed' | 'failed' | 'error'
  packageId?: string
  treasuryCapId?: string
  coinMetadataId?: string
  error?: string
  deploymentMethod?: 'api' | 'nodejs'
}) {
  try {
    // Call database update API
    await fetch('/api/update-deployment-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenSymbol,
        ...status
      }),
    })

    console.log('📊 Database updated for token:', tokenSymbol, 'Status:', status.status)
  } catch (error) {
    console.error('❌ Failed to update database:', error)
  }
}

/**
 * Process TokenCreated events and save to database
 */
async function handleTokenCreatedEvent(event: TokenCreatedEvent) {
  try {
    console.log('Processing TokenCreated event:', event)

    const tokenData = {
      name: event.parsedJson.name,
      symbol: event.parsedJson.symbol,
      description: event.parsedJson.description,
      image_url: event.parsedJson.icon_url,
      metadata_url: event.parsedJson.metadata_url || '',
      website: event.parsedJson.website,
      twitter: event.parsedJson.twitter,
      telegram: event.parsedJson.telegram,
      tiktok: event.parsedJson.tiktok,
      total_supply: event.parsedJson.total_supply,
      buy_fee_bps: event.parsedJson.buy_fee_bps,
      sell_fee_bps: event.parsedJson.sell_fee_bps,
      creator_address: event.parsedJson.creator,
      token_object_id: event.parsedJson.token_id,
      linked_label_id: event.parsedJson.linked_label_id,
      is_private_pool: event.parsedJson.is_private_pool,
      required_nft_id: event.parsedJson.required_nft_id,
      migration_status: 'pre-migration' as const,
      // Initialize market data
      current_price: 0,
      price_change_24h: 0,
      volume_24h: 0,
      market_cap: 0,
      holders_count: 1 // Creator is the first holder
    }

    await createTokenRecord(tokenData)
    console.log('Token saved to database:', tokenData.symbol)
  } catch (error) {
    console.error('Failed to process TokenCreated event:', error)
  }
}

/**
 * Process TokenSwapped events and update market data
 */
async function handleTokenSwappedEvent(event: TokenSwappedEvent) {
  try {
    console.log('Processing TokenSwapped event:', event)

    // Update market data based on the trade
    const marketData = {
      current_price: parseFloat(event.parsedJson.new_price),
      volume_24h: parseFloat(event.parsedJson.sui_amount), // This should be accumulated
      // TODO: Calculate price change, market cap, etc.
    }

    await updateTokenMarketData(event.parsedJson.token_id, marketData)
    console.log('Token market data updated:', event.parsedJson.token_id)
  } catch (error) {
    console.error('Failed to process TokenSwapped event:', error)
  }
}

/**
 * Start listening to events from the Dexsta package
 */
export async function startEventListener() {
  try {
    console.log('Starting event listener for package:', DEXSTA_PACKAGE_ID)
    console.log('👂 Starting polling for TokenModuleDeploymentRequested events...')

    let isListening = true
    let lastProcessedEventSeq: string | null = null

    // Poll for events every 10 seconds
    const pollInterval = 10000

    const pollForEvents = async () => {
      while (isListening) {
        try {
          // Query for TokenModuleDeploymentRequested events
          const deploymentEventQuery = {
            MoveEventType: `${DEXSTA_PACKAGE_ID}::dexsta_token::TokenModuleDeploymentRequested`
          }

          const deploymentEvents = await suiClient.queryEvents({
            query: deploymentEventQuery,
            limit: 10,
            order: 'descending'
          })

          if (deploymentEvents.data && deploymentEvents.data.length > 0) {
            // Process new events (skip already processed ones)
            for (const event of deploymentEvents.data.reverse()) {
              if (lastProcessedEventSeq && event.id.eventSeq <= lastProcessedEventSeq) {
                continue
              }

              await handleTokenModuleDeploymentRequestedEvent(event as TokenModuleDeploymentRequestedEvent)
              lastProcessedEventSeq = event.id.eventSeq
            }
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
        } catch (error) {
          console.error('❌ Error in event polling:', error)
          await new Promise(resolve => setTimeout(resolve, pollInterval))
        }
      }
    }

    // Start polling in background
    pollForEvents()

    const unsubscribe = () => {
      console.log('🛑 Event listener unsubscribed')
      isListening = false
    }

    console.log('✅ Event listener started successfully (polling mode)')
    return unsubscribe
  } catch (error) {
    console.error('Failed to start event listener:', error)
    throw error
  }
}

/**
 * Query historical events and sync to database
 */
export async function syncHistoricalEvents(fromCheckpoint?: string) {
  try {
    console.log('Syncing historical events...')

    // For now, disable historical event syncing to avoid API issues
    // This can be re-enabled once the correct query format is determined
    console.log('Historical event syncing temporarily disabled')
    return

    /*
    const events = await suiClient.queryEvents({
      query: { Package: DEXSTA_PACKAGE_ID },
      cursor: fromCheckpoint,
      limit: 100,
      order: 'ascending'
    })

    for (const event of events.data) {
      const eventType = event.type

      if (eventType.includes('TokenCreated')) {
        await handleTokenCreatedEvent(event as TokenCreatedEvent)
      } else if (eventType.includes('TokenSwapped')) {
        await handleTokenSwappedEvent(event as TokenSwappedEvent)
      }
    }

    console.log(`Synced ${events.data.length} historical events`)

    // If there are more events, recursively sync
    if (events.hasNextPage && events.nextCursor) {
      await syncHistoricalEvents(events.nextCursor)
    }
    */
  } catch (error) {
    console.error('Failed to sync historical events:', error)
    // Don't throw error to prevent app crashes
    console.log('Continuing without historical event sync')
  }
}

/**
 * Initialize event listening system
 */
export async function initializeEventSystem() {
  try {
    // First sync any historical events we might have missed
    await syncHistoricalEvents()
    
    // Then start listening for new events
    const unsubscribe = await startEventListener()
    
    return unsubscribe
  } catch (error) {
    console.error('Failed to initialize event system:', error)
    throw error
  }
}

/**
 * Get the latest checkpoint for resuming event listening
 */
export async function getLatestCheckpoint(): Promise<string | undefined> {
  try {
    const checkpoint = await suiClient.getLatestCheckpointSequenceNumber()
    return checkpoint
  } catch (error) {
    console.error('Failed to get latest checkpoint:', error)
    return undefined
  }
}
