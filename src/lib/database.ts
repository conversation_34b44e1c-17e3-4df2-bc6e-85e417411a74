/**
 * Database service for Dexsta platform
 * 
 * Handles all database operations using Supabase
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseKey)

// Token interface matching our database schema
export interface Token {
  id: string
  name: string
  symbol: string
  description: string
  image_url?: string
  metadata_url?: string
  website?: string
  twitter?: string
  telegram?: string
  tiktok?: string
  total_supply: string
  buy_fee_bps: number
  sell_fee_bps: number
  creator_address: string
  token_object_id: string
  pool_object_id?: string
  current_price?: number
  price_change_24h?: number
  volume_24h?: number
  market_cap?: number
  holders_count?: number
  created_at: string
  updated_at: string
  // Advanced features
  linked_label_id?: string
  is_private_pool: boolean
  required_nft_id?: string
  migration_status: 'pre-migration' | 'migrated'
}

/**
 * Fetch all tokens with optional filtering
 */
export async function getTokens(options?: {
  limit?: number
  offset?: number
  sortBy?: 'created_at' | 'market_cap' | 'volume_24h' | 'price_change_24h'
  sortOrder?: 'asc' | 'desc'
  category?: string
  search?: string
}): Promise<{ tokens: Token[]; total: number }> {
  try {
    let query = supabase
      .from('tokens')
      .select('*', { count: 'exact' })

    // Apply search filter
    if (options?.search) {
      query = query.or(`name.ilike.%${options.search}%,symbol.ilike.%${options.search}%`)
    }

    // Apply sorting
    if (options?.sortBy) {
      query = query.order(options.sortBy, { 
        ascending: options.sortOrder === 'asc' 
      })
    } else {
      // Default sort by creation date (newest first)
      query = query.order('created_at', { ascending: false })
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit)
    }
    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1)
    }

    const { data, error, count } = await query

    if (error) {
      console.error('Error fetching tokens:', error)
      throw error
    }

    return {
      tokens: data || [],
      total: count || 0
    }
  } catch (error) {
    console.error('Database error:', error)
    throw error
  }
}

/**
 * Get a single token by ID
 */
export async function getToken(id: string): Promise<Token | null> {
  try {
    const { data, error } = await supabase
      .from('tokens')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching token:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Database error:', error)
    return null
  }
}

/**
 * Create a new token record
 */
export async function createTokenRecord(tokenData: Omit<Token, 'id' | 'created_at' | 'updated_at'>): Promise<Token> {
  try {
    const { data, error } = await supabase
      .from('tokens')
      .insert([tokenData])
      .select()
      .single()

    if (error) {
      console.error('Error creating token:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Database error:', error)
    throw error
  }
}

/**
 * Update token market data (price, volume, etc.)
 */
export async function updateTokenMarketData(
  tokenId: string, 
  marketData: {
    current_price?: number
    price_change_24h?: number
    volume_24h?: number
    market_cap?: number
    holders_count?: number
  }
): Promise<Token | null> {
  try {
    const { data, error } = await supabase
      .from('tokens')
      .update({
        ...marketData,
        updated_at: new Date().toISOString()
      })
      .eq('id', tokenId)
      .select()
      .single()

    if (error) {
      console.error('Error updating token market data:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Database error:', error)
    return null
  }
}

/**
 * Get trending tokens (highest price change in 24h)
 */
export async function getTrendingTokens(limit: number = 10): Promise<Token[]> {
  try {
    const { data, error } = await supabase
      .from('tokens')
      .select('*')
      .order('price_change_24h', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching trending tokens:', error)
      throw error
    }

    return data || []
  } catch (error) {
    console.error('Database error:', error)
    return []
  }
}

/**
 * Get featured tokens (manually curated or highest market cap)
 */
export async function getFeaturedTokens(limit: number = 5): Promise<Token[]> {
  try {
    const { data, error } = await supabase
      .from('tokens')
      .select('*')
      .order('market_cap', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching featured tokens:', error)
      throw error
    }

    return data || []
  } catch (error) {
    console.error('Database error:', error)
    return []
  }
}

/**
 * Get tokens by creator address
 */
export async function getTokensByCreator(creatorAddress: string): Promise<Token[]> {
  try {
    const { data, error } = await supabase
      .from('tokens')
      .select('*')
      .eq('creator_address', creatorAddress)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching creator tokens:', error)
      throw error
    }

    return data || []
  } catch (error) {
    console.error('Database error:', error)
    return []
  }
}

/**
 * Search tokens by name or symbol
 */
export async function searchTokens(query: string, limit: number = 20): Promise<Token[]> {
  try {
    const { data, error } = await supabase
      .from('tokens')
      .select('*')
      .or(`name.ilike.%${query}%,symbol.ilike.%${query}%`)
      .order('market_cap', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error searching tokens:', error)
      throw error
    }

    return data || []
  } catch (error) {
    console.error('Database error:', error)
    return []
  }
}
