/**
 * Dexsta Animation Variants
 * Mobile-first animation system with physics-based transitions
 */

export const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
}

export const fadeInDown = {
  initial: { opacity: 0, y: -20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 20 }
}

export const scaleIn = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.8 }
}

export const slideInFromRight = {
  initial: { opacity: 0, x: 100 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 100 }
}

export const slideInFromLeft = {
  initial: { opacity: 0, x: -100 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -100 }
}

export const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

export const staggerItem = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 }
}

// Spring physics configurations
export const springConfig = {
  type: "spring" as const,
  stiffness: 400,
  damping: 25
}

export const gentleSpring = {
  type: "spring" as const,
  stiffness: 200,
  damping: 20
}

export const bouncySpring = {
  type: "spring" as const,
  stiffness: 600,
  damping: 15
}

// Button interaction animations
export const buttonHover = {
  scale: 1.05,
  rotate: 2,
  transition: springConfig
}

export const buttonTap = {
  scale: 0.95,
  transition: { duration: 0.1 }
}

// Navigation animations
export const navSlideIn = {
  initial: { x: "100%" },
  animate: { x: 0 },
  exit: { x: "100%" },
  transition: springConfig
}

export const bottomNavSlideUp = {
  initial: { y: 100 },
  animate: { y: 0 },
  exit: { y: 100 },
  transition: springConfig
}

// Modal animations
export const modalBackdrop = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 }
}

export const modalContent = {
  initial: { opacity: 0, scale: 0.8, y: 50 },
  animate: { opacity: 1, scale: 1, y: 0 },
  exit: { opacity: 0, scale: 0.8, y: 50 },
  transition: springConfig
}

// Loading animations
export const pulseAnimation = {
  scale: [1, 1.05, 1],
  transition: {
    duration: 2,
    repeat: Infinity,
    ease: "easeInOut"
  }
}

export const spinAnimation = {
  rotate: 360,
  transition: {
    duration: 1,
    repeat: Infinity,
    ease: "linear"
  }
}

// Token creation flow animations
export const tokenFormSlide = {
  initial: { opacity: 0, x: 50 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -50 },
  transition: springConfig
}

// Swap interface animations
export const swapFlip = {
  rotateX: 180,
  transition: { duration: 0.6, ease: "easeInOut" }
}

export const priceUpdate = {
  scale: [1, 1.1, 1],
  color: ["#14F195", "#9945FF", "#14F195"],
  transition: { duration: 0.5 }
}
