import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types for better TypeScript support

// Core User & Admin Types
export interface User {
  id: string
  wallet_address: string
  username?: string
  email?: string
  avatar_url?: string
  bio?: string
  twitter_handle?: string
  discord_handle?: string
  telegram_handle?: string
  website_url?: string
  default_slippage_bps: number
  quick_buy_amount_1: number
  quick_buy_amount_2: number
  quick_buy_amount_3: number
  auto_approve_transactions: boolean
  show_price_in_usd: boolean
  enable_notifications: boolean
  enable_sound_effects: boolean
  profile_visibility: 'public' | 'private' | 'friends'
  show_trading_history: boolean
  show_portfolio_value: boolean
  total_trades: number
  total_volume_sui: number
  total_rewards_earned: number
  favorite_tokens: string[]
  // XFT Bank & LTV rating
  current_ltv_rating: number
  total_loans_taken: number
  successful_repayments: number
  defaults: number
  total_borrowed_sui: number
  total_repaid_sui: number
  // Show Love history
  total_love_given: number
  total_love_received: number
  love_interactions_given: number
  love_interactions_received: number
  xfts_loved: number[]
  created_at: string
  updated_at: string
  last_active_at: string
  is_verified: boolean
  is_banned: boolean
}

export interface Admin {
  id: string
  user_id: string
  wallet_address: string
  admin_level: number
  permissions: Record<string, any>
  can_manage_users: boolean
  can_manage_tokens: boolean
  can_manage_pools: boolean
  can_manage_labels: boolean
  can_manage_marketplace: boolean
  can_manage_operators: boolean
  can_view_analytics: boolean
  can_moderate_content: boolean
  can_manage_fees: boolean
  can_pause_platform: boolean
  created_at: string
  created_by?: string
  is_active: boolean
  last_login_at?: string
}

// Token & Pool Types
export interface Token {
  id: string
  token_address: string
  global_id?: number
  name: string
  symbol: string
  description?: string
  image_url?: string
  website_url?: string
  twitter_url?: string
  telegram_url?: string
  discord_url?: string
  total_supply: number
  decimals: number
  creator_address: string
  owner_address: string
  private_pool: boolean
  max_wallet_percentage: number
  initial_liquidity_sui: number
  bonding_curve_type: string
  linked_label_global_id?: number
  linked_label_object_id?: string
  linked_label_creator?: string
  buy_fee_bps: number
  sell_fee_bps: number
  platform_fee_bps: number
  reward_pot_enabled: boolean
  initial_reward_goal: number
  current_reward_goal: number
  current_trade_count: number
  total_reward_distributed: number
  current_price_scaled: number
  market_cap_sui: number
  total_volume_sui: number
  total_trades: number
  holder_count: number
  migration_status: 'pre-migration' | 'migrated' | 'failed'
  migration_tx_hash?: string
  migrated_at?: string
  created_at: string
  updated_at: string
  is_active: boolean
  is_featured: boolean
  is_verified: boolean
}

// Label & XFT Types
export interface Label {
  id: string
  label_object_id: string
  global_id: number
  label_type: 1 | 2 | 3 | 4 | 5 | 6
  name: string
  description?: string
  image_url?: string
  banner_url?: string
  website_url?: string
  social_links: Record<string, any>
  creator_address: string
  owner_address: string
  is_transferable: boolean
  is_public: boolean
  max_supply?: number
  current_supply: number
  expires_at?: string
  is_expired: boolean
  linked_to_label_id?: number
  commission_fee_bps: number
  created_at: string
  updated_at: string
  is_active: boolean
  is_verified: boolean
}

export interface XFT {
  id: string
  xft_object_id: string
  global_id: number
  name: string
  description?: string
  image_url?: string
  animation_url?: string
  external_url?: string
  xft_type: '1-of-1' | 'limited-edition' | 'generative'
  edition_number?: number
  total_editions?: number
  creator_address: string
  owner_address: string
  minter_address?: string
  linked_label_global_id?: number
  linked_label_object_id?: string
  stored_assets: any[]
  vault_locked: boolean
  vault_unlock_date?: string
  is_time_locked: boolean
  unlock_timestamp?: number
  is_for_sale: boolean
  sale_price?: number
  sale_currency: string
  marketplace_license_id?: number
  collection_id?: string
  generation_seed?: string
  traits: Record<string, any>
  rarity_score?: number
  // Show Love tracking
  total_love_received: number
  total_love_count: number
  unique_lovers: number
  last_love_received?: string
  created_at: string
  updated_at: string
  is_active: boolean
  is_verified: boolean
}

// Event Types
export interface TokenEvent {
  id: string
  event_type: 'token_created' | 'token_swapped' | 'label_linked' | 'ownership_transferred' | 'pool_created' | 'migration_completed'
  token_address: string
  transaction_hash: string
  block_number: number
  timestamp: string
  creator_address?: string
  trader_address?: string
  token_name?: string
  token_symbol?: string
  total_supply?: number
  sui_amount?: number
  token_amount?: number
  is_buy?: boolean
  linked_label_global_id?: number
  linked_label_object_id?: string
  linked_label_creator?: string
  price_scaled?: number
  virtual_pool_sui_balance?: number
  virtual_pool_token_balance?: number
  created_at: string
  updated_at: string
}

export interface LabelEvent {
  id: string
  event_type: 'label_created' | 'label_linked' | 'label_unlinked' | 'operator_added' | 'operator_removed' | 'license_created' | 'license_expired' | 'label_transferred'
  label_global_id: number
  label_object_id: string
  transaction_hash: string
  timestamp: string
  creator_address?: string
  owner_address?: string
  label_type?: number
  operator_address?: string
  operator_role?: number
  assigned_by?: string
  license_object_id?: string
  linked_to_label_id?: number
  commission_fee_bps?: number
  token_address?: string
  created_at: string
  updated_at: string
}

export interface XFTEvent {
  id: string
  event_type: 'xft_created' | 'xft_transferred' | 'xft_wrapped' | 'vault_locked' | 'vault_unlocked' | 'assets_deposited' | 'assets_withdrawn' | 'xft_burned'
  xft_object_id: string
  global_id?: number
  transaction_hash: string
  timestamp: string
  creator_address?: string
  minter_address?: string
  xft_type?: string
  linked_label_global_id?: number
  from_address?: string
  to_address?: string
  assets_deposited?: any
  assets_withdrawn?: any
  unlock_timestamp?: number
  created_at: string
  updated_at: string
}

export interface MarketplaceEvent {
  id: string
  event_type: 'item_listed' | 'item_sold' | 'item_cancelled' | 'license_created' | 'license_used' | 'commission_paid'
  transaction_hash: string
  timestamp: string
  listing_id?: number
  xft_object_id?: string
  seller_address?: string
  buyer_address?: string
  price?: number
  license_object_id?: string
  label_global_id?: number
  license_holder?: string
  commission_fee_bps?: number
  commission_amount?: number
  created_at: string
  updated_at: string
}

export interface TradingEvent {
  id: string
  token_address: string
  trader_address: string
  transaction_hash: string
  block_number: number
  timestamp: string
  sui_amount: number
  token_amount: number
  is_buy: boolean
  price_scaled: number
  platform_fee?: number
  buy_fee?: number
  sell_fee?: number
  reward_contribution?: number
  virtual_pool_sui_balance: number
  virtual_pool_token_balance: number
  created_at: string
  updated_at: string
}

export interface RewardEvent {
  id: string
  token_address: string
  winner_address: string
  reward_amount: number
  trade_count: number
  new_reward_goal: number
  transaction_hash: string
  block_number: number
  timestamp: string
  created_at: string
  updated_at: string
}

// Marketplace & Portfolio Types
export interface MarketplaceListing {
  id: string
  listing_id: number
  xft_object_id: string
  seller_address: string
  price: number
  currency: string
  listed_under_label_id?: number
  marketplace_license_id?: number
  commission_fee_bps: number
  status: 'active' | 'sold' | 'cancelled' | 'expired'
  expires_at?: string
  buyer_address?: string
  sold_at?: string
  sold_price?: number
  created_at: string
  updated_at: string
}

export interface UserPortfolio {
  id: string
  user_address: string
  token_address: string
  token_balance: number
  average_buy_price: number
  total_invested_sui: number
  unrealized_pnl: number
  realized_pnl: number
  total_buys: number
  total_sells: number
  total_buy_volume: number
  total_sell_volume: number
  first_purchase_at?: string
  last_trade_at?: string
  created_at: string
  updated_at: string
}

export interface TokenSummary {
  token_address: string
  token_name: string
  token_symbol: string
  creator_address: string
  linked_label_global_id?: number
  linked_label_object_id?: string
  linked_label_creator?: string
  total_trades: number
  total_volume_sui: number
  total_volume_tokens: number
  current_price_scaled: number
  current_sui_reserve: number
  current_token_reserve: number
  current_trade_count: number
  current_reward_goal: number
  total_rewards_distributed: number
  created_at: string
  updated_at: string
}

// Helper functions for database operations
export const insertTokenEvent = async (event: Omit<TokenEvent, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('token_events')
    .insert(event)
    .select()
    .single()

  if (error) {
    console.error('Error inserting token event:', error)
    throw error
  }

  return data
}

export const insertLabelEvent = async (event: Omit<LabelEvent, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('label_events')
    .insert(event)
    .select()
    .single()

  if (error) {
    console.error('Error inserting label event:', error)
    throw error
  }

  return data
}

export const insertTradingEvent = async (event: Omit<TradingEvent, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('trading_events')
    .insert(event)
    .select()
    .single()

  if (error) {
    console.error('Error inserting trading event:', error)
    throw error
  }

  return data
}

export const insertRewardEvent = async (event: Omit<RewardEvent, 'id' | 'created_at' | 'updated_at'>) => {
  const { data, error } = await supabase
    .from('reward_events')
    .insert(event)
    .select()
    .single()

  if (error) {
    console.error('Error inserting reward event:', error)
    throw error
  }

  return data
}

// Query functions for retrieving data
export const getTokenEvents = async (tokenAddress?: string, limit = 50) => {
  let query = supabase
    .from('token_events')
    .select('*')
    .order('timestamp', { ascending: false })
    .limit(limit)

  if (tokenAddress) {
    query = query.eq('token_address', tokenAddress)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error fetching token events:', error)
    throw error
  }

  return data
}

export const getTradingEvents = async (tokenAddress?: string, traderAddress?: string, limit = 50) => {
  let query = supabase
    .from('trading_events')
    .select('*')
    .order('timestamp', { ascending: false })
    .limit(limit)

  if (tokenAddress) {
    query = query.eq('token_address', tokenAddress)
  }

  if (traderAddress) {
    query = query.eq('trader_address', traderAddress)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error fetching trading events:', error)
    throw error
  }

  return data
}

export const getRewardEvents = async (tokenAddress?: string, limit = 50) => {
  let query = supabase
    .from('reward_events')
    .select('*')
    .order('timestamp', { ascending: false })
    .limit(limit)

  if (tokenAddress) {
    query = query.eq('token_address', tokenAddress)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error fetching reward events:', error)
    throw error
  }

  return data
}

// Real-time subscription helpers
export const subscribeToTokenEvents = (callback: (payload: any) => void) => {
  return supabase
    .channel('token_events')
    .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'token_events' }, callback)
    .subscribe()
}

export const subscribeToTradingEvents = (callback: (payload: any) => void) => {
  return supabase
    .channel('trading_events')
    .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'trading_events' }, callback)
    .subscribe()
}

export const subscribeToRewardEvents = (callback: (payload: any) => void) => {
  return supabase
    .channel('reward_events')
    .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'reward_events' }, callback)
    .subscribe()
}

// Bank & Love System Types
export interface XFTBank {
  id: string
  total_sui_balance: number
  total_loans_issued: number
  total_loans_pending: number
  total_defaults: number
  total_loan_amount_issued: number
  total_loan_amount_outstanding: number
  total_defaults_amount: number
  total_interest_earned: number
  default_rate_percentage: number
  average_ltv_ratio: number
  created_at: string
  updated_at: string
}

export interface UserLoan {
  id: string
  borrower_address: string
  xft_object_id: string
  loan_amount: number
  collateral_value: number
  ltv_ratio: number
  interest_rate_bps: number
  status: 'active' | 'repaid' | 'defaulted' | 'liquidated'
  loan_date: string
  due_date: string
  repaid_date?: string
  amount_repaid: number
  interest_paid: number
  remaining_balance: number
  days_overdue: number
  default_date?: string
  liquidation_date?: string
  created_at: string
  updated_at: string
}

export interface UserLTVRating {
  id: string
  user_address: string
  current_ltv_percentage: number
  max_ltv_percentage: number
  min_ltv_percentage: number
  successful_repayments: number
  total_loans: number
  defaults: number
  last_rating_increase?: string
  last_rating_decrease?: string
  rating_change_reason?: string
  created_at: string
  updated_at: string
}

export interface XFTLoveHistory {
  id: string
  giver_address: string
  receiver_xft_object_id: string
  receiver_address: string
  love_amount: number
  giver_ltv_rating: number
  giver_vault_locked: boolean
  receiver_vault_locked: boolean
  transaction_hash: string
  block_number: number
  timestamp: string
  created_at: string
  updated_at: string
}

export interface XFTLoveSummary {
  xft_object_id: string
  total_love_received: number
  total_love_count: number
  unique_lovers: number
  average_love_amount: number
  highest_love_amount: number
  last_love_received?: string
  first_love_received?: string
  created_at: string
  updated_at: string
}

export interface BankEvent {
  id: string
  event_type: 'loan_issued' | 'loan_repaid' | 'loan_defaulted' | 'loan_liquidated' | 'love_given' | 'ltv_updated'
  transaction_hash: string
  timestamp: string
  borrower_address?: string
  lender_address?: string
  xft_object_id?: string
  loan_amount?: number
  collateral_value?: number
  ltv_ratio?: number
  interest_rate_bps?: number
  giver_address?: string
  receiver_address?: string
  receiver_xft_object_id?: string
  love_amount?: number
  giver_ltv_rating?: number
  user_address?: string
  old_ltv_rating?: number
  new_ltv_rating?: number
  rating_change_reason?: string
  created_at: string
  updated_at: string
}
