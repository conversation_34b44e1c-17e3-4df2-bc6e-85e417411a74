/**
 * Pinata IPFS Upload Utility
 * 
 * Simple utility for uploading files to IPFS using Pinata API
 */

// Pinata v3 API response interfaces
interface PinataV3Response {
  data: {
    id: string;
    cid: string;
    name: string;
    user_id: string;
    size: number;
    number_of_files: number;
    mime_type: string;
    group_id: string | null;
  };
}

interface PinataError {
  error: string;
  details?: string;
}

/**
 * Upload a file to IPFS using Pinata
 */
export async function uploadToIPFS(file: File): Promise<string> {
  try {
    console.log('Starting IPFS upload for file:', file.name);

    // Check if JWT token is available
    if (!process.env.PINATA_JWT) {
      throw new Error('PINATA_JWT environment variable is not set');
    }

    console.log('Using Pinata v3 API');

    const formData = new FormData();
    formData.append('file', file);

    // Add metadata for v3 API
    const metadata = JSON.stringify({
      name: file.name,
    });
    formData.append('pinataMetadata', metadata);

    console.log('Making request to Pinata v3 API...');

    const response = await fetch('https://uploads.pinata.cloud/v3/files', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.PINATA_JWT}`,
      },
      body: formData,
    });

    console.log('Pinata API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Pinata API error response:', errorText);

      try {
        const errorData = JSON.parse(errorText);
        throw new Error(`Pinata upload failed (${response.status}): ${errorData.error || errorText}`);
      } catch (parseError) {
        throw new Error(`Pinata upload failed (${response.status}): ${errorText}`);
      }
    }

    const data = await response.json();
    console.log('Upload successful, response:', data);

    // V3 API returns different structure
    const ipfsHash = data.data?.cid || data.cid;
    if (!ipfsHash) {
      throw new Error('No IPFS hash returned from Pinata');
    }

    console.log('IPFS hash extracted:', ipfsHash);

    // Return multiple IPFS URLs for fallback
    // Use Pinata's gateway first (most reliable for newly uploaded files)
    const ipfsUrl = `https://gateway.pinata.cloud/ipfs/${ipfsHash}`;
    console.log('Final IPFS URL:', ipfsUrl);

    // Also log alternative URLs for debugging
    console.log('Alternative URLs:');
    console.log('- Cloudflare:', `https://cloudflare-ipfs.com/ipfs/${ipfsHash}`);
    console.log('- IPFS.io:', `https://ipfs.io/ipfs/${ipfsHash}`);
    console.log('- dweb.link:', `https://dweb.link/ipfs/${ipfsHash}`);

    return ipfsUrl;
  } catch (error) {
    console.error('IPFS upload error:', error);
    throw new Error(`Failed to upload to IPFS: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload JSON data to IPFS using Pinata
 */
export async function uploadJSONToIPFS(data: any, name: string): Promise<string> {
  try {
    if (!process.env.PINATA_JWT) {
      throw new Error('PINATA_JWT environment variable is not set');
    }

    // Create a blob from the JSON data
    const jsonBlob = new Blob([JSON.stringify(data)], { type: 'application/json' });
    const file = new File([jsonBlob], `${name}.json`, { type: 'application/json' });

    const formData = new FormData();
    formData.append('file', file);

    // Add metadata for v3 API
    const metadata = JSON.stringify({
      name: name,
    });
    formData.append('pinataMetadata', metadata);

    const response = await fetch('https://uploads.pinata.cloud/v3/files', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.PINATA_JWT}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(`Pinata JSON upload failed (${response.status}): ${errorData.error || errorText}`);
      } catch (parseError) {
        throw new Error(`Pinata JSON upload failed (${response.status}): ${errorText}`);
      }
    }

    const result = await response.json();
    const ipfsHash = result.data?.cid || result.cid;

    if (!ipfsHash) {
      throw new Error('No IPFS hash returned from Pinata');
    }

    // Return the IPFS URL
    return `https://gateway.pinata.cloud/ipfs/${ipfsHash}`;
  } catch (error) {
    console.error('IPFS JSON upload error:', error);
    throw new Error(`Failed to upload JSON to IPFS: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create token metadata and upload to IPFS (client-side function)
 */
export async function uploadTokenMetadata(tokenData: {
  name: string;
  symbol: string;
  description: string;
  image?: string;
  website?: string;
  twitter?: string;
  telegram?: string;
  discord?: string;
}): Promise<string> {
  const metadata = {
    name: tokenData.name,
    symbol: tokenData.symbol,
    description: tokenData.description,
    image: tokenData.image || '',
    external_url: tokenData.website || '',
    attributes: [
      {
        trait_type: 'Token Type',
        value: 'Dexsta Token'
      },
      {
        trait_type: 'Platform',
        value: 'Sui Blockchain'
      }
    ],
    properties: {
      website: tokenData.website || '',
      twitter: tokenData.twitter || '',
      telegram: tokenData.telegram || '',
      discord: tokenData.discord || '',
    }
  };

  // Use API route for metadata upload
  const response = await fetch('/api/upload-metadata', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      metadata,
      name: `${tokenData.symbol}-metadata`
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Metadata upload failed');
  }

  const result = await response.json();
  return result.ipfsUrl;
}

/**
 * Validate file for IPFS upload
 */
export function validateFile(file: File): { valid: boolean; error?: string } {
  // Check file size (max 100MB)
  const maxSize = 100 * 1024 * 1024; // 100MB
  if (file.size > maxSize) {
    return { valid: false, error: 'File size must be less than 100MB' };
  }

  // Check file type (images only for now)
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: 'Only image files (JPEG, PNG, GIF, WebP) are allowed' };
  }

  return { valid: true };
}

/**
 * Get IPFS hash from URL
 */
export function getIPFSHash(url: string): string | null {
  const match = url.match(/\/ipfs\/([a-zA-Z0-9]+)/);
  return match ? match[1] : null;
}

/**
 * Convert IPFS URL to different gateway
 */
export function convertIPFSUrl(url: string, gateway: string = 'https://gateway.pinata.cloud'): string {
  const hash = getIPFSHash(url);
  if (!hash) return url;

  return `${gateway}/ipfs/${hash}`;
}

/**
 * Get list of IPFS gateways for fallback
 */
export function getIPFSGateways(): string[] {
  return [
    'https://gateway.pinata.cloud',
    'https://cloudflare-ipfs.com',
    'https://ipfs.io',
    'https://dweb.link',
    'https://ipfs.infura.io'
  ];
}

/**
 * Try loading IPFS content from multiple gateways
 */
export async function loadIPFSWithFallback(hash: string): Promise<string> {
  const gateways = getIPFSGateways();

  for (const gateway of gateways) {
    try {
      const url = `${gateway}/ipfs/${hash}`;
      const response = await fetch(url, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      if (response.ok) {
        console.log(`✅ IPFS content accessible via ${gateway}`);
        return url;
      }
    } catch (error) {
      console.log(`❌ Failed to load from ${gateway}:`, error);
      continue;
    }
  }

  // If all gateways fail, return the first one anyway
  console.warn('⚠️ All IPFS gateways failed, returning Pinata gateway');
  return `${gateways[0]}/ipfs/${hash}`;
}
