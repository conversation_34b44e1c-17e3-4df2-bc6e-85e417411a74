/**
 * Module Deployment Service (Server-side only)
 * Handles template-based token module generation, compilation, and deployment
 */

import { promises as fs } from 'fs'
import path from 'path'
import { exec } from 'child_process'
import { promisify } from 'util'
import { SuiClient, getFullnodeUrl } from '@mysten/sui/client'
import { Transaction } from '@mysten/sui/transactions'
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519'
import { fromB64 } from '@mysten/sui/utils'

const execAsync = promisify(exec)

export interface TokenDeploymentRequest {
  tokenId: string
  name: string
  symbol: string
  description: string
  iconUrl: string
  decimals: number
  creator: string
  totalSupply: string
  buyFeeBps: number
  sellFeeBps: number
}

export interface DeploymentResult {
  success: boolean
  packageId?: string
  treasuryCapId?: string
  coinMetadataId?: string
  error?: string
}

export class ModuleDeploymentService {
  private client: SuiClient
  private keypair: Ed25519Keypair
  private network: string

  constructor() {
    this.network = process.env.NEXT_PUBLIC_SUI_NETWORK || 'devnet'
    this.client = new SuiClient({ url: getFullnodeUrl(this.network as any) })
    
    // Use deployment account private key
    const privateKey = process.env.SUI_PRIVATE_KEY
    if (!privateKey) {
      throw new Error('SUI_PRIVATE_KEY environment variable is required')
    }

    // Handle both base64 and bech32 private key formats
    try {
      if (privateKey.startsWith('suiprivkey')) {
        // Bech32 format - use fromSecretKey directly
        this.keypair = Ed25519Keypair.fromSecretKey(privateKey)
      } else {
        // Base64 format - decode and slice
        this.keypair = Ed25519Keypair.fromSecretKey(fromB64(privateKey).slice(1))
      }
    } catch (error) {
      throw new Error(`Invalid private key format: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate Move module from template (using nodejs_deploy.js approach)
   */
  async generateModule(request: TokenDeploymentRequest): Promise<{
    moduleContent: string
    moveToml: string
    moduleName: string
    structName: string
    moduleAddress: string
  }> {
    console.log('🔧 Generating module for token:', request.symbol)

    // Generate unique identifiers
    const moduleAddress = "0x0" // Must be 0x0 for publishing
    const moduleName = `${request.symbol.toLowerCase()}`.replace(/[^a-z0-9_]/g, '_')
    const structName = `${request.symbol.replace(/[^A-Za-z0-9]/g, '')}Coin`

    // Generate Move module content (based on template.move)
    const moduleContent = `
module ${moduleAddress}::${moduleName} {
    use sui::coin::{Self, TreasuryCap, Coin};
    use sui::object::{Self, UID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use std::option;

    struct ${structName} has store, drop {}

    fun init(ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<${structName}>(
            ${structName} {},
            ${request.decimals}, // decimals
            b"${request.symbol}", // symbol
            b"${request.name}", // name
            b"${request.description}", // description
            option::none(), // icon_url (will be set later)
            ctx
        );

        // Transfer the treasury cap to the deployer
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));

        // Share the metadata object
        transfer::public_share_object(metadata);
    }

    /// Mint tokens to a recipient
    public entry fun mint(
        treasury_cap: &mut TreasuryCap<${structName}>,
        amount: u64,
        recipient: address,
        ctx: &mut TxContext
    ) {
        let coin = coin::mint(treasury_cap, amount, ctx);
        transfer::public_transfer(coin, recipient);
    }

    /// Get total supply
    public fun total_supply(treasury_cap: &TreasuryCap<${structName}>): u64 {
        coin::total_supply(treasury_cap)
    }
}`.trim()

    // Generate Move.toml content
    const moveToml = `
[package]
name = "${moduleName}"
version = "0.0.1"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
${moduleName} = "0x0"
`.trim()

    console.log('✅ Module generated:', {
      moduleAddress,
      moduleName,
      structName
    })

    return {
      moduleContent,
      moveToml,
      moduleName,
      structName,
      moduleAddress
    }
  }

  /**
   * Write module to temporary directory and compile (using nodejs_deploy.js approach)
   */
  async compileModule(
    moduleContent: string,
    moveToml: string,
    moduleName: string
  ): Promise<string> {
    console.log('🔨 Compiling module:', moduleName)

    // Create temporary directory (similar to nodejs_deploy.js)
    const tempDir = path.join(process.cwd(), 'generated_tokens', moduleName)
    await fs.mkdir(tempDir, { recursive: true })

    // Create sources directory
    const sourcesDir = path.join(tempDir, 'sources')
    await fs.mkdir(sourcesDir, { recursive: true })

    // Write Move.toml file
    await fs.writeFile(path.join(tempDir, 'Move.toml'), moveToml)

    // Write module file
    const moduleFile = path.join(sourcesDir, 'coin.move')
    await fs.writeFile(moduleFile, moduleContent)

    console.log('📁 Module files created in:', tempDir)
    return tempDir
  }

  /**
   * Deploy module to Sui network (using nodejs_deploy.js approach)
   */
  async deployModule(
    moduleDir: string,
    request: TokenDeploymentRequest
  ): Promise<DeploymentResult> {
    try {
      console.log('🚀 Deploying module to', this.network)

      // Build the module first (like nodejs_deploy.js)
      console.log('Building module...')
      const buildResult = await execAsync(`sui move build`, { cwd: moduleDir })
      console.log('Build output:', buildResult.stdout)

      if (buildResult.stderr && !buildResult.stderr.includes('warning')) {
        throw new Error(`Build failed: ${buildResult.stderr}`)
      }

      console.log('✅ Build successful.')

      // Publish the module (like nodejs_deploy.js)
      console.log('Publishing module...')
      const publishCmd = `sui client publish --gas-budget 100000000 --json`
      const publishResult = await execAsync(publishCmd, { cwd: moduleDir })

      const publishData = JSON.parse(publishResult.stdout)
      console.log('Publish result:', publishData)

      if (!publishData.objectChanges) {
        throw new Error('No object changes in publish result')
      }

      // Extract package ID
      const packageChange = publishData.objectChanges.find((change: any) =>
        change.type === 'published'
      )

      if (!packageChange) {
        throw new Error('No package published in transaction')
      }

      const packageId = packageChange.packageId
      console.log('✅ Module published! Package ID:', packageId)

      // Extract TreasuryCap and CoinMetadata from the publish transaction
      let treasuryCapId = ''
      let coinMetadataId = ''

      if (publishData.objectChanges) {
        for (const change of publishData.objectChanges) {
          if (change.type === 'created') {
            if (change.objectType?.includes('TreasuryCap')) {
              treasuryCapId = change.objectId
            } else if (change.objectType?.includes('CoinMetadata')) {
              coinMetadataId = change.objectId
            }
          }
        }
      }

      if (!treasuryCapId || !coinMetadataId) {
        throw new Error('Failed to extract TreasuryCap or CoinMetadata IDs from publish transaction')
      }

      console.log('✅ Token objects created:', {
        packageId,
        treasuryCapId,
        coinMetadataId
      })

      // Keep the generated files for debugging (don't clean up immediately)
      console.log('📁 Generated files kept in:', moduleDir)

      return {
        success: true,
        packageId,
        treasuryCapId,
        coinMetadataId
      }

    } catch (error) {
      console.error('❌ Module deployment failed:', error)

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown deployment error'
      }
    }
  }

  /**
   * Complete the token creation process with initial token minting
   */
  async completeTokenCreation(
    tokenSymbol: string,
    deploymentResult: DeploymentResult,
    tokenData: any
  ): Promise<boolean> {
    if (!deploymentResult.success || !deploymentResult.packageId ||
        !deploymentResult.treasuryCapId || !deploymentResult.coinMetadataId) {
      throw new Error('Invalid deployment result')
    }

    console.log('🎯 Completing token creation for token:', tokenSymbol)

    try {
      // Step 1: Complete token creation in main contract
      const tx = new Transaction()
      tx.moveCall({
        target: `${process.env.NEXT_PUBLIC_PACKAGE_ID}::dexsta_token::complete_token_creation`,
        arguments: [
          tx.object(process.env.NEXT_PUBLIC_TOKEN_REGISTRY!),
          tx.pure.string(tokenSymbol),
          tx.pure.address(deploymentResult.packageId),
          tx.pure.string(tokenData.moduleName || tokenSymbol.toLowerCase()),
          tx.pure.string(`${deploymentResult.packageId}::${tokenData.moduleName || tokenSymbol.toLowerCase()}::${tokenData.structName || tokenSymbol}Coin`),
          tx.pure.id(deploymentResult.treasuryCapId),
          tx.pure.id(deploymentResult.coinMetadataId),
          tx.pure.u64(tokenData.totalSupply || '1000000'),
          tx.pure.string(tokenData.website || ''),
          tx.pure.string(tokenData.twitter || ''),
          tx.pure.string(tokenData.telegram || ''),
          tx.pure.string(tokenData.tiktok || ''),
          tx.pure.u16(tokenData.buyFeeBps || 250),
          tx.pure.u16(tokenData.sellFeeBps || 250),
          tx.pure.u16(1000), // max_wallet_percentage (10%)
          tx.pure.u64(0), // link_to_label (none)
          tx.pure.u64(0), // xft_id (none)
        ],
      })

      const result = await this.client.signAndExecuteTransaction({
        transaction: tx,
        signer: this.keypair,
        options: {
          showEffects: true,
          showEvents: true,
        }
      })

      console.log('✅ Token pool created:', result.digest)

      // Step 2: Mint initial tokens to creator
      console.log('🪙 Minting initial tokens to creator...')

      const mintTx = new Transaction()

      // Calculate initial tokens based on creator's SUI purchase
      const initialSuiAmount = tokenData.suiAmount || 100000000 // 0.1 SUI in MIST
      const initialTokens = Math.floor(initialSuiAmount * 100 / 1000000000) // 1 SUI = 100 tokens

      // Mint tokens using the deployed token's mint function
      mintTx.moveCall({
        target: `${deploymentResult.packageId}::${tokenData.moduleName}::mint`,
        typeArguments: [`${deploymentResult.packageId}::${tokenData.moduleName}::${tokenData.structName}`],
        arguments: [
          mintTx.object(deploymentResult.treasuryCapId),
          mintTx.pure.u64(initialTokens),
          mintTx.pure.address(tokenData.creator),
        ],
      })

      const mintResult = await this.client.signAndExecuteTransaction({
        transaction: mintTx,
        signer: this.keypair,
        options: {
          showEffects: true,
          showEvents: true,
        }
      })

      console.log('✅ Initial tokens minted:', mintResult.digest)
      console.log('🎯 Creator received:', initialTokens, 'tokens')

      return true

    } catch (error) {
      console.error('❌ Token completion failed:', error)
      throw error
    }
  }

  /**
   * Full deployment pipeline (using nodejs_deploy.js approach)
   */
  async deployToken(request: TokenDeploymentRequest): Promise<DeploymentResult> {
    try {
      console.log('🚀 Starting full token deployment pipeline for:', request.symbol)

      // Step 1: Generate module (like nodejs_deploy.js)
      const moduleData = await this.generateModule(request)

      // Step 2: Write and compile module
      const moduleDir = await this.compileModule(
        moduleData.moduleContent,
        moduleData.moveToml,
        moduleData.moduleName
      )

      // Step 3: Deploy module
      const deploymentResult = await this.deployModule(moduleDir, request)

      if (!deploymentResult.success) {
        return deploymentResult
      }

      // Step 4: Complete token creation (call the contract)
      try {
        await this.completeTokenCreation(request.symbol, deploymentResult, {
          moduleName: moduleData.moduleName,
          structName: moduleData.structName,
          totalSupply: request.totalSupply,
          website: request.website || '',
          twitter: request.twitter || '',
          telegram: request.telegram || '',
          tiktok: request.tiktok || '',
          buyFeeBps: request.buyFeeBps,
          sellFeeBps: request.sellFeeBps,
          creator: request.creator,
          suiAmount: 100000000 // 0.1 SUI default initial purchase
        })
      } catch (completionError) {
        console.warn('⚠️ Token deployment succeeded but completion failed:', completionError)
        // Don't fail the entire deployment if completion fails
      }

      console.log('🎉 Token deployment pipeline completed successfully!')
      return deploymentResult

    } catch (error) {
      console.error('❌ Token deployment pipeline failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown pipeline error'
      }
    }
  }
}
