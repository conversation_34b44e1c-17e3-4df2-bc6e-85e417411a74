# Comprehensive Database Schema Documentation

## 🎉 **COMPLETE COMPREHENSIVE DATABASE SCHEMA FOR DEXSTA PLATFORM!**

### **✅ FULL ECOSYSTEM COVERAGE:**

## 📊 **1. CORE USER & ADMIN SYSTEM**

### **Users Table - Complete User Profiles:**
```sql
-- Comprehensive user data including all form fields
users (
    wallet_address,           -- Sui wallet address
    username, email, bio,     -- Profile information
    avatar_url,              -- Profile picture
    social_links,            -- Twitter, Discord, Telegram, Website
    
    -- Trading Preferences (from UI forms)
    default_slippage_bps,    -- User's preferred slippage
    quick_buy_amount_1/2/3,  -- Quick buy button amounts
    auto_approve_transactions, -- Auto-approve setting
    show_price_in_usd,       -- Price display preference
    enable_notifications,    -- Notification settings
    enable_sound_effects,    -- Sound preference
    
    -- Privacy Settings
    profile_visibility,      -- public/private/friends
    show_trading_history,    -- Show trade history
    show_portfolio_value,    -- Show portfolio value
    
    -- Platform Statistics
    total_trades, total_volume_sui, total_rewards_earned,
    favorite_tokens[]        -- Array of favorite token addresses
)
```

### **Admins Table - Platform Administration:**
```sql
-- Complete admin permission system
admins (
    admin_level,             -- 1=Admin, 2=Super Admin, 3=Owner
    permissions JSONB,       -- Flexible permissions object
    
    -- Granular Permissions
    can_manage_users, can_manage_tokens, can_manage_pools,
    can_manage_labels, can_manage_marketplace, can_manage_operators,
    can_view_analytics, can_moderate_content, can_manage_fees,
    can_pause_platform
)
```

---

## 🪙 **2. TOKEN & POOL ECOSYSTEM**

### **Tokens Table - Complete Token Data:**
```sql
-- All data from token creation forms
tokens (
    -- Basic Info (from create form)
    name, symbol, description, image_url,
    website_url, twitter_url, telegram_url, discord_url,
    
    -- Token Economics
    total_supply, decimals, creator_address, owner_address,
    
    -- Pool Configuration (from create form)
    private_pool,            -- Private pool toggle
    max_wallet_percentage,   -- Max wallet limit
    initial_liquidity_sui,   -- Initial liquidity
    bonding_curve_type,      -- Curve type
    
    -- Label Linking (optimized for performance)
    linked_label_global_id,  -- Global ID for reference
    linked_label_object_id,  -- Object ID for direct transfers
    linked_label_creator,    -- Creator address for fees
    
    -- Fee Configuration
    buy_fee_bps, sell_fee_bps, platform_fee_bps,
    
    -- Trading Game Settings
    reward_pot_enabled, initial_reward_goal, current_reward_goal,
    current_trade_count, total_reward_distributed,
    
    -- Current State
    current_price_scaled, market_cap_sui, total_volume_sui,
    total_trades, holder_count,
    
    -- Migration Status
    migration_status, migration_tx_hash, migrated_at
)
```

### **Pool Admin Settings Table:**
```sql
-- Complete pool configuration from admin forms
pool_admin_settings (
    platform_fee_bps, reward_fee_bps, mint_fee,
    min_reward_trade_amount, bonding_curve_goal,
    reward_goal_increase, reward_goal_decrease_amount,
    migration_fee_percentage, migration_gas_fee,
    is_paused
)
```

---

## 🏷️ **3. LABEL & XFT ECOSYSTEM**

### **Labels Table - All Label Types:**
```sql
-- Complete label system covering all 6 types
labels (
    label_type,              -- 1=Lead, 2=Profile, 3=Tags, 4=Chapters, 5=Operator License, 6=Marketplace License
    
    -- Basic Info (from create form)
    name, description, image_url, banner_url,
    website_url, social_links JSONB,
    
    -- Creator Info
    creator_address, owner_address,
    
    -- Configuration
    is_transferable, is_public, max_supply, current_supply,
    
    -- Expiration
    expires_at, is_expired,
    
    -- License-specific Fields
    linked_to_label_id,      -- For licenses linked to main labels
    commission_fee_bps       -- For marketplace licenses
)
```

### **XFTs Table - Complete NFT System:**
```sql
-- All XFT types and properties from creation forms
xfts (
    -- Basic NFT Info (from create form)
    name, description, image_url, animation_url, external_url,
    
    -- XFT Classification
    xft_type,                -- '1-of-1', 'limited-edition', 'generative'
    edition_number, total_editions,
    
    -- Creator Info
    creator_address, owner_address, minter_address,
    
    -- Label Linking
    linked_label_global_id, linked_label_object_id,
    
    -- Asset Storage (for 1-of-1 XFTs)
    stored_assets JSONB,     -- Array of stored asset objects
    vault_locked, vault_unlock_date,
    
    -- Time Locking
    is_time_locked, unlock_timestamp,
    
    -- Marketplace Info
    is_for_sale, sale_price, sale_currency,
    marketplace_license_id,
    
    -- Generative Collection Info
    collection_id, generation_seed, traits JSONB, rarity_score
)
```

### **Operators Table - Label Operator System:**
```sql
-- Complete operator permission system
operators (
    operator_address, label_global_id, license_object_id,
    
    -- Permissions
    role,                    -- 1=MINT, 2=MANAGE, 3=SUPER
    can_mint, can_manage_operators, can_withdraw, can_update_metadata,
    
    -- Status
    is_active, assigned_by, license_expires_at, is_license_expired
)
```

---

## 🛒 **4. MARKETPLACE ECOSYSTEM**

### **Marketplace Listings Table:**
```sql
-- Complete marketplace listing system
marketplace_listings (
    listing_id,              -- On-chain listing ID
    xft_object_id, seller_address,
    
    -- Pricing
    price, currency,
    
    -- Label Association
    listed_under_label_id,   -- Label the item is sold under
    marketplace_license_id,  -- If using marketplace license
    commission_fee_bps,      -- Commission fee
    
    -- Status
    status,                  -- 'active', 'sold', 'cancelled', 'expired'
    expires_at, buyer_address, sold_at, sold_price
)
```

### **Generative Collections Table:**
```sql
-- Generative XFT collections from creation forms
generative_collections (
    -- Collection Info (from create form)
    name, description, base_image_url,
    creator_address, owner_address,
    
    -- Label Linking
    linked_label_global_id,
    
    -- Generation Settings
    max_supply, current_supply, generation_algorithm,
    trait_layers JSONB,      -- Array of trait layer definitions
    rarity_weights JSONB,    -- Rarity weights for traits
    
    -- Pricing & Status
    mint_price, is_active, is_revealed, reveal_date
)
```

---

## 📊 **5. COMPREHENSIVE EVENT SYSTEM**

### **All Contract Events Covered:**

**Token Events:**
- `token_created`, `token_swapped`, `label_linked`, `ownership_transferred`, `pool_created`, `migration_completed`

**Label Events:**
- `label_created`, `label_linked`, `label_unlinked`, `operator_added`, `operator_removed`, `license_created`, `license_expired`, `label_transferred`

**XFT Events:**
- `xft_created`, `xft_transferred`, `xft_wrapped`, `vault_locked`, `vault_unlocked`, `assets_deposited`, `assets_withdrawn`, `xft_burned`

**Marketplace Events:**
- `item_listed`, `item_sold`, `item_cancelled`, `license_created`, `license_used`, `commission_paid`

**Trading Events:**
- Detailed trading information with fee breakdown

**Reward Events:**
- Trading reward distributions

---

## 📈 **6. ANALYTICS & PORTFOLIO SYSTEM**

### **Token Summary Table:**
```sql
-- Aggregated token statistics for performance
token_summary (
    -- Label Information (cached for performance)
    linked_label_global_id, linked_label_object_id, linked_label_creator,
    
    -- Trading Statistics
    total_trades, total_volume_sui, total_volume_tokens,
    current_price_scaled, current_sui_reserve, current_token_reserve,
    
    -- Reward Information
    current_trade_count, current_reward_goal, total_rewards_distributed
)
```

### **User Portfolio Table:**
```sql
-- Complete user portfolio tracking
user_portfolios (
    user_address, token_address,
    
    -- Holdings
    token_balance, average_buy_price, total_invested_sui,
    unrealized_pnl, realized_pnl,
    
    -- Trading Stats
    total_buys, total_sells, total_buy_volume, total_sell_volume,
    first_purchase_at, last_trade_at
)
```

### **Platform Analytics Table:**
```sql
-- Daily/hourly platform statistics
platform_analytics (
    date, hour,              -- Daily or hourly granularity
    
    -- Trading Metrics
    total_trades, total_volume_sui, unique_traders, new_tokens_created,
    
    -- User Metrics
    new_users, active_users,
    
    -- XFT Metrics
    new_xfts_created, xft_trades, xft_volume_sui,
    
    -- Label Metrics
    new_labels_created, label_linkings,
    
    -- Marketplace Metrics
    marketplace_listings, marketplace_sales, marketplace_volume_sui
)
```

---

## 🔧 **7. PERFORMANCE & SECURITY**

### **Comprehensive Indexing:**
- **User Indexes** - wallet_address, username, created_at
- **Token Indexes** - creator_address, symbol, migration_status, linked_label
- **Label Indexes** - creator_address, label_type, global_id
- **XFT Indexes** - creator_address, owner_address, xft_type, linked_label
- **Event Indexes** - timestamp, event_type, transaction_hash
- **Portfolio Indexes** - user_address, token_address, last_trade

### **Automatic Triggers:**
- **Timestamp Updates** - Auto-update `updated_at` on all tables
- **Token Summary Updates** - Auto-update aggregated stats on trades
- **Portfolio Updates** - Auto-calculate PnL and trading stats

### **Row Level Security:**
- **Public Read Access** - All public data (tokens, labels, XFTs, events)
- **User-Specific Access** - Users can only modify their own data
- **Creator/Owner Access** - Creators can update their creations
- **Service Role Access** - Event synchronization permissions
- **Admin Access** - Granular admin permissions

---

## 🎯 **8. FORM DATA COVERAGE**

### **✅ ALL FORM FIELDS CAPTURED:**

**Token Creation Form:**
- Basic info, economics, pool config, label linking, fees, trading game

**XFT Creation Form:**
- Basic NFT info, classification, asset storage, time locking, marketplace

**Label Creation Form:**
- All label types, configuration, expiration, license settings

**User Settings Form:**
- Trading preferences, privacy settings, notification preferences

**Admin Configuration:**
- Platform settings, fee configuration, pool parameters

---

## 🏆 **9. ACHIEVEMENT SUMMARY**

### **✅ COMPLETE ECOSYSTEM COVERAGE:**
- **👥 User System** - Profiles, settings, preferences, portfolios
- **🪙 Token System** - Complete token lifecycle and configuration
- **🏷️ Label System** - All 6 label types with full functionality
- **🎨 XFT System** - All XFT types with asset storage and time locking
- **👨‍💼 Operator System** - Complete permission and license management
- **🛒 Marketplace System** - Listings, sales, licenses, commissions
- **📊 Event System** - All contract events from all contracts
- **📈 Analytics System** - Portfolio tracking and platform analytics

### **✅ PRODUCTION FEATURES:**
- **⚡ Performance** - Comprehensive indexing and caching
- **🛡️ Security** - Row Level Security with granular permissions
- **🔄 Automation** - Triggers for data consistency
- **📊 Analytics** - Real-time aggregated statistics
- **🎯 Scalability** - Optimized for high-volume operations

## **🎉 COMPLETE COMPREHENSIVE DATABASE SCHEMA COVERING THE ENTIRE DEXSTA ECOSYSTEM!** 📊🔗✨

**Every form field, every contract, every event, every user setting - all captured in a production-ready, high-performance database schema!**
