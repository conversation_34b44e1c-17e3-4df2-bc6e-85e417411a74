'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { staggerContainer, staggerItem } from '@/lib/animations'

export default function OpenLiquidityPage() {
  return (
    <MobileLayout headerTitle="Open Liquidity" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="text-6xl">🌊</div>
          <h1 className="text-2xl font-bold text-white font-heading">
            Open Liquidity
          </h1>
          <div className="w-20 h-0.5 bg-gradient-to-r from-cyan-400 to-blue-600 rounded-full mx-auto" />
          <p className="text-gray-300">
            Public liquidity pools accessible to all traders
          </p>
        </motion.div>

        {/* What is Open Liquidity */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-cyan-400/30 bg-cyan-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">What is Open Liquidity?</h2>
              <p className="text-gray-200 leading-relaxed">
                Open liquidity allows migrated tokens to receive direct SOL injections into their liquidity pools. Unlike token burns (which have no effect), this directly increases the SOL balance of the pool, creating real value growth for token holders.
              </p>
            </div>
          </Card>
        </motion.div>

        {/* Benefits */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Benefits of Open Liquidity</h2>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">💰</div>
                  <div>
                    <h3 className="font-semibold text-cyan-300">Real Value Growth</h3>
                    <p className="text-gray-300 text-sm">Direct SOL injection increases actual token value, not just market cap</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">🔄</div>
                  <div>
                    <h3 className="font-semibold text-cyan-300">Better Than Burning</h3>
                    <p className="text-gray-300 text-sm">Unlike token burns which have no effect, SOL injection creates tangible value</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">📈</div>
                  <div>
                    <h3 className="font-semibold text-cyan-300">Sustainable Growth</h3>
                    <p className="text-gray-300 text-sm">Continuous revenue streams can steadily increase token backing</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* How It Works */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">How Open Liquidity Works</h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-cyan-400 to-cyan-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-cyan-300 mb-1">Token Migration</h3>
                    <p className="text-gray-300 text-sm">Only migrated tokens can receive open liquidity injections</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-emerald-300 mb-1">Direct SOL Injection</h3>
                    <p className="text-gray-300 text-sm">SOL is added directly to the pair address, increasing the pool's SOL balance</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-yellow-400 to-orange-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-yellow-300 mb-1">Value Increase</h3>
                    <p className="text-gray-300 text-sm">More SOL in the pool means higher token value for all holders</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Use Cases */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-emerald-400/30 bg-emerald-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Perfect Use Cases</h2>
              <div className="space-y-4">
                <div className="p-4 rounded-xl bg-purple-400/10 border border-purple-400/20">
                  <h3 className="font-semibold text-purple-300 mb-2">🎵 Artist Revenue Sharing</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    "10% of all ticket and merch sales go directly into our token's liquidity pool, increasing value for all fans who hold our tokens."
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-blue-400/10 border border-blue-400/20">
                  <h3 className="font-semibold text-blue-300 mb-2">🏢 Business Profit Sharing</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Companies can inject a percentage of profits directly into their token's liquidity, creating real value for token holders.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                  <h3 className="font-semibold text-yellow-300 mb-2">🎮 Gaming Revenue</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Game developers can inject in-app purchase revenue into their game token's liquidity, rewarding players.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-pink-400/10 border border-pink-400/20">
                  <h3 className="font-semibold text-pink-300 mb-2">🌱 Community Projects</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    DAOs and communities can inject treasury funds to steadily increase their token's backing and value.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* vs Token Burning */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-red-400/30 bg-red-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Why Not Token Burning?</h2>
              <div className="grid grid-cols-1 gap-4">
                <div className="p-4 rounded-xl bg-red-400/10 border border-red-400/20">
                  <h3 className="font-semibold text-red-300 mb-2">❌ Token Burning</h3>
                  <p className="text-gray-300 text-sm">
                    Reduces supply but doesn't add real value. Often has minimal or no effect on price.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-emerald-400/10 border border-emerald-400/20">
                  <h3 className="font-semibold text-emerald-300 mb-2">✅ SOL Injection</h3>
                  <p className="text-gray-300 text-sm">
                    Adds real SOL backing to the token, creating tangible value increase for all holders.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Pool Stats */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Pool Statistics</h2>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 rounded-xl bg-cyan-400/10 border border-cyan-400/20">
                  <p className="text-2xl font-bold text-white">1,247</p>
                  <p className="text-xs text-gray-400">Active Pools</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-blue-400/10 border border-blue-400/20">
                  <p className="text-2xl font-bold text-white">$45.2M</p>
                  <p className="text-xs text-gray-400">Total Liquidity</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-purple-400/10 border border-purple-400/20">
                  <p className="text-2xl font-bold text-white">$12.8M</p>
                  <p className="text-xs text-gray-400">24h Volume</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-emerald-400/10 border border-emerald-400/20">
                  <p className="text-2xl font-bold text-white">8,934</p>
                  <p className="text-xs text-gray-400">Active Traders</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
