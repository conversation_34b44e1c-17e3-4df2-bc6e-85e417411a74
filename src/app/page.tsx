'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { TokenCard } from '@/components/shared/TokenCard'
import { Button } from '@/components/shared/Button'
import { Card } from '@/components/shared/Card'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { FilterModal, type FilterOptions } from '@/components/shared/FilterModal'
import { useWallet, ConnectButton } from '@suiet/wallet-kit'
import {
  Search,
  Filter,
  Rocket,
  Users,
  Music,
  Building,
  Coins,
  Lock,
  Unlock,
  ArrowRight,
  Star,
  Shield,
  Wallet,
  X,
  Check,
  Trophy,
  Target,
  Zap,
  AlertCircle
} from 'lucide-react'
import { AnimatePresence } from 'framer-motion'
import { staggerContainer, staggerItem } from '@/lib/animations'
import { useTrendingTokens, useFeaturedTokens } from '@/hooks/useTokens'
import { type Token } from '@/lib/database'

// Helper function to convert database token to TokenCard format
function convertTokenToCardFormat(token: Token) {
  return {
    id: token.id,
    name: token.name,
    symbol: token.symbol,
    price: token.current_price || 0,
    priceChange24h: token.price_change_24h || 0,
    volume24h: token.volume_24h || 0,
    marketCap: token.market_cap || 0,
    holders: token.holders_count || 0,
    ath: token.current_price || 0, // For now, use current price as ATH
    priceHistory: [0, 0, 0, token.current_price || 0], // Simplified price history
    launchedAt: new Date(token.created_at),
    migrationStatus: token.migration_status,
    image: token.image_url || 'https://images.unsplash.com/photo-1635805737707-575885ab0820?w=100&h=100&fit=crop&crop=face',
    description: token.description
  }
}

// Logged Out Landing Page Component
function LoggedOutHome() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState(0)

  const tokenTypes = [
    {
      emoji: '🚀',
      title: 'Meme',
      subtitle: 'Viral Tokens',
      description: 'Launch community-driven tokens with built-in trading fees'
    },
    {
      emoji: '🏢',
      title: 'Startup',
      subtitle: 'Business Tokens',
      description: 'Tokenize your business and raise capital'
    },
    {
      emoji: '⭐',
      title: 'Personal',
      subtitle: 'Brand Tokens',
      description: 'Create unique tokens for your identity'
    },
    {
      emoji: '🎵',
      title: 'Band',
      subtitle: 'Music Tokens',
      description: 'Artists tokenize music and connect with fans'
    },
    {
      emoji: '🤖',
      title: 'AI Bots',
      subtitle: 'Trading Bots',
      description: 'AI won\'t take your job, it will make you money... AI will make 100,000 trades a second - Michael Saylor'
    }
  ]

  const [selectedFeature, setSelectedFeature] = useState<typeof features[0] | null>(null)

  const features = [
    {
      emoji: '💰',
      title: 'Service Fees',
      description: 'Earn from every trade',
      fullDescription: 'Collect a percentage fee from every trade of your token. Set your own fee rate and earn passive income as your token gains popularity and trading volume increases.',
      benefits: ['Passive income stream', 'Customizable fee rates', 'Automatic collection', 'Real-time earnings']
    },
    {
      emoji: '🌊',
      title: 'Open Pools',
      description: 'Grow token value with SUI',
      fullDescription: 'Send SUI directly to your liquidity pool address to increase your token\'s value. Perfect for artists who can direct a percentage of ticket sales back to the liquidity pool.',
      benefits: ['Direct value growth', 'Artist-friendly', 'Ticket sales integration', 'Community funding']
    },
    {
      emoji: '🔒',
      title: 'Private Pools',
      description: 'NFT-gated trading',
      fullDescription: 'Require holders to own a specific NFT to trade in your pool. Perfect for exclusive clubs, organizations, and premium communities who want gated access.',
      benefits: ['Exclusive access', 'NFT integration', 'Community control', 'Premium features']
    },
    {
      emoji: '👑',
      title: 'Pool Ownership',
      description: 'Full control & transfer',
      fullDescription: 'Maintain complete control over your token\'s liquidity pool with the ability to transfer ownership. Full governance over your token\'s future and trading mechanics.',
      benefits: ['Complete control', 'Ownership transfer', 'Governance rights', 'Future flexibility']
    }
  ]

  return (
    <MobileLayout headerTitle="Dexsta">
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-8"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-6">
          {/* <div className="relative">
            <motion.h1
              className="text-5xl font-black font-brand bg-gradient-to-r from-[#9945FF] via-[#14F195] to-[#00D4FF] bg-clip-text text-transparent"
            >
              dexsta
            </motion.h1>
            <div className="absolute -inset-2 bg-gradient-to-r from-emerald-400/10 via-transparent to-emerald-400/10 rounded-2xl blur-xl -z-10" />
          </div> */}
          <br/>
          <div className="bg-emerald-400/15 rounded-2xl p-4 border border-emerald-400/30">
            <motion.p
              className="text-lg font-bold text-white font-heading"
            >
              Token Launch & Trading Bots
            </motion.p>
          </div>
        </motion.div>

        {/* Token Type Tabs */}
        <motion.section variants={staggerItem} className="space-y-6">
          <div className="flex overflow-x-auto space-x-2 -mx-4 px-4" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
            <style jsx>{`
              div::-webkit-scrollbar {
                display: none;
              }
            `}</style>
            {tokenTypes.map((type, index) => (
              <motion.button
                key={type.title}
                onClick={() => setActiveTab(index)}
                className={`flex-shrink-0 p-3 rounded-xl transition-all duration-300 w-[calc(33.333%-0.5rem)] ${
                  activeTab === index
                    ? 'bg-gradient-to-br from-emerald-400/20 to-emerald-600/20 border-2 border-emerald-400/50 shadow-lg shadow-emerald-400/20'
                    : 'bg-white/5 border border-gray-600/30 hover:bg-white/10'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="text-center space-y-1.5">
                  <div className="text-2xl">{type.emoji}</div>
                  <div className="space-y-0.5">
                    <div className={`font-semibold text-xs ${
                      activeTab === index ? 'text-emerald-300' : 'text-white'
                    }`}>
                      {type.title}
                    </div>
                    <div className="text-[10px] text-gray-400 leading-tight">
                      {type.subtitle}
                    </div>
                  </div>
                  {activeTab === index && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="w-1.5 h-1.5 bg-emerald-400 rounded-full mx-auto"
                    />
                  )}
                </div>
              </motion.button>
            ))}
          </div>

          {/* Active Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="text-center"
          >
            <Card
              variant="glass"
              padding="lg"
              className="border border-cyan-400/30 bg-gradient-to-br from-cyan-400/10 to-indigo-600/15"
            >
              <div className="space-y-6">
                {/* Emoji */}
                <div className="text-6xl text-center">
                  {tokenTypes[activeTab].emoji}
                </div>

                {/* Content */}
                <div className="space-y-3 text-center">
                  <h3 className="text-2xl font-bold text-white font-heading">
                    {tokenTypes[activeTab].title === 'AI Bots' ? 'AI Trading Bots' : `${tokenTypes[activeTab].title} Tokens`}
                  </h3>
                  <div className="w-16 h-0.5 bg-gradient-to-r from-cyan-400 to-indigo-600 rounded-full mx-auto" />

                  <div className="px-4 py-3 rounded-xl bg-white/10 border border-cyan-400/20">
                    <p className="text-gray-200 font-body leading-relaxed">
                      {tokenTypes[activeTab].description}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
            
          </motion.div>
        </motion.section>

        {/* Divider */}
        <motion.div variants={staggerItem} className="py-6">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-cyan-400/50 to-cyan-400/50"></div>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
              <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            </div>
            <div className="flex-1 h-px bg-gradient-to-l from-transparent via-cyan-400/50 to-cyan-400/50"></div>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.section variants={staggerItem} className="space-y-6">
          <div className="text-center">
            <h3 className="text-xl font-bold text-white font-heading mb-2">
              Powerful Features
            </h3>
            <div className="w-20 h-0.5 bg-gradient-to-r from-cyan-400 to-indigo-600 rounded-full mx-auto" />
          </div>
          <div className="grid grid-cols-2 gap-4">
            {features.map((feature, index) => {
              const colors = [
                'border-rose-400/30 bg-gradient-to-br from-rose-400/10 to-pink-600/15 hover:from-rose-400/15 hover:to-pink-600/20',
                'border-amber-400/30 bg-gradient-to-br from-amber-400/10 to-orange-600/15 hover:from-amber-400/15 hover:to-orange-600/20',
                'border-violet-400/30 bg-gradient-to-br from-violet-400/10 to-purple-600/15 hover:from-violet-400/15 hover:to-purple-600/20',
                'border-emerald-400/30 bg-gradient-to-br from-emerald-400/10 to-green-600/15 hover:from-emerald-400/15 hover:to-green-600/20'
              ]
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card
                    variant="glass"
                    padding="md"
                    interactive
                    className={`${colors[index]} text-center h-32 flex items-center justify-center cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg`}
                    onClick={() => setSelectedFeature(feature)}
                  >
                    <div className="space-y-2">
                      <div className="text-3xl filter drop-shadow-lg">{feature.emoji}</div>
                      <div className="space-y-1">
                        <h4 className="font-bold text-white text-sm font-heading">
                          {feature.title}
                        </h4>
                        <p className="text-xs text-gray-300 font-body leading-tight">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </motion.section>

        {/* Divider */}
        <motion.div variants={staggerItem} className="py-6">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-blue-400/50 to-blue-400/50"></div>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            </div>
            <div className="flex-1 h-px bg-gradient-to-l from-transparent via-blue-400/50 to-blue-400/50"></div>
          </div>
        </motion.div>

        {/* Community Bank Section */}
        <motion.section variants={staggerItem} className="space-y-6">
          <div className="text-center">
            <h3 className="text-xl font-bold text-white font-heading mb-2">
              Community Bank
            </h3>
            <div className="w-20 h-0.5 bg-gradient-to-r from-blue-400 to-indigo-600 rounded-full mx-auto" />
          </div>

          <Card
            variant="glass"
            padding="lg"
            className="border border-blue-400/30 bg-gradient-to-br from-blue-400/10 to-indigo-600/15"
          >
            <div className="space-y-6">
              {/* Bank Icon */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-blue-400 to-indigo-600 flex items-center justify-center mb-4">
                  <Building className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-white font-heading mb-2">
                  0% Interest Loans
                </h4>
                <div className="w-16 h-0.5 bg-gradient-to-r from-blue-400 to-indigo-600 rounded-full mx-auto" />
              </div>

              {/* Bank Description */}
              <div className="space-y-4">
                <div className="p-4 rounded-xl bg-blue-400/10 border border-blue-400/20">
                  <p className="text-gray-200 font-body leading-relaxed text-center">
                    Use your XFT (NFT 2.0) as collateral for instant SUI loans with 0% interest
                  </p>
                </div>

                {/* How it Works */}
                <div className="space-y-3">
                  <h5 className="text-lg font-semibold text-blue-300 font-heading text-center">
                    How It Works
                  </h5>

                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-400 flex items-center justify-center">
                        <span className="text-xs font-bold text-black">1</span>
                      </div>
                      <div>
                        <p className="text-sm text-gray-300 font-body">
                          <span className="font-semibold text-blue-300">50% of platform service fees</span> are sent to the community vault
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-400 flex items-center justify-center">
                        <span className="text-xs font-bold text-black">2</span>
                      </div>
                      <div>
                        <p className="text-sm text-gray-300 font-body">
                          Use your <span className="font-semibold text-blue-300">XFT (NFT 2.0)</span> as collateral for instant loans
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-400 flex items-center justify-center">
                        <span className="text-xs font-bold text-black">3</span>
                      </div>
                      <div>
                        <p className="text-sm text-gray-300 font-body">
                          Borrow up to <span className="font-semibold text-blue-300">95% of your XFT&apos;s value</span> with 0% interest
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Bank Stats Display */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 rounded-xl bg-gradient-to-r from-blue-400/20 to-indigo-600/20 border border-blue-400/30">
                    <p className="text-xs text-blue-300 font-medium mb-1">Community Vault</p>
                    <p className="text-lg font-bold text-white">🏦 1,234 SUI</p>
                  </div>
                  <div className="text-center p-3 rounded-xl bg-gradient-to-r from-blue-400/20 to-indigo-600/20 border border-blue-400/30">
                    <p className="text-xs text-blue-300 font-medium mb-1">Max LTV Ratio</p>
                    <p className="text-lg font-bold text-white">📊 95%</p>
                  </div>
                </div>

                {/* Learn More Button */}
                <div className="text-center pt-2">
                  <Button
                    variant="glass"
                    size="md"
                    className="bg-blue-400/20 border-blue-400/30 text-blue-300 hover:bg-blue-400/30"
                    onClick={() => router.push('/bank')}
                  >
                    <Building className="w-4 h-4 mr-2" />
                    Learn More About Community Bank
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </motion.section>

        {/* Divider */}
        <motion.div variants={staggerItem} className="py-6">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-yellow-400/50 to-yellow-400/50"></div>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            </div>
            <div className="flex-1 h-px bg-gradient-to-l from-transparent via-yellow-400/50 to-yellow-400/50"></div>
          </div>
        </motion.div>

        {/* Trading Game Section */}
        <motion.section variants={staggerItem} className="space-y-6">
          <div className="text-center">
            <h3 className="text-xl font-bold text-white font-heading mb-2">
              Trading Game
            </h3>
            <div className="w-20 h-0.5 bg-gradient-to-r from-cyan-400 to-indigo-600 rounded-full mx-auto" />
          </div>

          <Card
            variant="glass"
            padding="lg"
            className="border border-yellow-400/30 bg-gradient-to-br from-yellow-400/10 to-orange-600/15"
          >
            <div className="space-y-6">
              {/* Game Icon */}
              <div className="text-center">
                <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-yellow-400 to-orange-600 flex items-center justify-center mb-4">
                  <Trophy className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-white font-heading mb-2">
                  Win Big by Trading
                </h4>
                <div className="w-16 h-0.5 bg-gradient-to-r from-yellow-400 to-orange-600 rounded-full mx-auto" />
              </div>

              {/* Game Description */}
              <div className="space-y-4">
                <div className="p-4 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                  <p className="text-gray-200 font-body leading-relaxed text-center">
                    A game that incentivizes trading and rewards active traders with massive prize pools
                  </p>
                </div>

                {/* How it Works */}
                <div className="space-y-3">
                  <h5 className="text-lg font-semibold text-yellow-300 font-heading text-center">
                    How It Works
                  </h5>

                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-yellow-400 flex items-center justify-center">
                        <span className="text-xs font-bold text-black">1</span>
                      </div>
                      <div>
                        <p className="text-sm text-gray-300 font-body">
                          <span className="font-semibold text-yellow-300">50% of creator fees</span> from every trade goes into the traders pot
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-yellow-400 flex items-center justify-center">
                        <span className="text-xs font-bold text-black">2</span>
                      </div>
                      <div>
                        <p className="text-sm text-gray-300 font-body">
                          Every trade counts as an entry - the more you trade, the better your chances
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                      <div className="flex-shrink-0 w-6 h-6 rounded-full bg-yellow-400 flex items-center justify-center">
                        <span className="text-xs font-bold text-black">3</span>
                      </div>
                      <div>
                        <p className="text-sm text-gray-300 font-body">
                          The <span className="font-semibold text-yellow-300">Xth trader wins the entire pot</span> - could be you!
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Prize Pool Display */}
                <div className="text-center p-4 rounded-xl bg-gradient-to-r from-yellow-400/20 to-orange-600/20 border border-yellow-400/30">
                  <p className="text-sm text-yellow-300 font-medium mb-1">Current Prize Pool</p>
                  <p className="text-2xl font-bold text-white">🏆 234.5 SOL</p>
                  <p className="text-xs text-gray-400 mt-1">Next winner at trade #1,247</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.section>

        {/* Divider */}
        <motion.div variants={staggerItem} className="py-6">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-emerald-400/50 to-emerald-400/50"></div>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
            </div>
            <div className="flex-1 h-px bg-gradient-to-l from-transparent via-emerald-400/50 to-emerald-400/50"></div>
          </div>
        </motion.div>

        {/* Feature Modal */}
        <AnimatePresence>
          {selectedFeature && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4"
              style={{ background: 'rgba(10, 10, 15, 0.9)' }}
              onClick={() => setSelectedFeature(null)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.9, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9, y: 20 }}
                className="w-full max-w-md bg-gradient-to-br from-cyan-400/10 to-indigo-600/10 border-2 border-cyan-400/30 rounded-2xl p-6 backdrop-blur-sm shadow-xl shadow-cyan-400/20"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="text-3xl">{selectedFeature.emoji}</div>
                    <h3 className="text-xl font-bold text-white font-heading">
                      {selectedFeature.title}
                    </h3>
                  </div>
                  <button
                    onClick={() => setSelectedFeature(null)}
                    className="p-2 rounded-full hover:bg-white/10 transition-colors"
                  >
                    <X className="w-5 h-5 text-gray-400" />
                  </button>
                </div>

                <div className="space-y-4">
                  <p className="text-gray-200 font-body leading-relaxed">
                    {selectedFeature.fullDescription}
                  </p>

                  <div className="space-y-3">
                    <h4 className="text-lg font-semibold text-cyan-300 font-heading">
                      Key Benefits
                    </h4>
                    <div className="space-y-2">
                      {selectedFeature.benefits.map((benefit: string, index: number) => (
                        <div key={index} className="flex items-center space-x-3">
                          <Check className="w-4 h-4 text-emerald-400 flex-shrink-0" />
                          <span className="text-gray-300 text-sm font-body">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>



        {/* Call to Action */}
        <motion.section variants={staggerItem} className="text-center pt-8 pb-48">
          <Card
            variant="glass"
            padding="lg"
            className="border border-emerald-400/30 bg-gradient-to-br from-emerald-400/15 to-emerald-600/20"
          >
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="text-5xl">🚀</div>
                <div className="space-y-2">
                  <h3 className="text-2xl font-bold text-white font-heading">
                    Ready to Launch?
                  </h3>
                  <div className="w-20 h-0.5 bg-gradient-to-r from-emerald-400 to-emerald-600 rounded-full mx-auto" />
                </div>
                <p className="text-gray-300 font-body leading-relaxed max-w-sm mx-auto">
                  Connect your wallet and start creating your token in minutes
                </p>
              </div>

              <div className="w-full">
                <ConnectButton
                  style={{
                    width: '100%',
                    padding: '16px 24px',
                    borderRadius: '12px',
                    background: 'linear-gradient(to right, rgb(52, 211, 153), rgb(16, 185, 129))',
                    border: 'none',
                    color: 'white',
                    fontSize: '18px',
                    fontWeight: 'bold',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '12px',
                    transition: 'all 0.2s',
                    cursor: 'pointer'
                  }}
                  onConnectSuccess={(wallet) => {
                    console.log('Wallet connected successfully:', wallet)
                  }}
                  onConnectError={(error) => {
                    console.error('Wallet connection failed:', error)
                  }}
                >
                  <Wallet className="w-5 h-5" />
                  Connect Wallet
                  <ArrowRight className="w-5 h-5" />
                </ConnectButton>
              </div>
            </div>
          </Card>
        </motion.section>
      </motion.div>
    </MobileLayout>
  )
}

// Logged In Dashboard Component
function LoggedInHome() {
  const router = useRouter()
  const [activeCategory, setActiveCategory] = useState(0)
  const [showFilterModal, setShowFilterModal] = useState(false)
  const [currentFilter, setCurrentFilter] = useState<FilterOptions>({
    sortBy: 'marketcap',
    sortOrder: 'highest'
  })

  // Fetch real token data
  const { tokens: trendingTokens, loading: trendingLoading, error: trendingError } = useTrendingTokens(20)

  const tokenCategories = [
    { title: 'Featured', emoji: '⭐' },
    { title: 'Meme', emoji: '🚀' },
    { title: 'Startup', emoji: '🏢' },
    { title: 'Personal', emoji: '⭐' },
    { title: 'Band', emoji: '🎵' },
    { title: 'AI Bots', emoji: '🤖' }
  ]

  // Convert database tokens to card format and sort based on current filter
  const convertedTokens = trendingTokens.map(convertTokenToCardFormat)
  const sortedTokens = [...convertedTokens].sort((a, b) => {
    switch (currentFilter.sortBy) {
      case 'marketcap':
        return currentFilter.sortOrder === 'highest' ? b.marketCap - a.marketCap : a.marketCap - b.marketCap
      case 'volume':
        return currentFilter.sortOrder === 'highest' ? b.volume24h - a.volume24h : a.volume24h - b.volume24h
      case 'created':
        const aTime = a.launchedAt?.getTime() || 0
        const bTime = b.launchedAt?.getTime() || 0
        return currentFilter.sortOrder === 'newest' ? bTime - aTime : aTime - bTime
      case 'lastTrade':
        // For demo purposes, use price change as proxy for recent activity
        return currentFilter.sortOrder === 'newest' ? b.priceChange24h - a.priceChange24h : a.priceChange24h - b.priceChange24h
      case 'holders':
        const aHolders = a.holders || 0
        const bHolders = b.holders || 0
        return currentFilter.sortOrder === 'highest' ? bHolders - aHolders : aHolders - bHolders
      default:
        return 0
    }
  })

  return (
    <MobileLayout headerTitle="Dexsta">
      <PageSeparator />
      <div className="space-y-6">
        {/* Bank Vault Balance Card */}
        <br/>
        <motion.div variants={staggerItem}>
          <motion.div
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            transition={{ duration: 0.2 }}
          >
            <Card
              variant="glass"
              padding="lg"
              className="border-2 border-emerald-400/50 bg-gradient-to-br from-emerald-400/5 to-green-600/10 cursor-pointer hover:border-emerald-400/70 hover:from-emerald-400/10 hover:to-green-600/15 transition-all duration-300"
              onClick={() => router.push('/bank')}
            >
              <div className="flex items-center justify-between">
                {/* Left side - Vault info */}
                <div>
                  <h3 className="text-lg font-bold text-white font-heading">
                    All Vault
                  </h3>
                  <p className="text-sm text-emerald-300 font-body">
                    0% Interest Loans
                  </p>
                </div>

                {/* Right side - Vault balance */}
                <div className="text-right">
                  <p className="text-xs text-gray-400 mb-1">Balance</p>
                  <p className="text-xl font-bold text-white">12,450 SUI</p>
                </div>
              </div>
            </Card>
          </motion.div>
        </motion.div>

        {/* Token Category Tabs */}
        <motion.div variants={staggerItem} className="space-y-4">
          <div className="flex overflow-x-auto space-x-2 -mx-4 px-4" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
            <style jsx>{`
              div::-webkit-scrollbar {
                display: none;
              }
            `}</style>
            {tokenCategories.map((category, index) => (
              <motion.button
                key={category.title}
                onClick={() => setActiveCategory(index)}
                className={`flex-shrink-0 px-4 py-2 rounded-xl transition-all duration-300 min-w-[80px] ${
                  activeCategory === index
                    ? 'bg-gradient-to-br from-cyan-400/20 to-indigo-600/20 border border-cyan-400/50'
                    : 'bg-white/5 border border-gray-600/30 hover:bg-white/10'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center space-x-2">
                  <div className="text-lg">{category.emoji}</div>
                  <div className={`font-semibold text-xs ${
                    activeCategory === index ? 'text-cyan-300' : 'text-white'
                  }`}>
                    {category.title}
                  </div>
                </div>
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Search and Filter */}
        <motion.div
          variants={staggerItem}
          className="flex space-x-3"
        >
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search tokens..."
              className="w-full pl-10 pr-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
            />
          </div>
          <Button
            variant="glass"
            size="md"
            onClick={() => setShowFilterModal(true)}
          >
            <Filter className="w-5 h-5" />
          </Button>
        </motion.div>

        {/* Category Tokens - Fast Updates */}
        <motion.section variants={staggerItem} className="space-y-3 pb-32">
          {trendingLoading ? (
            // Loading state
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <Card variant="glass" padding="md">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gray-600 rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-600 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-600 rounded w-1/2"></div>
                      </div>
                      <div className="w-16 h-8 bg-gray-600 rounded"></div>
                    </div>
                  </Card>
                </div>
              ))}
            </div>
          ) : trendingError ? (
            // Error state
            <Card variant="glass" padding="lg" className="text-center">
              <div className="space-y-4">
                <AlertCircle className="w-12 h-12 text-red-400 mx-auto" />
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Failed to Load Tokens</h3>
                  <p className="text-gray-400 text-sm">{trendingError}</p>
                </div>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => window.location.reload()}
                >
                  Try Again
                </Button>
              </div>
            </Card>
          ) : sortedTokens.length === 0 ? (
            // Empty state
            <Card variant="glass" padding="lg" className="text-center">
              <div className="space-y-4">
                <Coins className="w-12 h-12 text-gray-400 mx-auto" />
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">No Tokens Found</h3>
                  <p className="text-gray-400 text-sm">Be the first to create a token!</p>
                </div>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => router.push('/create')}
                >
                  Create Token
                </Button>
              </div>
            </Card>
          ) : (
            // Token list
            sortedTokens.map((token) => (
              <TokenCard
                key={token.id}
                token={token}
                variant="compact"
                onTrade={(token) => console.log('Trade', token)}
                onView={(token) => console.log('View', token)}
              />
            ))
          )}
        </motion.section>

        {/* Filter Modal */}
        <FilterModal
          isOpen={showFilterModal}
          onClose={() => setShowFilterModal(false)}
          currentFilter={currentFilter}
          onApplyFilter={(filter) => {
            setCurrentFilter(filter)
            setShowFilterModal(false)
          }}
        />
      </div>
    </MobileLayout>
  )
}

// Main Home Component
export default function Home() {
  const { connected } = useWallet()

  // Show different content based on wallet connection status
  if (connected) {
    return <LoggedInHome />
  }

  return <LoggedOutHome />
}
