'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { TokenHeader } from '@/components/token/TokenHeader'
import { TokenTabs } from '@/components/token/TokenTabs'
import { TokenChart } from '@/components/token/TokenChart'
import { TokenSwap } from '@/components/token/TokenSwap'
import { TokenInfo } from '@/components/token/TokenInfo'
import { TokenChat } from '@/components/token/TokenChat'
import { TokenHolders } from '@/components/token/TokenHolders'
import { MigrationProgress } from '@/components/token/MigrationProgress'
import { TradingGameStats } from '@/components/token/TradingGameStats'
import { getTokenService } from '@/services/token/tokenService'
import { useBondingCurve } from '@/hooks/useBondingCurve'
import { staggerContainer, staggerItem } from '@/lib/animations'

export default function TokenPage() {
  const params = useParams()
  const tokenObjectId = params.address as string

  const [activeTab, setActiveTab] = useState('chart')
  const [showChart, setShowChart] = useState(true)
  const [tokenInfo, setTokenInfo] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const tokenService = getTokenService()
  const { currentPrice, marketCap } = useBondingCurve(tokenInfo)

  // Fetch token info
  useEffect(() => {
    const fetchTokenInfo = async () => {
      if (!tokenObjectId) return

      try {
        setIsLoading(true)
        const info = await tokenService.getTokenInfo(tokenObjectId)

        if (info) {
          setTokenInfo(info)
        } else {
          setError('Token not found')
        }
      } catch (err) {
        console.error('Error fetching token info:', err)
        setError('Failed to load token information')
      } finally {
        setIsLoading(false)
      }
    }

    fetchTokenInfo()
  }, [tokenObjectId, tokenService])

  // Auto-refresh token info every 30 seconds
  useEffect(() => {
    if (!tokenObjectId) return

    const interval = setInterval(async () => {
      try {
        const info = await tokenService.getTokenInfo(tokenObjectId)
        if (info) {
          setTokenInfo(info)
        }
      } catch (err) {
        console.error('Error refreshing token info:', err)
      }
    }, 30000)

    return () => clearInterval(interval)
  }, [tokenObjectId, tokenService])

  const tabs = [
    { id: 'chart', label: 'Chart', icon: '📈' },
    { id: 'info', label: 'Info', icon: 'ℹ️' },
    { id: 'chat', label: 'Chat', icon: '💬' },
    { id: 'holders', label: 'Holders', icon: '👥' }
  ]

  if (isLoading) {
    return (
      <MobileLayout headerTitle="Loading..." showBackButton={true}>
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="w-8 h-8 border-2 border-blue-400/30 border-t-blue-400 rounded-full animate-spin" />
        </div>
      </MobileLayout>
    )
  }

  if (error || !tokenInfo) {
    return (
      <MobileLayout headerTitle="Error" showBackButton={true}>
        <div className="flex flex-col items-center justify-center min-h-[50vh] space-y-4">
          <div className="text-6xl">😞</div>
          <h2 className="text-xl font-bold text-white">Token Not Found</h2>
          <p className="text-gray-400 text-center">{error || 'This token does not exist or has been removed.'}</p>
        </div>
      </MobileLayout>
    )
  }

  const isMigrated = tokenInfo.isMigrated || tokenInfo.liquiditySuiAmount > 0 // Check for migration status

  return (
    <MobileLayout
      headerTitle={tokenInfo.symbol}
      showBackButton={true}
      className="pb-24"
    >
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-4"
      >
        {/* Token Header */}
        <motion.div variants={staggerItem}>
          <TokenHeader
            tokenInfo={tokenInfo}
            currentPrice={currentPrice}
            marketCap={marketCap}
            isMigrated={isMigrated}
          />
        </motion.div>

        {/* Migration Progress (Pre-migration only) */}
        {!isMigrated && (
          <motion.div variants={staggerItem}>
            <MigrationProgress
              tokenInfo={tokenInfo}
              currentPrice={currentPrice}
            />
          </motion.div>
        )}

        {/* Trading Game Stats */}
        <motion.div variants={staggerItem}>
          <TradingGameStats
            tokenInfo={tokenInfo}
            isMigrated={isMigrated}
          />
        </motion.div>

        {/* Token Tabs */}
        <motion.div variants={staggerItem}>
          <TokenTabs
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />
        </motion.div>

        {/* Tab Content */}
        <motion.div variants={staggerItem} className="space-y-4">
          {activeTab === 'chart' && (
            <div className="space-y-4">
              {/* Chart Toggle */}
              <div className="flex items-center justify-between px-4">
                <h3 className="text-lg font-semibold text-white">Price Chart</h3>
                <button
                  onClick={() => setShowChart(!showChart)}
                  className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                >
                  {showChart ? 'Hide Chart' : 'Show Chart'}
                </button>
              </div>

              {/* Chart */}
              {showChart && (
                <TokenChart
                  tokenObjectId={tokenObjectId}
                  tokenInfo={tokenInfo}
                  isMigrated={isMigrated}
                />
              )}

              {/* Swap Component */}
              <TokenSwap
                tokenObjectId={tokenObjectId}
                tokenInfo={tokenInfo}
                isMigrated={isMigrated}
                onSwapComplete={async () => {
                  try {
                    const info = await tokenService.getTokenInfo(tokenObjectId)
                    if (info) {
                      setTokenInfo(info)
                    }
                  } catch (err) {
                    console.error('Error refreshing token info:', err)
                  }
                }}
              />
            </div>
          )}

          {activeTab === 'info' && (
            <TokenInfo
              tokenInfo={tokenInfo}
              currentPrice={currentPrice}
              marketCap={marketCap}
              isMigrated={isMigrated}
            />
          )}

          {activeTab === 'chat' && (
            <TokenChat
              tokenObjectId={tokenObjectId}
              tokenInfo={tokenInfo}
            />
          )}

          {activeTab === 'holders' && (
            <TokenHolders
              tokenObjectId={tokenObjectId}
              tokenInfo={tokenInfo}
            />
          )}
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
