'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { staggerContainer, staggerItem } from '@/lib/animations'

export default function TradingFeesPage() {
  return (
    <MobileLayout headerTitle="Trading Fees" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="text-6xl">💰</div>
          <h1 className="text-2xl font-bold text-white font-heading">
            Trading Fees
          </h1>
          <div className="w-20 h-0.5 bg-gradient-to-r from-emerald-400 to-emerald-600 rounded-full mx-auto" />
        </motion.div>

        {/* Platform Fees */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-emerald-400/30 bg-emerald-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Platform Fees</h2>
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 rounded-xl bg-white/5">
                  <span className="text-gray-300">Token Creation</span>
                  <span className="font-bold text-emerald-300">0.1 SOL</span>
                </div>
                <div className="flex justify-between items-center p-3 rounded-xl bg-white/5">
                  <span className="text-gray-300">Trading (Platform Fee)</span>
                  <span className="font-bold text-emerald-300">0%</span>
                </div>
                <div className="flex justify-between items-center p-3 rounded-xl bg-white/5">
                  <span className="text-gray-300">Liquidity Pool Creation</span>
                  <span className="font-bold text-emerald-300">Free</span>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Creator Fees */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Creator Fees</h2>
              <p className="text-gray-300 text-sm">
                Token creators can set their own trading fees when launching tokens:
              </p>
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                  <span className="text-gray-300">Buy Fee Range</span>
                  <span className="font-bold text-yellow-300">0% - 10%</span>
                </div>
                <div className="flex justify-between items-center p-3 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                  <span className="text-gray-300">Sell Fee Range</span>
                  <span className="font-bold text-yellow-300">0% - 10%</span>
                </div>
                <div className="flex justify-between items-center p-3 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                  <span className="text-gray-300">Recommended</span>
                  <span className="font-bold text-yellow-300">0.5% - 2%</span>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Trading Game Fees */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Trading Game</h2>
              <p className="text-gray-300 text-sm">
                How creator fees are distributed in the trading game:
              </p>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-purple-400/20 to-pink-400/20 border border-purple-400/30">
                  <div>
                    <p className="font-semibold text-purple-300">To Creator</p>
                    <p className="text-xs text-gray-400">Direct revenue</p>
                  </div>
                  <span className="text-2xl font-bold text-purple-300">50%</span>
                </div>
                <div className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-emerald-400/20 to-green-400/20 border border-emerald-400/30">
                  <div>
                    <p className="font-semibold text-emerald-300">To Traders Pot</p>
                    <p className="text-xs text-gray-400">Prize pool for winners</p>
                  </div>
                  <span className="text-2xl font-bold text-emerald-300">50%</span>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Fee Examples */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Fee Examples</h2>
              <div className="space-y-4">
                <div className="p-4 rounded-xl bg-cyan-400/10 border border-cyan-400/20">
                  <h3 className="font-semibold text-cyan-300 mb-2">Example: 1% Buy/Sell Fee</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Trade Amount:</span>
                      <span className="text-white">1 SOL</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Creator Fee (1%):</span>
                      <span className="text-white">0.01 SOL</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">To Creator:</span>
                      <span className="text-emerald-300">0.005 SOL</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">To Traders Pot:</span>
                      <span className="text-emerald-300">0.005 SOL</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Important Notes */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-yellow-400/30 bg-yellow-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Important Notes</h2>
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">⚠️</div>
                  <p className="text-gray-300">
                    Creator fees are set at token creation and cannot be changed later
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">💡</div>
                  <p className="text-gray-300">
                    Lower fees typically attract more traders and higher volume
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">🎯</div>
                  <p className="text-gray-300">
                    Fees are automatically collected and distributed on every trade
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
