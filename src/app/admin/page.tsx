'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useWallet, ConnectButton } from '@suiet/wallet-kit'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { But<PERSON> } from '@/components/shared/Button'
import { Card } from '@/components/shared/Card'
import { PageSeparator } from '@/components/shared/PageSeparator'
import {
  Shield,
  Settings,
  CheckCircle,
  AlertTriangle,
  Key,
  Users,
  DollarSign,
  Target,
  Zap,
  Clock,
  Trophy
} from 'lucide-react'
import { staggerContainer, staggerItem } from '@/lib/animations'
import { useContracts } from '@/hooks/useContracts'
import {
  PLATFORM_ADMIN_ADDRESS,
  TOKEN_PLATFORM_SETTINGS_ID,
  POOL_PLATFORM_SETTINGS_ID
} from '@/constants/contracts'

interface PoolSettings {
  platformFeeBps: number
  lpFeeBps: number
  mintFee: number
  minRewardTradeAmount: number
  initialRewardGoal: number
  bondingCurveGoal: number
  rewardGoalIncrease: number
  rewardGoalDecreaseAmount: number
  rewardGoalDecreaseThreshold: number
  rewardGoalProximityThreshold: number
  migrationFeePercentage: number
  migrationGasFee: number
  platformFeeAddress: string
}

interface TokenSettings {
  platformFeeBps: number
  rewardFeeBps: number
  mintFee: number
  minRewardTradeAmount: number
  initialRewardGoal: number
  bondingCurveGoal: number
  rewardGoalIncrease: number
  rewardGoalDecreaseAmount: number
  rewardGoalDecreaseThreshold: number
  rewardGoalProximityThreshold: number
  migrationFeePercentage: number
  migrationGasFee: number
  platformFeeAddress: string
}

export default function AdminPage() {
  const { connected, account } = useWallet()
  const {
    initializeTokenAdmin,
    initializePoolAdmin,
    initializeFireRegistry,
    updateContractAddresses,
    updateTokenPlatformSettings,
    updatePoolPlatformSettings,
    updateTokenCurveAggressiveness,
    updateTokenVirtualLiquidityBuffer,
    updatePoolCurveAggressiveness,
    updatePoolVirtualLiquidityBuffer,
    updateLabelSettings,
    loading
  } = useContracts()

  const [activeTab, setActiveTab] = useState<'pool' | 'token' | 'fire' | 'platform' | 'settings'>('pool')
  const [isInitializing, setIsInitializing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [initResults, setInitResults] = useState<{
    tokenAdmin?: { success: boolean; error?: string; txHash?: string }
    poolAdmin?: { success: boolean; error?: string; txHash?: string }
    fireRegistry?: { success: boolean; error?: string; txHash?: string }
  }>({})

  // Settings for contract updates
  const [contractAddresses, setContractAddresses] = useState({
    tokenContract: '',
    poolContract: '',
    labelContract: '',
    operatorContract: '',
    xftContract: '',
    marketplaceContract: '',
    bankContract: ''
  })

  const [labelSettings, setLabelSettings] = useState({
    annualMintPrice: '*********0', // 1 SUI in MIST
    marketplaceFee: '250' // 2.5% in BPS
  })

  const [platformSettings, setPlatformSettings] = useState({
    platformFeeAddress: PLATFORM_ADMIN_ADDRESS,
    platformLabelId: '', // Platform label object ID (optional)
    platformFeeBps: 300, // 3%
    rewardFeeBps: 100, // 1%
    lpFeeBps: 20, // 20% of platform fee for pools
    mintFee: '*********', // 0.1 SUI
    minRewardTradeAmount: '********', // 0.01 SUI
    initialRewardGoal: '*********0', // 1 SUI
    bondingCurveGoal: '***********', // 60 SUI
    rewardGoalIncrease: '*********', // 0.5 SUI
    rewardGoalDecreaseAmount: '*********', // 0.1 SUI
    rewardGoalDecreaseThreshold: 300, // 5 minutes
    rewardGoalProximityThreshold: 90, // 90%
    migrationFeePercentage: 5, // 5%
    migrationGasFee: '5000000', // 0.005 SUI
    existingTokenImportFee: '*********', // 0.1 SUI for pools
    // Bonding curve aggressiveness controls
    tokenCurveAggressiveness: 1000, // 1.0x normal curve (range: 100-5000)
    tokenVirtualLiquidityBuffer: '1000000', // 0.001 SUI for tokens
    poolCurveAggressiveness: 1000, // 1.0x normal curve (range: 100-5000)
    poolVirtualLiquidityBuffer: '********', // 0.01 SUI for pools
  })

  // Check if user is admin
  const isAdmin = connected && account?.address === PLATFORM_ADMIN_ADDRESS

  // Check if contracts are already initialized
  const isTokenAdminInitialized = TOKEN_PLATFORM_SETTINGS_ID !== '0x0000000000000000000000000000000000000000000000000000000000000000'
  const isPoolAdminInitialized = POOL_PLATFORM_SETTINGS_ID !== '0x0000000000000000000000000000000000000000000000000000000000000000'



  const handleInitializePool = async () => {
    if (!connected || !account) return

    setIsInitializing(true)
    setError(null)
    setInitResults(prev => ({ ...prev, poolAdmin: undefined }))

    try {
      const result = await initializePoolAdmin()

      setInitResults(prev => ({
        ...prev,
        poolAdmin: {
          success: true,
          txHash: result.digest
        }
      }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Pool initialization failed'
      setError(errorMessage)
      setInitResults(prev => ({
        ...prev,
        poolAdmin: {
          success: false,
          error: errorMessage
        }
      }))
    } finally {
      setIsInitializing(false)
    }
  }

  const handleInitializeToken = async () => {
    if (!connected || !account) return

    setIsInitializing(true)
    setError(null)
    setInitResults(prev => ({ ...prev, tokenAdmin: undefined }))

    try {
      const result = await initializeTokenAdmin()

      setInitResults(prev => ({
        ...prev,
        tokenAdmin: {
          success: true,
          txHash: result.digest
        }
      }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Token initialization failed'
      setError(errorMessage)
      setInitResults(prev => ({
        ...prev,
        tokenAdmin: {
          success: false,
          error: errorMessage
        }
      }))
    } finally {
      setIsInitializing(false)
    }
  }

  const handleInitializeFireRegistry = async () => {
    if (!connected || !account) return

    setIsInitializing(true)
    setError(null)
    setInitResults(prev => ({ ...prev, fireRegistry: undefined }))

    try {
      // Check if Fire Registry is deployed and all contracts are provided
      const requiredContracts = [
        'tokenContract', 'poolContract', 'labelContract', 'operatorContract',
        'xftContract', 'marketplaceContract', 'bankContract'
      ]

      const missingContracts = requiredContracts.filter(contract =>
        !contractAddresses[contract as keyof typeof contractAddresses]
      )

      if (missingContracts.length > 0) {
        throw new Error(`Please enter all contract addresses. Missing: ${missingContracts.join(', ')}`)
      }

      const result = await initializeFireRegistry(contractAddresses)

      setInitResults(prev => ({
        ...prev,
        fireRegistry: {
          success: true,
          txHash: result.digest
        }
      }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Fire Registry initialization failed'
      setError(errorMessage)
      setInitResults(prev => ({
        ...prev,
        fireRegistry: {
          success: false,
          error: errorMessage
        }
      }))
    } finally {
      setIsInitializing(false)
    }
  }

  const handleUpdateContractAddresses = async () => {
    if (!connected || !account) return

    setIsInitializing(true)
    setError(null)

    try {
      await updateContractAddresses(contractAddresses)
      alert('Contract addresses updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update contract addresses'
      setError(errorMessage)
    } finally {
      setIsInitializing(false)
    }
  }

  const handleUpdateLabelSettings = async () => {
    if (!connected || !account) return

    setIsInitializing(true)
    setError(null)

    try {
      await updateLabelSettings(labelSettings)
      alert('Label settings updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update label settings'
      setError(errorMessage)
    } finally {
      setIsInitializing(false)
    }
  }

  const handleUpdateTokenPlatformSettings = async () => {
    if (!connected || !account) return

    setIsInitializing(true)
    setError(null)

    try {
      await updateTokenPlatformSettings({
        platformFeeAddress: platformSettings.platformFeeAddress,
        platformFeeBps: platformSettings.platformFeeBps,
        rewardFeeBps: platformSettings.rewardFeeBps,
        mintFee: platformSettings.mintFee,
        minRewardTradeAmount: platformSettings.minRewardTradeAmount,
        initialRewardGoal: platformSettings.initialRewardGoal,
        bondingCurveGoal: platformSettings.bondingCurveGoal,
        rewardGoalIncrease: platformSettings.rewardGoalIncrease,
        rewardGoalDecreaseAmount: platformSettings.rewardGoalDecreaseAmount,
        rewardGoalDecreaseThreshold: platformSettings.rewardGoalDecreaseThreshold,
        rewardGoalProximityThreshold: platformSettings.rewardGoalProximityThreshold,
        migrationFeePercentage: platformSettings.migrationFeePercentage,
        migrationGasFee: platformSettings.migrationGasFee,
      })
      alert('Token platform settings updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update token platform settings'
      setError(errorMessage)
    } finally {
      setIsInitializing(false)
    }
  }

  const handleUpdatePoolPlatformSettings = async () => {
    if (!connected || !account) return

    setIsInitializing(true)
    setError(null)

    try {
      await updatePoolPlatformSettings({
        platformFeeAddress: platformSettings.platformFeeAddress,
        platformFeeBps: platformSettings.platformFeeBps,
        lpFeeBps: platformSettings.lpFeeBps,
        rewardFeeBps: platformSettings.rewardFeeBps,
        existingTokenImportFee: platformSettings.existingTokenImportFee,
        minRewardTradeAmount: platformSettings.minRewardTradeAmount,
        initialRewardGoal: platformSettings.initialRewardGoal,
        rewardGoalIncrease: platformSettings.rewardGoalIncrease,
        rewardGoalDecreaseAmount: platformSettings.rewardGoalDecreaseAmount,
        rewardGoalDecreaseThreshold: platformSettings.rewardGoalDecreaseThreshold,
        rewardGoalProximityThreshold: platformSettings.rewardGoalProximityThreshold,
      })
      alert('Pool platform settings updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update pool platform settings'
      setError(errorMessage)
    } finally {
      setIsInitializing(false)
    }
  }

  const handleUpdateBondingCurveSettings = async () => {
    if (!connected || !account) return

    setIsInitializing(true)
    setError(null)

    try {
      // Update token curve aggressiveness
      await updateTokenCurveAggressiveness(platformSettings.tokenCurveAggressiveness)

      // Update token virtual liquidity buffer
      await updateTokenVirtualLiquidityBuffer(platformSettings.tokenVirtualLiquidityBuffer)

      // Update pool curve aggressiveness
      await updatePoolCurveAggressiveness(platformSettings.poolCurveAggressiveness)

      // Update pool virtual liquidity buffer
      await updatePoolVirtualLiquidityBuffer(platformSettings.poolVirtualLiquidityBuffer)

      alert('Bonding curve settings updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update bonding curve settings'
      setError(errorMessage)
    } finally {
      setIsInitializing(false)
    }
  }

  const handleUpdateRewardGameSettings = async () => {
    if (!connected || !account) return

    setIsInitializing(true)
    setError(null)

    try {
      // Update token reward game settings
      await updateTokenPlatformSettings({
        platformFeeAddress: platformSettings.platformFeeAddress,
        platformFeeBps: platformSettings.platformFeeBps,
        rewardFeeBps: platformSettings.rewardFeeBps,
        mintFee: platformSettings.mintFee,
        minRewardTradeAmount: platformSettings.minRewardTradeAmount,
        initialRewardGoal: platformSettings.initialRewardGoal,
        bondingCurveGoal: platformSettings.bondingCurveGoal,
        rewardGoalIncrease: platformSettings.rewardGoalIncrease,
        rewardGoalDecreaseAmount: platformSettings.rewardGoalDecreaseAmount,
        rewardGoalDecreaseThreshold: platformSettings.rewardGoalDecreaseThreshold,
        rewardGoalProximityThreshold: platformSettings.rewardGoalProximityThreshold,
        migrationFeePercentage: platformSettings.migrationFeePercentage,
        migrationGasFee: platformSettings.migrationGasFee,
      })

      // Update pool reward game settings
      await updatePoolPlatformSettings({
        platformFeeAddress: platformSettings.platformFeeAddress,
        platformFeeBps: platformSettings.platformFeeBps,
        lpFeeBps: platformSettings.lpFeeBps,
        rewardFeeBps: platformSettings.rewardFeeBps,
        existingTokenImportFee: platformSettings.existingTokenImportFee,
        minRewardTradeAmount: platformSettings.minRewardTradeAmount,
        initialRewardGoal: platformSettings.initialRewardGoal,
        rewardGoalIncrease: platformSettings.rewardGoalIncrease,
        rewardGoalDecreaseAmount: platformSettings.rewardGoalDecreaseAmount,
        rewardGoalDecreaseThreshold: platformSettings.rewardGoalDecreaseThreshold,
        rewardGoalProximityThreshold: platformSettings.rewardGoalProximityThreshold,
      })

      alert('Reward game settings updated successfully for both token and pool contracts!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update reward game settings'
      setError(errorMessage)
    } finally {
      setIsInitializing(false)
    }
  }





  return (
    <MobileLayout headerTitle="Admin Panel" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Admin Status */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className={`border ${
            (initResults.poolAdmin?.success && initResults.tokenAdmin?.success && initResults.fireRegistry?.success)
              ? 'border-emerald-400/30 bg-emerald-400/5'
              : (initResults.poolAdmin?.success || initResults.tokenAdmin?.success || initResults.fireRegistry?.success)
              ? 'border-yellow-400/30 bg-yellow-400/5'
              : 'border-blue-400/30 bg-blue-400/5'
          }`}>
            <div className="text-center space-y-4">
              <div className={`w-16 h-16 mx-auto rounded-full bg-gradient-to-br flex items-center justify-center ${
                (initResults.poolAdmin?.success && initResults.tokenAdmin?.success && initResults.fireRegistry?.success)
                  ? 'from-emerald-400 to-emerald-600'
                  : (initResults.poolAdmin?.success || initResults.tokenAdmin?.success || initResults.fireRegistry?.success)
                  ? 'from-yellow-400 to-yellow-600'
                  : 'from-blue-400 to-blue-600'
              }`}>
                <Shield className="w-8 h-8 text-white" />
              </div>

              <div>
                <h1 className="text-xl font-bold text-white font-heading">
                  Platform Administration
                </h1>
                <p className="text-gray-300 text-sm mt-2">
                  Initialize and manage Dexsta platform contracts
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className={`flex items-center space-x-2 ${connected ? 'text-emerald-400' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${connected ? 'bg-emerald-400' : 'bg-gray-400'}`} />
                    <span>Wallet Connected</span>
                  </div>
                  <div className={`flex items-center space-x-2 ${isAdmin ? 'text-emerald-400' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${isAdmin ? 'bg-emerald-400' : 'bg-gray-400'}`} />
                    <span>Admin Authorized</span>
                  </div>
                  <div className={`flex items-center space-x-2 ${initResults.fireRegistry?.success ? 'text-emerald-400' : 'text-yellow-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${initResults.fireRegistry?.success ? 'bg-emerald-400' : 'bg-yellow-400'}`} />
                    <span>Fire Registry</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className={`flex items-center space-x-2 ${initResults.poolAdmin?.success ? 'text-emerald-400' : 'text-yellow-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${initResults.poolAdmin?.success ? 'bg-emerald-400' : 'bg-yellow-400'}`} />
                    <span>Pool Contract</span>
                  </div>
                  <div className={`flex items-center space-x-2 ${initResults.tokenAdmin?.success ? 'text-emerald-400' : 'text-yellow-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${initResults.tokenAdmin?.success ? 'bg-emerald-400' : 'bg-yellow-400'}`} />
                    <span>Token Contract</span>
                  </div>
                  <div className="flex items-center space-x-2 text-purple-400">
                    <div className="w-2 h-2 rounded-full bg-purple-400" />
                    <span>Settings Active</span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Non-Admin Warning */}
        {connected && !isAdmin && (
          <motion.div variants={staggerItem}>
            <Card variant="glass" padding="md" className="border border-red-400/30 bg-red-400/5">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-red-300 text-sm">Access Restricted</h3>
                  <p className="text-gray-300 text-xs leading-relaxed mt-1">
                    This admin panel is restricted to platform administrators only.
                    Your wallet address does not have admin privileges.
                  </p>
                  <p className="text-red-300 text-xs font-mono mt-2 break-all">
                    Your address: {account?.address}
                  </p>
                  <p className="text-gray-400 text-xs font-mono mt-1 break-all">
                    Admin address: {PLATFORM_ADMIN_ADDRESS}
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Dynamic Status Notice */}
        <motion.div variants={staggerItem}>
          {((initResults.poolAdmin?.success || isPoolAdminInitialized) && (initResults.tokenAdmin?.success || isTokenAdminInitialized)) ? (
            <Card variant="glass" padding="md" className="border border-emerald-400/30 bg-emerald-400/5">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-emerald-300 text-sm">Platform Ready</h3>
                  <p className="text-gray-300 text-xs leading-relaxed mt-1">
                    {(isPoolAdminInitialized && isTokenAdminInitialized) ?
                      'Admin contracts are pre-initialized and ready for token creation.' :
                      'Platform is fully initialized and ready for token creation.'
                    } Admin wallet:
                  </p>
                  <p className="text-emerald-300 text-xs font-mono mt-1 break-all">
                    {PLATFORM_ADMIN_ADDRESS}
                  </p>
                  {(isPoolAdminInitialized || isTokenAdminInitialized) && (
                    <div className="mt-2 text-xs text-emerald-200">
                      <p>✅ Pool Admin: {isPoolAdminInitialized ? 'Pre-initialized' : 'Ready'}</p>
                      <p>✅ Token Admin: {isTokenAdminInitialized ? 'Pre-initialized' : 'Ready'}</p>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ) : ((initResults.poolAdmin?.success || isPoolAdminInitialized) || (initResults.tokenAdmin?.success || isTokenAdminInitialized)) ? (
            <Card variant="glass" padding="md" className="border border-yellow-400/30 bg-yellow-400/5">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-yellow-300 text-sm">Partial Initialization</h3>
                  <p className="text-gray-300 text-xs leading-relaxed mt-1">
                    Some contracts are initialized. Complete all initializations to enable full platform functionality.
                  </p>
                </div>
              </div>
            </Card>
          ) : (
            <Card variant="glass" padding="md" className="border border-blue-400/30 bg-blue-400/5">
              <div className="flex items-start space-x-3">
                <Shield className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-blue-300 text-sm">Ready for Initialization</h3>
                  <p className="text-gray-300 text-xs leading-relaxed mt-1">
                    Initialize contracts to set up the Dexsta platform. Start with Pool and Token contracts.
                  </p>
                </div>
              </div>
            </Card>
          )}
        </motion.div>

        {/* Contract Tabs */}
        <motion.div variants={staggerItem}>
          <div className="grid grid-cols-2 gap-2 bg-gray-800/50 rounded-2xl p-1">
            {[
              { id: 'pool', label: 'Pool Contract', icon: '🏊' },
              { id: 'token', label: 'Token Contract', icon: '🪙' },
              { id: 'fire', label: 'Fire Registry', icon: '🔥' },
              { id: 'platform', label: 'Platform Settings', icon: '💰' },
              { id: 'settings', label: 'XFT Settings', icon: '⚙️' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center justify-center space-x-2 py-3 px-2 rounded-xl font-medium transition-all duration-300 text-sm ${
                  activeTab === tab.id
                    ? "bg-gradient-primary text-white shadow-lg"
                    : "text-gray-400 hover:text-white"
                }`}
              >
                <span>{tab.icon}</span>
                <span className="hidden sm:inline">{tab.label}</span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Tab Content */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-white font-heading">
                  {activeTab === 'pool' && 'Pool Contract Initialization'}
                  {activeTab === 'token' && 'Token Contract Initialization'}
                  {activeTab === 'fire' && 'Fire Registry Setup'}
                  {activeTab === 'platform' && 'Platform Settings'}
                  {activeTab === 'settings' && 'XFT Settings'}
                </h2>
                <Settings className="w-6 h-6 text-gray-400" />
              </div>

              {/* Pool/Token Contract Info */}
              {(activeTab === 'pool' || activeTab === 'token') && (
                <div className="space-y-4">
                  <div className="p-4 rounded-xl bg-blue-500/10 border border-blue-500/20">
                    <h3 className="text-blue-300 font-medium mb-2">Default Settings</h3>
                    <div className="space-y-2 text-sm text-gray-300">
                      <div className="flex justify-between">
                        <span>Platform Fee:</span>
                        <span>3% (300 BPS)</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Creator Fee:</span>
                        <span>1% (100 BPS)</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Platform Address:</span>
                        <span className="font-mono text-xs">{PLATFORM_ADMIN_ADDRESS.slice(0, 8)}...</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-yellow-500/10 border border-yellow-500/20">
                    <h3 className="text-yellow-300 font-medium mb-2">⚠️ Important</h3>
                    <p className="text-sm text-gray-300">
                      Contract initialization sets up platform-wide settings and can only be done once.
                      Make sure you're connected with the correct admin wallet.
                    </p>
                  </div>
                </div>
              )}

              {/* Fire Registry Setup */}
              {activeTab === 'fire' && (
                <div className="space-y-4">
                  <div className="p-4 rounded-xl bg-orange-500/10 border border-orange-500/20">
                    <h3 className="text-orange-300 font-medium mb-2">Contract Addresses</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Token Contract <span className="text-xs text-gray-400">(Dexsta Package)</span>
                        </label>
                        <input
                          type="text"
                          value={contractAddresses.tokenContract}
                          onChange={(e) => setContractAddresses(prev => ({ ...prev, tokenContract: e.target.value }))}
                          placeholder="0x5c2c5dfb5174b0c95e2c7052b5e1fadce6e08b58759368e70848a8adbc3e374e"
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm font-mono"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Pool Contract <span className="text-xs text-gray-400">(Same as Token)</span>
                        </label>
                        <input
                          type="text"
                          value={contractAddresses.poolContract}
                          onChange={(e) => setContractAddresses(prev => ({ ...prev, poolContract: e.target.value }))}
                          placeholder="0x5c2c5dfb5174b0c95e2c7052b5e1fadce6e08b58759368e70848a8adbc3e374e"
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm font-mono"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Label Contract <span className="text-xs text-gray-400">(XFT Package)</span>
                        </label>
                        <input
                          type="text"
                          value={contractAddresses.labelContract}
                          onChange={(e) => setContractAddresses(prev => ({ ...prev, labelContract: e.target.value }))}
                          placeholder="0x4dc8f30853631d23aef24cbf60797b13e70457241928d6d82290d54690cb9f42"
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm font-mono"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Operator Contract <span className="text-xs text-gray-400">(Same as XFT)</span>
                        </label>
                        <input
                          type="text"
                          value={contractAddresses.operatorContract}
                          onChange={(e) => setContractAddresses(prev => ({ ...prev, operatorContract: e.target.value }))}
                          placeholder="0x4dc8f30853631d23aef24cbf60797b13e70457241928d6d82290d54690cb9f42"
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm font-mono"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          XFT Contract <span className="text-xs text-gray-400">(NFT 2.0 - Same as XFT)</span>
                        </label>
                        <input
                          type="text"
                          value={contractAddresses.xftContract}
                          onChange={(e) => setContractAddresses(prev => ({ ...prev, xftContract: e.target.value }))}
                          placeholder="0x4dc8f30853631d23aef24cbf60797b13e70457241928d6d82290d54690cb9f42"
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm font-mono"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Marketplace Contract <span className="text-xs text-gray-400">(To be deployed)</span>
                        </label>
                        <input
                          type="text"
                          value={contractAddresses.marketplaceContract}
                          onChange={(e) => setContractAddresses(prev => ({ ...prev, marketplaceContract: e.target.value }))}
                          placeholder="0x... (marketplace package address)"
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm font-mono"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Bank Contract <span className="text-xs text-gray-400">(All Vault - To be deployed)</span>
                        </label>
                        <input
                          type="text"
                          value={contractAddresses.bankContract}
                          onChange={(e) => setContractAddresses(prev => ({ ...prev, bankContract: e.target.value }))}
                          placeholder="0x... (bank package address)"
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm font-mono"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-yellow-500/10 border border-yellow-500/20">
                    <h3 className="text-yellow-300 font-medium mb-2">🔥 Fire Registry</h3>
                    <p className="text-sm text-gray-300 mb-3">
                      The Fire Registry enables inter-contract communication by storing all contract addresses.
                      Deploy this first, then update other contracts with the registry address.
                    </p>
                    <div className="text-xs text-gray-400 space-y-1">
                      <p><strong>Manages:</strong></p>
                      <p>• Token & Pool contracts (Dexsta trading)</p>
                      <p>• Label & Operator contracts (XFT permissions)</p>
                      <p>• XFT contract (NFT 2.0 system)</p>
                      <p>• Marketplace contract (XFT trading)</p>
                      <p>• Bank contract (All Vault lending)</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Platform Settings */}
              {activeTab === 'platform' && (
                <div className="space-y-4">
                  <div className="p-4 rounded-xl bg-green-500/10 border border-green-500/20">
                    <h3 className="text-green-300 font-medium mb-2">💰 Platform Fee Configuration</h3>
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-3 h-3 bg-green-500 rounded"></div>
                      <span className="text-xs text-green-300 font-medium">Updated by: Token Platform Settings button</span>
                    </div>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Platform Fee Address
                        </label>
                        <input
                          type="text"
                          value={platformSettings.platformFeeAddress}
                          onChange={(e) => setPlatformSettings(prev => ({ ...prev, platformFeeAddress: e.target.value }))}
                          placeholder="0x..."
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm font-mono"
                        />
                        <p className="text-xs text-gray-400 mt-1">
                          Wallet address where platform fees are sent
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Platform Label ID (Optional)
                        </label>
                        <input
                          type="text"
                          value={platformSettings.platformLabelId}
                          onChange={(e) => setPlatformSettings(prev => ({ ...prev, platformLabelId: e.target.value }))}
                          placeholder="0x... (platform label object ID)"
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm font-mono"
                        />
                        <p className="text-xs text-gray-400 mt-1">
                          If set, fees go to this label instead of the wallet address
                        </p>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Platform Fee (BPS)
                          </label>
                          <input
                            type="number"
                            value={platformSettings.platformFeeBps}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, platformFeeBps: parseInt(e.target.value) || 0 }))}
                            placeholder="300"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(platformSettings.platformFeeBps / 100).toFixed(2)}%
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Reward Fee (BPS)
                          </label>
                          <input
                            type="number"
                            value={platformSettings.rewardFeeBps}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, rewardFeeBps: parseInt(e.target.value) || 0 }))}
                            placeholder="100"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(platformSettings.rewardFeeBps / 100).toFixed(2)}%
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-blue-500/10 border border-blue-500/20">
                    <h3 className="text-blue-300 font-medium mb-2">⚙️ Pool Trading Settings</h3>
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-3 h-3 bg-blue-500 rounded"></div>
                      <span className="text-xs text-blue-300 font-medium">Updated by: Pool Platform Settings button</span>
                    </div>
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Token Mint Fee
                          </label>
                          <input
                            type="text"
                            value={platformSettings.mintFee}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, mintFee: e.target.value }))}
                            placeholder="*********"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(parseInt(platformSettings.mintFee) / 1_000_000_000).toFixed(3)} SUI
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Min Reward Trade
                          </label>
                          <input
                            type="text"
                            value={platformSettings.minRewardTradeAmount}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, minRewardTradeAmount: e.target.value }))}
                            placeholder="********"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(parseInt(platformSettings.minRewardTradeAmount) / 1_000_000_000).toFixed(3)} SUI
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-green-500/10 border border-green-500/20">
                    <h3 className="text-green-300 font-medium mb-2">📈 Migration Settings</h3>
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-3 h-3 bg-green-500 rounded"></div>
                      <span className="text-xs text-green-300 font-medium">Updated by: Token Platform Settings button</span>
                    </div>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Migration Goal (Token Contract Only)
                        </label>
                        <input
                          type="text"
                          value={platformSettings.bondingCurveGoal}
                          onChange={(e) => setPlatformSettings(prev => ({ ...prev, bondingCurveGoal: e.target.value }))}
                          placeholder="***********"
                          className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                        />
                        <p className="text-xs text-gray-400 mt-1">
                          {(parseInt(platformSettings.bondingCurveGoal) / 1_000_000_000).toFixed(1)} SUI - Virtual pool size to trigger migration
                        </p>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Migration Fee %
                          </label>
                          <input
                            type="number"
                            value={platformSettings.migrationFeePercentage}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, migrationFeePercentage: parseInt(e.target.value) || 0 }))}
                            placeholder="5"
                            min="0"
                            max="10"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {platformSettings.migrationFeePercentage}% of virtual liquidity
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Migration Gas Fee
                          </label>
                          <input
                            type="text"
                            value={platformSettings.migrationGasFee}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, migrationGasFee: e.target.value }))}
                            placeholder="5000000"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(parseInt(platformSettings.migrationGasFee) / 1_000_000_000).toFixed(3)} SUI
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-orange-500/10 border border-orange-500/20">
                    <h3 className="text-orange-300 font-medium mb-2">🎯 Reward Game Settings</h3>
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-3 h-3 bg-orange-500 rounded"></div>
                      <span className="text-xs text-orange-300 font-medium">Updated by: Reward Game Settings button</span>
                    </div>
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Initial Reward Goal
                          </label>
                          <input
                            type="text"
                            value={platformSettings.initialRewardGoal}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, initialRewardGoal: e.target.value }))}
                            placeholder="*********0"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(parseInt(platformSettings.initialRewardGoal) / 1_000_000_000).toFixed(3)} SUI
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Goal Increase
                          </label>
                          <input
                            type="text"
                            value={platformSettings.rewardGoalIncrease}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, rewardGoalIncrease: e.target.value }))}
                            placeholder="*********"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(parseInt(platformSettings.rewardGoalIncrease) / 1_000_000_000).toFixed(3)} SUI
                          </p>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Goal Decrease
                          </label>
                          <input
                            type="text"
                            value={platformSettings.rewardGoalDecreaseAmount}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, rewardGoalDecreaseAmount: e.target.value }))}
                            placeholder="*********"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(parseInt(platformSettings.rewardGoalDecreaseAmount) / 1_000_000_000).toFixed(3)} SUI
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Decrease Threshold
                          </label>
                          <input
                            type="number"
                            value={platformSettings.rewardGoalDecreaseThreshold}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, rewardGoalDecreaseThreshold: parseInt(e.target.value) || 0 }))}
                            placeholder="300"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {Math.floor(platformSettings.rewardGoalDecreaseThreshold / 60)} min
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Proximity %
                          </label>
                          <input
                            type="number"
                            value={platformSettings.rewardGoalProximityThreshold}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, rewardGoalProximityThreshold: parseInt(e.target.value) || 0 }))}
                            placeholder="90"
                            min="50"
                            max="99"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {platformSettings.rewardGoalProximityThreshold}%
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-indigo-500/10 border border-indigo-500/20">
                    <h3 className="text-indigo-300 font-medium mb-2">⚡ Bonding Curve Aggressiveness</h3>
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-3 h-3 bg-indigo-500 rounded"></div>
                      <span className="text-xs text-indigo-300 font-medium">Updated by: Bonding Curve Settings button</span>
                    </div>
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Token Curve Aggressiveness
                          </label>
                          <input
                            type="number"
                            value={platformSettings.tokenCurveAggressiveness}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, tokenCurveAggressiveness: parseInt(e.target.value) || 1000 }))}
                            placeholder="1000"
                            min="100"
                            max="5000"
                            step="100"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(platformSettings.tokenCurveAggressiveness / 1000).toFixed(1)}x steepness (1.0x = normal)
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Token Virtual Liquidity
                          </label>
                          <input
                            type="text"
                            value={platformSettings.tokenVirtualLiquidityBuffer}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, tokenVirtualLiquidityBuffer: e.target.value }))}
                            placeholder="1000000"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(parseInt(platformSettings.tokenVirtualLiquidityBuffer) / 1_000_000_000).toFixed(4)} SUI buffer
                          </p>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Pool Curve Aggressiveness
                          </label>
                          <input
                            type="number"
                            value={platformSettings.poolCurveAggressiveness}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, poolCurveAggressiveness: parseInt(e.target.value) || 1000 }))}
                            placeholder="1000"
                            min="100"
                            max="5000"
                            step="100"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(platformSettings.poolCurveAggressiveness / 1000).toFixed(1)}x steepness (1.0x = normal)
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-1">
                            Pool Virtual Liquidity
                          </label>
                          <input
                            type="text"
                            value={platformSettings.poolVirtualLiquidityBuffer}
                            onChange={(e) => setPlatformSettings(prev => ({ ...prev, poolVirtualLiquidityBuffer: e.target.value }))}
                            placeholder="********"
                            className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <p className="text-xs text-gray-400 mt-1">
                            {(parseInt(platformSettings.poolVirtualLiquidityBuffer) / 1_000_000_000).toFixed(3)} SUI buffer
                          </p>
                        </div>
                      </div>
                      <div className="p-3 rounded-lg bg-gray-800/30 border border-gray-600/30">
                        <h4 className="text-sm font-medium text-gray-300 mb-2">💡 Settings Guide</h4>
                        <div className="text-xs text-gray-400 space-y-1">
                          <p><strong>Curve Aggressiveness:</strong></p>
                          <p>• 0.5x (500): Gentle curve, smaller price impact</p>
                          <p>• 1.0x (1000): Normal curve, balanced pricing</p>
                          <p>• 2.0x (2000): Aggressive curve, higher price impact</p>
                          <p>• 5.0x (5000): Very aggressive, maximum price impact</p>
                          <p className="mt-2"><strong>Virtual Liquidity:</strong></p>
                          <p>• Token: Price stability for bonding curve trading (pre-migration)</p>
                          <p>• Pool: Price stability for AMM pool trading (post-migration)</p>
                          <p>• Higher buffer = more stable pricing, lower volatility</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-cyan-500/10 border border-cyan-500/20">
                    <h3 className="text-cyan-300 font-medium mb-2">🎯 Update Button Guide</h3>
                    <div className="text-sm text-gray-300 space-y-2">
                      <p className="text-xs text-gray-400 mb-3">
                        Each form section is color-coded to match its update button:
                      </p>
                      <div className="grid grid-cols-1 gap-2">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-500 rounded"></div>
                          <span><strong>Green sections:</strong> Token Platform Settings button</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-blue-500 rounded"></div>
                          <span><strong>Blue sections:</strong> Pool Platform Settings button</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-indigo-500 rounded"></div>
                          <span><strong>Indigo sections:</strong> Bonding Curve Settings button</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-orange-500 rounded"></div>
                          <span><strong>Orange sections:</strong> Reward Game Settings button</span>
                        </div>
                      </div>
                      <div className="mt-3 p-2 rounded bg-gray-800/30 border border-gray-600/30">
                        <p className="text-xs text-gray-400">
                          💡 Look for the colored dot and label at the top of each section to see which button updates it
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-yellow-500/10 border border-yellow-500/20">
                    <h3 className="text-yellow-300 font-medium mb-2">⚠️ Important</h3>
                    <p className="text-sm text-gray-300 mb-2">
                      Platform settings updates require admin privileges and affect all future transactions.
                    </p>
                    <div className="text-xs text-gray-400 space-y-1">
                      <p>• Platform fees are collected automatically during trades</p>
                      <p>• If Platform Label ID is set, fees go to the label instead of wallet</p>
                      <p>• Changes take effect immediately after transaction confirmation</p>
                    </div>
                  </div>
                </div>
              )}

              {/* XFT Settings */}
              {activeTab === 'settings' && (
                <div className="space-y-4">
                  <div className="p-4 rounded-xl bg-purple-500/10 border border-purple-500/20">
                    <h3 className="text-purple-300 font-medium mb-2">Label Settings</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Annual Label Mint Price
                        </label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={labelSettings.annualMintPrice}
                            onChange={(e) => setLabelSettings(prev => ({ ...prev, annualMintPrice: e.target.value }))}
                            placeholder="*********0"
                            className="flex-1 px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <span className="text-gray-400 text-sm">MIST</span>
                        </div>
                        <p className="text-xs text-gray-400 mt-1">
                          {(parseInt(labelSettings.annualMintPrice) / 1_000_000_000).toFixed(3)} SUI
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Marketplace Fee
                        </label>
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={labelSettings.marketplaceFee}
                            onChange={(e) => setLabelSettings(prev => ({ ...prev, marketplaceFee: e.target.value }))}
                            placeholder="250"
                            className="flex-1 px-3 py-2 bg-gray-800/50 border border-gray-600 rounded-lg text-white text-sm"
                          />
                          <span className="text-gray-400 text-sm">BPS</span>
                        </div>
                        <p className="text-xs text-gray-400 mt-1">
                          {(parseInt(labelSettings.marketplaceFee) / 100).toFixed(2)}%
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-green-500/10 border border-green-500/20">
                    <h3 className="text-green-300 font-medium mb-2">💡 Info</h3>
                    <p className="text-sm text-gray-300">
                      These settings control label pricing and marketplace commissions.
                      Changes take effect immediately after transaction confirmation.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div variants={staggerItem}>
            <Card variant="glass" padding="md" className="border border-red-400/30 bg-red-400/5">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-red-300 text-sm">Error</h3>
                  <p className="text-gray-300 text-xs leading-relaxed mt-1">{error}</p>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Action Buttons */}
        <motion.div variants={staggerItem} className="space-y-3">
          {/* Pool Contract Actions */}
          {activeTab === 'pool' && (
            <Button
              variant="gradient"
              size="lg"
              className="w-full bg-gradient-to-r from-blue-400 to-blue-600 hover:from-blue-500 hover:to-blue-700"
              onClick={handleInitializePool}
              disabled={isInitializing || !connected || !isAdmin || initResults.poolAdmin?.success || isPoolAdminInitialized}
            >
              {isInitializing ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                  Initializing Pool Contract...
                </>
              ) : (initResults.poolAdmin?.success || isPoolAdminInitialized) ? (
                <>
                  <CheckCircle className="w-5 h-5 mr-2" />
                  {isPoolAdminInitialized ? 'Pool Contract Pre-initialized' : 'Pool Contract Initialized'}
                </>
              ) : (
                <>
                  <Zap className="w-5 h-5 mr-2" />
                  Initialize Pool Contract
                </>
              )}
            </Button>
          )}

          {/* Token Contract Actions */}
          {activeTab === 'token' && (
            <Button
              variant="gradient"
              size="lg"
              className="w-full bg-gradient-to-r from-emerald-400 to-emerald-600 hover:from-emerald-500 hover:to-emerald-700"
              onClick={handleInitializeToken}
              disabled={isInitializing || !connected || !isAdmin || initResults.tokenAdmin?.success || isTokenAdminInitialized}
            >
              {isInitializing ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                  Initializing Token Contract...
                </>
              ) : (initResults.tokenAdmin?.success || isTokenAdminInitialized) ? (
                <>
                  <CheckCircle className="w-5 h-5 mr-2" />
                  {isTokenAdminInitialized ? 'Token Contract Pre-initialized' : 'Token Contract Initialized'}
                </>
              ) : (
                <>
                  <Zap className="w-5 h-5 mr-2" />
                  Initialize Token Contract
                </>
              )}
            </Button>
          )}

          {/* Fire Registry Actions */}
          {activeTab === 'fire' && (
            <div className="space-y-3">
              <div className="p-4 rounded-xl bg-orange-500/10 border border-orange-500/20">
                <h3 className="text-orange-300 font-medium mb-2">🚧 Fire Registry Deployment Required</h3>
                <p className="text-sm text-gray-300 mb-3">
                  The Fire Registry contract needs to be deployed first before it can be initialized.
                </p>
                <div className="text-xs text-gray-400 space-y-1">
                  <p>1. Deploy Fire Registry contract separately</p>
                  <p>2. Update FIRE_REGISTRY_ID in constants</p>
                  <p>3. Return here to register contracts</p>
                </div>
              </div>

              <Button
                variant="secondary"
                size="lg"
                className="w-full"
                disabled={true}
              >
                <Settings className="w-5 h-5 mr-2" />
                Deploy Fire Registry First
              </Button>
            </div>
          )}

          {/* Platform Settings Actions */}
          {activeTab === 'platform' && (
            <div className="space-y-3">
              <Button
                variant="gradient"
                size="lg"
                className="w-full bg-gradient-to-r from-green-400 to-green-600 hover:from-green-500 hover:to-green-700"
                onClick={handleUpdateTokenPlatformSettings}
                disabled={isInitializing || !connected || !isAdmin}
              >
                {isInitializing ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                    Updating Token Settings...
                  </>
                ) : (
                  <>
                    <Settings className="w-5 h-5 mr-2" />
                    Update Token Platform Settings
                  </>
                )}
              </Button>

              <Button
                variant="gradient"
                size="lg"
                className="w-full bg-gradient-to-r from-blue-400 to-blue-600 hover:from-blue-500 hover:to-blue-700"
                onClick={handleUpdatePoolPlatformSettings}
                disabled={isInitializing || !connected || !isAdmin}
              >
                {isInitializing ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                    Updating Pool Settings...
                  </>
                ) : (
                  <>
                    <Settings className="w-5 h-5 mr-2" />
                    Update Pool Platform Settings
                  </>
                )}
              </Button>

              <Button
                variant="gradient"
                size="lg"
                className="w-full bg-gradient-to-r from-indigo-400 to-indigo-600 hover:from-indigo-500 hover:to-indigo-700"
                onClick={handleUpdateBondingCurveSettings}
                disabled={isInitializing || !connected || !isAdmin}
              >
                {isInitializing ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                    Updating Bonding Curves...
                  </>
                ) : (
                  <>
                    <Zap className="w-5 h-5 mr-2" />
                    Update Bonding Curve Settings
                  </>
                )}
              </Button>

              <Button
                variant="gradient"
                size="lg"
                className="w-full bg-gradient-to-r from-orange-400 to-orange-600 hover:from-orange-500 hover:to-orange-700"
                onClick={handleUpdateRewardGameSettings}
                disabled={isInitializing || !connected || !isAdmin}
              >
                {isInitializing ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                    Updating Reward Game...
                  </>
                ) : (
                  <>
                    <Trophy className="w-5 h-5 mr-2" />
                    Update Reward Game Settings
                  </>
                )}
              </Button>
            </div>
          )}

          {/* XFT Settings Actions */}
          {activeTab === 'settings' && (
            <Button
              variant="gradient"
              size="lg"
              className="w-full bg-gradient-to-r from-purple-400 to-purple-600 hover:from-purple-500 hover:to-purple-700"
              onClick={handleUpdateLabelSettings}
              disabled={isInitializing || !connected || !isAdmin}
            >
              {isInitializing ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                  Updating Settings...
                </>
              ) : (
                <>
                  <Settings className="w-5 h-5 mr-2" />
                  Update Label Settings
                </>
              )}
            </Button>
          )}

          {/* Success Messages */}
          {((activeTab === 'pool' && initResults.poolAdmin?.success) ||
            (activeTab === 'token' && initResults.tokenAdmin?.success) ||
            (activeTab === 'fire' && initResults.fireRegistry?.success)) && (
            <div className="p-4 rounded-xl bg-green-500/10 border border-green-500/20">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="text-green-300 font-medium">
                  {activeTab === 'pool' && 'Pool Contract Initialized Successfully!'}
                  {activeTab === 'token' && 'Token Contract Initialized Successfully!'}
                  {activeTab === 'fire' && 'Fire Registry Initialized Successfully!'}
                </span>
              </div>
              {((activeTab === 'pool' && initResults.poolAdmin?.txHash) ||
                (activeTab === 'token' && initResults.tokenAdmin?.txHash) ||
                (activeTab === 'fire' && initResults.fireRegistry?.txHash)) && (
                <p className="text-green-300 text-xs font-mono mt-2 break-all">
                  TX: {activeTab === 'pool' ? initResults.poolAdmin?.txHash :
                       activeTab === 'token' ? initResults.tokenAdmin?.txHash :
                       initResults.fireRegistry?.txHash}
                </p>
              )}
            </div>
          )}

          {connected ? (
            <Button
              variant="glass"
              size="lg"
              className="w-full"
              disabled={true}
            >
              <Key className="w-5 h-5 mr-2" />
              Connected: {account?.address?.slice(0, 8)}...
            </Button>
          ) : (
            <div className="w-full">
              <ConnectButton
                style={{
                  width: '100%',
                  padding: '12px 24px',
                  borderRadius: '12px',
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  fontSize: '16px',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onConnectSuccess={(wallet) => {
                  console.log('Admin wallet connected:', wallet)
                }}
                onConnectError={(error) => {
                  console.error('Admin wallet connection failed:', error)
                }}
              >
                <Key className="w-5 h-5" />
                Connect Admin Wallet
              </ConnectButton>
            </div>
          )}
        </motion.div>

        {/* Status Cards */}
        <motion.div variants={staggerItem} className="grid grid-cols-2 gap-4">
          <Card variant="glass" padding="md" className={`border ${(initResults.poolAdmin?.success || isPoolAdminInitialized) ? 'border-emerald-400/30 bg-emerald-400/5' : 'border-yellow-400/30 bg-yellow-400/5'}`}>
            <div className="text-center space-y-2">
              <div className="text-2xl">🏊</div>
              <p className={`text-lg font-bold ${(initResults.poolAdmin?.success || isPoolAdminInitialized) ? 'text-emerald-400' : 'text-yellow-400'}`}>
                {(initResults.poolAdmin?.success || isPoolAdminInitialized) ? 'Ready' : 'Pending'}
              </p>
              <p className="text-xs text-gray-400">Pool Contract</p>
              {isPoolAdminInitialized && (
                <p className="text-xs text-emerald-300">Pre-initialized</p>
              )}
            </div>
          </Card>

          <Card variant="glass" padding="md" className={`border ${(initResults.tokenAdmin?.success || isTokenAdminInitialized) ? 'border-emerald-400/30 bg-emerald-400/5' : 'border-yellow-400/30 bg-yellow-400/5'}`}>
            <div className="text-center space-y-2">
              <div className="text-2xl">🪙</div>
              <p className={`text-lg font-bold ${(initResults.tokenAdmin?.success || isTokenAdminInitialized) ? 'text-emerald-400' : 'text-yellow-400'}`}>
                {(initResults.tokenAdmin?.success || isTokenAdminInitialized) ? 'Ready' : 'Pending'}
              </p>
              <p className="text-xs text-gray-400">Token Contract</p>
              {isTokenAdminInitialized && (
                <p className="text-xs text-emerald-300">Pre-initialized</p>
              )}
            </div>
          </Card>

          <Card variant="glass" padding="md" className={`border ${initResults.fireRegistry?.success ? 'border-emerald-400/30 bg-emerald-400/5' : 'border-yellow-400/30 bg-yellow-400/5'}`}>
            <div className="text-center space-y-2">
              <div className="text-2xl">🔥</div>
              <p className={`text-lg font-bold ${initResults.fireRegistry?.success ? 'text-emerald-400' : 'text-yellow-400'}`}>
                {initResults.fireRegistry?.success ? 'Ready' : 'Pending'}
              </p>
              <p className="text-xs text-gray-400">Fire Registry</p>
            </div>
          </Card>

          <Card variant="glass" padding="md" className="border border-purple-400/30 bg-purple-400/5">
            <div className="text-center space-y-2">
              <div className="text-2xl">⚙️</div>
              <p className="text-lg font-bold text-purple-400">
                Active
              </p>
              <p className="text-xs text-gray-400">Settings</p>
            </div>
          </Card>
        </motion.div>

        {/* Admin Info */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="md" className="border border-purple-400/30 bg-purple-400/5">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Users className="w-6 h-6 text-purple-400" />
                <div>
                  <p className="font-semibold text-white">Admin Access</p>
                  <p className="text-xs text-gray-400">Contract administration privileges</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-purple-400">1</p>
                <p className="text-xs text-gray-400">Super Admin</p>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
