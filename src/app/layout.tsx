import type { <PERSON>ada<PERSON> } from "next";
import { Raleway } from "next/font/google";
import { WalletContextProvider } from "@/contexts/WalletContext";
import { EventProvider } from "@/components/providers/EventProvider";
import "./globals.css";

const raleway = Raleway({
  subsets: ["latin"],
  variable: "--font-raleway",
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Dexsta - Solana Token Launch Platform",
  description: "Create, discover, and trade tokens on Solana with Dexsta's mobile-first platform",
  keywords: ["Solana", "DeFi", "Token Launch", "Cryptocurrency", "Web3"],
  authors: [{ name: "Dexsta Team" }],
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: "#9945FF",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="theme-color" content="#9945FF" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Matangi:wght@400&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Huninn:wght@400;500;600;700&display=swap" rel="stylesheet" />
      </head>
      <body className={`${raleway.variable} font-body antialiased`}>
        <WalletContextProvider>
          <EventProvider>
            {children}
          </EventProvider>
        </WalletContextProvider>
      </body>
    </html>
  );
}
