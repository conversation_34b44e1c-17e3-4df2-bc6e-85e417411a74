'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { staggerContainer, staggerItem } from '@/lib/animations'
import { MessageCircle, Mail, FileText, HelpCircle } from 'lucide-react'

export default function SupportPage() {
  return (
    <MobileLayout headerTitle="Support" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="text-6xl">💬</div>
          <h1 className="text-2xl font-bold text-white font-heading">
            Support Center
          </h1>
          <div className="w-20 h-0.5 bg-gradient-to-r from-cyan-400 to-blue-600 rounded-full mx-auto" />
          <p className="text-gray-300">
            We're here to help you with any questions or issues
          </p>
        </motion.div>

        {/* Contact Methods */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-cyan-400/30 bg-cyan-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Get in Touch</h2>
              <div className="space-y-3">
                <button className="w-full flex items-center space-x-4 p-4 rounded-xl bg-blue-400/10 border border-blue-400/20 hover:bg-blue-400/20 transition-colors">
                  <MessageCircle className="w-6 h-6 text-blue-400" />
                  <div className="text-left">
                    <h3 className="font-semibold text-blue-300">Live Chat</h3>
                    <p className="text-gray-300 text-sm">Get instant help from our support team</p>
                  </div>
                </button>
                <button className="w-full flex items-center space-x-4 p-4 rounded-xl bg-emerald-400/10 border border-emerald-400/20 hover:bg-emerald-400/20 transition-colors">
                  <Mail className="w-6 h-6 text-emerald-400" />
                  <div className="text-left">
                    <h3 className="font-semibold text-emerald-300">Email Support</h3>
                    <p className="text-gray-300 text-sm"><EMAIL></p>
                  </div>
                </button>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* FAQ */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Frequently Asked Questions</h2>
              <div className="space-y-3">
                {[
                  {
                    question: "How do I create a token?",
                    answer: "Connect your wallet, fill in token details, set trading fees, and deploy for 0.1 SOL."
                  },
                  {
                    question: "What are trading fees?",
                    answer: "Creators set buy/sell fees (0-10%). 50% goes to creators, 50% to the trading game prize pool."
                  },
                  {
                    question: "How does the trading game work?",
                    answer: "Every trade is an entry. The Xth trader wins the accumulated prize pool from trading fees."
                  },
                  {
                    question: "Can I change my token fees after creation?",
                    answer: "No, trading fees are set permanently at token creation and cannot be changed later."
                  }
                ].map((faq, index) => (
                  <div key={index} className="p-4 rounded-xl bg-gray-800/30 border border-gray-600/20">
                    <div className="flex items-start space-x-3">
                      <HelpCircle className="w-5 h-5 text-cyan-400 flex-shrink-0 mt-0.5" />
                      <div>
                        <h3 className="font-semibold text-cyan-300 text-sm mb-2">{faq.question}</h3>
                        <p className="text-gray-300 text-xs leading-relaxed">{faq.answer}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Common Issues */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Common Issues</h2>
              <div className="space-y-3">
                <div className="p-4 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                  <h3 className="font-semibold text-yellow-300 text-sm mb-2">Wallet Connection Issues</h3>
                  <p className="text-gray-300 text-xs leading-relaxed">
                    Make sure your wallet is unlocked and try refreshing the page. Supported wallets: Phantom, Solflare, Backpack.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-red-400/10 border border-red-400/20">
                  <h3 className="font-semibold text-red-300 text-sm mb-2">Transaction Failed</h3>
                  <p className="text-gray-300 text-xs leading-relaxed">
                    Check your SOL balance for gas fees. Ensure you have enough SOL for the transaction and try again.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-purple-400/10 border border-purple-400/20">
                  <h3 className="font-semibold text-purple-300 text-sm mb-2">Token Not Showing</h3>
                  <p className="text-gray-300 text-xs leading-relaxed">
                    New tokens may take a few minutes to appear. Check the transaction on Solscan and refresh the page.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Resources */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Helpful Resources</h2>
              <div className="space-y-3">
                <button className="w-full flex items-center space-x-4 p-4 rounded-xl bg-emerald-400/10 border border-emerald-400/20 hover:bg-emerald-400/20 transition-colors">
                  <FileText className="w-6 h-6 text-emerald-400" />
                  <div className="text-left">
                    <h3 className="font-semibold text-emerald-300">Documentation</h3>
                    <p className="text-gray-300 text-sm">Complete guides and tutorials</p>
                  </div>
                </button>
                <button className="w-full flex items-center space-x-4 p-4 rounded-xl bg-purple-400/10 border border-purple-400/20 hover:bg-purple-400/20 transition-colors">
                  <MessageCircle className="w-6 h-6 text-purple-400" />
                  <div className="text-left">
                    <h3 className="font-semibold text-purple-300">Community Discord</h3>
                    <p className="text-gray-300 text-sm">Join our community for help and updates</p>
                  </div>
                </button>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Contact Form */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-blue-400/30 bg-blue-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Send us a Message</h2>
              <div className="space-y-3">
                <input
                  type="email"
                  placeholder="Your email address"
                  className="w-full p-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-400/50"
                />
                <input
                  type="text"
                  placeholder="Subject"
                  className="w-full p-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-400/50"
                />
                <textarea
                  placeholder="Describe your issue or question..."
                  rows={4}
                  className="w-full p-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-400/50 resize-none"
                />
                <Button
                  variant="gradient"
                  size="lg"
                  className="w-full bg-gradient-to-r from-blue-400 to-cyan-600 hover:from-blue-500 hover:to-cyan-700"
                >
                  Send Message
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
