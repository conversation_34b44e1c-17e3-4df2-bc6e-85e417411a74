'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Button } from '@/components/shared/Button'
import { Card } from '@/components/shared/Card'
import { ArrowUpDown, ChevronDown, Zap, TrendingUp, Coins, X, Search, Wallet } from 'lucide-react'
import { staggerContainer, staggerItem, swapFlip, priceUpdate } from '@/lib/animations'
import { cn, formatNumber, generateTokenGradient } from '@/lib/utils'
import { formatSuiAmount } from '@/constants/contracts'
import { triggerHaptic } from '@/utils/haptic'
import { useWallet } from '@suiet/wallet-kit'

interface DexstaToken {
  symbol: string
  name: string
  address: string
  price: number
  balance: number
  migrationStatus: 'pre-migration' | 'migrated'
  rewardPot: number
}

// Mock Dexsta tokens - replace with actual wallet data
const dexstaTokens: DexstaToken[] = [
  {
    symbol: '<PERSON>O<PERSON>',
    name: '<PERSON><PERSON>',
    address: '0x123...abc',
    price: 0.0234,
    balance: 50000,
    migrationStatus: 'migrated',
    rewardPot: 45.67
  },
  {
    symbol: 'PEPE',
    name: 'Pepe Solana',
    address: '0x456...def',
    price: 0.0156,
    balance: 25000,
    migrationStatus: 'migrated',
    rewardPot: 123.45
  },
  {
    symbol: 'MOON',
    name: 'Moon Ape',
    address: '0x789...ghi',
    price: 0.0089,
    balance: 75000,
    migrationStatus: 'pre-migration',
    rewardPot: 89.23
  }
]

// SUI is always available
const SUI_TOKEN: DexstaToken = {
  symbol: 'SUI',
  name: 'Sui',
  address: '0x2::sui::SUI',
  price: 1.70,
  balance: 2.5,
  migrationStatus: 'migrated',
  rewardPot: 0
}

export default function SwapPage() {
  const { connected, account } = useWallet()
  const [fromToken, setFromToken] = useState<DexstaToken>(SUI_TOKEN)
  const [toToken, setToToken] = useState<DexstaToken | null>(null)
  const [fromAmount, setFromAmount] = useState('')
  const [toAmount, setToAmount] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showTokenSelector, setShowTokenSelector] = useState(false)
  const [showAddressInput, setShowAddressInput] = useState(false)
  const [tokenAddress, setTokenAddress] = useState('')
  const [priceImpact, setPriceImpact] = useState(0.12)
  const [selectorType, setSelectorType] = useState<'from' | 'to'>('from')

  // Calculate exchange rate and amounts
  useEffect(() => {
    if (fromAmount && fromToken && toToken) {
      const rate = fromToken.price / toToken.price
      const calculatedAmount = (parseFloat(fromAmount) * rate).toFixed(6)
      setToAmount(calculatedAmount)
    } else {
      setToAmount('')
    }
  }, [fromAmount, fromToken, toToken])

  const handleSwapTokens = () => {
    if (!toToken) return
    const tempToken = fromToken
    setFromToken(toToken)
    setToToken(tempToken)
    setFromAmount(toAmount)
    setToAmount(fromAmount)
    triggerHaptic('medium')
  }

  const handleTokenSelect = (token: DexstaToken) => {
    if (selectorType === 'from') {
      setFromToken(token)
    } else {
      setToToken(token)
    }
    setShowTokenSelector(false)
    triggerHaptic('light')
  }

  const handleAddressSubmit = () => {
    // TODO: Validate and fetch token info from address
    setShowAddressInput(false)
    setTokenAddress('')
    triggerHaptic('light')
  }

  const handleSwap = async () => {
    if (!connected || !toToken) return
    setIsLoading(true)
    triggerHaptic('heavy')
    // TODO: Implement actual swap logic
    await new Promise(resolve => setTimeout(resolve, 3000))
    setIsLoading(false)
  }

  const isSwapValid = connected && fromAmount && toToken && parseFloat(fromAmount) > 0 && parseFloat(fromAmount) <= fromToken.balance

  const availableTokens = [SUI_TOKEN, ...dexstaTokens]

  return (
    <MobileLayout headerTitle="Swap">
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* 3 Dots Separator */}
        <motion.div variants={staggerItem} className="py-4">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-600/50 to-gray-600/50"></div>
            <div className="flex space-x-1">
              <div className="w-1.5 h-1.5 bg-primary-purple rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-primary-purple rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-primary-purple rounded-full"></div>
            </div>
            <div className="flex-1 h-px bg-gradient-to-l from-transparent via-gray-600/50 to-gray-600/50"></div>
          </div>
        </motion.div>

        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-r from-primary-purple to-primary-pink flex items-center justify-center">
            <ArrowUpDown className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-white font-heading">
            Token Swap
          </h1>
          <p className="text-gray-300 font-body">
            Trade Dexsta tokens instantly with SUI
          </p>
        </motion.div>

        {/* Swap Interface */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="space-y-6">
            {/* From Token */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium text-gray-400">From</label>
                <span className="text-sm text-gray-400">
                  Balance: {formatSuiAmount(fromToken.balance)}
                </span>
              </div>

              <div className="flex items-center space-x-3 p-4 rounded-2xl glass-card border border-gray-700/50">
                <button
                  onClick={() => {
                    setSelectorType('from')
                    setShowTokenSelector(true)
                    triggerHaptic('light')
                  }}
                  className="flex items-center space-x-3 hover:bg-gray-700/30 rounded-xl p-2 transition-colors"
                >
                  <div
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm"
                    style={{ background: generateTokenGradient(fromToken.symbol) }}
                  >
                    {fromToken.symbol.slice(0, 2)}
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-white">{fromToken.symbol}</div>
                    <div className="text-xs text-gray-400">{fromToken.name}</div>
                  </div>
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                </button>

                <div className="flex-1 text-right">
                  <input
                    type="number"
                    value={fromAmount}
                    onChange={(e) => setFromAmount(e.target.value)}
                    placeholder="0.0"
                    className="w-full bg-transparent text-white text-xl font-medium placeholder-gray-400 focus:outline-none text-right"
                    autoFocus
                  />
                  <div className="text-sm text-gray-400 mt-1">
                    ~${formatNumber(parseFloat(fromAmount || '0') * fromToken.price, 2)}
                  </div>
                </div>
              </div>
            </div>

            {/* Swap Button */}
            <div className="flex justify-center py-2">
              <motion.button
                whileTap={swapFlip}
                onClick={handleSwapTokens}
                disabled={!toToken}
                className="p-3 rounded-full glass-button border-2 border-gray-700/50 hover:border-primary-purple/50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowUpDown className="w-5 h-5 text-white" />
              </motion.button>
            </div>

            {/* To Token */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium text-gray-400">To</label>
                {toToken && (
                  <span className="text-sm text-gray-400">
                    Balance: {formatSuiAmount(toToken.balance)}
                  </span>
                )}
              </div>

              <div className="flex items-center space-x-3 p-4 rounded-2xl glass-card border border-gray-700/50">
                {toToken ? (
                  <>
                    <button
                      onClick={() => {
                        setSelectorType('to')
                        setShowTokenSelector(true)
                        triggerHaptic('light')
                      }}
                      className="flex items-center space-x-3 hover:bg-gray-700/30 rounded-xl p-2 transition-colors"
                    >
                      <div
                        className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm"
                        style={{ background: generateTokenGradient(toToken.symbol) }}
                      >
                        {toToken.symbol.slice(0, 2)}
                      </div>
                      <div className="text-left">
                        <div className="font-medium text-white">{toToken.symbol}</div>
                        <div className="text-xs text-gray-400">{toToken.name}</div>
                      </div>
                      <ChevronDown className="w-4 h-4 text-gray-400" />
                    </button>

                    <div className="flex-1 text-right">
                      <div className="text-xl font-medium text-white">
                        {toAmount || '0.0'}
                      </div>
                      <div className="text-sm text-gray-400 mt-1">
                        ~${formatNumber(parseFloat(toAmount || '0') * toToken.price, 2)}
                      </div>
                    </div>
                  </>
                ) : (
                  <button
                    onClick={() => {
                      setSelectorType('to')
                      setShowTokenSelector(true)
                      triggerHaptic('light')
                    }}
                    className="flex-1 flex items-center justify-center space-x-2 py-4 text-gray-400 hover:text-white transition-colors"
                  >
                    <Coins className="w-5 h-5" />
                    <span>Select a token</span>
                    <ChevronDown className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Swap Details */}
        {toToken && (
          <motion.div variants={staggerItem}>
            <Card variant="glass" padding="md" className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Exchange Rate</span>
                <motion.span
                  variants={priceUpdate}
                  className="text-sm text-white font-medium"
                >
                  1 {fromToken.symbol} = {formatNumber(fromToken.price / toToken.price, 6)} {toToken.symbol}
                </motion.span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Price Impact</span>
                <span className={cn(
                  "text-sm font-medium",
                  priceImpact < 1 ? "text-green-400" : priceImpact < 3 ? "text-yellow-400" : "text-red-400"
                )}>
                  {priceImpact.toFixed(2)}%
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Network Fee</span>
                <span className="text-sm text-white">~0.001 SUI</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Migration Status</span>
                <span className={cn(
                  "text-sm font-medium px-2 py-1 rounded-lg",
                  toToken.migrationStatus === 'migrated'
                    ? "text-green-400 bg-green-400/10"
                    : "text-yellow-400 bg-yellow-400/10"
                )}>
                  {toToken.migrationStatus === 'migrated' ? 'Migrated' : 'Pre-Migration'}
                </span>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Swap Button */}
        <motion.div variants={staggerItem}>
          <Button
            variant="primary"
            size="lg"
            className="w-full py-4 text-lg font-semibold"
            onClick={handleSwap}
            isLoading={isLoading}
            disabled={!isSwapValid}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                <span>Swapping...</span>
              </div>
            ) : !connected ? (
              'Connect Wallet to Swap'
            ) : !toToken ? (
              'Select a Token'
            ) : !fromAmount ? (
              'Enter Amount'
            ) : parseFloat(fromAmount) > fromToken.balance ? (
              'Insufficient Balance'
            ) : (
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5" />
                <span>Swap Tokens</span>
              </div>
            )}
          </Button>
        </motion.div>

        {/* Wallet Tokens */}
        {connected && dexstaTokens.length > 0 && (
          <motion.div variants={staggerItem}>
            <Card variant="glass" padding="lg">
              <div className="flex items-center space-x-2 mb-4">
                <Wallet className="w-5 h-5 text-primary-purple" />
                <h3 className="text-lg font-semibold text-white font-heading">Your Dexsta Tokens</h3>
              </div>

              <div className="space-y-3">
                {dexstaTokens.map((token) => (
                  <div
                    key={token.address}
                    className="flex items-center justify-between p-3 rounded-xl glass-card hover:bg-gray-700/30 transition-colors cursor-pointer"
                    onClick={() => {
                      if (selectorType === 'to' || fromToken.symbol !== token.symbol) {
                        handleTokenSelect(token)
                      }
                    }}
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm"
                        style={{ background: generateTokenGradient(token.symbol) }}
                      >
                        {token.symbol.slice(0, 2)}
                      </div>
                      <div>
                        <div className="font-medium text-white">{token.symbol}</div>
                        <div className="text-sm text-gray-400">{token.name}</div>
                      </div>
                      <div className={cn(
                        "px-2 py-1 rounded-lg text-xs font-medium",
                        token.migrationStatus === 'migrated'
                          ? "text-green-400 bg-green-400/10 border border-green-400/20"
                          : "text-yellow-400 bg-yellow-400/10 border border-yellow-400/20"
                      )}>
                        {token.migrationStatus === 'migrated' ? 'Migrated' : 'Pre-Migration'}
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="font-medium text-white">{formatNumber(token.balance, 2)}</div>
                      <div className="text-sm text-gray-400">${formatNumber(token.balance * token.price, 2)}</div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                <p className="text-blue-400 text-sm text-center">
                  💡 Only Dexsta tokens can be swapped on this platform
                </p>
              </div>
            </Card>
          </motion.div>
        )}
        {/* Token Selector Modal */}
        {showTokenSelector && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end"
            onClick={() => setShowTokenSelector(false)}
          >
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              className="w-full bg-gray-900 rounded-t-3xl p-6 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white font-heading">Select Token</h3>
                <button
                  onClick={() => setShowTokenSelector(false)}
                  className="p-2 rounded-full glass-button"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
              </div>

              <div className="space-y-3">
                {availableTokens
                  .filter(token => token.symbol !== (selectorType === 'from' ? toToken?.symbol : fromToken.symbol))
                  .map((token) => (
                    <button
                      key={token.address}
                      onClick={() => handleTokenSelect(token)}
                      className="w-full flex items-center justify-between p-4 rounded-xl glass-card hover:bg-gray-700/30 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold"
                          style={{ background: generateTokenGradient(token.symbol) }}
                        >
                          {token.symbol.slice(0, 2)}
                        </div>
                        <div className="text-left">
                          <div className="font-medium text-white">{token.symbol}</div>
                          <div className="text-sm text-gray-400">{token.name}</div>
                        </div>
                      </div>

                      <div className="text-right">
                        <div className="font-medium text-white">{formatSuiAmount(token.balance)}</div>
                        <div className="text-sm text-gray-400">${formatNumber(token.price, 4)}</div>
                      </div>
                    </button>
                  ))}
              </div>

              <div className="mt-6 pt-4 border-t border-gray-700">
                <button
                  onClick={() => {
                    setShowTokenSelector(false)
                    setShowAddressInput(true)
                  }}
                  className="w-full flex items-center justify-center space-x-2 p-4 rounded-xl glass-card hover:bg-gray-700/30 transition-colors"
                >
                  <Search className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-400">Enter token address</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Address Input Modal */}
        {showAddressInput && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowAddressInput(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="w-full max-w-md bg-gray-900 rounded-2xl p-6"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white font-heading">Enter Token Address</h3>
                <button
                  onClick={() => setShowAddressInput(false)}
                  className="p-2 rounded-full glass-button"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
              </div>

              <div className="space-y-4">
                <input
                  type="text"
                  value={tokenAddress}
                  onChange={(e) => setTokenAddress(e.target.value)}
                  placeholder="0x..."
                  className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                  autoFocus
                />

                <div className="p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
                  <p className="text-yellow-400 text-sm">
                    ⚠️ Only Dexsta tokens are supported. Make sure the address is for a token created on this platform.
                  </p>
                </div>

                <div className="flex space-x-3">
                  <Button
                    variant="ghost"
                    onClick={() => setShowAddressInput(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleAddressSubmit}
                    disabled={!tokenAddress.trim()}
                    className="flex-1"
                  >
                    Add Token
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}

      </motion.div>
    </MobileLayout>
  )
}
