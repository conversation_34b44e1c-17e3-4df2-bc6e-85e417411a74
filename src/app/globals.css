@import "tailwindcss";

:root {
  /* Dexsta Brand Colors - Solana/Web3 Inspired */
  --primary-purple: #9945FF;
  --primary-green: #14F195;
  --primary-blue: #00D4FF;
  --dark-bg: #0A0A0F;
  --dark-surface: #1A1A2E;
  --dark-border: #2A2A3E;
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: rgba(0, 0, 0, 0.1);

  /* Light theme */
  --background: #ffffff;
  --foreground: #171717;
  --surface: #f8fafc;
  --border: #e2e8f0;
}

@theme inline {
  /* Custom color palette for Dexsta */
  --color-primary-purple: var(--primary-purple);
  --color-primary-green: var(--primary-green);
  --color-primary-blue: var(--primary-blue);
  --color-dark-bg: var(--dark-bg);
  --color-dark-surface: var(--dark-surface);
  --color-dark-border: var(--dark-border);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-surface: var(--surface);
  --color-border: var(--border);

  /* Custom font families */
  --font-brand: 'Matangi', cursive;
  --font-heading: 'Huninn', sans-serif;
  --font-body: var(--font-raleway), 'Raleway', sans-serif;
  --font-sans: var(--font-raleway), 'Raleway', sans-serif;

  /* Custom spacing for mobile-first */
  --spacing-safe-bottom: env(safe-area-inset-bottom);
  --spacing-safe-top: env(safe-area-inset-top);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--dark-bg);
    --foreground: #ffffff;
    --surface: var(--dark-surface);
    --border: var(--dark-border);
  }
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-body);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Glass Morphism Utilities */
.glass-nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-nav-light {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.1);
}

.glass-dark {
  background: rgba(26, 26, 46, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: none;
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Gradient Backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-purple), var(--primary-blue));
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
}

.gradient-cosmic {
  background: linear-gradient(135deg,
    rgba(153, 69, 255, 0.1),
    rgba(20, 241, 149, 0.1),
    rgba(0, 212, 255, 0.1)
  );
}

/* Golden Ratio Spacing Utilities */
.spacing-golden-xs { margin: 0.618rem; }
.spacing-golden-sm { margin: 1rem; }
.spacing-golden-md { margin: 1.618rem; }
.spacing-golden-lg { margin: 2.618rem; }
.spacing-golden-xl { margin: 4.236rem; }

.padding-golden-xs { padding: 0.618rem; }
.padding-golden-sm { padding: 1rem; }
.padding-golden-md { padding: 1.618rem; }
.padding-golden-lg { padding: 2.618rem; }
.padding-golden-xl { padding: 4.236rem; }

/* Mobile-first responsive utilities */
.mobile-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 768px) {
  .mobile-container {
    max-width: 768px;
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .mobile-container {
    max-width: 1024px;
  }
}

/* Safe area handling for mobile */
.safe-top {
  padding-top: var(--spacing-safe-top);
}

.safe-bottom {
  padding-bottom: var(--spacing-safe-bottom);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(153, 69, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(153, 69, 255, 0.5);
}

/* Wallet Adapter Base Styles */
.wallet-adapter-dropdown {
  position: relative;
  display: inline-block;
}

.wallet-adapter-dropdown-list {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 99;
  display: flex;
  flex-direction: column;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(52, 211, 153, 0.3);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  padding: 8px;
  margin-top: 8px;
  min-width: 200px;
}

.wallet-adapter-dropdown-list-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #ffffff;
  font-family: var(--font-body);
  text-decoration: none;
}

.wallet-adapter-dropdown-list-item:hover {
  background: rgba(52, 211, 153, 0.1);
  border-color: rgba(52, 211, 153, 0.3);
  transform: translateY(-2px);
}

.wallet-adapter-dropdown-list-item:last-child {
  margin-bottom: 0;
}

.wallet-adapter-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: rgba(10, 10, 15, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.wallet-adapter-modal {
  background: rgba(26, 26, 46, 0.95);
  border: 1px solid rgba(52, 211, 153, 0.3);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  padding: 24px;
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.wallet-adapter-modal-title {
  color: #ffffff;
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
}

.wallet-adapter-modal-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.wallet-adapter-modal-list-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #ffffff;
  font-family: var(--font-body);
  text-decoration: none;
}

.wallet-adapter-modal-list-item:hover {
  background: rgba(52, 211, 153, 0.1);
  border-color: rgba(52, 211, 153, 0.3);
  transform: translateY(-2px);
}

.wallet-adapter-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(52, 211, 153, 0.3);
  border-radius: 12px;
  color: #34d399;
  font-family: var(--font-body);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.wallet-adapter-button:hover {
  background: rgba(52, 211, 153, 0.1);
  border-color: rgba(52, 211, 153, 0.6);
  transform: translateY(-2px);
}

.wallet-adapter-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.wallet-adapter-button-trigger {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(52, 211, 153, 0.3);
  border-radius: 12px;
  color: #34d399;
}

.wallet-adapter-button-trigger:hover {
  background: rgba(52, 211, 153, 0.1);
  border-color: rgba(52, 211, 153, 0.6);
}
