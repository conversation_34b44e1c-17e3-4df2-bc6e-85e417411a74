'use client'

import { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useWallet } from '@suiet/wallet-kit'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { <PERSON><PERSON> } from '@/components/shared/Button'
import { Card } from '@/components/shared/Card'
import { Upload, Image as ImageIcon, Zap, Info, CheckCircle, AlertCircle, Globe, Twitter, MessageCircle, Music, Loader2, Check, X } from 'lucide-react'
import { uploadTokenMetadata } from '@/lib/pinata'
import { staggerContainer, staggerItem, tokenFormSlide } from '@/lib/animations'
import { cn } from '@/lib/utils'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { NotificationModal } from '@/components/shared/NotificationModal'
import { useContracts } from '@/hooks/useContracts'
import { suiToMist } from '@/constants/contracts'

interface TokenFormData {
  name: string
  symbol: string
  description: string
  website: string
  twitter: string
  telegram: string
  tiktok: string
  totalSupply: string
  buyFee: string
  sellFee: string
  initialSuiPurchase: string
  // Advanced settings
  linkToLabel: string
  useOperatorLicense: boolean
  operatorLicenseId: string
  privatePool: boolean
  requiredNftId: string
  image?: File
  imageUrl?: string
}

export default function CreateTokenPage() {
  const router = useRouter()
  const { connected, account } = useWallet()
  const { createToken, createPrivateToken, loading, error } = useContracts()

  const [formData, setFormData] = useState<TokenFormData>({
    name: '',
    symbol: '',
    description: '',
    website: '',
    twitter: '',
    telegram: '',
    tiktok: '',
    totalSupply: '1000000',
    buyFee: '2.5', // Default 2.5% buy fee
    sellFee: '2.5', // Default 2.5% sell fee
    initialSuiPurchase: '0.1', // Default 0.1 SUI initial purchase
    linkToLabel: '',
    useOperatorLicense: false,
    operatorLicenseId: '',
    privatePool: false,
    requiredNftId: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [imageUploading, setImageUploading] = useState(false)
  const isContractLoading = loading
  const [creationResult, setCreationResult] = useState<{
    success: boolean
    tokenObjectId?: string
    error?: string
    message?: string
  } | null>(null)

  // Image upload modal state
  const [imageUploadModal, setImageUploadModal] = useState<{
    isOpen: boolean
    type: 'success' | 'error'
    title: string
    message: string
  }>({
    isOpen: false,
    type: 'success',
    title: '',
    message: ''
  })

  // Validation states
  const [labelValidation, setLabelValidation] = useState<{
    isValidating: boolean
    isValid: boolean
    exists: boolean
    owned: boolean
    error?: string
  }>({
    isValidating: false,
    isValid: false,
    exists: false,
    owned: false
  })

  const [nftValidation, setNftValidation] = useState<{
    isValidating: boolean
    isValid: boolean
    exists: boolean
    linkedToLabel: boolean
    error?: string
  }>({
    isValidating: false,
    isValid: false,
    exists: false,
    linkedToLabel: false
  })

  const [operatorValidation, setOperatorValidation] = useState<{
    isValidating: boolean
    isValid: boolean
    exists: boolean
    linkedToLabel: boolean
    ownedByUser: boolean
    notExpired: boolean
    error?: string
  }>({
    isValidating: false,
    isValid: false,
    exists: false,
    linkedToLabel: false,
    ownedByUser: false,
    notExpired: false
  })

  // UI state for dropdowns
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false)
  const [showSocialLinks, setShowSocialLinks] = useState(false)

  const handleInputChange = (field: keyof TokenFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Trigger validation when label, operator license, or NFT ID changes
    if (field === 'linkToLabel' && typeof value === 'string') {
      if (value.trim()) {
        validateLabel(value.trim())
      } else {
        setLabelValidation({
          isValidating: false,
          isValid: false,
          exists: false,
          owned: false
        })
      }
    }

    if (field === 'operatorLicenseId' && typeof value === 'string') {
      if (value.trim() && formData.linkToLabel) {
        validateOperatorLicense(value.trim(), formData.linkToLabel)
      } else {
        setOperatorValidation({
          isValidating: false,
          isValid: false,
          exists: false,
          linkedToLabel: false,
          ownedByUser: false,
          notExpired: false
        })
      }
    }

    if (field === 'requiredNftId' && typeof value === 'string') {
      if (value.trim() && formData.linkToLabel) {
        validateNFT(value.trim(), formData.linkToLabel)
      } else {
        setNftValidation({
          isValidating: false,
          isValid: false,
          exists: false,
          linkedToLabel: false
        })
      }
    }
  }

  // Contract-level validation functions that mirror actual Sui contract checks
  const validateLabel = useCallback(async (labelId: string) => {
    if (!connected || !account) return

    setLabelValidation(prev => ({ ...prev, isValidating: true }))

    try {
      // Simulate contract calls to label registry
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Contract validation checks:
      // 1. Label exists in registry (check global ID mapping)
      // 2. Label is not expired (check expiration timestamp)
      // 3. User owns the label (check owner field)

      const exists = labelId.length > 0 && !labelId.includes('invalid') && !labelId.includes('fake')
      const notExpired = exists && !labelId.includes('expired')
      const owned = exists && notExpired && !labelId.includes('notowned') && !labelId.includes('other')

      // Simulate network/contract errors
      if (labelId.includes('error')) {
        throw new Error('Contract call failed')
      }

      setLabelValidation({
        isValidating: false,
        isValid: exists && notExpired && owned,
        exists,
        owned,
        error: !exists
          ? 'Label does not exist in registry'
          : !notExpired
          ? 'Label has expired'
          : !owned
          ? 'You do not own this label'
          : undefined
      })
    } catch (error) {
      setLabelValidation({
        isValidating: false,
        isValid: false,
        exists: false,
        owned: false,
        error: 'Failed to validate label - please try again'
      })
    }
  }, [connected, account])

  const validateOperatorLicense = useCallback(async (licenseId: string, labelId: string) => {
    if (!connected || !account) return

    setOperatorValidation(prev => ({ ...prev, isValidating: true }))

    try {
      // Frontend validation: Call contract getter functions for real-time feedback
      // This mirrors the exact checks that will be done in the contract mint function
      await new Promise(resolve => setTimeout(resolve, 1300))

      // Contract validation logic simulation:
      // 1. Check if operator license exists (getLicense function)
      // 2. Check if license is linked to the specified label (license.linked_label_id == labelId)
      // 3. Check if user owns the license (license.owner == tx.sender)
      // 4. Check if license is not expired (license.expiration > current_time)
      // 5. Check if operator is active (license.status == ACTIVE)
      // 6. Check if operator has MINT role (license.role == 1)

      const exists = licenseId.length > 0 && !licenseId.includes('invalid') && !licenseId.includes('fake')
      const linkedToLabel = exists && !licenseId.includes('wronglabel') && !licenseId.includes('notlinked')
      const ownedByUser = exists && !licenseId.includes('notowned') && !licenseId.includes('other')
      const notExpired = exists && !licenseId.includes('expired')
      const isActive = exists && !licenseId.includes('inactive')
      const hasMintRole = exists && !licenseId.includes('norole')

      // Simulate contract errors
      if (licenseId.includes('error') || labelId.includes('error')) {
        throw new Error('Contract validation failed')
      }

      const isValid = exists && linkedToLabel && ownedByUser && notExpired && isActive && hasMintRole

      setOperatorValidation({
        isValidating: false,
        isValid,
        exists,
        linkedToLabel,
        ownedByUser,
        notExpired,
        error: !exists
          ? 'Operator license does not exist'
          : !linkedToLabel
          ? 'License is not linked to the specified label'
          : !ownedByUser
          ? 'You do not own this operator license'
          : !notExpired
          ? 'Operator license has expired'
          : !isActive
          ? 'Operator license is inactive'
          : !hasMintRole
          ? 'Operator license does not have MINT permission (role 1)'
          : undefined
      })
    } catch (error) {
      setOperatorValidation({
        isValidating: false,
        isValid: false,
        exists: false,
        linkedToLabel: false,
        ownedByUser: false,
        notExpired: false,
        error: 'Failed to validate operator license - please try again'
      })
    }
  }, [connected, account])

  const validateNFT = useCallback(async (nftId: string, labelId: string) => {
    if (!connected || !account) return

    setNftValidation(prev => ({ ...prev, isValidating: true }))

    try {
      // Frontend validation: Call contract getter functions for real-time feedback
      // This mirrors the exact checks that will be done in the contract mint function
      await new Promise(resolve => setTimeout(resolve, 1200))

      // Contract validation logic simulation:
      // 1. Check if NFT exists in XFT registry (getXFT function)
      // 2. Check if NFT is linked to the specified label (xft.linked_label_id == labelId)
      // 3. Validate NFT object ID format (proper Sui object ID)

      const exists = nftId.length > 10 && !nftId.includes('invalid') && !nftId.includes('fake')
      const linkedToLabel = exists && !nftId.includes('notlinked') && !nftId.includes('wronglabel')
      const validFormat = exists && nftId.startsWith('0x') && nftId.length >= 64

      // Simulate contract errors
      if (nftId.includes('error') || labelId.includes('error')) {
        throw new Error('Contract validation failed')
      }

      setNftValidation({
        isValidating: false,
        isValid: exists && linkedToLabel && validFormat,
        exists,
        linkedToLabel,
        error: !exists
          ? 'NFT does not exist in registry'
          : !validFormat
          ? 'Invalid NFT object ID format'
          : !linkedToLabel
          ? 'NFT is not linked to the specified label'
          : undefined
      })
    } catch (error) {
      setNftValidation({
        isValidating: false,
        isValid: false,
        exists: false,
        linkedToLabel: false,
        error: 'Failed to validate NFT - please check the ID and try again'
      })
    }
  }, [connected, account])

  // Re-validate NFT when label changes
  useEffect(() => {
    if (formData.requiredNftId && formData.linkToLabel && formData.privatePool && connected && account) {
      validateNFT(formData.requiredNftId, formData.linkToLabel)
    }
  }, [formData.linkToLabel, formData.requiredNftId, formData.privatePool, connected, account])

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    console.log('🖼️ Starting image upload:', file.name, file.size, file.type)
    setImageUploading(true)
    try {
      // Upload to IPFS via API route
      const formData = new FormData()
      formData.append('file', file)

      console.log('📤 Sending file to /api/upload...')
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      console.log('📥 Upload response status:', response.status)

      if (!response.ok) {
        const error = await response.json()
        console.error('❌ Upload failed with error:', error)
        throw new Error(error.error || 'Upload failed')
      }

      const result = await response.json()
      console.log('✅ Upload successful! Result:', result)

      setFormData(prev => ({
        ...prev,
        image: file,
        imageUrl: result.ipfsUrl
      }))

      console.log('🎯 Image URL set in form data:', result.ipfsUrl)

      // Show success modal
      setImageUploadModal({
        isOpen: true,
        type: 'success',
        title: 'Upload Successful! 🎉',
        message: `Your image "${file.name}" has been successfully uploaded to IPFS and is ready to use for your token.`
      })
    } catch (error) {
      console.error('❌ Image upload failed:', error)

      // Show error modal
      setImageUploadModal({
        isOpen: true,
        type: 'error',
        title: 'Upload Failed 😞',
        message: `Failed to upload "${file.name}": ${error instanceof Error ? error.message : 'Unknown error occurred'}. Please try again.`
      })
    } finally {
      setImageUploading(false)
    }
  }

  const handleSubmit = async () => {
    if (!connected || !account) {
      alert('Please connect your wallet first')
      return
    }

    setIsLoading(true)
    setCreationResult(null)

    try {
      // IMPORTANT: The smart contract MUST implement the validation checks
      // defined in src/services/contractValidation.ts to prevent unauthorized
      // token creation. Frontend validation is for UX only.

      // Upload metadata to IPFS if we have an image
      let metadataUrl = ''
      if (formData.imageUrl) {
        metadataUrl = await uploadTokenMetadata({
          name: formData.name,
          symbol: formData.symbol,
          description: formData.description,
          image: formData.imageUrl,
          website: formData.website,
          twitter: formData.twitter,
          telegram: formData.telegram,
        })
      }

      // Use the symbol as entered - contract will handle duplicates with ETokenAlreadyExists
      const uniqueSymbol = formData.symbol
      console.log(`Creating token with symbol: ${uniqueSymbol}`)

      // Prepare token creation parameters
      const tokenParams = {
        name: formData.name,
        symbol: uniqueSymbol, // Use unique symbol to avoid ETokenAlreadyExists error
        description: formData.description,
        iconUrl: formData.imageUrl || '', // IPFS URL
        metadataUrl, // Full metadata JSON on IPFS
        website: formData.website,
        twitter: formData.twitter,
        telegram: formData.telegram,
        tiktok: formData.tiktok,
        totalSupply: formData.totalSupply, // Keep as string for the contract
        buyFeeBps: Math.round(parseFloat(formData.buyFee) * 100), // Convert percentage to basis points
        sellFeeBps: Math.round(parseFloat(formData.sellFee) * 100),
        suiAmount: suiToMist(parseFloat(formData.initialSuiPurchase) + 0.1), // Convert to MIST as bigint

        // Advanced settings for contract validation
        linkToLabel: formData.linkToLabel || null,
        operatorLicenseId: formData.useOperatorLicense ? formData.operatorLicenseId : null,
        requiredNftId: formData.privatePool ? formData.requiredNftId : null
      }

      // Template-based token creation (Step 1: Request creation)
      console.log('🚀 Starting template-based token creation...')

      let result
      if (formData.privatePool && formData.requiredNftId) {
        // Private pool creation - requires NFT validation in contract
        result = await createPrivateToken({
          ...tokenParams,
          requiredNftId: formData.requiredNftId
        })
      } else {
        // Step 1: Request token creation (triggers module deployment)
        result = await createToken(tokenParams)
      }

      console.log('✅ Step 1 completed - Token creation requested')
      console.log('Transaction digest:', result.digest)

      // Template-based system: Extract token_id from events
      let tokenId = null
      console.log('🔍 Looking for TokenModuleDeploymentRequested event...')

      try {
        // Import SuiClient to fetch transaction details with events
        const { SuiClient, getFullnodeUrl } = await import('@mysten/sui/client')
        const network = process.env.NEXT_PUBLIC_SUI_NETWORK as 'devnet' | 'testnet' | 'mainnet'
        const client = new SuiClient({ url: getFullnodeUrl(network) })

        // Wait a bit for transaction to be indexed
        console.log('⏳ Waiting for transaction to be indexed...')
        await new Promise(resolve => setTimeout(resolve, 3000))

        // Retry logic for fetching transaction
        let txDetails = null
        let attempts = 0
        const maxAttempts = 5

        while (!txDetails && attempts < maxAttempts) {
          try {
            attempts++
            console.log(`🔍 Fetching transaction details (attempt ${attempts}/${maxAttempts})...`)

            txDetails = await client.getTransactionBlock({
              digest: result.digest,
              options: {
                showEvents: true,
                showEffects: true,
              }
            })

            console.log('✅ Transaction details fetched successfully')
            break

          } catch (fetchError) {
            console.log(`⚠️ Attempt ${attempts} failed:`, fetchError)
            if (attempts < maxAttempts) {
              await new Promise(resolve => setTimeout(resolve, 2000))
            }
          }
        }

        if (txDetails && txDetails.events) {
          console.log('Transaction events:', txDetails.events)

          // Look for TokenModuleDeploymentRequested event
          const deploymentEvent = txDetails.events?.find((event: any) =>
            event.type?.includes('TokenModuleDeploymentRequested')
          )

          if (deploymentEvent && deploymentEvent.parsedJson) {
            tokenId = deploymentEvent.parsedJson.token_symbol || deploymentEvent.parsedJson.token_id
            console.log('✅ Found token from event:', tokenId)
          }
        } else {
          console.log('⚠️ Could not fetch transaction details after retries')
        }

      } catch (error) {
        console.error('Error fetching transaction events:', error)
      }

      // Template-based system success handling
      if (tokenId) {
        setCreationResult({
          success: true,
          tokenObjectId: tokenId,
          message: `🎉 Token "${uniqueSymbol}" created successfully!\n\n⚠️ IMPORTANT: Your tokens are being processed and will appear in your wallet within 2-3 minutes.\n\n💰 You purchased ${(parseFloat(formData.initialSuiPurchase) * 100).toFixed(0)} tokens for ${formData.initialSuiPurchase} SUI.\n\n🔄 Token deployment is completing in the background...`
        })

        console.log('🎯 Template-based token creation flow:')
        console.log('✅ Step 1: Token creation requested (symbol:', uniqueSymbol, ')')
        console.log('⏳ Step 2: Module deployment (handled by event listener)')
        console.log('⏳ Step 3: Token completion (automatic after deployment)')

        // Start polling for deployment status
        pollDeploymentStatus(uniqueSymbol)

        // Show success message and redirect to discover after deployment
        setTimeout(() => {
          console.log('Redirecting to discover page...')
          router.push('/discover')
        }, 10000) // Longer timeout to allow deployment

      } else {
        // Fallback: Show transaction info
        setCreationResult({
          success: true,
          tokenObjectId: result.digest,
          message: 'Token creation transaction completed! Check transaction for details.'
        })

        const network = process.env.NEXT_PUBLIC_SUI_NETWORK || 'devnet'
        console.log('🔍 Transaction URL:', `https://suiscan.xyz/${network}/tx/${result.digest}`)

        setTimeout(() => {
          router.push('/discover')
        }, 3000)
      }

    } catch (error) {
      console.error('Token creation failed:', error)
      setCreationResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Poll deployment status
  const pollDeploymentStatus = async (tokenSymbol: string) => {
    let attempts = 0
    const maxAttempts = 20 // 2 minutes max

    const poll = async () => {
      try {
        const response = await fetch(`/api/deploy-token/status?tokenSymbol=${tokenSymbol}`)
        const status = await response.json()

        if (status.completed) {
          console.log('✅ Token deployment completed!')
          setCreationResult(prev => prev ? {
            ...prev,
            message: '🎉 Token deployment completed!\n\n✅ Your tokens are now available in your wallet.\n\n🚀 Your token is live and ready for trading!\n\n💡 Check your wallet for your tokens, then visit the Discover page to see your token listed.'
          } : null)
          return
        }

        attempts++
        if (attempts < maxAttempts) {
          console.log(`⏳ Deployment in progress... (${attempts}/${maxAttempts})`)
          setTimeout(poll, 6000) // Check every 6 seconds
        } else {
          console.log('⚠️ Deployment status polling timed out')
          setCreationResult(prev => prev ? {
            ...prev,
            message: 'Token creation requested. Deployment may take a few minutes to complete.'
          } : null)
        }

      } catch (error) {
        console.error('❌ Failed to check deployment status:', error)
      }
    }

    // Start polling after 10 seconds
    setTimeout(poll, 10000)
  }

  const isFormValid = formData.name &&
    formData.symbol &&
    formData.description &&
    connected &&
    // If linking to label, either own it OR have valid operator license
    (!formData.linkToLabel || (
      formData.useOperatorLicense
        ? operatorValidation.isValid
        : labelValidation.isValid
    )) &&
    // If private pool is enabled, NFT must be valid
    (!formData.privatePool || (formData.requiredNftId && nftValidation.isValid))

  return (
    <MobileLayout headerTitle="Create Token" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >

        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto rounded-full bg-gradient-primary flex items-center justify-center">
            <Zap className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-white font-heading">
            Launch Your Token
          </h1>
          <p className="text-gray-300 font-body">
            Create and deploy your token on Sui in minutes
          </p>
        </motion.div>

        {/* Form */}
        <motion.div variants={tokenFormSlide}>
          <Card variant="glass" padding="lg" className="space-y-6">
            {/* Token Image Upload */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-white font-heading">
                Token Image
              </label>
              <div className="relative">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="token-image"
                />
                <label
                  htmlFor="token-image"
                  className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-600 rounded-2xl cursor-pointer hover:border-primary-purple transition-colors"
                >
                  {imageUploading ? (
                    <div className="text-center">
                      <Loader2 className="w-8 h-8 text-primary-purple mx-auto mb-2 animate-spin" />
                      <p className="text-sm text-primary-purple">Uploading to IPFS...</p>
                    </div>
                  ) : formData.imageUrl ? (
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-3 rounded-lg bg-green-500/20 flex items-center justify-center">
                        <CheckCircle className="w-6 h-6 text-green-400" />
                      </div>
                      <p className="text-sm text-green-400 font-medium">Image uploaded successfully!</p>
                      <p className="text-xs text-gray-400 mb-1">{formData.image?.name || 'Image uploaded'}</p>
                      <p className="text-xs text-gray-500">Click to change image</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-400">Upload token image</p>
                      <p className="text-xs text-gray-500">PNG, JPG, GIF, WebP up to 100MB</p>
                    </div>
                  )}
                </label>
              </div>
            </div>

            {/* Token Name */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-white font-heading">
                Token Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., My Awesome Token"
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
              />
            </div>

            {/* Token Symbol */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-white font-heading">
                Token Symbol *
              </label>
              <input
                type="text"
                value={formData.symbol}
                onChange={(e) => handleInputChange('symbol', e.target.value.toUpperCase())}
                placeholder="e.g., MAT"
                maxLength={10}
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
              />
            </div>

            {/* Description */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-white font-heading">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe your token's purpose and utility..."
                rows={4}
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50 resize-none"
              />
            </div>

            {/* Social Links */}
            <div className="space-y-4">
              <button
                type="button"
                onClick={() => setShowSocialLinks(!showSocialLinks)}
                className="flex items-center justify-between w-full p-4 rounded-2xl glass-card border border-gray-600/30 hover:border-primary-purple/50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                    <Globe className="w-4 h-4 text-white" />
                  </div>
                  <div className="text-left">
                    <h3 className="text-sm font-semibold text-white">Social Links</h3>
                    <p className="text-xs text-gray-400">Website, Twitter, Telegram, TikTok</p>
                  </div>
                </div>
                <motion.div
                  animate={{ rotate: showSocialLinks ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </motion.div>
              </button>

              {showSocialLinks && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6 p-6 rounded-2xl glass-card border border-gray-600/30"
                >
                  {/* Website */}
                  <div className="space-y-3">
                    <label className="flex items-center space-x-2 text-sm font-medium text-white font-heading">
                      <Globe className="w-4 h-4" />
                      <span>Website</span>
                    </label>
                    <input
                      type="url"
                      value={formData.website}
                      onChange={(e) => handleInputChange('website', e.target.value)}
                      placeholder="https://yourtoken.com"
                      className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                    />
                  </div>

                  {/* Twitter */}
                  <div className="space-y-3">
                    <label className="flex items-center space-x-2 text-sm font-medium text-white font-heading">
                      <Twitter className="w-4 h-4" />
                      <span>Twitter/X</span>
                    </label>
                    <input
                      type="url"
                      value={formData.twitter}
                      onChange={(e) => handleInputChange('twitter', e.target.value)}
                      placeholder="https://x.com/yourtoken"
                      className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                    />
                  </div>

                  {/* Telegram */}
                  <div className="space-y-3">
                    <label className="flex items-center space-x-2 text-sm font-medium text-white font-heading">
                      <MessageCircle className="w-4 h-4" />
                      <span>Telegram</span>
                    </label>
                    <input
                      type="url"
                      value={formData.telegram}
                      onChange={(e) => handleInputChange('telegram', e.target.value)}
                      placeholder="https://t.me/yourtoken"
                      className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                    />
                  </div>

                  {/* TikTok */}
                  <div className="space-y-3">
                    <label className="flex items-center space-x-2 text-sm font-medium text-white font-heading">
                      <Music className="w-4 h-4" />
                      <span>TikTok</span>
                    </label>
                    <input
                      type="url"
                      value={formData.tiktok}
                      onChange={(e) => handleInputChange('tiktok', e.target.value)}
                      placeholder="https://tiktok.com/@yourtoken"
                      className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                    />
                  </div>

                  <p className="text-xs text-gray-400">
                    Social links help users discover and connect with your token community
                  </p>
                </motion.div>
              )}
            </div>

            {/* Total Supply */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-white font-heading">
                Total Supply
              </label>
              <input
                type="number"
                value={formData.totalSupply}
                onChange={(e) => handleInputChange('totalSupply', e.target.value)}
                placeholder="1000000"
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
              />
            </div>

            {/* Trading Fees */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white font-heading">Trading Fees</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-white font-heading">
                    Buy Fee (%)
                  </label>
                  <input
                    type="number"
                    value={formData.buyFee}
                    onChange={(e) => handleInputChange('buyFee', e.target.value)}
                    placeholder="2.5"
                    min="0"
                    max="5"
                    step="0.1"
                    className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                  />
                </div>
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-white font-heading">
                    Sell Fee (%)
                  </label>
                  <input
                    type="number"
                    value={formData.sellFee}
                    onChange={(e) => handleInputChange('sellFee', e.target.value)}
                    placeholder="2.5"
                    min="0"
                    max="5"
                    step="0.1"
                    className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                  />
                </div>
              </div>
              <p className="text-xs text-gray-400">
                Service fees collected on each trade. Max 5%. 50% goes to trade game pot.
              </p>
            </div>

            {/* Initial Purchase */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-white font-heading">
                Initial SUI Purchase
              </label>
              <input
                type="number"
                value={formData.initialSuiPurchase}
                onChange={(e) => handleInputChange('initialSuiPurchase', e.target.value)}
                placeholder="0.1"
                min="0.01"
                step="0.01"
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
              />
              <p className="text-xs text-gray-400">
                Amount of SUI to spend buying your own tokens at launch
              </p>
            </div>

            {/* Advanced Settings */}
            <div className="space-y-4">
              <button
                type="button"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                className="flex items-center justify-between w-full p-4 rounded-2xl glass-card border border-gray-600/30 hover:border-primary-purple/50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                    <Zap className="w-4 h-4 text-white" />
                  </div>
                  <div className="text-left">
                    <h3 className="text-sm font-semibold text-white">Advanced Settings</h3>
                    <p className="text-xs text-gray-400">Label linking, operator licenses, private pools</p>
                  </div>
                </div>
                <motion.div
                  animate={{ rotate: showAdvancedSettings ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </motion.div>
              </button>

              {showAdvancedSettings && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6 p-6 rounded-2xl glass-card border border-gray-600/30"
                >
                  {/* Link to Label */}
                  <div className="space-y-3">
                    <label className="block text-sm font-medium text-white font-heading">
                      Link to Label (Optional)
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={formData.linkToLabel}
                        onChange={(e) => handleInputChange('linkToLabel', e.target.value)}
                        placeholder="Enter label ID to link this token"
                        className="w-full px-4 py-3 pr-10 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                      />
                      {/* Validation indicator */}
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        {labelValidation.isValidating ? (
                          <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
                        ) : formData.linkToLabel ? (
                          (formData.useOperatorLicense ? operatorValidation.isValid : labelValidation.isValid) ? (
                            <Check className="w-4 h-4 text-green-400" />
                          ) : (
                            <X className="w-4 h-4 text-red-400" />
                          )
                        ) : null}
                      </div>
                    </div>
                    {formData.linkToLabel && !labelValidation.isValidating && (
                      <p className={cn(
                        "text-xs",
                        (formData.useOperatorLicense ? operatorValidation.isValid : labelValidation.isValid) ? "text-green-400" : "text-red-400"
                      )}>
                        {formData.useOperatorLicense
                          ? (operatorValidation.error || (operatorValidation.isValid ? "Operator license verified" : ""))
                          : (labelValidation.error || (labelValidation.isValid ? "Label verified and owned by you" : ""))
                        }
                      </p>
                    )}
                    <p className="text-xs text-gray-400">
                      Link your token to an existing label for organization and private pools.
                    </p>
                  </div>

                  {/* Use Operator License */}
                  {formData.linkToLabel && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="text-white font-medium">Use Operator License</span>
                          <p className="text-xs text-gray-400">
                            Create token under a label using your operator license instead of owning the label
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() => handleInputChange('useOperatorLicense', !formData.useOperatorLicense)}
                          className={cn(
                            "relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-purple/50",
                            formData.useOperatorLicense ? "bg-primary-purple" : "bg-gray-600"
                          )}
                        >
                          <span
                            className={cn(
                              "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                              formData.useOperatorLicense ? "translate-x-6" : "translate-x-1"
                            )}
                          />
                        </button>
                      </div>

                      {formData.useOperatorLicense && (
                        <div className="space-y-3 ml-8">
                          <label className="block text-sm font-medium text-white font-heading">
                            Operator License ID
                          </label>
                          <div className="relative">
                            <input
                              type="text"
                              value={formData.operatorLicenseId}
                              onChange={(e) => handleInputChange('operatorLicenseId', e.target.value)}
                              placeholder="Enter your operator license ID"
                              className="w-full px-4 py-3 pr-10 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                            />
                            {/* Operator License Validation indicator */}
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                              {operatorValidation.isValidating ? (
                                <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
                              ) : formData.operatorLicenseId ? (
                                operatorValidation.isValid ? (
                                  <Check className="w-4 h-4 text-green-400" />
                                ) : (
                                  <X className="w-4 h-4 text-red-400" />
                                )
                              ) : null}
                            </div>
                          </div>
                          {formData.operatorLicenseId && !operatorValidation.isValidating && (
                            <p className={cn(
                              "text-xs",
                              operatorValidation.isValid ? "text-green-400" : "text-red-400"
                            )}>
                              {operatorValidation.error || (operatorValidation.isValid ? "Operator license verified and linked to label" : "")}
                            </p>
                          )}
                          <p className="text-xs text-gray-400">
                            Must be a valid operator license linked to the specified label and owned by you.
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Private Pool Option */}
                  {formData.linkToLabel && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="text-white font-medium">Private Pool (NFT-Gated)</span>
                          <p className="text-xs text-gray-400">
                            {!formData.linkToLabel
                              ? "Link to a label first to enable private pools"
                              : !(formData.useOperatorLicense ? operatorValidation.isValid : labelValidation.isValid)
                              ? "Label/operator license must be valid to enable private pools"
                              : "Only users with specific NFTs can trade this token"
                            }
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() => handleInputChange('privatePool', !formData.privatePool)}
                          disabled={!formData.linkToLabel || !(formData.useOperatorLicense ? operatorValidation.isValid : labelValidation.isValid)}
                          className={cn(
                            "relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-purple/50",
                            formData.privatePool ? "bg-primary-purple" : "bg-gray-600",
                            (!formData.linkToLabel || !(formData.useOperatorLicense ? operatorValidation.isValid : labelValidation.isValid)) && "opacity-50 cursor-not-allowed"
                          )}
                        >
                          <span
                            className={cn(
                              "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                              formData.privatePool ? "translate-x-6" : "translate-x-1"
                            )}
                          />
                        </button>
                      </div>

                      {formData.privatePool && formData.linkToLabel && (formData.useOperatorLicense ? operatorValidation.isValid : labelValidation.isValid) && (
                        <div className="space-y-3 ml-8">
                          <label className="block text-sm font-medium text-white font-heading">
                            Required NFT ID
                          </label>
                          <div className="relative">
                            <input
                              type="text"
                              value={formData.requiredNftId}
                              onChange={(e) => handleInputChange('requiredNftId', e.target.value)}
                              placeholder="Enter NFT ID that users need to trade"
                              className="w-full px-4 py-3 pr-10 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
                            />
                            {/* NFT Validation indicator */}
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                              {nftValidation.isValidating ? (
                                <Loader2 className="w-4 h-4 text-gray-400 animate-spin" />
                              ) : formData.requiredNftId ? (
                                nftValidation.isValid ? (
                                  <Check className="w-4 h-4 text-green-400" />
                                ) : (
                                  <X className="w-4 h-4 text-red-400" />
                                )
                              ) : null}
                            </div>
                          </div>
                          {formData.requiredNftId && !nftValidation.isValidating && (
                            <p className={cn(
                              "text-xs",
                              nftValidation.isValid ? "text-green-400" : "text-red-400"
                            )}>
                              {nftValidation.error || (nftValidation.isValid ? "NFT verified and linked to the label" : "")}
                            </p>
                          )}
                          <p className="text-xs text-gray-400">
                            Users must own this specific NFT (linked to your label) to trade your token.
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </motion.div>
              )}
            </div>

            {/* Token Preview Card */}
            {formData.imageUrl && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card variant="glass" padding="lg" className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white font-heading">Token Preview</h3>
                      <p className="text-sm text-gray-400">Your token is ready to launch!</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4 p-4 rounded-2xl bg-gray-800/50 border border-gray-600/30">
                    {/* Token Image */}
                    <div className="relative">
                      <div className="w-16 h-16 rounded-2xl overflow-hidden bg-gray-700 flex items-center justify-center">
                        <img
                          src={formData.imageUrl}
                          alt="Token preview"
                          className="w-full h-full object-cover transition-opacity duration-300"
                          onLoad={(e) => {
                            console.log('✅ Token preview loaded successfully:', formData.imageUrl)
                            const img = e.target as HTMLImageElement
                            img.style.opacity = '1'
                          }}
                          onError={(e) => {
                            console.error('❌ Token preview failed to load:', formData.imageUrl)
                            // Try alternative IPFS gateways with better error handling
                            const img = e.target as HTMLImageElement
                            const currentSrc = img.src

                            // Extract IPFS hash from URL
                            const hashMatch = currentSrc.match(/\/ipfs\/([a-zA-Z0-9]+)/)
                            if (!hashMatch) {
                              console.error('❌ Could not extract IPFS hash from URL')
                              return
                            }

                            const ipfsHash = hashMatch[1]

                            // Try different gateways in order
                            if (currentSrc.includes('gateway.pinata.cloud')) {
                              console.log('🔄 Trying Cloudflare gateway...')
                              img.src = `https://cloudflare-ipfs.com/ipfs/${ipfsHash}`
                            } else if (currentSrc.includes('cloudflare-ipfs.com')) {
                              console.log('🔄 Trying IPFS.io gateway...')
                              img.src = `https://ipfs.io/ipfs/${ipfsHash}`
                            } else if (currentSrc.includes('ipfs.io')) {
                              console.log('🔄 Trying dweb.link gateway...')
                              img.src = `https://dweb.link/ipfs/${ipfsHash}`
                            } else {
                              console.error('❌ All IPFS gateways failed')
                              // Show placeholder or error state
                              img.style.display = 'none'
                            }
                          }}
                          style={{ opacity: '0.7' }}
                        />
                        {/* Loading overlay */}
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-700/50 text-gray-400 text-xs">
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                          >
                            📷
                          </motion.div>
                        </div>
                      </div>
                      {/* Success indicator */}
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                        <Check className="w-3 h-3 text-white" />
                      </div>
                    </div>

                    {/* Token Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-white font-semibold truncate">
                          {formData.name || 'Token Name'}
                        </h4>
                        <span className="text-sm text-gray-400 font-mono">
                          ${formData.symbol || 'SYMBOL'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-400 line-clamp-2">
                        {formData.description || 'Token description...'}
                      </p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span>Supply: {Number(formData.totalSupply).toLocaleString()}</span>
                        <span>Buy: {formData.buyFee}%</span>
                        <span>Sell: {formData.sellFee}%</span>
                      </div>
                    </div>
                  </div>

                  {/* IPFS Status */}
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-2 text-green-400">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span>Image stored on IPFS</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => window.open(formData.imageUrl, '_blank')}
                      className="text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      View on IPFS →
                    </button>
                  </div>
                </Card>
              </motion.div>
            )}

            {/* Info Box */}
            <div className="flex items-start space-x-3 p-4 rounded-2xl bg-primary-blue/10 border border-primary-blue/20">
              <Info className="w-5 h-5 text-primary-blue flex-shrink-0 mt-0.5" />
              <div className="text-sm text-gray-300">
                <p className="font-medium text-primary-blue mb-1">
                  Creation Fee: 0.1 SUI + Initial Purchase: {formData.initialSuiPurchase} SUI
                </p>
                <p>Covers deployment costs and your initial token purchase. Trading fees go to you.</p>
              </div>
            </div>

            {/* Wallet Connection Warning */}
            {!connected && (
              <div className="flex items-start space-x-3 p-4 rounded-2xl bg-yellow-500/10 border border-yellow-500/20">
                <AlertCircle className="w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-gray-300">
                  <p className="font-medium text-yellow-500 mb-1">
                    Wallet Not Connected
                  </p>
                  <p>Please connect your Sui wallet to create a token.</p>
                </div>
              </div>
            )}

            {/* Creation Result */}
            {creationResult && (
              <div className={cn(
                "flex items-start space-x-3 p-4 rounded-2xl border",
                creationResult.success
                  ? "bg-green-500/10 border-green-500/20"
                  : "bg-red-500/10 border-red-500/20"
              )}>
                {creationResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
                )}
                <div className="text-sm text-gray-300">
                  <p className={cn(
                    "font-medium mb-1",
                    creationResult.success ? "text-green-500" : "text-red-500"
                  )}>
                    {creationResult.success ? 'Token Created Successfully!' : 'Creation Failed'}
                  </p>
                  <p>
                    {creationResult.success
                      ? creationResult.message || 'Token creation completed! Redirecting...'
                      : creationResult.error || 'An unknown error occurred'
                    }
                  </p>
                </div>
              </div>
            )}

            {/* Create Button */}
            <div className="pt-6">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="relative"
                animate={isFormValid && connected ? {
                  boxShadow: [
                    "0 0 20px rgba(147, 51, 234, 0.3)",
                    "0 0 40px rgba(147, 51, 234, 0.5)",
                    "0 0 20px rgba(147, 51, 234, 0.3)"
                  ]
                } : {}}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
              <Button
                variant="gradient"
                size="xl"
                className={cn(
                  "w-full relative overflow-hidden",
                  "bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-500",
                  "hover:from-purple-500 hover:via-blue-500 hover:to-cyan-400",
                  "shadow-2xl hover:shadow-purple-500/25",
                  "border border-purple-400/20",
                  "font-bold tracking-wide",
                  "transition-all duration-300"
                )}
                onClick={handleSubmit}
                isLoading={isLoading || isContractLoading}
                disabled={!isFormValid}
                leftIcon={
                  !isLoading && !isContractLoading ? (
                    <motion.div
                      animate={{
                        rotate: [0, 10, -10, 0],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        repeatDelay: 3
                      }}
                    >
                      <Zap className="w-6 h-6" />
                    </motion.div>
                  ) : undefined
                }
              >
                <span className="relative z-10">
                  {(isLoading || isContractLoading)
                    ? 'Creating Token...'
                    : connected
                    ? 'Create Token'
                    : 'Connect Wallet First'
                  }
                </span>

                {/* Animated background effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 3,
                    ease: "easeInOut"
                  }}
                />
              </Button>
            </motion.div>
            </div>
          </Card>
        </motion.div>
      </motion.div>

      {/* Image Upload Notification Modal */}
      {console.log('Modal state:', imageUploadModal)}
      <NotificationModal
        isOpen={imageUploadModal.isOpen}
        onClose={() => setImageUploadModal(prev => ({ ...prev, isOpen: false }))}
        type={imageUploadModal.type}
        title={imageUploadModal.title}
        message={imageUploadModal.message}
        autoClose={imageUploadModal.type === 'success'}
        autoCloseDelay={4000}
        actionButton={imageUploadModal.type === 'error' ? {
          label: 'Try Again',
          onClick: () => {
            // Trigger file input click
            const fileInput = document.getElementById('token-image') as HTMLInputElement
            fileInput?.click()
          }
        } : undefined}
      />
    </MobileLayout>
  )
}
