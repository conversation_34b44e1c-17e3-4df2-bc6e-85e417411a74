'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { staggerContainer, staggerItem } from '@/lib/animations'
import { useRouter } from 'next/navigation'

export default function HowToLaunchPage() {
  const router = useRouter()

  return (
    <MobileLayout headerTitle="How to Launch a Token" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="text-6xl">🚀</div>
          <h1 className="text-2xl font-bold text-white font-heading">
            Launch Your Token
          </h1>
          <div className="w-20 h-0.5 bg-gradient-to-r from-emerald-400 to-emerald-600 rounded-full mx-auto" />
          <p className="text-gray-300">
            Create and deploy your token on Solana in just a few steps
          </p>
        </motion.div>

        {/* Steps */}
        <motion.div variants={staggerItem} className="space-y-4">
          {[
            {
              step: 1,
              title: "Connect Your Wallet",
              description: "Connect your Solana wallet (Phantom, Solflare, etc.) to get started",
              icon: "🔗",
              color: "cyan"
            },
            {
              step: 2,
              title: "Fill Token Details",
              description: "Enter your token name, symbol, description, and upload an image",
              icon: "📝",
              color: "emerald"
            },
            {
              step: 3,
              title: "Set Trading Fees",
              description: "Configure buy/sell fees (0-10%) to earn revenue from trades",
              icon: "💰",
              color: "yellow"
            },
            {
              step: 4,
              title: "Choose Initial Purchase",
              description: "Decide how much SOL to spend buying your own tokens at launch",
              icon: "🛒",
              color: "purple"
            },
            {
              step: 5,
              title: "Deploy & Launch",
              description: "Pay 0.1 SOL deployment fee and your token goes live instantly",
              icon: "🎯",
              color: "pink"
            }
          ].map((item) => (
            <Card key={item.step} variant="glass" padding="lg" className={`border border-${item.color}-400/30 bg-${item.color}-400/5`}>
              <div className="flex items-start space-x-4">
                <div className={`w-12 h-12 rounded-full bg-gradient-to-br from-${item.color}-400 to-${item.color}-600 flex items-center justify-center flex-shrink-0`}>
                  <span className="text-2xl">{item.icon}</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className={`text-xs font-bold px-2 py-1 rounded-full bg-${item.color}-400/20 text-${item.color}-300`}>
                      STEP {item.step}
                    </span>
                  </div>
                  <h3 className="text-lg font-bold text-white font-heading mb-2">
                    {item.title}
                  </h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {item.description}
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </motion.div>

        {/* Requirements */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-blue-400/30 bg-blue-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Requirements</h2>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 rounded-full bg-emerald-400 flex items-center justify-center">
                    <span className="text-xs font-bold text-black">✓</span>
                  </div>
                  <span className="text-gray-300">Solana wallet with at least 0.15 SOL</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 rounded-full bg-emerald-400 flex items-center justify-center">
                    <span className="text-xs font-bold text-black">✓</span>
                  </div>
                  <span className="text-gray-300">Token name and symbol (unique)</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 rounded-full bg-emerald-400 flex items-center justify-center">
                    <span className="text-xs font-bold text-black">✓</span>
                  </div>
                  <span className="text-gray-300">Token description and image (optional)</span>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Tips */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-yellow-400/30 bg-yellow-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Pro Tips</h2>
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">💡</div>
                  <p className="text-gray-300">
                    Keep trading fees reasonable (0.5-2%) to attract more traders
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">🎨</div>
                  <p className="text-gray-300">
                    Use eye-catching images - they're crucial for meme coin success
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">📱</div>
                  <p className="text-gray-300">
                    Share your token on social media to build community
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">🎮</div>
                  <p className="text-gray-300">
                    Remember: 50% of your fees go to the trading game prize pool
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* CTA */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <Card variant="glass" padding="lg" className="border border-emerald-400/30 bg-emerald-400/5">
            <div className="space-y-4">
              <h3 className="text-xl font-bold text-white font-heading">
                Ready to Launch?
              </h3>
              <p className="text-gray-300">
                Start creating your token now and join the Solana ecosystem
              </p>
              <Button
                variant="gradient"
                size="lg"
                className="w-full bg-gradient-to-r from-emerald-400 to-emerald-600 hover:from-emerald-500 hover:to-emerald-700"
                onClick={() => router.push('/create')}
              >
                Create Your Token
              </Button>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
