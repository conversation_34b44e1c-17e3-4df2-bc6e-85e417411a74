'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { staggerContainer, staggerItem } from '@/lib/animations'
import { Trophy, Target, Zap, Users } from 'lucide-react'

export default function TradingGamePage() {
  return (
    <MobileLayout headerTitle="Trading Game" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="text-6xl">🎮</div>
          <h1 className="text-2xl font-bold text-white font-heading">
            Trading Game
          </h1>
          <div className="w-20 h-0.5 bg-gradient-to-r from-yellow-400 to-orange-600 rounded-full mx-auto" />
          <p className="text-gray-300">
            Win massive prizes by trading tokens on Sui
          </p>
        </motion.div>

        {/* Current Prize Pool */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-yellow-400/30 bg-gradient-to-br from-yellow-400/10 to-orange-600/15">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-yellow-400 to-orange-600 flex items-center justify-center">
                <Trophy className="w-8 h-8 text-white" />
              </div>
              <div>
                <p className="text-sm text-yellow-300 font-medium mb-1">Current Prize Pool</p>
                <p className="text-4xl font-bold text-white mb-2">🏆 1,247.8 SOL</p>
                <p className="text-lg text-emerald-300 font-bold mb-1">Trade #300 Wins!</p>
                <p className="text-sm text-gray-400">Current trade count: #287</p>
              </div>
              <div className="grid grid-cols-3 gap-3 pt-4 border-t border-yellow-400/20">
                <div className="text-center">
                  <p className="text-xl font-bold text-yellow-300">13</p>
                  <p className="text-xs text-gray-400">Trades to Win</p>
                </div>
                <div className="text-center">
                  <p className="text-xl font-bold text-yellow-300">2,847</p>
                  <p className="text-xs text-gray-400">Active Traders</p>
                </div>
                <div className="text-center">
                  <p className="text-xl font-bold text-yellow-300">156</p>
                  <p className="text-xs text-gray-400">Winners Today</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* How It Works */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">How the Xth Trade Game Works</h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-emerald-300 mb-1">Auto Goal Setting</h3>
                    <p className="text-gray-300 text-sm">Game automatically sets a trade goal (e.g., trade #300 wins the pot)</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-purple-300 mb-1">Fees Build Prize Pool</h3>
                    <p className="text-gray-300 text-sm">50% of creator fees from every trade automatically go into the pot</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-yellow-400 to-orange-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-yellow-300 mb-1">Xth Trade Wins!</h3>
                    <p className="text-gray-300 text-sm">The trader who makes the target trade (e.g., #300) wins the entire pot</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-cyan-400 to-cyan-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">4</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-cyan-300 mb-1">Game Resets</h3>
                    <p className="text-gray-300 text-sm">After a win, the game resets with a higher trade goal and new pot</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Dynamic Goal System */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-cyan-400/30 bg-cyan-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Smart Goal Adjustment</h2>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">📈</div>
                  <div>
                    <h3 className="font-semibold text-cyan-300">Goal Increases After Wins</h3>
                    <p className="text-gray-300 text-sm">After each win, the next goal increases (300 → 350 → 400, etc.)</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">⏰</div>
                  <div>
                    <h3 className="font-semibold text-cyan-300">Auto-Decrease on Inactivity</h3>
                    <p className="text-gray-300 text-sm">If 5+ minutes pass between trades, the goal decreases to encourage activity</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">🎯</div>
                  <div>
                    <h3 className="font-semibold text-cyan-300">Perfect for Re-energizing</h3>
                    <p className="text-gray-300 text-sm">Great for reviving slow charts and maintaining community engagement</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Community Features */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-purple-400/30 bg-purple-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Community & Creator Features</h2>
              <div className="space-y-4">
                <div className="p-4 rounded-xl bg-emerald-400/10 border border-emerald-400/20">
                  <h3 className="font-semibold text-emerald-300 mb-2">💰 Boost the Pot</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Community members and token creators can add extra SOL to the reward pot at any time to increase excitement
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-blue-400/10 border border-blue-400/20">
                  <h3 className="font-semibold text-blue-300 mb-2">🤖 Bot Activity</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Perfect for generating trading bot activity on your token - bots compete for prizes too!
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                  <h3 className="font-semibold text-yellow-300 mb-2">👥 Retain Community</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Keeps community members engaged and trading, preventing token abandonment
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-pink-400/10 border border-pink-400/20">
                  <h3 className="font-semibold text-pink-300 mb-2">🌍 All Tokens Included</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Trading rewards apply to ALL tokens - both pre-migration and migrated tokens
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Game Stats */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Game Statistics</h2>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 rounded-xl bg-cyan-400/10 border border-cyan-400/20">
                  <Target className="w-8 h-8 text-cyan-400 mx-auto mb-2" />
                  <p className="text-xl font-bold text-white">1:300</p>
                  <p className="text-xs text-gray-400">Current Win Odds</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-emerald-400/10 border border-emerald-400/20">
                  <Zap className="w-8 h-8 text-emerald-400 mx-auto mb-2" />
                  <p className="text-xl font-bold text-white">127.3 SUI</p>
                  <p className="text-xs text-gray-400">Average Prize</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-purple-400/10 border border-purple-400/20">
                  <Users className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                  <p className="text-xl font-bold text-white">8,934</p>
                  <p className="text-xs text-gray-400">Total Winners</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                  <Trophy className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                  <p className="text-xl font-bold text-white">2,847.6 SUI</p>
                  <p className="text-xs text-gray-400">Biggest Win</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Recent Winners */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Recent Xth Trade Winners</h2>
              <div className="space-y-3">
                {[
                  { address: 'A1B2...X9Y0', amount: 1247.8, trade: 300, time: '2 minutes ago', token: 'DOGE' },
                  { address: 'C3D4...Z1A2', amount: 892.4, trade: 275, time: '15 minutes ago', token: 'PEPE' },
                  { address: 'E5F6...B3C4', amount: 456.2, trade: 250, time: '1 hour ago', token: 'MOON' },
                  { address: 'G7H8...D5E6', amount: 678.9, trade: 225, time: '3 hours ago', token: 'CATS' }
                ].map((winner, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-emerald-400/10 border border-emerald-400/20">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
                        <Trophy className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-white text-sm">{winner.address}</p>
                        <p className="text-xs text-emerald-300">Trade #{winner.trade} • ${winner.token}</p>
                        <p className="text-xs text-gray-400">{winner.time}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-emerald-300">{winner.amount} SOL</p>
                      <p className="text-xs text-gray-400">🎯 Xth Trade Win</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </motion.div>

        {/* CTA */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-emerald-400/30 bg-emerald-400/5">
            <div className="text-center space-y-4">
              <h3 className="text-xl font-bold text-white font-heading">
                Start Playing Now
              </h3>
              <p className="text-gray-300">
                Every trade is a chance to win. The more you trade, the better your odds!
              </p>
              <Button
                variant="gradient"
                size="lg"
                className="w-full bg-gradient-to-r from-yellow-400 to-orange-600 hover:from-yellow-500 hover:to-orange-700"
              >
                🎮 Start Trading & Playing
              </Button>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
