'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { staggerContainer, staggerItem } from '@/lib/animations'

export default function AboutPage() {
  return (
    <MobileLayout headerTitle="About Us" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="text-6xl">🚀</div>
          <h1 className="text-2xl font-bold text-white font-heading">
            About Dexsta
          </h1>
          <div className="w-20 h-0.5 bg-gradient-to-r from-emerald-400 to-emerald-600 rounded-full mx-auto" />
        </motion.div>

        {/* Mission */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-emerald-400/30 bg-emerald-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Our Mission</h2>
              <p className="text-gray-200 leading-relaxed">
                Dexsta is revolutionizing token creation and trading on Solana. We believe everyone should have the power to create, launch, and trade tokens with ease, transparency, and fairness.
              </p>
            </div>
          </Card>
        </motion.div>

        {/* What We Do */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">What We Do</h2>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">🎯</div>
                  <div>
                    <h3 className="font-semibold text-emerald-300">Token Launch Platform</h3>
                    <p className="text-gray-300 text-sm">Create and deploy tokens on Solana in minutes with built-in trading fees and liquidity management.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">🎮</div>
                  <div>
                    <h3 className="font-semibold text-emerald-300">Trading Game</h3>
                    <p className="text-gray-300 text-sm">Gamified trading experience where traders compete for prize pools funded by creator fees.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">🤖</div>
                  <div>
                    <h3 className="font-semibold text-emerald-300">AI Trading Bots</h3>
                    <p className="text-gray-300 text-sm">Advanced AI-powered trading bots that execute thousands of trades per second.</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Values */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Our Values</h2>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 rounded-xl bg-cyan-400/10 border border-cyan-400/20">
                  <div className="text-3xl mb-2">⚡</div>
                  <h3 className="font-semibold text-cyan-300 text-sm">Speed</h3>
                  <p className="text-gray-400 text-xs">Fast, efficient trading</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-emerald-400/10 border border-emerald-400/20">
                  <div className="text-3xl mb-2">🔒</div>
                  <h3 className="font-semibold text-emerald-300 text-sm">Security</h3>
                  <p className="text-gray-400 text-xs">Safe, secure platform</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-purple-400/10 border border-purple-400/20">
                  <div className="text-3xl mb-2">🌍</div>
                  <h3 className="font-semibold text-purple-300 text-sm">Accessibility</h3>
                  <p className="text-gray-400 text-xs">Open to everyone</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                  <div className="text-3xl mb-2">💡</div>
                  <h3 className="font-semibold text-yellow-300 text-sm">Innovation</h3>
                  <p className="text-gray-400 text-xs">Cutting-edge features</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Team */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Built on Solana</h2>
              <p className="text-gray-200 leading-relaxed">
                Dexsta is built on the Solana blockchain, leveraging its high-speed, low-cost infrastructure to provide the best possible trading experience. We're committed to the Solana ecosystem and its vision of decentralized finance for everyone.
              </p>
              <div className="flex items-center justify-center space-x-4 pt-4">
                <div className="text-4xl">⚡</div>
                <div className="text-center">
                  <p className="font-bold text-emerald-300">65,000+ TPS</p>
                  <p className="text-xs text-gray-400">Transaction Speed</p>
                </div>
                <div className="text-center">
                  <p className="font-bold text-emerald-300">$0.00025</p>
                  <p className="text-xs text-gray-400">Average Fee</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
