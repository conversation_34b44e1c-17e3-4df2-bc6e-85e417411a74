'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { useWallet } from '@/contexts/WalletContext'
import { 
  User, 
  Bell, 
  Shield, 
  Palette, 
  HelpCircle, 
  LogOut,
  Copy,
  ExternalLink,
  Settings as SettingsIcon
} from 'lucide-react'
import { staggerContainer, staggerItem } from '@/lib/animations'
import { cn, truncateAddress } from '@/lib/utils'
import { useRouter } from 'next/navigation'

interface SettingItem {
  id: string
  label: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  action: () => void
}

export default function SettingsPage() {
  const { connected, publicKey, disconnect } = useWallet()
  const router = useRouter()

  const walletAddress = publicKey?.toBase58()

  const copyAddress = () => {
    if (walletAddress) {
      navigator.clipboard.writeText(walletAddress)
      // TODO: Add toast notification
    }
  }

  const handleDisconnect = async () => {
    try {
      await disconnect()
      router.push('/')
    } catch (error) {
      console.error('Failed to disconnect wallet:', error)
    }
  }

  const settingsSections = [
    {
      title: 'Account',
      items: [
        {
          id: 'profile',
          label: 'Profile Settings',
          description: 'Manage your profile information',
          icon: User,
          action: () => router.push('/profile')
        },
        {
          id: 'notifications',
          label: 'Notifications',
          description: 'Configure notification preferences',
          icon: Bell,
          action: () => console.log('Notifications')
        }
      ]
    },
    {
      title: 'Security',
      items: [
        {
          id: 'security',
          label: 'Security & Privacy',
          description: 'Manage security settings',
          icon: Shield,
          action: () => console.log('Security')
        }
      ]
    },
    {
      title: 'Preferences',
      items: [
        {
          id: 'appearance',
          label: 'Appearance',
          description: 'Customize app appearance',
          icon: Palette,
          action: () => console.log('Appearance')
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          label: 'Help & Support',
          description: 'Get help and contact support',
          icon: HelpCircle,
          action: () => console.log('Help')
        }
      ]
    }
  ]

  return (
    <MobileLayout headerTitle="Settings" showBackButton>
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6"
      >
        {/* Wallet Info */}
        {connected && walletAddress && (
          <motion.div variants={staggerItem}>
            <Card variant="glass" padding="md">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
                    <SettingsIcon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white font-heading">Connected Wallet</h3>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-400 font-mono">
                        {truncateAddress(walletAddress)}
                      </span>
                      <button onClick={copyAddress} className="p-1 hover:bg-white/10 rounded">
                        <Copy className="w-4 h-4 text-gray-400" />
                      </button>
                      <button className="p-1 hover:bg-white/10 rounded">
                        <ExternalLink className="w-4 h-4 text-gray-400" />
                      </button>
                    </div>
                  </div>
                </div>
                
                <Button 
                  variant="glass" 
                  size="sm" 
                  onClick={handleDisconnect}
                  leftIcon={<LogOut className="w-4 h-4" />}
                  className="w-full text-red-400 hover:text-red-300"
                >
                  Disconnect Wallet
                </Button>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Settings Sections */}
        {settingsSections.map((section, sectionIndex) => (
          <motion.div 
            key={section.title}
            variants={staggerItem}
            className="space-y-3"
          >
            <h2 className="text-lg font-semibold text-white font-heading px-2">
              {section.title}
            </h2>
            
            <Card variant="glass" padding="none">
              {section.items.map((item, itemIndex) => {
                const Icon = item.icon
                return (
                  <motion.button
                    key={item.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: (sectionIndex * 0.1) + (itemIndex * 0.05) }}
                    onClick={item.action}
                    className={cn(
                      "w-full flex items-center space-x-4 p-4 text-left",
                      "transition-colors duration-200",
                      "hover:bg-white/5 active:bg-white/10",
                      itemIndex !== section.items.length - 1 && "border-b border-gray-600/20"
                    )}
                  >
                    <div className="w-10 h-10 rounded-full bg-gray-600/30 flex items-center justify-center">
                      <Icon className="w-5 h-5 text-gray-300" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-white font-medium">{item.label}</h3>
                      <p className="text-sm text-gray-400">{item.description}</p>
                    </div>
                    <ExternalLink className="w-4 h-4 text-gray-500" />
                  </motion.button>
                )
              })}
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </MobileLayout>
  )
}
