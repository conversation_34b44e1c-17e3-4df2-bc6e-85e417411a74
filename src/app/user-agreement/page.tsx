'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { staggerContainer, staggerItem } from '@/lib/animations'

export default function UserAgreementPage() {
  return (
    <MobileLayout headerTitle="User Agreement" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="text-6xl">📋</div>
          <h1 className="text-2xl font-bold text-white font-heading">
            User Agreement
          </h1>
          <div className="w-20 h-0.5 bg-gradient-to-r from-blue-400 to-purple-600 rounded-full mx-auto" />
          <p className="text-gray-300 text-sm">
            Last updated: December 2024
          </p>
        </motion.div>

        {/* Introduction */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-blue-400/30 bg-blue-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Introduction</h2>
              <p className="text-gray-200 leading-relaxed text-sm">
                Welcome to Dexsta. By using our platform, you agree to these terms and conditions. Please read them carefully before using our services.
              </p>
            </div>
          </Card>
        </motion.div>

        {/* Key Terms */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Key Terms</h2>
              <div className="space-y-4">
                <div className="p-4 rounded-xl bg-gray-800/30 border border-gray-600/20">
                  <h3 className="font-semibold text-blue-300 mb-2">1. Platform Use</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Dexsta is a decentralized token creation and trading platform. Users must be 18+ and comply with local laws.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-gray-800/30 border border-gray-600/20">
                  <h3 className="font-semibold text-blue-300 mb-2">2. Token Creation</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Users are responsible for their token content and compliance with applicable regulations. No illegal or harmful content allowed.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-gray-800/30 border border-gray-600/20">
                  <h3 className="font-semibold text-blue-300 mb-2">3. Trading Risks</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Cryptocurrency trading involves significant risk. Users trade at their own risk and should only invest what they can afford to lose.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-gray-800/30 border border-gray-600/20">
                  <h3 className="font-semibold text-blue-300 mb-2">4. Fees and Payments</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Platform fees are clearly disclosed. Creator fees are set by token creators and automatically collected.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Prohibited Activities */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-red-400/30 bg-red-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Prohibited Activities</h2>
              <div className="space-y-2 text-sm">
                <div className="flex items-start space-x-3">
                  <div className="text-red-400">❌</div>
                  <p className="text-gray-300">Creating tokens that violate intellectual property rights</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-red-400">❌</div>
                  <p className="text-gray-300">Market manipulation or fraudulent trading activities</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-red-400">❌</div>
                  <p className="text-gray-300">Using the platform for illegal activities</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-red-400">❌</div>
                  <p className="text-gray-300">Attempting to hack or exploit platform vulnerabilities</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Disclaimers */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-yellow-400/30 bg-yellow-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Important Disclaimers</h2>
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">⚠️</div>
                  <p className="text-gray-300">
                    Dexsta is provided "as is" without warranties of any kind
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">⚠️</div>
                  <p className="text-gray-300">
                    We are not responsible for token performance or trading losses
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">⚠️</div>
                  <p className="text-gray-300">
                    Users are responsible for their own tax obligations
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">⚠️</div>
                  <p className="text-gray-300">
                    Platform availability is not guaranteed and may be interrupted
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Contact */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Questions?</h2>
              <p className="text-gray-300 text-sm leading-relaxed">
                If you have questions about this User Agreement, please contact our support team. By continuing to use Dexsta, you acknowledge that you have read, understood, and agree to be bound by these terms.
              </p>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
