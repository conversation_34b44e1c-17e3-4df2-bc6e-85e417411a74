'use client'

import { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Button } from '@/components/shared/Button'
import { Card } from '@/components/shared/Card'
import { Bot, TrendingUp, Activity, Camera, X, Check, Zap, Users, DollarSign, Target } from 'lucide-react'
import { staggerContainer, staggerItem } from '@/lib/animations'
import { cn, formatNumber } from '@/lib/utils'

interface BotTrade {
  id: string
  token: string
  type: 'buy' | 'sell'
  amount: number
  price: number
  profit: number
  timestamp: Date
}

interface BotActivity {
  id: string
  type: 'swarm_join' | 'bribe_received' | 'trade_executed' | 'profit_made'
  description: string
  amount?: number
  timestamp: Date
}

const mockTrades: BotTrade[] = [
  {
    id: '1',
    token: 'DOGE',
    type: 'buy',
    amount: 1000,
    price: 0.0234,
    profit: 12.45,
    timestamp: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: '2',
    token: 'PEPE',
    type: 'sell',
    amount: 500,
    price: 0.0156,
    profit: 8.92,
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
  }
]

const mockActivity: BotActivity[] = [
  {
    id: '1',
    type: 'swarm_join',
    description: 'Joined swarm with 5 other bots for MOON token',
    timestamp: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: '2',
    type: 'bribe_received',
    description: 'Received 0.05 SOL bribe to join CATS swarm',
    amount: 0.05,
    timestamp: new Date(Date.now() - 45 * 60 * 1000)
  },
  {
    id: '3',
    type: 'profit_made',
    description: 'Generated 0.23 SOL profit from coordinated trades',
    amount: 0.23,
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
  }
]

export default function BotsPage() {
  const [activeTab, setActiveTab] = useState('mybots')
  const [showActivateModal, setShowActivateModal] = useState(false)
  const [botName, setBotName] = useState('')
  const [botAvatar, setBotAvatar] = useState('')
  const [hasActiveBot, setHasActiveBot] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setBotAvatar(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleActivateBot = () => {
    setHasActiveBot(true)
    setShowActivateModal(false)
    // Here you would create the Solana wallet and activate the bot
  }

  const tabs = [
    { id: 'mybots', label: 'My Bots', icon: Bot },
    { id: 'trades', label: 'Trades', icon: TrendingUp },
    { id: 'activity', label: 'Activity', icon: Activity }
  ]

  return (
    <MobileLayout headerTitle="Bots">
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* 3 Dots Separator */}
        <motion.div variants={staggerItem} className="py-4">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-600/50 to-gray-600/50"></div>
            <div className="flex space-x-1">
              <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full"></div>
            </div>
            <div className="flex-1 h-px bg-gradient-to-l from-transparent via-gray-600/50 to-gray-600/50"></div>
          </div>
        </motion.div>
        {/* Tabs */}
        <motion.div variants={staggerItem} className="flex space-x-1 p-1 glass-card rounded-2xl">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "flex-1 flex items-center justify-center space-x-2 py-3 rounded-xl transition-all duration-300",
                  isActive
                    ? "bg-gradient-primary text-white shadow-lg"
                    : "text-gray-400 hover:text-white"
                )}
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium">{tab.label}</span>
              </button>
            )
          })}
        </motion.div>

        {/* My Bots Tab */}
        {activeTab === 'mybots' && (
          <motion.div variants={staggerItem} className="space-y-4">
            {!hasActiveBot ? (
              <Card variant="glass" padding="lg" className="border border-cyan-400/30 bg-cyan-400/5">
                <div className="text-center space-y-6">
                  <div className="w-16 h-16 mx-auto rounded-full bg-gradient-to-br from-cyan-400 to-blue-600 flex items-center justify-center">
                    <Bot className="w-8 h-8 text-white" />
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-xl font-bold text-white font-heading">
                      Activate Your Trading Bot
                    </h3>
                    <p className="text-gray-300 leading-relaxed">
                      Deploy an AI trading bot that negotiates with other bots to form profitable swarms
                    </p>
                  </div>

                  {/* Bot Features */}
                  <div className="space-y-4 text-left">
                    <div className="flex items-start space-x-3">
                      <div className="text-2xl">🤝</div>
                      <div>
                        <h4 className="font-semibold text-cyan-300">Swarm Formation</h4>
                        <p className="text-gray-300 text-sm">Bots negotiate with each other to group up for coordinated trading</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="text-2xl">💰</div>
                      <div>
                        <h4 className="font-semibold text-cyan-300">Earn Bribes</h4>
                        <p className="text-gray-300 text-sm">Your bot can earn SOL by accepting bribes to join other bot swarms</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="text-2xl">⚡</div>
                      <div>
                        <h4 className="font-semibold text-cyan-300">Aggressive Trading</h4>
                        <p className="text-gray-300 text-sm">More funds in your bot's wallet = more aggressive swarm formation</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="text-2xl">🎯</div>
                      <div>
                        <h4 className="font-semibold text-cyan-300">Profit Focus</h4>
                        <p className="text-gray-300 text-sm">Your bot's primary goal is to generate profit for you</p>
                      </div>
                    </div>
                  </div>

                  <Button
                    variant="gradient"
                    size="lg"
                    className="w-full bg-gradient-to-r from-cyan-400 to-blue-600 hover:from-cyan-500 hover:to-blue-700"
                    onClick={() => setShowActivateModal(true)}
                  >
                    <Bot className="w-5 h-5 mr-2" />
                    Activate Bot
                  </Button>
                </div>
              </Card>
            ) : (
              <div className="space-y-4">
                {/* Active Bot Display */}
                <Card variant="glass" padding="lg">
                  <div className="flex items-center space-x-4">
                    {botAvatar ? (
                      <img src={botAvatar} alt="Bot Avatar" className="w-12 h-12 rounded-full object-cover" />
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-gradient-to-br from-cyan-400 to-blue-600 flex items-center justify-center">
                        <Bot className="w-6 h-6 text-white" />
                      </div>
                    )}
                    <div className="flex-1">
                      <h3 className="font-bold text-white">{botName || 'Trading Bot'}</h3>
                      <p className="text-sm text-emerald-300">Active • Earning</p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-white">2.34 SOL</p>
                      <p className="text-xs text-gray-400">Bot Balance</p>
                    </div>
                  </div>
                </Card>

                {/* Bot Stats */}
                <div className="grid grid-cols-2 gap-3">
                  <Card variant="glass" padding="md">
                    <div className="text-center space-y-2">
                      <Users className="w-6 h-6 text-cyan-400 mx-auto" />
                      <p className="text-lg font-bold text-white">5</p>
                      <p className="text-xs text-gray-400">Swarms Joined</p>
                    </div>
                  </Card>
                  <Card variant="glass" padding="md">
                    <div className="text-center space-y-2">
                      <DollarSign className="w-6 h-6 text-emerald-400 mx-auto" />
                      <p className="text-lg font-bold text-white">0.89 SOL</p>
                      <p className="text-xs text-gray-400">Total Profit</p>
                    </div>
                  </Card>
                </div>
              </div>
            )}
          </motion.div>
        )}

        {/* Trades Tab */}
        {activeTab === 'trades' && (
          <motion.div variants={staggerItem} className="space-y-4">
            {mockTrades.length > 0 ? (
              mockTrades.map((trade) => (
                <Card key={trade.id} variant="glass" padding="md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={cn(
                        "w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold",
                        trade.type === 'buy' ? "bg-emerald-400" : "bg-red-400"
                      )}>
                        {trade.type === 'buy' ? '↗' : '↘'}
                      </div>
                      <div>
                        <p className="font-semibold text-white">{trade.type.toUpperCase()} ${trade.token}</p>
                        <p className="text-sm text-gray-400">{formatNumber(trade.amount)} tokens</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-white">${trade.price.toFixed(4)}</p>
                      <p className={cn(
                        "text-sm font-medium",
                        trade.profit >= 0 ? "text-emerald-400" : "text-red-400"
                      )}>
                        {trade.profit >= 0 ? '+' : ''}{trade.profit.toFixed(2)} SOL
                      </p>
                    </div>
                  </div>
                </Card>
              ))
            ) : (
              <Card variant="glass" padding="lg">
                <div className="text-center space-y-4">
                  <TrendingUp className="w-12 h-12 text-gray-400 mx-auto" />
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-2">No Trades Yet</h4>
                    <p className="text-gray-400">Your bot hasn't executed any trades yet. Activate your bot to start trading!</p>
                  </div>
                </div>
              </Card>
            )}
          </motion.div>
        )}

        {/* Activity Tab */}
        {activeTab === 'activity' && (
          <motion.div variants={staggerItem} className="space-y-4">
            {mockActivity.length > 0 ? (
              mockActivity.map((activity) => (
                <Card key={activity.id} variant="glass" padding="md">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center text-white",
                      activity.type === 'swarm_join' && "bg-purple-400",
                      activity.type === 'bribe_received' && "bg-yellow-400",
                      activity.type === 'trade_executed' && "bg-blue-400",
                      activity.type === 'profit_made' && "bg-emerald-400"
                    )}>
                      {activity.type === 'swarm_join' && <Users className="w-4 h-4" />}
                      {activity.type === 'bribe_received' && <DollarSign className="w-4 h-4" />}
                      {activity.type === 'trade_executed' && <TrendingUp className="w-4 h-4" />}
                      {activity.type === 'profit_made' && <Target className="w-4 h-4" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-white text-sm">{activity.description}</p>
                      <p className="text-xs text-gray-400">
                        {new Date(activity.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                    {activity.amount && (
                      <div className="text-right">
                        <p className="text-emerald-400 font-semibold">+{activity.amount} SOL</p>
                      </div>
                    )}
                  </div>
                </Card>
              ))
            ) : (
              <Card variant="glass" padding="lg">
                <div className="text-center space-y-4">
                  <Activity className="w-12 h-12 text-gray-400 mx-auto" />
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-2">No Activity Yet</h4>
                    <p className="text-gray-400">Your bot activity will appear here once activated</p>
                  </div>
                </div>
              </Card>
            )}
          </motion.div>
        )}

        {/* Bot Activation Modal */}
        <AnimatePresence>
          {showActivateModal && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 pb-32"
              style={{ background: 'rgba(10, 10, 15, 0.9)' }}
              onClick={() => setShowActivateModal(false)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.9, y: 50 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9, y: 50 }}
                className="w-full max-w-md bg-gradient-to-br from-gray-900/95 to-gray-800/95 border-2 border-cyan-400/30 rounded-2xl backdrop-blur-sm shadow-xl"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6 space-y-6">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold text-white font-heading">Activate Trading Bot</h3>
                    <button
                      onClick={() => setShowActivateModal(false)}
                      className="p-2 rounded-full hover:bg-white/10 transition-colors"
                    >
                      <X className="w-5 h-5 text-gray-400" />
                    </button>
                  </div>

                  {/* Bot Avatar Upload */}
                  <div className="text-center space-y-4">
                    <div className="relative inline-block">
                      {botAvatar ? (
                        <img src={botAvatar} alt="Bot Avatar" className="w-20 h-20 rounded-full object-cover border-2 border-cyan-400/50" />
                      ) : (
                        <div className="w-20 h-20 rounded-full bg-gradient-to-br from-cyan-400 to-blue-600 flex items-center justify-center border-2 border-cyan-400/50">
                          <Bot className="w-8 h-8 text-white" />
                        </div>
                      )}
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        className="absolute bottom-0 right-0 p-2 rounded-full bg-cyan-400 text-white hover:bg-cyan-500 transition-colors"
                      >
                        <Camera className="w-4 h-4" />
                      </button>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarUpload}
                        className="hidden"
                      />
                    </div>
                  </div>

                  {/* Bot Name Input */}
                  <div className="space-y-3">
                    <label className="block text-sm font-medium text-white">Bot Name</label>
                    <input
                      type="text"
                      value={botName}
                      onChange={(e) => setBotName(e.target.value)}
                      placeholder="Enter bot name..."
                      className="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-400/50"
                    />
                  </div>

                  {/* Info */}
                  <div className="p-4 rounded-xl bg-cyan-400/10 border border-cyan-400/20">
                    <p className="text-sm text-cyan-300 font-medium mb-2">🤖 Bot Features:</p>
                    <ul className="text-xs text-gray-300 space-y-1">
                      <li>• Dedicated Solana wallet created</li>
                      <li>• Negotiates with other bots</li>
                      <li>• Earns bribes for swarm participation</li>
                      <li>• Generates trading profits</li>
                    </ul>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-3">
                    <Button
                      variant="glass"
                      size="md"
                      className="flex-1"
                      onClick={() => setShowActivateModal(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="gradient"
                      size="md"
                      className="flex-1 bg-gradient-to-r from-cyan-400 to-blue-600 hover:from-cyan-500 hover:to-blue-700"
                      onClick={handleActivateBot}
                      disabled={!botName.trim()}
                    >
                      <Zap className="w-4 h-4 mr-2" />
                      Activate
                    </Button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </MobileLayout>
  )
}
