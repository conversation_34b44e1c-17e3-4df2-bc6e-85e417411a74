'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft, Share2, Heart, Lock, Clock, Coins, Tag, Edit3, Trash2, Eye, ExternalLink, Star, Sparkles, Users, Link, ChevronDown, ChevronRight, Calendar, Shield, Info } from 'lucide-react'
import { triggerHaptic } from '@/utils/haptic'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { useWallet } from '@suiet/wallet-kit'
import { staggerContainer, staggerItem } from '@/lib/animations'

interface NFTAsset {
  type: 'SUI' | 'TOKEN'
  symbol: string
  amount: number
  value?: number // USD value
}

interface LinkedXFT {
  id: string
  name: string
  imageUrl: string
  isForSale: boolean
  price?: number
}

interface Label {
  id: string
  name: string
  type: string
  imageUrl: string
  totalXFTs: number
}

interface NFTData {
  id: string
  name: string
  description: string
  imageUrl: string
  owner: string
  creator: string
  supply: number
  minted: number
  isLocked: boolean
  lockUntil?: number
  storedAssets: NFTAsset[]
  forSale?: {
    price: number
    listedAt: number
  }
  attributes?: Array<{
    trait_type: string
    value: string
  }>
  createdAt: number
  // XFT-specific fields
  linkedLabel?: Label
  isWrapped: boolean
  wrappedFrom?: {
    originalId: string
    originalName: string
    wrappedAt: number
  }
  linkedXFTs?: LinkedXFT[]
  rarity?: string
  utilityFeatures: string[]
}

// Mock NFT data - replace with actual data fetching
const mockNFTData: Record<string, NFTData> = {
  '1001': {
    id: '1001',
    name: 'VIP Access Pass',
    description: 'Exclusive trading access for premium tokens. This NFT grants you access to private liquidity pools and exclusive trading opportunities on the Dexsta platform.',
    imageUrl: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?w=400&h=400&fit=crop&crop=center',
    owner: '0x123...abc',
    creator: '0x123...abc',
    supply: 1,
    minted: 1,
    isLocked: false,
    storedAssets: [
      { type: 'SUI', symbol: 'SUI', amount: 0.05, value: 0.85 },
    ],
    forSale: {
      price: 2.5,
      listedAt: Date.now() - 2 * 60 * 60 * 1000 // 2 hours ago
    },
    attributes: [
      { trait_type: 'Access Level', value: 'VIP' },
      { trait_type: 'Pool Access', value: 'Private' },
      { trait_type: 'Rarity', value: 'Legendary' }
    ],
    createdAt: Date.now() - 7 * 24 * 60 * 60 * 1000, // 7 days ago
    linkedLabel: {
      id: 'label_001',
      name: 'Dexsta Premium',
      type: 'Lead Label',
      imageUrl: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=100&h=100&fit=crop&crop=center',
      totalXFTs: 15
    },
    isWrapped: false,
    rarity: 'Legendary',
    utilityFeatures: ['Private Pool Access', 'Fee Discounts', 'Early Access'],
    linkedXFTs: [
      {
        id: '1003',
        name: 'Premium Trader',
        imageUrl: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=100&h=100&fit=crop&crop=center',
        isForSale: true,
        price: 1.8
      },
      {
        id: '1004',
        name: 'Elite Member',
        imageUrl: 'https://images.unsplash.com/photo-*************-d088224ddc74?w=100&h=100&fit=crop&crop=center',
        isForSale: false
      },
      {
        id: '1005',
        name: 'Gold Access',
        imageUrl: 'https://images.unsplash.com/photo-1614680376573-df3480f0c6ff?w=100&h=100&fit=crop&crop=center',
        isForSale: true,
        price: 0.9
      }
    ]
  },
  '1002': {
    id: '1002',
    name: 'Diamond Trader',
    description: 'Elite trader status NFT with special privileges and asset storage capabilities. This wrapped XFT was upgraded from a limited edition to enable asset storage.',
    imageUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop&crop=center',
    owner: '0x123...abc',
    creator: '0x456...def',
    supply: 1,
    minted: 1,
    isLocked: true,
    lockUntil: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days
    storedAssets: [
      { type: 'SUI', symbol: 'SUI', amount: 0.1, value: 1.70 },
      { type: 'TOKEN', symbol: 'TEST', amount: 1000, value: 50.00 }
    ],
    attributes: [
      { trait_type: 'Status', value: 'Diamond' },
      { trait_type: 'Trading Power', value: 'Elite' },
      { trait_type: 'Lock Period', value: '30 Days' }
    ],
    createdAt: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3 days ago
    linkedLabel: {
      id: 'label_002',
      name: 'Trading Elite',
      type: 'Profile Label',
      imageUrl: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=100&h=100&fit=crop&crop=center',
      totalXFTs: 8
    },
    isWrapped: true,
    wrappedFrom: {
      originalId: '2001',
      originalName: 'Diamond Trader (Limited Edition)',
      wrappedAt: Date.now() - 1 * 24 * 60 * 60 * 1000 // 1 day ago
    },
    rarity: 'Epic',
    utilityFeatures: ['Asset Storage', 'Time Locking', 'Premium Trading'],
    linkedXFTs: [
      {
        id: '1006',
        name: 'Silver Trader',
        imageUrl: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=100&h=100&fit=crop&crop=center',
        isForSale: false
      },
      {
        id: '1007',
        name: 'Bronze Access',
        imageUrl: 'https://images.unsplash.com/photo-*************-d088224ddc74?w=100&h=100&fit=crop&crop=center',
        isForSale: true,
        price: 0.5
      }
    ]
  }
}

export default function XFTDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { connected, account } = useWallet()
  const [isListing, setIsListing] = useState(false)
  const [listPrice, setListPrice] = useState('')
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'collection' | 'history'>('overview')
  const [openAccordion, setOpenAccordion] = useState<string | null>('basic-info')
  
  const nftId = params.id as string
  const nft = mockNFTData[nftId]
  
  if (!nft) {
    return (
      <MobileLayout headerTitle="NFT Not Found">
        <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
          <div className="text-6xl mb-4">🔍</div>
          <h2 className="text-xl font-bold text-white mb-2">NFT Not Found</h2>
          <p className="text-gray-400 mb-6">The NFT you're looking for doesn't exist.</p>
          <Button onClick={() => router.back()}>Go Back</Button>
        </div>
      </MobileLayout>
    )
  }

  const isOwner = connected && account?.address === nft.owner
  const totalAssetValue = nft.storedAssets.reduce((sum, asset) => sum + (asset.value || 0), 0)
  
  const formatTimeRemaining = (timestamp: number) => {
    const remaining = timestamp - Date.now()
    const days = Math.floor(remaining / (24 * 60 * 60 * 1000))
    const hours = Math.floor((remaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
    return `${days}d ${hours}h`
  }

  const handleListForSale = () => {
    setIsListing(true)
    // TODO: Implement listing logic
  }

  const handleWithdrawAssets = () => {
    // TODO: Implement asset withdrawal
    triggerHaptic('medium')
  }

  return (
    <MobileLayout headerTitle={nft.name}>
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* NFT Image & Status */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="none" className="overflow-hidden">
            <div className="relative">
              <img
                src={nft.imageUrl}
                alt={nft.name}
                className="w-full aspect-square object-cover"
              />

              {/* Status Badges */}
              <div className="absolute top-4 left-4 right-4 flex justify-between">
                {/* For Sale Badge */}
                {nft.forSale && (
                  <div className="bg-green-500/20 backdrop-blur-sm border border-green-500/30 rounded-xl px-3 py-2">
                    <div className="flex items-center space-x-2 text-green-400">
                      <Tag className="w-4 h-4" />
                      <span className="text-sm font-medium">{nft.forSale.price} SUI</span>
                    </div>
                  </div>
                )}

                {/* Lock Status */}
                {nft.isLocked && (
                  <div className="bg-yellow-500/20 backdrop-blur-sm border border-yellow-500/30 rounded-xl px-3 py-2">
                    <div className="flex items-center space-x-2 text-yellow-400">
                      <Lock className="w-4 h-4" />
                      <span className="text-sm font-medium">{formatTimeRemaining(nft.lockUntil!)}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Wrapped Badge */}
              {nft.isWrapped && (
                <div className="absolute bottom-4 right-4 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-xl px-3 py-2">
                  <div className="flex items-center space-x-2 text-purple-400">
                    <Sparkles className="w-4 h-4" />
                    <span className="text-sm font-medium">Wrapped</span>
                  </div>
                </div>
              )}

              {/* Rarity Badge */}
              {nft.rarity && (
                <div className="absolute bottom-4 left-4 bg-cyan-500/20 backdrop-blur-sm border border-cyan-500/30 rounded-xl px-3 py-2">
                  <div className="flex items-center space-x-2 text-cyan-400">
                    <Star className="w-4 h-4" />
                    <span className="text-sm font-medium">{nft.rarity}</span>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </motion.div>

        {/* XFT Title & Quick Info */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="bg-gradient-to-br from-purple-500/10 via-pink-500/5 to-cyan-500/10 border-purple-500/20">
            <div className="space-y-6">
              {/* Title & Description */}
              <div className="text-center">
                <h1 className="text-3xl font-bold text-white mb-3 font-heading bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                  ✨ {nft.name} ✨
                </h1>
                <p className="text-gray-300 leading-relaxed text-lg">{nft.description}</p>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 gap-6">
                <div className="p-5 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl text-center border border-purple-500/30 hover:scale-105 transition-transform">
                  <div className="text-3xl mb-2">🎯</div>
                  <p className="text-3xl font-bold text-white mb-1">{nft.supply === 1 ? '1-of-1' : `${nft.minted}/${nft.supply}`}</p>
                  <p className="text-purple-300 text-sm font-medium">Supply</p>
                </div>
                <div className="p-5 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-2xl text-center border border-cyan-500/30 hover:scale-105 transition-transform">
                  <div className="text-3xl mb-2">{nft.forSale ? '💰' : '🔒'}</div>
                  <p className="text-3xl font-bold text-white mb-1">{nft.forSale ? `${nft.forSale.price} SUI` : 'Not Listed'}</p>
                  <p className="text-cyan-300 text-sm font-medium">Price</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Tabbed Content */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="none" className="overflow-hidden">
            {/* Tab Navigation */}
            <div className="flex bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-cyan-500/10 border-b border-purple-500/20">
              {[
                { id: 'overview', label: 'Overview', icon: Info, emoji: '🎯' },
                { id: 'details', label: 'Details', icon: Eye, emoji: '🔍' },
                { id: 'collection', label: 'Collection', icon: Users, emoji: '🎨' },
                { id: 'history', label: 'History', icon: Clock, emoji: '📜' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex-1 flex flex-col items-center justify-center py-4 px-3 transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-b from-purple-500/30 to-pink-500/20 text-white border-b-3 border-purple-400 scale-105'
                      : 'text-gray-400 hover:text-gray-200 hover:bg-white/10 hover:scale-102'
                  }`}
                >
                  <div className="text-xl mb-1">{tab.emoji}</div>
                  <span className="text-xs font-bold uppercase tracking-wider">{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {activeTab === 'overview' && (
                <div className="space-y-8">
                  {/* Basic Info Accordion */}
                  <div className="border border-purple-500/30 rounded-2xl overflow-hidden bg-gradient-to-br from-purple-500/5 to-pink-500/5 hover:from-purple-500/10 hover:to-pink-500/10 transition-all duration-300">
                    <button
                      onClick={() => setOpenAccordion(openAccordion === 'basic-info' ? null : 'basic-info')}
                      className="w-full flex items-center justify-between p-6 hover:bg-white/5 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="p-3 bg-blue-500/20 rounded-xl">
                          <Shield className="w-6 h-6 text-blue-400" />
                        </div>
                        <div className="text-left">
                          <h3 className="text-xl font-bold text-white">🛡️ Basic Info</h3>
                          <p className="text-gray-400 text-sm">Who owns this beauty?</p>
                        </div>
                      </div>
                      <ChevronDown className={`w-6 h-6 text-purple-400 transition-transform duration-300 ${
                        openAccordion === 'basic-info' ? 'rotate-180' : ''
                      }`} />
                    </button>
                    {openAccordion === 'basic-info' && (
                      <div className="px-6 pb-8 pt-2">
                        <div className="grid grid-cols-2 gap-6">
                          <div className="p-4 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-xl border border-blue-500/20">
                            <p className="text-blue-300 mb-2 font-medium">👤 Owner</p>
                            <p className="text-white font-bold text-lg">{nft.owner.slice(0, 6)}...{nft.owner.slice(-4)}</p>
                          </div>
                          <div className="p-4 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-xl border border-green-500/20">
                            <p className="text-green-300 mb-2 font-medium">🎨 Creator</p>
                            <p className="text-white font-bold text-lg">{nft.creator.slice(0, 6)}...{nft.creator.slice(-4)}</p>
                          </div>
                          <div className="p-4 bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-xl border border-yellow-500/20">
                            <p className="text-yellow-300 mb-2 font-medium">⏰ Created</p>
                            <p className="text-white font-bold text-lg">{Math.floor((Date.now() - nft.createdAt) / (24 * 60 * 60 * 1000))}d ago</p>
                          </div>
                          <div className="p-4 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-500/20">
                            <p className="text-purple-300 mb-2 font-medium">✨ Type</p>
                            <p className="text-white font-bold text-lg">{nft.isWrapped ? '🎁 Wrapped XFT' : '🎯 Standard XFT'}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Lock Status Accordion */}
                  <div className={`border rounded-2xl overflow-hidden transition-all duration-300 ${
                    nft.isLocked
                      ? 'border-yellow-500/30 bg-gradient-to-br from-yellow-500/5 to-orange-500/5 hover:from-yellow-500/10 hover:to-orange-500/10'
                      : 'border-green-500/30 bg-gradient-to-br from-green-500/5 to-emerald-500/5 hover:from-green-500/10 hover:to-emerald-500/10'
                  }`}>
                    <button
                      onClick={() => setOpenAccordion(openAccordion === 'lock-status' ? null : 'lock-status')}
                      className="w-full flex items-center justify-between p-6 hover:bg-white/5 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`p-3 rounded-xl ${
                          nft.isLocked ? 'bg-yellow-500/20' : 'bg-green-500/20'
                        }`}>
                          <Lock className={`w-6 h-6 ${nft.isLocked ? 'text-yellow-400' : 'text-green-400'}`} />
                        </div>
                        <div className="text-left">
                          <h3 className="text-xl font-bold text-white">
                            {nft.isLocked ? '🔒 Locked Vault' : '🔓 Free Assets'}
                          </h3>
                          <p className="text-gray-400 text-sm">
                            {nft.isLocked ? 'Patience pays off!' : 'Ready to withdraw!'}
                          </p>
                        </div>
                      </div>
                      <ChevronDown className={`w-6 h-6 transition-transform duration-300 ${
                        nft.isLocked ? 'text-yellow-400' : 'text-green-400'
                      } ${openAccordion === 'lock-status' ? 'rotate-180' : ''}`} />
                    </button>
                    {openAccordion === 'lock-status' && (
                      <div className="px-6 pb-8 pt-2">
                        {nft.isLocked && nft.lockUntil ? (
                          <div className="space-y-6">
                            <div className="p-6 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-2xl">
                              <div className="flex items-center space-x-3 mb-4">
                                <div className="text-2xl">⏳</div>
                                <div>
                                  <h4 className="text-yellow-300 font-bold text-lg">Countdown Active!</h4>
                                  <p className="text-yellow-200 text-sm">Your assets are safely locked</p>
                                </div>
                              </div>
                              <div className="grid grid-cols-1 gap-4">
                                <div className="p-4 bg-yellow-500/10 rounded-xl">
                                  <p className="text-yellow-200 text-sm mb-1">🗓️ Unlock Date</p>
                                  <p className="text-white font-bold text-lg">
                                    {new Date(nft.lockUntil).toLocaleDateString()} at {new Date(nft.lockUntil).toLocaleTimeString()}
                                  </p>
                                </div>
                                <div className="p-4 bg-orange-500/10 rounded-xl">
                                  <p className="text-orange-200 text-sm mb-1">⏰ Time Remaining</p>
                                  <p className="text-white font-bold text-xl">
                                    {formatTimeRemaining(nft.lockUntil)}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                              <div className="flex items-start space-x-3">
                                <div className="text-xl">💡</div>
                                <div>
                                  <h5 className="text-blue-300 font-medium mb-1">Why Lock Assets?</h5>
                                  <p className="text-gray-300 text-sm">
                                    Locking prevents emotional selling and helps you stick to your investment strategy.
                                    Diamond hands mode activated! 💎🙌
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-6">
                            <div className="p-6 bg-gradient-to-br from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-2xl">
                              <div className="flex items-center space-x-3 mb-4">
                                <div className="text-2xl">🎉</div>
                                <div>
                                  <h4 className="text-green-300 font-bold text-lg">Assets Unlocked!</h4>
                                  <p className="text-green-200 text-sm">Ready for action whenever you are</p>
                                </div>
                              </div>
                              <div className="p-4 bg-green-500/10 rounded-xl">
                                <p className="text-green-200 text-sm mb-1">✅ Status</p>
                                <p className="text-white font-bold text-lg">
                                  Free to withdraw anytime! 🚀
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Utility Features Accordion */}
                  {nft.utilityFeatures && nft.utilityFeatures.length > 0 && (
                    <div className="border border-cyan-500/30 rounded-2xl overflow-hidden bg-gradient-to-br from-cyan-500/5 to-blue-500/5 hover:from-cyan-500/10 hover:to-blue-500/10 transition-all duration-300">
                      <button
                        onClick={() => setOpenAccordion(openAccordion === 'utility' ? null : 'utility')}
                        className="w-full flex items-center justify-between p-6 hover:bg-white/5 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="p-3 bg-cyan-500/20 rounded-xl">
                            <Star className="w-6 h-6 text-cyan-400" />
                          </div>
                          <div className="text-left">
                            <h3 className="text-xl font-bold text-white">⚡ Super Powers</h3>
                            <p className="text-gray-400 text-sm">What makes this XFT special?</p>
                          </div>
                        </div>
                        <ChevronDown className={`w-6 h-6 text-cyan-400 transition-transform duration-300 ${
                          openAccordion === 'utility' ? 'rotate-180' : ''
                        }`} />
                      </button>
                      {openAccordion === 'utility' && (
                        <div className="px-6 pb-8 pt-2">
                          <div className="grid grid-cols-1 gap-4">
                            {nft.utilityFeatures.map((feature, index) => (
                              <div key={index} className="flex items-center space-x-4 p-5 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-xl border border-cyan-500/30 hover:scale-102 transition-transform">
                                <div className="text-2xl">
                                  {index === 0 ? '🎯' : index === 1 ? '💎' : index === 2 ? '🚀' : '✨'}
                                </div>
                                <div>
                                  <span className="text-white font-bold text-lg">{feature}</span>
                                  <div className="w-full h-1 bg-cyan-500/30 rounded-full mt-2">
                                    <div className="h-full bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full" style={{width: `${85 + index * 5}%`}}></div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                          <div className="mt-6 p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl">
                            <div className="flex items-center space-x-3">
                              <div className="text-xl">🎮</div>
                              <p className="text-purple-300 text-sm">
                                <strong>Pro Tip:</strong> These utilities unlock exclusive features and benefits in the Dexsta ecosystem!
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'details' && (
                <div className="space-y-4">
                  {/* Stored Assets */}
                  {nft.storedAssets.length > 0 && (
                    <div className="border border-gray-700/30 rounded-xl overflow-hidden">
                      <button
                        onClick={() => setOpenAccordion(openAccordion === 'assets' ? null : 'assets')}
                        className="w-full flex items-center justify-between p-4 hover:bg-white/5 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <Coins className="w-5 h-5 text-green-400" />
                          <h3 className="text-lg font-semibold text-white">Stored Assets</h3>
                          <span className="text-green-400 font-bold">${totalAssetValue.toFixed(2)}</span>
                        </div>
                        <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${
                          openAccordion === 'assets' ? 'rotate-180' : ''
                        }`} />
                      </button>
                      {openAccordion === 'assets' && (
                        <div className="p-4 pt-0 border-t border-gray-700/30">
                          <div className="space-y-3">
                            {nft.storedAssets.map((asset, index) => (
                              <div key={index} className="flex items-center justify-between p-4 bg-green-500/10 rounded-xl">
                                <div className="flex items-center space-x-3">
                                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                                    asset.type === 'SUI' ? 'bg-blue-500/20 text-blue-400' : 'bg-purple-500/20 text-purple-400'
                                  }`}>
                                    <Coins className="w-5 h-5" />
                                  </div>
                                  <div>
                                    <p className="text-white font-semibold">{asset.amount} {asset.symbol}</p>
                                    {asset.value && <p className="text-gray-400 text-sm">${asset.value.toFixed(2)} USD</p>}
                                  </div>
                                </div>
                              </div>
                            ))}
                            {isOwner && !nft.isLocked && (
                              <Button
                                variant="outline"
                                size="lg"
                                onClick={handleWithdrawAssets}
                                className="w-full mt-3"
                              >
                                <Coins className="w-5 h-5 mr-2" />
                                Withdraw Assets
                              </Button>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Wrapped Information */}
                  {nft.isWrapped && nft.wrappedFrom && (
                    <div className="border border-gray-700/30 rounded-xl overflow-hidden">
                      <button
                        onClick={() => setOpenAccordion(openAccordion === 'wrapped' ? null : 'wrapped')}
                        className="w-full flex items-center justify-between p-4 hover:bg-white/5 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <Sparkles className="w-5 h-5 text-purple-400" />
                          <h3 className="text-lg font-semibold text-white">Wrapped Information</h3>
                        </div>
                        <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${
                          openAccordion === 'wrapped' ? 'rotate-180' : ''
                        }`} />
                      </button>
                      {openAccordion === 'wrapped' && (
                        <div className="p-4 pt-0 border-t border-gray-700/30">
                          <div className="space-y-3">
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-gray-400">Original XFT:</span>
                                <span className="text-white font-medium">{nft.wrappedFrom.originalName}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-400">Original ID:</span>
                                <span className="text-white font-medium">{nft.wrappedFrom.originalId}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-400">Wrapped:</span>
                                <span className="text-white font-medium">
                                  {Math.floor((Date.now() - nft.wrappedFrom.wrappedAt) / (24 * 60 * 60 * 1000))}d ago
                                </span>
                              </div>
                            </div>
                            <div className="p-3 bg-purple-500/20 rounded-lg">
                              <p className="text-purple-400 text-sm">
                                ✨ This XFT was upgraded from a limited edition to a 1-of-1 with asset storage capabilities
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Attributes */}
                  {nft.attributes && nft.attributes.length > 0 && (
                    <div className="border border-gray-700/30 rounded-xl overflow-hidden">
                      <button
                        onClick={() => setOpenAccordion(openAccordion === 'attributes' ? null : 'attributes')}
                        className="w-full flex items-center justify-between p-4 hover:bg-white/5 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <Tag className="w-5 h-5 text-orange-400" />
                          <h3 className="text-lg font-semibold text-white">Attributes</h3>
                        </div>
                        <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${
                          openAccordion === 'attributes' ? 'rotate-180' : ''
                        }`} />
                      </button>
                      {openAccordion === 'attributes' && (
                        <div className="p-4 pt-0 border-t border-gray-700/30">
                          <div className="grid grid-cols-2 gap-3">
                            {nft.attributes.map((attr, index) => (
                              <div key={index} className="p-4 bg-gray-800/30 rounded-xl text-center border border-gray-700/50">
                                <p className="text-gray-400 text-xs uppercase tracking-wide mb-1">{attr.trait_type}</p>
                                <p className="text-white font-semibold">{attr.value}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'collection' && (
                <div className="space-y-4">
                  {/* Linked Label */}
                  {nft.linkedLabel && (
                    <div className="border border-gray-700/30 rounded-xl overflow-hidden">
                      <button
                        onClick={() => setOpenAccordion(openAccordion === 'label' ? null : 'label')}
                        className="w-full flex items-center justify-between p-4 hover:bg-white/5 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <Link className="w-5 h-5 text-blue-400" />
                          <h3 className="text-lg font-semibold text-white">Linked Label</h3>
                        </div>
                        <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${
                          openAccordion === 'label' ? 'rotate-180' : ''
                        }`} />
                      </button>
                      {openAccordion === 'label' && (
                        <div className="p-4 pt-0 border-t border-gray-700/30">
                          <div className="flex items-center space-x-4 p-4 bg-blue-500/10 rounded-xl">
                            <img
                              src={nft.linkedLabel.imageUrl}
                              alt={nft.linkedLabel.name}
                              className="w-16 h-16 rounded-xl object-cover border border-blue-500/30"
                            />
                            <div className="flex-1">
                              <h4 className="text-white font-semibold">{nft.linkedLabel.name}</h4>
                              <p className="text-blue-400 text-sm">{nft.linkedLabel.type}</p>
                              <p className="text-gray-400 text-sm">{nft.linkedLabel.totalXFTs} XFTs in collection</p>
                            </div>
                            <Button variant="ghost" size="sm">
                              <Eye className="w-4 h-4 mr-2" />
                              View Label
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Other XFTs in Collection */}
                  {nft.linkedXFTs && nft.linkedXFTs.length > 0 && (
                    <div className="border border-gray-700/30 rounded-xl overflow-hidden">
                      <button
                        onClick={() => setOpenAccordion(openAccordion === 'collection-items' ? null : 'collection-items')}
                        className="w-full flex items-center justify-between p-4 hover:bg-white/5 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <Users className="w-5 h-5 text-indigo-400" />
                          <h3 className="text-lg font-semibold text-white">Other XFTs in Collection</h3>
                          <span className="text-indigo-400 text-sm">{nft.linkedXFTs.length} items</span>
                        </div>
                        <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${
                          openAccordion === 'collection-items' ? 'rotate-180' : ''
                        }`} />
                      </button>
                      {openAccordion === 'collection-items' && (
                        <div className="p-4 pt-0 border-t border-gray-700/30">
                          <div className="space-y-3">
                            {nft.linkedXFTs.map((linkedXFT) => (
                              <div key={linkedXFT.id} className="flex items-center space-x-4 p-3 bg-indigo-500/10 rounded-xl hover:bg-indigo-500/20 transition-colors cursor-pointer">
                                <img
                                  src={linkedXFT.imageUrl}
                                  alt={linkedXFT.name}
                                  className="w-12 h-12 rounded-lg object-cover border border-indigo-500/30"
                                />
                                <div className="flex-1">
                                  <h4 className="text-white font-medium">{linkedXFT.name}</h4>
                                  <div className="flex items-center space-x-2">
                                    {linkedXFT.isForSale ? (
                                      <span className="text-green-400 text-sm">For Sale: {linkedXFT.price} SUI</span>
                                    ) : (
                                      <span className="text-gray-400 text-sm">Not for sale</span>
                                    )}
                                  </div>
                                </div>
                                <Button variant="ghost" size="sm">
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </div>
                            ))}
                            <Button variant="outline" size="sm" className="w-full mt-3">
                              <Link className="w-4 h-4 mr-2" />
                              View Full Collection
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'history' && (
                <div className="space-y-4">
                  <div className="text-center py-8">
                    <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-white mb-2">Transaction History</h3>
                    <p className="text-gray-400 text-sm">Transaction history will be displayed here</p>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </motion.div>



        {/* Buy/Sale Section for Non-Owners */}
        {!isOwner && nft.forSale && (
          <motion.div variants={staggerItem}>
            <Card variant="glass" padding="lg" className="border border-green-500/30 bg-gradient-to-br from-green-500/10 via-emerald-500/5 to-cyan-500/10 overflow-hidden relative">
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-green-400/10 to-emerald-400/10 animate-pulse"></div>

              <div className="relative space-y-6">
                <div className="text-center">
                  <div className="text-4xl mb-3">💰</div>
                  <p className="text-green-300 mb-2 font-medium">🔥 Hot Deal Alert! 🔥</p>
                  <p className="text-5xl font-black text-white mb-2 bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                    {nft.forSale.price} SUI
                  </p>
                  <p className="text-green-300 text-sm">
                    ⏰ Listed {Math.floor((Date.now() - nft.forSale.listedAt) / (60 * 60 * 1000))}h ago
                  </p>
                </div>

                <Button
                  variant="primary"
                  size="lg"
                  className="w-full py-6 text-xl font-bold bg-gradient-to-r from-green-500 via-emerald-500 to-green-600 hover:from-green-600 hover:via-emerald-600 hover:to-green-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-green-500/25"
                  disabled={!connected}
                >
                  {connected ? (
                    <>
                      <div className="text-2xl mr-3">🚀</div>
                      Buy Now for {nft.forSale.price} SUI
                    </>
                  ) : (
                    <>
                      <div className="text-2xl mr-3">🔗</div>
                      Connect Wallet to Buy
                    </>
                  )}
                </Button>

                <div className="grid grid-cols-2 gap-4">
                  <Button variant="ghost" size="lg" className="py-4 border border-pink-500/30 hover:bg-pink-500/10 hover:border-pink-500/50 transition-all">
                    <Heart className="w-5 h-5 mr-2" />
                    💖 Wishlist
                  </Button>
                  <Button variant="ghost" size="lg" className="py-4 border border-blue-500/30 hover:bg-blue-500/10 hover:border-blue-500/50 transition-all">
                    <Share2 className="w-5 h-5 mr-2" />
                    📤 Share
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Owner Actions */}
        {isOwner && (
          <motion.div variants={staggerItem}>
            <Card variant="glass" padding="lg" className="border border-blue-500/20">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white font-heading">Owner Actions</h3>

                {/* Primary Actions */}
                <div className="space-y-3">
                  {!nft.forSale ? (
                    <Button
                      variant="primary"
                      size="lg"
                      onClick={handleListForSale}
                      className="w-full"
                    >
                      <Tag className="w-5 h-5 mr-2" />
                      List for Sale
                    </Button>
                  ) : (
                    <div className="space-y-2">
                      <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-xl text-center">
                        <p className="text-green-400 font-semibold">Listed for {nft.forSale.price} SUI</p>
                        <p className="text-gray-400 text-sm">
                          {Math.floor((Date.now() - nft.forSale.listedAt) / (60 * 60 * 1000))}h ago
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="lg"
                        className="w-full"
                      >
                        <Eye className="w-5 h-5 mr-2" />
                        Remove from Sale
                      </Button>
                    </div>
                  )}

                  {/* Wrap Option for Non-Wrapped XFTs */}
                  {!nft.isWrapped && nft.supply > 1 && (
                    <Button
                      variant="outline"
                      size="lg"
                      className="w-full border-purple-500/50 text-purple-400 hover:bg-purple-500/10"
                    >
                      <Sparkles className="w-5 h-5 mr-2" />
                      Wrap to 1-of-1 (Enable Asset Storage)
                    </Button>
                  )}
                </div>

                {/* Secondary Actions */}
                <div className="grid grid-cols-2 gap-3">
                  <Button variant="ghost" size="sm">
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Explorer
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Not for Sale Message */}
        {!isOwner && !nft.forSale && (
          <motion.div variants={staggerItem}>
            <Card variant="glass" padding="lg" className="border border-purple-500/30 bg-gradient-to-br from-purple-500/10 via-pink-500/5 to-indigo-500/10">
              <div className="text-center space-y-6">
                <div className="text-6xl">💎</div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-2 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                    Diamond Hands! 💎🙌
                  </h3>
                  <p className="text-gray-300 text-lg">This rare XFT is being HODLed tight!</p>
                  <p className="text-gray-400 text-sm mt-2">The owner believes in long-term value 📈</p>
                </div>

                <div className="p-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl border border-purple-500/30">
                  <p className="text-purple-300 text-sm">
                    <strong>💡 Pro Tip:</strong> Add to wishlist and we'll notify you if it goes on sale!
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Button variant="ghost" size="lg" className="py-4 border border-pink-500/30 hover:bg-pink-500/10 hover:border-pink-500/50 transition-all">
                    <Heart className="w-5 h-5 mr-2" />
                    💖 Wishlist
                  </Button>
                  <Button variant="ghost" size="lg" className="py-4 border border-blue-500/30 hover:bg-blue-500/10 hover:border-blue-500/50 transition-all">
                    <Share2 className="w-5 h-5 mr-2" />
                    📤 Share
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

      </motion.div>
    </MobileLayout>
  )
}
