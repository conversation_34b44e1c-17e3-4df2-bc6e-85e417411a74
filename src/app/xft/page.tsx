'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Plus, Image as ImageIcon, Eye, Tag, Clock, Zap, Info, Lock, TrendingUp, Calendar, Coins, Shield } from 'lucide-react'
import { triggerHaptic } from '@/utils/haptic'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { useContracts } from '@/hooks/useContracts'
import { useWallet } from '@suiet/wallet-kit'
import { staggerContainer, staggerItem } from '@/lib/animations'
import { XFTInfoSection } from '@/components/nft/XFTInfoSection'
import { XFTCreateSection } from '@/components/nft/XFTCreateSection'

interface NFTFormData {
  name: string
  description: string
  imageUrl: string
  supply: string
  image?: File
}

// Platform-set mint fee
const PLATFORM_MINT_FEE = 0.01 // 0.01 SUI

interface NFT {
  id: string
  name: string
  description: string
  imageUrl: string
  owner: string
  isLocked: boolean
  lockUntil?: number
  storedAssets?: {
    sui: number
    tokens: Array<{ symbol: string; amount: number }>
  }
  forSale?: {
    price: number
    listedAt: number
  }
}

// Mock NFT data - replace with actual data fetching
const mockNFTs: NFT[] = [
  {
    id: '1001',
    name: 'VIP Access Pass',
    description: 'Exclusive trading access for premium tokens',
    imageUrl: 'https://via.placeholder.com/300x300/6366f1/ffffff?text=VIP',
    owner: '0x123...abc',
    isLocked: false,
    storedAssets: { sui: 0.05, tokens: [] }
  },
  {
    id: '1002', 
    name: 'Diamond Trader',
    description: 'Elite trader status NFT with special privileges',
    imageUrl: 'https://via.placeholder.com/300x300/8b5cf6/ffffff?text=💎',
    owner: '0x123...abc',
    isLocked: true,
    lockUntil: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days
    storedAssets: { sui: 0.1, tokens: [{ symbol: 'TEST', amount: 1000 }] }
  }
]

const mockForSaleNFTs: NFT[] = [
  {
    id: '2001',
    name: 'Crypto Punk #1337',
    description: 'Rare crypto punk with unique traits',
    imageUrl: 'https://via.placeholder.com/300x300/f59e0b/ffffff?text=🤖',
    owner: '0x456...def',
    isLocked: false,
    forSale: { price: 0.5, listedAt: Date.now() - 2 * 60 * 60 * 1000 }
  },
  {
    id: '2002',
    name: 'Moon Walker',
    description: 'Astronaut NFT for space enthusiasts',
    imageUrl: 'https://via.placeholder.com/300x300/10b981/ffffff?text=🚀',
    owner: '0x789...ghi',
    isLocked: false,
    forSale: { price: 0.25, listedAt: Date.now() - 5 * 60 * 60 * 1000 }
  }
]

export default function NFTPage() {
  const router = useRouter()
  const { connected, account } = useWallet()
  const { mintOneOfOneNFT, loading, error } = useContracts()

  const [activeTab, setActiveTab] = useState<'info' | 'create' | 'my-nfts' | 'for-sale'>('info')
  const [isCreating, setIsCreating] = useState(false)
  
  const [formData, setFormData] = useState<NFTFormData>({
    name: '',
    description: '',
    imageUrl: '',
    supply: '1'
  })

  const handleInputChange = (field: keyof NFTFormData, value: string | File) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleCreateNFT = async () => {
    if (!connected || !account) {
      alert('Please connect your wallet first')
      return
    }

    setIsCreating(true)
    try {
      await mintOneOfOneNFT({
        name: formData.name,
        description: formData.description,
        imageUrl: formData.imageUrl,
        suiAmount: (PLATFORM_MINT_FEE * **********).toString() // Convert to MIST
      })

      // Reset form
      setFormData({
        name: '',
        description: '',
        imageUrl: '',
        supply: '1'
      })
      
      // Switch to My NFTs tab
      setActiveTab('my-nfts')
      
    } catch (error) {
      console.error('NFT creation failed:', error)
    } finally {
      setIsCreating(false)
    }
  }

  const isFormValid = formData.name && formData.description && formData.imageUrl && formData.supply && parseInt(formData.supply) > 0
  const isOneOfOne = parseInt(formData.supply) === 1

  const formatTimeRemaining = (timestamp: number) => {
    const remaining = timestamp - Date.now()
    const days = Math.floor(remaining / (24 * 60 * 60 * 1000))
    const hours = Math.floor((remaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
    return `${days}d ${hours}h`
  }

  return (
    <MobileLayout headerTitle="NFTs">
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* 3 Dots Separator */}
        <motion.div variants={staggerItem} className="py-4">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-600/50 to-gray-600/50"></div>
            <div className="flex space-x-1">
              <div className="w-1.5 h-1.5 bg-primary-purple rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-primary-purple rounded-full"></div>
              <div className="w-1.5 h-1.5 bg-primary-purple rounded-full"></div>
            </div>
            <div className="flex-1 h-px bg-gradient-to-l from-transparent via-gray-600/50 to-gray-600/50"></div>
          </div>
        </motion.div>
          
        {/* Tab Navigation */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="sm">
            <div className="grid grid-cols-4 gap-1">
              <Button
                variant={activeTab === 'info' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => {
                  triggerHaptic('light')
                  setActiveTab('info')
                }}
                className="flex flex-col items-center py-3 px-2 text-xs"
              >
                <Info className="w-4 h-4 mb-1" />
                Info
              </Button>
              <Button
                variant={activeTab === 'create' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => {
                  triggerHaptic('light')
                  setActiveTab('create')
                }}
                className="flex flex-col items-center py-3 px-2 text-xs"
              >
                <Plus className="w-4 h-4 mb-1" />
                Create
              </Button>
              <Button
                variant={activeTab === 'my-nfts' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => {
                  triggerHaptic('light')
                  setActiveTab('my-nfts')
                }}
                className="flex flex-col items-center py-3 px-2 text-xs"
              >
                <Eye className="w-4 h-4 mb-1" />
                My NFTs
              </Button>
              <Button
                variant={activeTab === 'for-sale' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => {
                  triggerHaptic('light')
                  setActiveTab('for-sale')
                }}
                className="flex flex-col items-center py-3 px-2 text-xs"
              >
                <Tag className="w-4 h-4 mb-1" />
                For Sale
              </Button>
            </div>
          </Card>
        </motion.div>

        {/* Tab Content */}
        <motion.div variants={staggerItem}>
          {/* Info Tab */}
          {activeTab === 'info' && (
            <XFTInfoSection onCreateClick={() => setActiveTab('create')} />
          )}



          {/* Create Tab */}
          {activeTab === 'create' && (
            <XFTCreateSection />
          )}

          {/* My NFTs Tab */}
          {activeTab === 'my-nfts' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2 font-heading">My NFTs</h2>
                <p className="text-gray-400">Your collected and created NFTs</p>
              </div>

              {mockNFTs.length === 0 ? (
                <div className="text-center py-12">
                  <ImageIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-400 mb-2">No NFTs Yet</h3>
                  <p className="text-gray-500 mb-4">Create your first NFT to get started</p>
                  <button
                    onClick={() => setActiveTab('create')}
                    className="px-6 py-2 bg-primary-purple text-white rounded-xl hover:bg-primary-purple/80 transition-colors"
                  >
                    Create NFT
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-4">
                  {mockNFTs.map((nft) => (
                    <Card
                      key={nft.id}
                      variant="glass"
                      padding="md"
                      className="cursor-pointer hover:border-primary-purple/30 transition-all"
                      onClick={() => {
                        triggerHaptic('light')
                        router.push(`/nft/${nft.id}`)
                      }}
                    >
                      <div className="flex space-x-4">
                        <img
                          src={nft.imageUrl}
                          alt={nft.name}
                          className="w-20 h-20 rounded-xl object-cover"
                        />
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <h3 className="font-semibold text-white">{nft.name}</h3>
                            {nft.isLocked && (
                              <div className="flex items-center space-x-1 text-yellow-400">
                                <Clock className="w-4 h-4" />
                                <span className="text-xs">{formatTimeRemaining(nft.lockUntil!)}</span>
                              </div>
                            )}
                          </div>
                          <p className="text-gray-400 text-sm mb-3 line-clamp-2">{nft.description}</p>

                          {nft.storedAssets && (
                            <div className="space-y-1">
                              <p className="text-xs text-gray-500">Stored Assets:</p>
                              <div className="flex items-center space-x-4 text-xs">
                                {nft.storedAssets.sui > 0 && (
                                  <span className="text-blue-400">{nft.storedAssets.sui} SUI</span>
                                )}
                                {nft.storedAssets.tokens.map((token, idx) => (
                                  <span key={idx} className="text-green-400">
                                    {token.amount} {token.symbol}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}

                          <div className="flex items-center justify-between mt-3">
                            <span className="text-xs text-gray-500">Click to view details</span>
                            <Eye className="w-4 h-4 text-gray-400" />
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* For Sale Tab */}
          {activeTab === 'for-sale' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-white mb-2 font-heading">NFTs For Sale</h2>
                <p className="text-gray-400">Browse and purchase available NFTs</p>
              </div>

              {mockForSaleNFTs.length === 0 ? (
                <div className="text-center py-12">
                  <Tag className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-400 mb-2">No NFTs For Sale</h3>
                  <p className="text-gray-500">Check back later for new listings</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-4">
                  {mockForSaleNFTs.map((nft) => (
                    <Card
                      key={nft.id}
                      variant="glass"
                      padding="md"
                      className="cursor-pointer hover:border-primary-purple/30 transition-all"
                      onClick={() => {
                        triggerHaptic('light')
                        router.push(`/nft/${nft.id}`)
                      }}
                    >
                      <div className="flex space-x-4">
                        <img
                          src={nft.imageUrl}
                          alt={nft.name}
                          className="w-20 h-20 rounded-xl object-cover"
                        />
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <h3 className="font-semibold text-white">{nft.name}</h3>
                            <div className="text-right">
                              <p className="text-lg font-bold text-primary-purple">
                                {nft.forSale!.price} SUI
                              </p>
                              <p className="text-xs text-gray-500">
                                Listed {Math.floor((Date.now() - nft.forSale!.listedAt) / (60 * 60 * 1000))}h ago
                              </p>
                            </div>
                          </div>
                          <p className="text-gray-400 text-sm mb-3 line-clamp-2">{nft.description}</p>
                          <p className="text-xs text-gray-500 mb-3">
                            Owner: {nft.owner.slice(0, 6)}...{nft.owner.slice(-4)}
                          </p>

                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">Click to view & buy</span>
                            <div className="flex items-center space-x-2">
                              <Zap className="w-4 h-4 text-primary-purple" />
                              <Eye className="w-4 h-4 text-gray-400" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
