'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { staggerContainer, staggerItem } from '@/lib/animations'

export default function PrivateLiquidityPage() {
  return (
    <MobileLayout headerTitle="Private Liquidity Pools" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="text-6xl">🔒</div>
          <h1 className="text-2xl font-bold text-white font-heading">
            Private Liquidity Pools
          </h1>
          <div className="w-20 h-0.5 bg-gradient-to-r from-purple-400 to-pink-600 rounded-full mx-auto" />
          <p className="text-gray-300">
            Exclusive trading pools with restricted access
          </p>
        </motion.div>

        {/* What are Private Pools */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-purple-400/30 bg-purple-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">What are NFT-Gated Pools?</h2>
              <p className="text-gray-200 leading-relaxed">
                Private liquidity pools are exclusive trading environments where access is controlled by NFT ownership. Only holders of specific NFT collections can access and trade in these premium pools.
              </p>
            </div>
          </Card>
        </motion.div>

        {/* NFT Requirements */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">NFT Access Requirements</h2>
              <div className="space-y-4">
                <div className="p-4 rounded-xl bg-purple-400/10 border border-purple-400/20">
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl">🖼️</div>
                    <div>
                      <h3 className="font-semibold text-purple-300 mb-2">Specific NFT Collections</h3>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        Pool creators specify which NFT collections grant access. You must hold at least one NFT from the required collection to trade.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-4 rounded-xl bg-pink-400/10 border border-pink-400/20">
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl">🔐</div>
                    <div>
                      <h3 className="font-semibold text-pink-300 mb-2">Automatic Verification</h3>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        Your wallet is automatically checked for the required NFTs when you attempt to access the pool.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="p-4 rounded-xl bg-indigo-400/10 border border-indigo-400/20">
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl">⚡</div>
                    <div>
                      <h3 className="font-semibold text-indigo-300 mb-2">Real-Time Access</h3>
                      <p className="text-gray-300 text-sm leading-relaxed">
                        Access is granted instantly upon NFT verification. If you sell your NFT, you lose pool access.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Benefits for NFT Holders */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Benefits for NFT Holders</h2>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">👑</div>
                  <div>
                    <h3 className="font-semibold text-pink-300">Exclusive Trading Rights</h3>
                    <p className="text-gray-300 text-sm">Only NFT holders can access these premium trading pools</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">💎</div>
                  <div>
                    <h3 className="font-semibold text-pink-300">Enhanced NFT Utility</h3>
                    <p className="text-gray-300 text-sm">Your NFTs gain additional utility beyond just collectible value</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">🎯</div>
                  <div>
                    <h3 className="font-semibold text-pink-300">Community Trading</h3>
                    <p className="text-gray-300 text-sm">Trade with fellow NFT holders who share similar interests</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-2xl">🔒</div>
                  <div>
                    <h3 className="font-semibold text-pink-300">Reduced Competition</h3>
                    <p className="text-gray-300 text-sm">Smaller pool of traders may lead to better pricing opportunities</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* How to Create NFT-Gated Pools */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Creating NFT-Gated Pools</h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-purple-300 mb-1">Select NFT Collection</h3>
                    <p className="text-gray-300 text-sm">Choose which NFT collection will grant access to your pool</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-pink-400 to-pink-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-pink-300 mb-1">Add Initial Liquidity</h3>
                    <p className="text-gray-300 text-sm">Provide initial SOL and tokens to start the NFT-gated pool</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-400 to-indigo-600 flex items-center justify-center flex-shrink-0">
                    <span className="text-white font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-indigo-300 mb-1">Share with NFT Community</h3>
                    <p className="text-gray-300 text-sm">Announce the exclusive pool to your NFT collection holders</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Private Pool Stats */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Private Pool Statistics</h2>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 rounded-xl bg-purple-400/10 border border-purple-400/20">
                  <p className="text-2xl font-bold text-white">156</p>
                  <p className="text-xs text-gray-400">Private Pools</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-pink-400/10 border border-pink-400/20">
                  <p className="text-2xl font-bold text-white">$8.9M</p>
                  <p className="text-xs text-gray-400">Private Liquidity</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-indigo-400/10 border border-indigo-400/20">
                  <p className="text-2xl font-bold text-white">2,847</p>
                  <p className="text-xs text-gray-400">Exclusive Members</p>
                </div>
                <div className="text-center p-4 rounded-xl bg-violet-400/10 border border-violet-400/20">
                  <p className="text-2xl font-bold text-white">$2.1M</p>
                  <p className="text-xs text-gray-400">24h Private Volume</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
