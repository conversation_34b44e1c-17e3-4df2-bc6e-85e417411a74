'use client'

import { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Button } from '@/components/shared/Button'
import { Card } from '@/components/shared/Card'
import {
  Copy,
  ExternalLink,
  Edit,
  Camera,
  Check,
  X,
  TrendingUp
} from 'lucide-react'
import { staggerContainer, staggerItem } from '@/lib/animations'
import { cn, formatNumber, truncateAddress, generateTokenGradient } from '@/lib/utils'



interface Token {
  id: string
  name: string
  symbol: string
  price: number
  balance: number
  value: number
  change24h: number
}



const portfolio: Token[] = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    symbol: 'SOL',
    price: 98.45,
    balance: 12.5,
    value: 1230.63,
    change24h: 5.67
  },
  {
    id: '2',
    name: 'SolanaBot',
    symbol: 'SBOT',
    price: 0.0234,
    balance: 50000,
    value: 1170.00,
    change24h: 15.67
  },
  {
    id: '3',
    name: 'USD Coin',
    symbol: 'USDC',
    price: 1.00,
    balance: 2500,
    value: 2500.00,
    change24h: 0.01
  }
]

const createdTokens = [
  {
    id: '1',
    name: 'My Token',
    symbol: 'MTK',
    price: 0.0156,
    marketCap: 156000,
    holders: 234,
    change24h: 12.45
  },
  {
    id: '2',
    name: 'Degen Coin',
    symbol: 'DEGEN',
    price: 0.0089,
    marketCap: 89000,
    holders: 156,
    change24h: -5.23
  }
]

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('portfolio')
  const [walletAddress] = useState('7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU')
  const [isEditing, setIsEditing] = useState(false)
  const [userName, setUserName] = useState('Anonymous User')
  const [avatarUrl, setAvatarUrl] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const tabs = [
    { id: 'portfolio', label: 'Portfolio' },
    { id: 'created', label: 'Created' },
    { id: 'activity', label: 'Activity' }
  ]

  const copyAddress = () => {
    navigator.clipboard.writeText(walletAddress)
    // Show toast notification
  }

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setAvatarUrl(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSaveProfile = () => {
    setIsEditing(false)
    // Here you would save to backend/blockchain
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    // Reset to original values if needed
  }

  return (
    <MobileLayout headerTitle="Profile">
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Profile Header */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="text-center space-y-4">
            {/* Avatar Section */}
            <div className="relative">
              {avatarUrl ? (
                <img
                  src={avatarUrl}
                  alt="Profile Avatar"
                  className="w-20 h-20 mx-auto rounded-full object-cover border-2 border-gray-600/40"
                />
              ) : (
                <img
                  src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=200&h=200&fit=crop&crop=face"
                  alt="Default Avatar"
                  className="w-20 h-20 mx-auto rounded-full object-cover border-2 border-gray-600/40"
                />
              )}
              <button
                onClick={() => fileInputRef.current?.click()}
                className="absolute bottom-0 right-1/2 transform translate-x-6 translate-y-1 p-2 rounded-full bg-emerald-400 text-white hover:bg-emerald-500 transition-colors"
              >
                <Camera className="w-4 h-4" />
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleAvatarUpload}
                className="hidden"
              />
            </div>

            {/* Name Section */}
            <div>
              {isEditing ? (
                <div className="space-y-3">
                  <input
                    type="text"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    className="w-full px-3 py-2 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white text-center font-heading text-xl font-bold focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
                    placeholder="Enter your name"
                  />
                  <div className="flex space-x-2 justify-center">
                    <Button variant="gradient" size="sm" onClick={handleSaveProfile}>
                      <Check className="w-4 h-4 mr-2" />
                      Save
                    </Button>
                    <Button variant="glass" size="sm" onClick={handleCancelEdit}>
                      <X className="w-4 h-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="flex items-center justify-center space-x-2">
                    <h1 className="text-xl font-bold text-white font-heading">{userName}</h1>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="p-1 hover:bg-white/10 rounded transition-colors"
                    >
                      <Edit className="w-4 h-4 text-gray-400" />
                    </button>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-center space-x-2 mt-2">
                <span className="text-sm text-gray-400 font-mono">
                  {truncateAddress(walletAddress)}
                </span>
                <button onClick={copyAddress} className="p-1 hover:bg-white/10 rounded transition-colors">
                  <Copy className="w-4 h-4 text-gray-400" />
                </button>
                <button className="p-1 hover:bg-white/10 rounded transition-colors">
                  <ExternalLink className="w-4 h-4 text-gray-400" />
                </button>
              </div>
            </div>
          </Card>
        </motion.div>



        {/* Tabs */}
        <motion.div variants={staggerItem} className="flex space-x-1 p-1 glass-card rounded-2xl">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "flex-1 py-3 rounded-xl transition-all duration-300 text-sm font-medium",
                  isActive 
                    ? "bg-gradient-primary text-white shadow-lg" 
                    : "text-gray-400 hover:text-white"
                )}
              >
                {tab.label}
              </button>
            )
          })}
        </motion.div>

        {/* Tab Content */}
        {activeTab === 'portfolio' && (
          <motion.div variants={staggerItem} className="space-y-4">
            
            {portfolio.map((token) => (
              <Card key={token.id} variant="glass" padding="md">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm"
                      style={{ background: generateTokenGradient(token.symbol) }}
                    >
                      {token.symbol.slice(0, 2)}
                    </div>
                    <div>
                      <p className="font-semibold text-white">{token.name}</p>
                      <p className="text-sm text-gray-400">{formatNumber(token.balance, 4)} {token.symbol}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-white">${formatNumber(token.value, 2)}</p>
                    <p className={cn(
                      "text-sm",
                      token.change24h >= 0 ? "text-primary-green" : "text-red-400"
                    )}>
                      {token.change24h >= 0 ? '+' : ''}{token.change24h.toFixed(2)}%
                    </p>
                  </div>
                </div>
              </Card>
            ))}
          </motion.div>
        )}

        {activeTab === 'created' && (
          <motion.div variants={staggerItem} className="space-y-4">
            
            {createdTokens.map((token) => (
              <Card key={token.id} variant="glass" padding="md">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm"
                        style={{ background: generateTokenGradient(token.symbol) }}
                      >
                        {token.symbol.slice(0, 2)}
                      </div>
                      <div>
                        <p className="font-semibold text-white">{token.name}</p>
                        <p className="text-sm text-gray-400">${token.symbol}</p>
                      </div>
                    </div>
                    <button className="p-2 hover:bg-white/10 rounded-lg">
                      <ExternalLink className="w-4 h-4 text-gray-400" />
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-sm font-semibold text-white">${formatNumber(token.price, 4)}</p>
                      <p className="text-xs text-gray-400">Price</p>
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-white">${formatNumber(token.marketCap)}</p>
                      <p className="text-xs text-gray-400">Market Cap</p>
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-white">{token.holders}</p>
                      <p className="text-xs text-gray-400">Holders</p>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </motion.div>
        )}

        {activeTab === 'activity' && (
          <motion.div variants={staggerItem} className="space-y-4">
            <Card variant="glass" padding="lg">
              <div className="text-center space-y-4">
                <TrendingUp className="w-12 h-12 text-gray-400 mx-auto" />
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">No Recent Activity</h4>
                  <p className="text-gray-400">
                    Your trading activity will appear here
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </motion.div>
    </MobileLayout>
  )
}
