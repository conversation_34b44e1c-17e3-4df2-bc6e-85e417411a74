import { NextRequest, NextResponse } from 'next/server';
import { uploadToIPFS, validateFile } from '@/lib/pinata';

export async function POST(request: NextRequest) {
  try {
    console.log('Upload API called');

    // Check if Pinata JWT is configured
    if (!process.env.PINATA_JWT) {
      console.error('PINATA_JWT environment variable not set');
      return NextResponse.json(
        { error: 'IPFS upload service not configured' },
        { status: 500 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      console.error('No file provided in request');
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    console.log(`File received: ${file.name}, size: ${file.size}, type: ${file.type}`);

    // Validate file
    const validation = validateFile(file);
    if (!validation.valid) {
      console.error('File validation failed:', validation.error);
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    console.log('File validation passed, uploading to IPFS...');

    // Upload to IPFS
    const ipfsUrl = await uploadToIPFS(file);

    console.log('Upload successful, final URL:', ipfsUrl);

    const response = {
      success: true,
      ipfsUrl,
      fileName: file.name,
      fileSize: file.size,
    };

    console.log('API response:', response);

    return NextResponse.json(response);

  } catch (error) {
    console.error('Upload API error:', error);

    // Provide more specific error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        error: errorMessage.includes('Pinata') ? errorMessage : 'Failed to upload file to IPFS',
        details: errorMessage
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
