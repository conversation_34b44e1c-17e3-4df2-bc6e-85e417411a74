/**
 * RPC-based Token Deployment API
 * Server-side deployment using Sui TypeScript SDK
 */

import { NextRequest, NextResponse } from 'next/server'
import { SuiClient, getFullnodeUrl } from '@mysten/sui/client'
import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519'
import { Transaction } from '@mysten/sui/transactions'
import { promises as fs } from 'fs'
import path from 'path'
import { execSync } from 'child_process'

interface TokenDeploymentRequest {
  symbol: string
  name: string
  description: string
  iconUrl: string
  decimals: number
  creator: string
}

interface DeploymentResult {
  success: boolean
  packageId?: string
  treasuryCapId?: string
  coinMetadataId?: string
  transactionDigest?: string
  error?: string
}

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 RPC deployment API called')
    
    const body = await request.json()
    const { tokenSymbol, tokenName, tokenDescription, iconUrl, decimals, creator } = body

    if (!tokenSymbol || !tokenName) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: tokenSymbol, tokenName' },
        { status: 400 }
      )
    }

    console.log('📋 RPC deployment request:', {
      symbol: tokenSymbol,
      name: tokenName,
      creator
    })

    // Initialize Sui client and keypair
    const network = process.env.NEXT_PUBLIC_SUI_NETWORK || 'devnet'
    const client = new SuiClient({ url: getFullnodeUrl(network as any) })

    const privateKey = process.env.DEPLOYER_PRIVATE_KEY || process.env.PRIVATE_KEY
    if (!privateKey) {
      throw new Error('DEPLOYER_PRIVATE_KEY not found in environment')
    }

    const keypair = Ed25519Keypair.fromSecretKey(privateKey)

    // Deploy the token module via RPC
    const deploymentResult = await deployTokenModuleRPC({
      symbol: tokenSymbol,
      name: tokenName,
      description: tokenDescription || 'Token created via Dexsta platform',
      iconUrl: iconUrl || '',
      decimals: decimals || 9,
      creator: creator
    }, client, keypair)

    if (deploymentResult.success) {
      console.log('✅ RPC deployment successful:', {
        packageId: deploymentResult.packageId,
        treasuryCapId: deploymentResult.treasuryCapId,
        coinMetadataId: deploymentResult.coinMetadataId
      })

      return NextResponse.json({
        success: true,
        packageId: deploymentResult.packageId,
        treasuryCapId: deploymentResult.treasuryCapId,
        coinMetadataId: deploymentResult.coinMetadataId,
        transactionDigest: deploymentResult.transactionDigest,
        deploymentMethod: 'rpc'
      })
    } else {
      console.error('❌ RPC deployment failed:', deploymentResult.error)
      
      return NextResponse.json(
        { 
          success: false, 
          error: deploymentResult.error || 'RPC deployment failed',
          deploymentMethod: 'rpc'
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ RPC deployment API error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown RPC deployment error',
        deploymentMethod: 'rpc'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'RPC Token Deployment API',
    status: 'ready',
    method: 'POST',
    description: 'Deploy token modules via Sui RPC without CLI dependency'
  })
}

/**
 * Deploy token module via RPC (server-side only)
 */
async function deployTokenModuleRPC(
  request: TokenDeploymentRequest,
  client: SuiClient,
  keypair: Ed25519Keypair
): Promise<DeploymentResult> {
  console.log('🚀 Starting RPC-based token deployment for:', request.symbol)

  try {
    // Step 1: Generate module content
    const moduleData = generateModuleContent(request)

    // Step 2: Create temporary files and build
    const tempDir = await createModuleFiles(moduleData)

    // Step 3: Get pre-compiled bytecode (CLI-free)
    const bytecode = await getPrecompiledBytecode(request)

    // Step 4: Deploy via RPC
    const result = await deployViaRPC(bytecode, client, keypair)

    console.log('✅ RPC deployment completed for:', request.symbol)
    return result

  } catch (error) {
    console.error('❌ RPC deployment failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown deployment error'
    }
  }
}

/**
 * Generate Move module content
 */
function generateModuleContent(request: TokenDeploymentRequest) {
  const moduleName = request.symbol.toLowerCase().replace(/[^a-z0-9_]/g, '_')
  const structName = `${request.symbol.replace(/[^A-Za-z0-9]/g, '')}Coin`

  const moduleContent = `
module 0x0::${moduleName} {
    use sui::coin::{Self, TreasuryCap, Coin};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use std::option;

    struct ${structName} has store, drop {}

    fun init(ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<${structName}>(
            ${structName} {},
            ${request.decimals}, // decimals
            b"${request.symbol}", // symbol
            b"${request.name}", // name
            b"${request.description}", // description
            option::none(), // icon_url (will be set later)
            ctx
        );

        // Transfer the treasury cap to the deployer
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));

        // Share the metadata object
        transfer::public_share_object(metadata);
    }

    /// Mint tokens to a recipient
    public entry fun mint(
        treasury_cap: &mut TreasuryCap<${structName}>,
        amount: u64,
        recipient: address,
        ctx: &mut TxContext
    ) {
        let coin = coin::mint(treasury_cap, amount, ctx);
        transfer::public_transfer(coin, recipient);
    }

    /// Get total supply
    public fun total_supply(treasury_cap: &TreasuryCap<${structName}>): u64 {
        coin::total_supply(treasury_cap)
    }
}`.trim()

  const moveToml = `
[package]
name = "${moduleName}"
version = "0.0.1"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
${moduleName} = "0x0"
`.trim()

  return {
    moduleName,
    structName,
    moduleContent,
    moveToml
  }
}

/**
 * Create module files in temporary directory
 */
async function createModuleFiles(moduleData: any): Promise<string> {
  const tempDir = path.join(process.cwd(), 'temp', `rpc_${moduleData.moduleName}_${Date.now()}`)
  const sourcesDir = path.join(tempDir, 'sources')

  await fs.mkdir(sourcesDir, { recursive: true })

  await fs.writeFile(path.join(tempDir, 'Move.toml'), moduleData.moveToml)
  await fs.writeFile(path.join(sourcesDir, 'coin.move'), moduleData.moduleContent)

  console.log('📁 Module files created in:', tempDir)
  return tempDir
}

/**
 * Get pre-compiled template bytecode (CLI-free approach)
 */
async function getPrecompiledBytecode(request: TokenDeploymentRequest): Promise<Uint8Array[]> {
  console.log('📦 Using pre-compiled template bytecode...')

  try {
    // For now, return an error to force fallback to the working Node.js script
    // TODO: Implement true pre-compiled bytecode template system
    throw new Error('Pre-compiled bytecode not yet implemented - falling back to Node.js deployment')

    // Future implementation would:
    // 1. Load pre-compiled template bytecode from file
    // 2. Modify bytecode to replace symbol/name placeholders
    // 3. Return modified bytecode ready for deployment

  } catch (error) {
    console.error('❌ Pre-compiled bytecode not available:', error)
    throw error
  }
}

/**
 * Deploy via Sui RPC
 */
async function deployViaRPC(
  modules: Uint8Array[],
  client: SuiClient,
  keypair: Ed25519Keypair
): Promise<DeploymentResult> {
  console.log('📡 Publishing via Sui RPC...')

  try {
    const tx = new Transaction()

    // Publish the modules using the correct SDK method
    tx.publish({
      modules: modules,
      dependencies: [],
    })

    // Execute the transaction
    const result = await client.signAndExecuteTransaction({
      transaction: tx,
      signer: keypair,
      options: {
        showEffects: true,
        showEvents: true,
        showObjectChanges: true,
      }
    })

    console.log('✅ RPC publish transaction:', result.digest)

    // Extract deployment information
    if (result.objectChanges) {
      const packageChange = result.objectChanges.find((change: any) =>
        change.type === 'published'
      )

      const treasuryCapChange = result.objectChanges.find((change: any) =>
        change.objectType?.includes('TreasuryCap')
      )

      const coinMetadataChange = result.objectChanges.find((change: any) =>
        change.objectType?.includes('CoinMetadata')
      )

      if (packageChange) {
        console.log('📋 Deployment successful:', {
          packageId: packageChange.packageId,
          treasuryCapId: treasuryCapChange?.objectId,
          coinMetadataId: coinMetadataChange?.objectId
        })

        return {
          success: true,
          packageId: packageChange.packageId,
          treasuryCapId: treasuryCapChange?.objectId || undefined,
          coinMetadataId: coinMetadataChange?.objectId || undefined,
          transactionDigest: result.digest
        }
      }
    }

    throw new Error('Failed to extract deployment information from RPC result')

  } catch (error) {
    console.error('❌ RPC publish failed:', error)
    throw error
  }
}
