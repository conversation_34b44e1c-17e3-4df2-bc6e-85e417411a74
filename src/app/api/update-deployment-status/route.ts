/**
 * Database Update API Route
 * Updates token deployment status in the database
 */

import { NextRequest, NextResponse } from 'next/server'
import { createTokenRecord, updateTokenMarketData } from '@/lib/database'

export async function POST(request: NextRequest) {
  try {
    const {
      tokenSymbol,
      status,
      packageId,
      treasuryCapId,
      coinMetadataId,
      error,
      deploymentMethod
    } = await request.json()

    if (!tokenSymbol || !status) {
      return NextResponse.json(
        { error: 'Token symbol and status are required' },
        { status: 400 }
      )
    }

    console.log('📊 Updating deployment status for token:', tokenSymbol)
    console.log('📋 Status:', status)

    // Update or create token record in database
    try {
      if (status === 'completed' && packageId && treasuryCapId && coinMetadataId) {
        // Token deployment completed successfully
        const tokenData = {
          symbol: tokenSymbol,
          name: tokenSymbol.replace(/\d+$/, ''), // Remove timestamp suffix for display
          description: 'Token created via Dexsta platform',
          package_id: packageId,
          treasury_cap_id: treasuryCapId,
          coin_metadata_id: coinMetadataId,
          deployment_method: deploymentMethod || 'api',
          deployment_status: 'completed'
        }

        await createTokenRecord(tokenData)
        console.log('✅ Token record created in database')

        // Initialize market data
        await updateTokenMarketData(tokenSymbol, {
          price: 0.001, // Initial price
          volume24h: 0,
          marketCap: 0,
          holders: 1, // Creator
          transactions: 1 // Creation transaction
        })
        console.log('✅ Market data initialized')

      } else if (status === 'failed' || status === 'error') {
        // Token deployment failed
        const tokenData = {
          symbol: tokenSymbol,
          name: tokenSymbol.replace(/\d+$/, ''),
          description: 'Token creation failed',
          deployment_status: status,
          deployment_error: error,
          deployment_method: deploymentMethod || 'api'
        }

        await createTokenRecord(tokenData)
        console.log('❌ Failed token record created in database')

      } else {
        // Pending or other status
        console.log('⏳ Status update:', status)
      }

      return NextResponse.json({
        success: true,
        message: 'Deployment status updated successfully'
      })

    } catch (dbError) {
      console.error('❌ Database update failed:', dbError)
      return NextResponse.json(
        { 
          error: 'Database update failed',
          details: dbError instanceof Error ? dbError.message : 'Unknown database error'
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ Update deployment status API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to update deployment status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Deployment status update API. Use POST to update status.' },
    { status: 200 }
  )
}
