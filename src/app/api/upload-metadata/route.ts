import { NextRequest, NextResponse } from 'next/server';
import { uploadJSONToIPFS } from '@/lib/pinata';

export async function POST(request: NextRequest) {
  try {
    console.log('Metadata upload API called');
    
    // Check if Pinata JWT is configured
    if (!process.env.PINATA_JWT) {
      console.error('PINATA_JWT environment variable not set');
      return NextResponse.json(
        { error: 'IPFS upload service not configured' },
        { status: 500 }
      );
    }

    const body = await request.json();
    const { metadata, name } = body;

    if (!metadata) {
      console.error('No metadata provided in request');
      return NextResponse.json(
        { error: 'No metadata provided' },
        { status: 400 }
      );
    }

    if (!name) {
      console.error('No name provided in request');
      return NextResponse.json(
        { error: 'No name provided' },
        { status: 400 }
      );
    }

    console.log(`Uploading metadata: ${name}`);

    // Upload metadata to IPFS
    const ipfsUrl = await uploadJSONToIPFS(metadata, name);

    console.log('Metadata upload successful:', ipfsUrl);

    return NextResponse.json({
      success: true,
      ipfsUrl,
      name,
    });

  } catch (error) {
    console.error('Metadata upload API error:', error);
    
    // Provide more specific error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return NextResponse.json(
      { 
        error: errorMessage.includes('Pinata') ? errorMessage : 'Failed to upload metadata to IPFS',
        details: errorMessage
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
