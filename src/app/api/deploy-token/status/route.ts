/**
 * API Route for Deployment Status
 * Returns the deployment status of a token
 */

import { NextRequest, NextResponse } from 'next/server'
import { SuiClient, getFullnodeUrl } from '@mysten/sui/client'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tokenSymbol = searchParams.get('tokenSymbol')

    if (!tokenSymbol) {
      return NextResponse.json(
        { error: 'Token symbol is required' },
        { status: 400 }
      )
    }

    console.log('🔍 Checking deployment status for token symbol:', tokenSymbol)

    // Get the deployment event data
    const network = process.env.NEXT_PUBLIC_SUI_NETWORK || 'devnet'
    const client = new SuiClient({ url: getFullnodeUrl(network as any) })
    const packageId = process.env.NEXT_PUBLIC_PACKAGE_ID

    if (!packageId) {
      return NextResponse.json(
        { error: 'Package ID not configured' },
        { status: 500 }
      )
    }

    // Check for deployment request event
    const requestEventQuery = {
      MoveEventType: `${packageId}::dexsta_token::TokenModuleDeploymentRequested`
    }

    const requestEvents = await client.queryEvents({
      query: requestEventQuery,
      limit: 50,
      order: 'descending'
    })

    const requestEvent = requestEvents.data?.find(event => {
      const eventData = event.parsedJson as any
      return eventData?.token_symbol === tokenSymbol
    })

    if (!requestEvent) {
      return NextResponse.json({
        requested: false,
        completed: false
      })
    }

    // Check for completion event
    const completionEventQuery = {
      MoveEventType: `${packageId}::dexsta_token::TokenCreated`
    }

    const completionEvents = await client.queryEvents({
      query: completionEventQuery,
      limit: 50,
      order: 'descending'
    })

    const completionEvent = completionEvents.data?.find(event => {
      const eventData = event.parsedJson as any
      return eventData?.token_symbol === tokenSymbol
    })

    return NextResponse.json({
      requested: true,
      completed: !!completionEvent,
      requestEvent: requestEvent.parsedJson,
      completionEvent: completionEvent?.parsedJson
    })

  } catch (error) {
    console.error('❌ Status check error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
