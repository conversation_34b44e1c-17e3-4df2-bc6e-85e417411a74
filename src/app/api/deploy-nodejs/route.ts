/**
 * Node.js Deployment API Route
 * Executes the Node.js deployment script for token deployment
 */

import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'
import path from 'path'

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  try {
    const { tokenSymbol } = await request.json()

    if (!tokenSymbol) {
      return NextResponse.json(
        { error: 'Token symbol is required' },
        { status: 400 }
      )
    }

    console.log('🎯 Node.js deployment request for token symbol:', tokenSymbol)

    // Execute the Node.js deployment script
    const scriptPath = path.join(process.cwd(), 'deploy-token-live.js')
    console.log('📜 Executing deployment script:', scriptPath)

    try {
      // Set environment variable for the specific token
      const env = {
        ...process.env,
        TARGET_TOKEN_SYMBOL: tokenSymbol
      }

      const result = await execAsync(`node deploy-token-live.js`, {
        cwd: process.cwd(),
        env,
        timeout: 300000 // 5 minute timeout
      })

      console.log('✅ Node.js deployment script completed')
      console.log('📋 Script output:', result.stdout)

      // Parse the output to extract deployment results
      const output = result.stdout
      let packageId = ''
      let treasuryCapId = ''
      let coinMetadataId = ''

      // Extract IDs from script output
      const packageMatch = output.match(/Package ID: (0x[a-fA-F0-9]+)/)
      const treasuryMatch = output.match(/Treasury Cap ID: (0x[a-fA-F0-9]+)/)
      const metadataMatch = output.match(/Coin Metadata ID: (0x[a-fA-F0-9]+)/)

      if (packageMatch) packageId = packageMatch[1]
      if (treasuryMatch) treasuryCapId = treasuryMatch[1]
      if (metadataMatch) coinMetadataId = metadataMatch[1]

      if (packageId && treasuryCapId && coinMetadataId) {
        console.log('✅ Successfully extracted deployment results:')
        console.log('📦 Package ID:', packageId)
        console.log('🏛️ Treasury Cap:', treasuryCapId)
        console.log('📋 Coin Metadata:', coinMetadataId)

        return NextResponse.json({
          success: true,
          message: 'Node.js deployment completed successfully',
          packageId,
          treasuryCapId,
          coinMetadataId,
          scriptOutput: output
        })
      } else {
        // Script ran but didn't produce expected results
        console.warn('⚠️ Script completed but missing deployment results')
        return NextResponse.json({
          success: false,
          error: 'Deployment script completed but failed to extract results',
          scriptOutput: output
        })
      }

    } catch (execError) {
      console.error('❌ Node.js deployment script failed:', execError)
      
      return NextResponse.json({
        success: false,
        error: 'Deployment script execution failed',
        details: execError instanceof Error ? execError.message : 'Unknown execution error'
      })
    }

  } catch (error) {
    console.error('❌ Node.js deployment API error:', error)
    return NextResponse.json(
      { 
        error: 'Node.js deployment failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Node.js token deployment API. Use POST with tokenSymbol to deploy.' },
    { status: 200 }
  )
}
