'use client'

import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { PageSeparator } from '@/components/shared/PageSeparator'
import { staggerContainer, staggerItem } from '@/lib/animations'

export default function PrivacyPolicyPage() {
  return (
    <MobileLayout headerTitle="Privacy Policy" showBackButton={true}>
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-6 py-4 pb-32"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-4">
          <div className="text-6xl">🔐</div>
          <h1 className="text-2xl font-bold text-white font-heading">
            Privacy Policy
          </h1>
          <div className="w-20 h-0.5 bg-gradient-to-r from-green-400 to-emerald-600 rounded-full mx-auto" />
          <p className="text-gray-300 text-sm">
            Last updated: December 2024
          </p>
        </motion.div>

        {/* Introduction */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-green-400/30 bg-green-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Our Commitment</h2>
              <p className="text-gray-200 leading-relaxed text-sm">
                Dexsta is committed to protecting your privacy. This policy explains how we collect, use, and protect your information when you use our platform.
              </p>
            </div>
          </Card>
        </motion.div>

        {/* Information We Collect */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Information We Collect</h2>
              <div className="space-y-4">
                <div className="p-4 rounded-xl bg-blue-400/10 border border-blue-400/20">
                  <h3 className="font-semibold text-blue-300 mb-2">Wallet Information</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    We collect your wallet address when you connect to our platform. This is necessary for trading and token creation.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-purple-400/10 border border-purple-400/20">
                  <h3 className="font-semibold text-purple-300 mb-2">Transaction Data</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    We record transaction data for platform functionality, including trades, token creations, and fee collections.
                  </p>
                </div>
                <div className="p-4 rounded-xl bg-cyan-400/10 border border-cyan-400/20">
                  <h3 className="font-semibold text-cyan-300 mb-2">Usage Analytics</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    We collect anonymous usage data to improve our platform performance and user experience.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* How We Use Information */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">How We Use Your Information</h2>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="text-emerald-400">✅</div>
                  <div>
                    <h3 className="font-semibold text-emerald-300 text-sm">Platform Operations</h3>
                    <p className="text-gray-300 text-xs">Enable trading, token creation, and platform features</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-emerald-400">✅</div>
                  <div>
                    <h3 className="font-semibold text-emerald-300 text-sm">Security & Fraud Prevention</h3>
                    <p className="text-gray-300 text-xs">Protect against malicious activities and ensure platform security</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-emerald-400">✅</div>
                  <div>
                    <h3 className="font-semibold text-emerald-300 text-sm">Platform Improvement</h3>
                    <p className="text-gray-300 text-xs">Analyze usage patterns to enhance user experience</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-emerald-400">✅</div>
                  <div>
                    <h3 className="font-semibold text-emerald-300 text-sm">Legal Compliance</h3>
                    <p className="text-gray-300 text-xs">Meet regulatory requirements and legal obligations</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Data Protection */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg" className="border border-blue-400/30 bg-blue-400/5">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Data Protection</h2>
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-3">
                  <div className="text-blue-400">🔒</div>
                  <p className="text-gray-300">
                    We use industry-standard encryption to protect your data
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-blue-400">🛡️</div>
                  <p className="text-gray-300">
                    Access to personal data is restricted to authorized personnel only
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-blue-400">🔐</div>
                  <p className="text-gray-300">
                    We regularly audit our security practices and update protocols
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-blue-400">💾</div>
                  <p className="text-gray-300">
                    Data is stored securely and backed up regularly
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Your Rights */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Your Rights</h2>
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">👁️</div>
                  <p className="text-gray-300">
                    <span className="font-semibold text-yellow-300">Access:</span> Request information about data we hold about you
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">✏️</div>
                  <p className="text-gray-300">
                    <span className="font-semibold text-yellow-300">Correction:</span> Request correction of inaccurate data
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">🗑️</div>
                  <p className="text-gray-300">
                    <span className="font-semibold text-yellow-300">Deletion:</span> Request deletion of your personal data (where legally possible)
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="text-yellow-400">📤</div>
                  <p className="text-gray-300">
                    <span className="font-semibold text-yellow-300">Portability:</span> Request a copy of your data in a portable format
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Contact */}
        <motion.div variants={staggerItem}>
          <Card variant="glass" padding="lg">
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-white font-heading">Contact Us</h2>
              <p className="text-gray-300 text-sm leading-relaxed">
                If you have questions about this Privacy Policy or want to exercise your rights, please contact our support team. We're committed to addressing your privacy concerns promptly.
              </p>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </MobileLayout>
  )
}
