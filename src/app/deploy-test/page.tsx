'use client'

import { useState } from 'react'
import { Button } from '@/components/shared/Button'
import { Card } from '@/components/shared/Card'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { getDeploymentClient } from '@/services/deploymentClient'

export default function DeployTestPage() {
  const [tokenSymbol, setTokenSymbol] = useState('') // Token symbol from your transaction
  const [isDeploying, setIsDeploying] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [isLoadingDebug, setIsLoadingDebug] = useState(false)
  const [suiTestResult, setSuiTestResult] = useState<any>(null)
  const [isTestingSui, setIsTestingSui] = useState(false)

  const handleDeploy = async () => {
    if (!tokenSymbol) {
      setError('Please enter a token symbol')
      return
    }

    setIsDeploying(true)
    setError(null)
    setResult(null)

    try {
      console.log('🚀 Starting manual deployment for token symbol:', tokenSymbol)

      const deploymentClient = getDeploymentClient()
      const result = await deploymentClient.deployToken(tokenSymbol)

      if (!result.success) {
        throw new Error(result.error || 'Deployment failed')
      }

      console.log('✅ Deployment successful:', result)
      setResult(result)

    } catch (error) {
      console.error('❌ Deployment failed:', error)
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setIsDeploying(false)
    }
  }

  const handleDebug = async () => {
    setIsLoadingDebug(true)
    setDebugInfo(null)

    try {
      console.log('🔍 Fetching debug information...')

      const response = await fetch('/api/debug-events')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Debug failed')
      }

      console.log('📋 Debug info:', data)
      setDebugInfo(data)

    } catch (error) {
      console.error('❌ Debug failed:', error)
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setIsLoadingDebug(false)
    }
  }

  const handleMockDeploy = async () => {
    if (!tokenSymbol) {
      setError('Please enter a token symbol')
      return
    }

    setIsDeploying(true)
    setError(null)
    setResult(null)

    try {
      console.log('🎭 Starting mock deployment for token symbol:', tokenSymbol)

      const response = await fetch('/api/deploy-token-mock', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tokenSymbol }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Mock deployment failed')
      }

      console.log('✅ Mock deployment successful:', result)
      setResult(result)

    } catch (error) {
      console.error('❌ Mock deployment failed:', error)
      setError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setIsDeploying(false)
    }
  }

  const handleTestSui = async () => {
    setIsTestingSui(true)
    setSuiTestResult(null)

    try {
      console.log('🔍 Testing Sui CLI...')

      const response = await fetch('/api/test-sui')
      const data = await response.json()

      console.log('📋 Sui test result:', data)
      setSuiTestResult(data)

    } catch (error) {
      console.error('❌ Sui test failed:', error)
      setSuiTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsTestingSui(false)
    }
  }

  return (
    <MobileLayout headerTitle="Token Deployment Test" showBackButton={true}>
      <div className="space-y-6 p-4">
        <Card variant="glass" padding="lg">
          <h1 className="text-2xl font-bold text-white mb-4">
            Manual Token Deployment
          </h1>
          <p className="text-gray-300 mb-6">
            Test the template-based token deployment system by manually deploying a token.
          </p>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Token Symbol
              </label>
              <input
                type="text"
                value={tokenSymbol}
                onChange={(e) => setTokenSymbol(e.target.value.toUpperCase())}
                placeholder="Enter token symbol (e.g., MYTOKEN123456)"
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-purple/50"
              />
              <p className="text-xs text-gray-400 mt-1">
                The token symbol from your token creation (includes timestamp suffix)
              </p>
            </div>

            <div className="space-y-3">
              <Button
                onClick={handleDeploy}
                disabled={isDeploying || !tokenSymbol}
                className="w-full"
              >
                {isDeploying ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Deploying Module...</span>
                  </div>
                ) : (
                  'Deploy Token Module'
                )}
              </Button>

              <Button
                onClick={handleDebug}
                disabled={isLoadingDebug}
                variant="outline"
                className="w-full"
              >
                {isLoadingDebug ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    <span>Loading Debug Info...</span>
                  </div>
                ) : (
                  'Debug: Check Events'
                )}
              </Button>

              <Button
                onClick={handleTestSui}
                disabled={isTestingSui}
                variant="outline"
                className="w-full"
              >
                {isTestingSui ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    <span>Testing Sui CLI...</span>
                  </div>
                ) : (
                  'Test: Check Sui CLI'
                )}
              </Button>
            </div>
          </div>
        </Card>

        {/* Error Display */}
        {error && (
          <Card variant="glass" padding="lg" className="border-red-500/20 bg-red-500/10">
            <h3 className="text-lg font-semibold text-red-400 mb-2">
              Deployment Failed
            </h3>
            <p className="text-red-300 text-sm">{error}</p>
          </Card>
        )}

        {/* Sui CLI Test Result */}
        {suiTestResult && (
          <Card variant="glass" padding="lg" className={`border-${suiTestResult.success ? 'green' : 'red'}-500/20 bg-${suiTestResult.success ? 'green' : 'red'}-500/10`}>
            <h3 className={`text-lg font-semibold text-${suiTestResult.success ? 'green' : 'red'}-400 mb-4`}>
              Sui CLI Test {suiTestResult.success ? '✅' : '❌'}
            </h3>
            <div className="space-y-2 text-sm">
              {suiTestResult.success ? (
                <>
                  <p className="text-green-300">Sui CLI is available!</p>
                  <p className="text-green-300 font-mono">{suiTestResult.version}</p>
                </>
              ) : (
                <>
                  <p className="text-red-300">Sui CLI is not available</p>
                  <p className="text-red-300 text-xs">{suiTestResult.error}</p>
                  <p className="text-red-300 text-xs mt-2">
                    Please install Sui CLI: <code>cargo install --locked --git https://github.com/MystenLabs/sui.git --branch devnet sui</code>
                  </p>
                </>
              )}
            </div>
          </Card>
        )}

        {/* Debug Info Display */}
        {debugInfo && (
          <Card variant="glass" padding="lg" className="border-blue-500/20 bg-blue-500/10">
            <h3 className="text-lg font-semibold text-blue-400 mb-4">
              Debug Information 🔍
            </h3>
            <div className="space-y-4 text-sm">
              <div>
                <span className="text-gray-400">Package ID:</span>
                <p className="text-blue-300 font-mono break-all">{debugInfo.packageId}</p>
              </div>
              <div>
                <span className="text-gray-400">Deployment Events Found:</span>
                <p className="text-blue-300">{debugInfo.deploymentEvents.count}</p>
                {debugInfo.deploymentEvents.events.map((event: any, index: number) => (
                  <div key={index} className="ml-4 mt-2 p-2 bg-gray-800/50 rounded">
                    <p className="text-xs text-gray-400">Event {index + 1}:</p>
                    <p className="text-blue-300">Symbol: {event.parsedJson?.token_symbol}</p>
                    <p className="text-blue-300">Name: {event.parsedJson?.token_name}</p>
                    <p className="text-blue-300">Creator: {event.parsedJson?.creator}</p>
                    <p className="text-xs text-gray-400">TX: {event.txDigest}</p>
                  </div>
                ))}
              </div>
              <div>
                <span className="text-gray-400">TokenCreated Events Found:</span>
                <p className="text-blue-300">{debugInfo.tokenCreatedEvents?.count || 0}</p>
              </div>
            </div>
          </Card>
        )}

        {/* Success Display */}
        {result && (
          <Card variant="glass" padding="lg" className="border-green-500/20 bg-green-500/10">
            <h3 className="text-lg font-semibold text-green-400 mb-4">
              Deployment Successful! 🎉
            </h3>
            <div className="space-y-3 text-sm">
              <div>
                <span className="text-gray-400">Package ID:</span>
                <p className="text-green-300 font-mono break-all">{result.packageId}</p>
              </div>
              <div>
                <span className="text-gray-400">Treasury Cap:</span>
                <p className="text-green-300 font-mono break-all">{result.treasuryCapId}</p>
              </div>
              <div>
                <span className="text-gray-400">Coin Metadata:</span>
                <p className="text-green-300 font-mono break-all">{result.coinMetadataId}</p>
              </div>
            </div>
          </Card>
        )}

        {/* Instructions */}
        <Card variant="glass" padding="lg">
          <h3 className="text-lg font-semibold text-white mb-3">
            How to Test
          </h3>
          <div className="space-y-2 text-sm text-gray-300">
            <p>1. Create a token using the normal create page</p>
            <p>2. Note the token symbol from the transaction (includes timestamp suffix)</p>
            <p>3. Click "Debug: Check Events" to see available tokens</p>
            <p>4. Enter the token symbol above and click "Deploy Token Module"</p>
            <p>5. Wait for the deployment to complete</p>
            <p>6. Check the results and verify the token is now tradeable</p>
          </div>
        </Card>

        {/* Node.js Deployment Instructions */}
        <Card variant="glass" padding="lg">
          <h3 className="text-lg font-semibold text-white mb-3">
            Node.js Deployment (Recommended)
          </h3>
          <div className="space-y-2 text-sm text-gray-300">
            <p>For production deployment, use the Node.js script:</p>
            <div className="bg-gray-800/50 p-3 rounded mt-2">
              <code className="text-green-300">
                # Install Sui CLI first<br/>
                cargo install --locked --git https://github.com/MystenLabs/sui.git --branch devnet sui<br/><br/>
                # Run the deployment test<br/>
                node test-nodejs-deploy.js
              </code>
            </div>
            <p className="text-yellow-300 text-xs mt-2">
              ⚠️ This requires Sui CLI to be installed on your server
            </p>
          </div>
        </Card>

        {/* Event Listener Status */}
        <Card variant="glass" padding="lg">
          <h3 className="text-lg font-semibold text-white mb-3">
            Event Listener Status
          </h3>
          <div className="space-y-2 text-sm text-gray-300">
            <p>• Event listener is running in the background</p>
            <p>• Polls for new deployment events every 10 seconds</p>
            <p>• Automatically deploys modules when events are detected</p>
            <p>• Manual deployment is for testing purposes</p>
          </div>
        </Card>
      </div>
    </MobileLayout>
  )
}
