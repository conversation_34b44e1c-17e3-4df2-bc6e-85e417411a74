'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { MobileLayout } from '@/components/mobile/MobileLayout'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { PageSeparator } from '@/components/shared/PageSeparator'
import {
  Building,
  Shield,
  TrendingUp,
  Clock,
  DollarSign,
  Lock,
  Unlock,
  ArrowRight,
  Info,
  CheckCircle,
  AlertCircle,
  Coins,
  Target,
  Users,
  BarChart3
} from 'lucide-react'
import { staggerContainer, staggerItem } from '@/lib/animations'

export default function CommunityBankPage() {
  const [activeTab, setActiveTab] = useState(0)

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Building },
    { id: 'how-it-works', label: 'How It Works', icon: Info },
    { id: 'ltv-system', label: 'LTV System', icon: TrendingUp },
    { id: 'defaulted-loans', label: 'Buy Defaults', icon: DollarSign },
    { id: 'stats', label: 'Bank Stats', icon: BarChart3 }
  ]

  const bankStats = {
    totalVault: 12450,
    activeLoans: 89,
    totalBorrowed: 8750,
    defaultRate: 2.3,
    averageLTV: 67.5,
    totalUsers: 1247
  }

  const ltvBenefits = [
    { ltv: 45, status: 'Starter', color: 'text-gray-400', description: 'New users start here' },
    { ltv: 60, status: 'Good', color: 'text-blue-400', description: '3+ successful repayments' },
    { ltv: 75, status: 'Great', color: 'text-green-400', description: '8+ successful repayments' },
    { ltv: 90, status: 'Excellent', color: 'text-purple-400', description: '15+ successful repayments' },
    { ltv: 95, status: 'Elite', color: 'text-yellow-400', description: '25+ successful repayments' }
  ]

  return (
    <MobileLayout headerTitle="Community Bank">
      <PageSeparator />
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="space-y-8"
      >
        {/* Hero Section */}
        <motion.div variants={staggerItem} className="text-center space-y-6">
          <div className="relative">
            <div className="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-blue-400 to-indigo-600 flex items-center justify-center mb-6">
              <Building className="w-10 h-10 text-white" />
            </div>
            <div className="absolute -inset-4 bg-gradient-to-r from-blue-400/10 via-transparent to-blue-400/10 rounded-3xl blur-xl -z-10" />
          </div>
          
          <div className="space-y-4">
            <h1 className="text-3xl font-black text-white font-heading">
              Community Bank
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-indigo-600 rounded-full mx-auto" />
            <p className="text-lg text-gray-300 font-body leading-relaxed max-w-sm mx-auto">
              0% interest loans using your XFT (NFT 2.0) as collateral
            </p>
          </div>
        </motion.div>

        {/* Key Features */}
        <motion.section variants={staggerItem} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Card
              variant="glass"
              padding="md"
              className="border-blue-400/30 bg-gradient-to-br from-blue-400/10 to-indigo-600/15 text-center"
            >
              <div className="space-y-3">
                <div className="text-3xl">💰</div>
                <div>
                  <h3 className="font-bold text-white text-sm">0% Interest</h3>
                  <p className="text-xs text-gray-400">No interest charges</p>
                </div>
              </div>
            </Card>

            <Card
              variant="glass"
              padding="md"
              className="border-emerald-400/30 bg-gradient-to-br from-emerald-400/10 to-green-600/15 text-center"
            >
              <div className="space-y-3">
                <div className="text-3xl">⚡</div>
                <div>
                  <h3 className="font-bold text-white text-sm">Instant Loans</h3>
                  <p className="text-xs text-gray-400">Get SUI immediately</p>
                </div>
              </div>
            </Card>

            <Card
              variant="glass"
              padding="md"
              className="border-purple-400/30 bg-gradient-to-br from-purple-400/10 to-violet-600/15 text-center"
            >
              <div className="space-y-3">
                <div className="text-3xl">🎨</div>
                <div>
                  <h3 className="font-bold text-white text-sm">XFT Collateral</h3>
                  <p className="text-xs text-gray-400">Use NFT 2.0 as security</p>
                </div>
              </div>
            </Card>

            <Card
              variant="glass"
              padding="md"
              className="border-yellow-400/30 bg-gradient-to-br from-yellow-400/10 to-orange-600/15 text-center"
            >
              <div className="space-y-3">
                <div className="text-3xl">📊</div>
                <div>
                  <h3 className="font-bold text-white text-sm">Up to 95% LTV</h3>
                  <p className="text-xs text-gray-400">High loan-to-value</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Additional Feature - Defaulted Loans */}
          <div className="mt-4">
            <Card
              variant="glass"
              padding="md"
              className="border-orange-400/30 bg-gradient-to-br from-orange-400/10 to-red-600/15 text-center"
            >
              <div className="space-y-3">
                <div className="text-3xl">💎</div>
                <div>
                  <h3 className="font-bold text-white text-sm">Buy Defaulted Loans</h3>
                  <p className="text-xs text-gray-400">Get discounted XFTs from defaults</p>
                </div>
              </div>
            </Card>
          </div>
        </motion.section>

        {/* Tabs */}
        <motion.section variants={staggerItem} className="space-y-6">
          <div className="flex overflow-x-auto space-x-2 -mx-4 px-4" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
            <style jsx>{`
              div::-webkit-scrollbar {
                display: none;
              }
            `}</style>
            {tabs.map((tab, index) => {
              const Icon = tab.icon
              return (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(index)}
                  className={`flex-shrink-0 px-4 py-3 rounded-xl transition-all duration-300 min-w-[120px] ${
                    activeTab === index
                      ? 'bg-gradient-to-br from-blue-400/20 to-indigo-600/20 border border-blue-400/50'
                      : 'bg-white/5 border border-gray-600/30 hover:bg-white/10'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center space-x-2">
                    <Icon className={`w-4 h-4 ${
                      activeTab === index ? 'text-blue-300' : 'text-gray-400'
                    }`} />
                    <div className={`font-semibold text-xs ${
                      activeTab === index ? 'text-blue-300' : 'text-white'
                    }`}>
                      {tab.label}
                    </div>
                  </div>
                </motion.button>
              )
            })}
          </div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 0 && (
              <Card
                variant="glass"
                padding="lg"
                className="border border-blue-400/30 bg-gradient-to-br from-blue-400/10 to-indigo-600/15"
              >
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-white font-heading mb-4">
                      Community-Funded Banking
                    </h3>
                  </div>

                  <div className="space-y-4">
                    <div className="p-4 rounded-xl bg-blue-400/10 border border-blue-400/20">
                      <p className="text-gray-200 font-body leading-relaxed">
                        The Community Bank is funded by 50% of all platform service fees, creating a sustainable lending pool that benefits the entire ecosystem.
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-3 rounded-xl bg-gradient-to-r from-blue-400/20 to-indigo-600/20 border border-blue-400/30">
                        <p className="text-xs text-blue-300 font-medium mb-1">Vault Balance</p>
                        <p className="text-lg font-bold text-white">{bankStats.totalVault.toLocaleString()} SUI</p>
                      </div>
                      <div className="text-center p-3 rounded-xl bg-gradient-to-r from-emerald-400/20 to-green-600/20 border border-emerald-400/30">
                        <p className="text-xs text-emerald-300 font-medium mb-1">Active Loans</p>
                        <p className="text-lg font-bold text-white">{bankStats.activeLoans}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="text-lg font-semibold text-blue-300 font-heading">
                        Why 0% Interest?
                      </h4>
                      <div className="space-y-2">
                        <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                          <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                          <p className="text-sm text-gray-300 font-body">
                            Community-funded through platform fees
                          </p>
                        </div>
                        <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                          <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                          <p className="text-sm text-gray-300 font-body">
                            XFT collateral ensures loan security
                          </p>
                        </div>
                        <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                          <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                          <p className="text-sm text-gray-300 font-body">
                            Builds stronger community ecosystem
                          </p>
                        </div>
                        <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                          <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                          <p className="text-sm text-gray-300 font-body">
                            <span className="font-semibold text-emerald-300">Investment opportunities</span> through defaulted loan purchases
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 1 && (
              <Card
                variant="glass"
                padding="lg"
                className="border border-emerald-400/30 bg-gradient-to-br from-emerald-400/10 to-green-600/15"
              >
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-white font-heading mb-4">
                      How Loans Work
                    </h3>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-3">
                      {[
                        {
                          step: 1,
                          title: 'Lock Your XFT',
                          description: 'Your XFT vault must be locked to use as collateral',
                          icon: Lock,
                          color: 'bg-blue-400'
                        },
                        {
                          step: 2,
                          title: 'Get Loan Approval',
                          description: 'Oracle evaluates your XFT value and determines loan amount',
                          icon: Shield,
                          color: 'bg-purple-400'
                        },
                        {
                          step: 3,
                          title: 'Receive SUI',
                          description: 'Get instant SUI based on your LTV rating (up to 95%)',
                          icon: Coins,
                          color: 'bg-emerald-400'
                        },
                        {
                          step: 4,
                          title: 'Repay When Ready',
                          description: 'Repay anytime to unlock your XFT - no interest charges',
                          icon: Unlock,
                          color: 'bg-yellow-400'
                        }
                      ].map((step) => {
                        const Icon = step.icon
                        return (
                          <div key={step.step} className="flex items-start space-x-4 p-4 rounded-xl bg-white/5">
                            <div className={`flex-shrink-0 w-8 h-8 rounded-full ${step.color} flex items-center justify-center`}>
                              <Icon className="w-4 h-4 text-white" />
                            </div>
                            <div className="flex-1">
                              <h4 className="font-semibold text-white text-sm mb-1">
                                {step.step}. {step.title}
                              </h4>
                              <p className="text-xs text-gray-400 leading-relaxed">
                                {step.description}
                              </p>
                            </div>
                          </div>
                        )
                      })}
                    </div>

                    <div className="p-4 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                      <div className="flex items-start space-x-3">
                        <AlertCircle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <h4 className="font-semibold text-yellow-300 text-sm mb-1">
                            Important Note
                          </h4>
                          <p className="text-xs text-gray-300 leading-relaxed">
                            If you default on your loan, your XFT collateral may be liquidated to recover the borrowed amount.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 2 && (
              <Card
                variant="glass"
                padding="lg"
                className="border border-purple-400/30 bg-gradient-to-br from-purple-400/10 to-violet-600/15"
              >
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-white font-heading mb-4">
                      Dynamic LTV System
                    </h3>
                  </div>

                  <div className="space-y-4">
                    <div className="p-4 rounded-xl bg-purple-400/10 border border-purple-400/20">
                      <p className="text-gray-200 font-body leading-relaxed text-center">
                        Your Loan-to-Value ratio increases with successful repayments and decreases with defaults
                      </p>
                    </div>

                    <div className="space-y-3">
                      <h4 className="text-lg font-semibold text-purple-300 font-heading">
                        LTV Rating Levels
                      </h4>
                      <div className="space-y-2">
                        {ltvBenefits.map((level, index) => (
                          <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-white/5">
                            <div className="flex items-center space-x-3">
                              <div className={`w-3 h-3 rounded-full ${level.color.replace('text-', 'bg-')}`} />
                              <div>
                                <span className={`font-semibold text-sm ${level.color}`}>
                                  {level.ltv}% LTV
                                </span>
                                <span className="text-gray-400 text-xs ml-2">
                                  ({level.status})
                                </span>
                              </div>
                            </div>
                            <p className="text-xs text-gray-400">
                              {level.description}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-3 rounded-xl bg-gradient-to-r from-emerald-400/20 to-green-600/20 border border-emerald-400/30">
                        <TrendingUp className="w-6 h-6 text-emerald-400 mx-auto mb-2" />
                        <p className="text-xs text-emerald-300 font-medium mb-1">Successful Repayment</p>
                        <p className="text-sm font-bold text-white">+5% LTV</p>
                      </div>
                      <div className="text-center p-3 rounded-xl bg-gradient-to-r from-red-400/20 to-pink-600/20 border border-red-400/30">
                        <AlertCircle className="w-6 h-6 text-red-400 mx-auto mb-2" />
                        <p className="text-xs text-red-300 font-medium mb-1">Default</p>
                        <p className="text-sm font-bold text-white">-15% LTV</p>
                      </div>
                    </div>

                    <div className="p-4 rounded-xl bg-blue-400/10 border border-blue-400/20">
                      <div className="flex items-start space-x-3">
                        <Info className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <h4 className="font-semibold text-blue-300 text-sm mb-1">
                            Building Your Credit
                          </h4>
                          <p className="text-xs text-gray-300 leading-relaxed">
                            Start at 45% LTV and work your way up to 95% by consistently repaying loans on time. Your rating is permanent and builds over time.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 3 && (
              <Card
                variant="glass"
                padding="lg"
                className="border border-orange-400/30 bg-gradient-to-br from-orange-400/10 to-red-600/15"
              >
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-white font-heading mb-4">
                      Buy Defaulted Loans
                    </h3>
                  </div>

                  <div className="space-y-4">
                    <div className="p-4 rounded-xl bg-orange-400/10 border border-orange-400/20">
                      <p className="text-gray-200 font-body leading-relaxed text-center">
                        Purchase defaulted loans at a discount and claim the XFT collateral
                      </p>
                    </div>

                    <div className="space-y-3">
                      <h4 className="text-lg font-semibold text-orange-300 font-heading">
                        How Default Purchases Work
                      </h4>
                      <div className="space-y-2">
                        {[
                          {
                            step: 1,
                            title: 'Loan Defaults',
                            description: 'When a borrower fails to repay, their loan becomes available for purchase',
                            icon: AlertCircle,
                            color: 'bg-red-400'
                          },
                          {
                            step: 2,
                            title: 'Discounted Price',
                            description: 'Buy the defaulted loan for 70-90% of the original loan amount',
                            icon: DollarSign,
                            color: 'bg-orange-400'
                          },
                          {
                            step: 3,
                            title: 'Claim XFT',
                            description: 'Receive the XFT collateral that was backing the loan',
                            icon: CheckCircle,
                            color: 'bg-emerald-400'
                          },
                          {
                            step: 4,
                            title: 'Profit Potential',
                            description: 'If XFT value > purchase price, you profit from the difference',
                            icon: TrendingUp,
                            color: 'bg-purple-400'
                          }
                        ].map((step) => {
                          const Icon = step.icon
                          return (
                            <div key={step.step} className="flex items-start space-x-4 p-4 rounded-xl bg-white/5">
                              <div className={`flex-shrink-0 w-8 h-8 rounded-full ${step.color} flex items-center justify-center`}>
                                <Icon className="w-4 h-4 text-white" />
                              </div>
                              <div className="flex-1">
                                <h4 className="font-semibold text-white text-sm mb-1">
                                  {step.step}. {step.title}
                                </h4>
                                <p className="text-xs text-gray-400 leading-relaxed">
                                  {step.description}
                                </p>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-3 rounded-xl bg-gradient-to-r from-orange-400/20 to-red-600/20 border border-orange-400/30">
                        <p className="text-xs text-orange-300 font-medium mb-1">Available Defaults</p>
                        <p className="text-lg font-bold text-white">7</p>
                        <p className="text-xs text-gray-400">Ready to buy</p>
                      </div>
                      <div className="text-center p-3 rounded-xl bg-gradient-to-r from-emerald-400/20 to-green-600/20 border border-emerald-400/30">
                        <p className="text-xs text-emerald-300 font-medium mb-1">Avg Discount</p>
                        <p className="text-lg font-bold text-white">25%</p>
                        <p className="text-xs text-gray-400">Below loan value</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="text-lg font-semibold text-orange-300 font-heading">
                        Investment Opportunities
                      </h4>
                      <div className="space-y-2">
                        <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                          <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                          <p className="text-sm text-gray-300 font-body">
                            <span className="font-semibold text-emerald-300">Discounted XFTs</span> - Get valuable XFTs below market price
                          </p>
                        </div>
                        <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                          <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                          <p className="text-sm text-gray-300 font-body">
                            <span className="font-semibold text-emerald-300">Immediate Ownership</span> - XFT transfers to you instantly
                          </p>
                        </div>
                        <div className="flex items-start space-x-3 p-3 rounded-xl bg-white/5">
                          <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                          <p className="text-sm text-gray-300 font-body">
                            <span className="font-semibold text-emerald-300">Help the Bank</span> - Your purchase recovers funds for the community
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 rounded-xl bg-yellow-400/10 border border-yellow-400/20">
                      <div className="flex items-start space-x-3">
                        <AlertCircle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <h4 className="font-semibold text-yellow-300 text-sm mb-1">
                            Investment Risk
                          </h4>
                          <p className="text-xs text-gray-300 leading-relaxed">
                            XFT values can fluctuate. Only purchase defaulted loans if you believe the XFT collateral is worth more than the purchase price.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="text-center pt-2">
                      <Button
                        variant="glass"
                        size="md"
                        className="w-full bg-orange-400/20 border-orange-400/30 text-orange-300 hover:bg-orange-400/30"
                      >
                        <DollarSign className="w-4 h-4 mr-2" />
                        View Available Defaults
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 4 && (
              <Card
                variant="glass"
                padding="lg"
                className="border border-yellow-400/30 bg-gradient-to-br from-yellow-400/10 to-orange-600/15"
              >
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-white font-heading mb-4">
                      Bank Statistics
                    </h3>
                  </div>

                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-4 rounded-xl bg-gradient-to-r from-blue-400/20 to-indigo-600/20 border border-blue-400/30">
                        <Building className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                        <p className="text-xs text-blue-300 font-medium mb-1">Total Vault</p>
                        <p className="text-xl font-bold text-white">{bankStats.totalVault.toLocaleString()}</p>
                        <p className="text-xs text-gray-400">SUI</p>
                      </div>
                      <div className="text-center p-4 rounded-xl bg-gradient-to-r from-emerald-400/20 to-green-600/20 border border-emerald-400/30">
                        <Users className="w-8 h-8 text-emerald-400 mx-auto mb-2" />
                        <p className="text-xs text-emerald-300 font-medium mb-1">Total Users</p>
                        <p className="text-xl font-bold text-white">{bankStats.totalUsers.toLocaleString()}</p>
                        <p className="text-xs text-gray-400">Borrowers</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-4 rounded-xl bg-gradient-to-r from-purple-400/20 to-violet-600/20 border border-purple-400/30">
                        <Coins className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                        <p className="text-xs text-purple-300 font-medium mb-1">Active Loans</p>
                        <p className="text-xl font-bold text-white">{bankStats.activeLoans}</p>
                        <p className="text-xs text-gray-400">Outstanding</p>
                      </div>
                      <div className="text-center p-4 rounded-xl bg-gradient-to-r from-yellow-400/20 to-orange-600/20 border border-yellow-400/30">
                        <Target className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                        <p className="text-xs text-yellow-300 font-medium mb-1">Avg LTV</p>
                        <p className="text-xl font-bold text-white">{bankStats.averageLTV}%</p>
                        <p className="text-xs text-gray-400">Platform Average</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-4 rounded-xl bg-gradient-to-r from-cyan-400/20 to-blue-600/20 border border-cyan-400/30">
                        <DollarSign className="w-8 h-8 text-cyan-400 mx-auto mb-2" />
                        <p className="text-xs text-cyan-300 font-medium mb-1">Total Borrowed</p>
                        <p className="text-xl font-bold text-white">{bankStats.totalBorrowed.toLocaleString()}</p>
                        <p className="text-xs text-gray-400">SUI Lifetime</p>
                      </div>
                      <div className="text-center p-4 rounded-xl bg-gradient-to-r from-red-400/20 to-pink-600/20 border border-red-400/30">
                        <AlertCircle className="w-8 h-8 text-red-400 mx-auto mb-2" />
                        <p className="text-xs text-red-300 font-medium mb-1">Default Rate</p>
                        <p className="text-xl font-bold text-white">{bankStats.defaultRate}%</p>
                        <p className="text-xs text-gray-400">Very Low</p>
                      </div>
                    </div>

                    <div className="p-4 rounded-xl bg-emerald-400/10 border border-emerald-400/20">
                      <div className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <h4 className="font-semibold text-emerald-300 text-sm mb-1">
                            Healthy Bank Metrics
                          </h4>
                          <p className="text-xs text-gray-300 leading-relaxed">
                            Low default rate and high vault balance ensure sustainable lending for the community. The bank is fully funded by platform fees.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}
          </motion.div>
        </motion.section>

        {/* Call to Action */}
        <motion.section variants={staggerItem} className="text-center pt-8 pb-32">
          <Card
            variant="glass"
            padding="lg"
            className="border border-blue-400/30 bg-gradient-to-br from-blue-400/15 to-indigo-600/20"
          >
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="text-5xl">🏦</div>
                <div className="space-y-2">
                  <h3 className="text-2xl font-bold text-white font-heading">
                    Ready to Borrow?
                  </h3>
                  <div className="w-20 h-0.5 bg-gradient-to-r from-blue-400 to-indigo-600 rounded-full mx-auto" />
                </div>
                <p className="text-gray-300 font-body leading-relaxed max-w-sm mx-auto">
                  Connect your wallet and use your XFT as collateral for instant 0% interest loans
                </p>
              </div>

              <div className="space-y-3">
                <Button
                  variant="primary"
                  size="lg"
                  className="w-full bg-gradient-to-r from-blue-400 to-indigo-600 hover:from-blue-500 hover:to-indigo-700"
                >
                  <Building className="w-5 h-5 mr-2" />
                  Get a Loan
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>

                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant="glass"
                    size="md"
                    className="bg-blue-400/20 border-blue-400/30 text-blue-300 hover:bg-blue-400/30"
                  >
                    View My Loans
                  </Button>
                  <Button
                    variant="glass"
                    size="md"
                    className="bg-orange-400/20 border-orange-400/30 text-orange-300 hover:bg-orange-400/30"
                  >
                    <DollarSign className="w-4 h-4 mr-1" />
                    Buy Defaults
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </motion.section>
      </motion.div>
    </MobileLayout>
  )
}
