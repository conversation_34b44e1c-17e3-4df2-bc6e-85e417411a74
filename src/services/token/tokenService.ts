import { SuiClient } from '@mysten/sui/client'
import { Transaction } from '@mysten/sui/transactions'
import { TokenCreationData } from '@/components/token/TokenCreationForm'
import { calculateTokensForSol, calculateSolForTokens } from '@/services/pricing/bondingCurve'
import {
  DEXSTA_PACKAGE_ID,
  DEXSTA_FUNCTIONS,
  DEXSTA_EVENTS,
  TOKEN_REGISTRY_ID,
  TOKEN_DECIMALS,
  TOTAL_SUPPLY,
  TEMPLATE_CONSTANTS,
  DEFAULT_GAS_BUDGET,
  getSuiNetworkUrl,
  DEFAULT_NETWORK,
  suiToMist,
  mistToSui
} from '@/constants/contracts'

interface WalletProvider {
  address: string
  signAndExecuteTransaction: (transaction: any) => Promise<any>
}

export interface TokenCreationResult {
  success: boolean
  tokenObjectId?: string
  signature?: string
  tokenData?: TokenInfo
  error?: string
}

export interface TokenInfo {
  objectId: string
  creator: string
  name: string
  symbol: string
  description: string
  imageUrl: string
  totalSupply: bigint
  circulatingSupply: bigint
  liquiditySuiAmount: bigint
  creatorFeeBps: number
  rewardPotBalance: bigint
  rewardGoal: bigint
  isMigrated: boolean
}

export interface BuyTokensResult {
  success: boolean
  signature?: string
  tokensReceived?: bigint
  suiSpent?: bigint
  error?: string
}

export class TokenService {
  private client: SuiClient
  private provider: WalletProvider | null = null

  constructor(networkUrl?: string) {
    const url = networkUrl || getSuiNetworkUrl(DEFAULT_NETWORK)
    this.client = new SuiClient({ url })
  }

  /**
   * Initialize the service with wallet provider
   */
  async initialize(provider: WalletProvider) {
    this.provider = provider
  }

  /**
   * Create a new token using template-based approach (Step 1: Request creation)
   */
  async createToken(tokenData: TokenCreationData): Promise<TokenCreationResult> {
    try {
      if (!this.provider) {
        throw new Error('Wallet not connected')
      }

      // Step 1: Request token creation (triggers module deployment)
      const requestResult = await this.requestTokenCreation(tokenData)
      if (!requestResult.success) {
        return requestResult
      }

      // Step 2: Listen for deployment request event and deploy module
      const deploymentResult = await this.handleModuleDeployment(requestResult.eventData!)
      if (!deploymentResult.success) {
        return deploymentResult
      }

      // Step 3: Complete token creation with deployed module info
      const completionResult = await this.completeTokenCreation(
        tokenData,
        deploymentResult.moduleInfo!
      )

      return completionResult

    } catch (error) {
      console.error('Error creating token:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Step 1: Request token creation (emits event for frontend to handle)
   */
  private async requestTokenCreation(tokenData: TokenCreationData): Promise<TokenCreationResult & { eventData?: any }> {
    try {
      const tx = new Transaction()

      // Convert SUI amount to MIST for payment
      const paymentMist = suiToMist(0.1) // 0.1 SUI creation fee
      const [coin] = tx.splitCoins(tx.gas, [paymentMist])

      // Call request_token_creation function
      tx.moveCall({
        target: DEXSTA_FUNCTIONS.TOKEN.REQUEST_CREATION,
        arguments: [
          tx.object(TOKEN_REGISTRY_ID),
          tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.name))),
          tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.symbol))),
          tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.description))),
          tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.imageUrl))),
          tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.website || ''))),
          tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.twitter || ''))),
          tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.telegram || ''))),
          tx.pure.vector('u8', Array.from(new TextEncoder().encode(tokenData.tiktok || ''))),
          tx.pure.u64(TOTAL_SUPPLY),
          tx.pure.u8(TOKEN_DECIMALS),
          tx.pure.u16(tokenData.buyFeeBps),
          tx.pure.u16(tokenData.sellFeeBps),
          tx.pure.u16(tokenData.maxWalletPercentage),
          tx.pure.u64(0), // operator_id (not used)
          tx.pure.u64(0), // link_to_label (not used for now)
          tx.pure.u64(0), // xft_id (not used)
          coin,
        ],
      })

      tx.setGasBudget(DEFAULT_GAS_BUDGET)

      const result = await this.provider!.signAndExecuteTransaction({
        transaction: tx,
        options: {
          showEffects: true,
          showEvents: true,
        },
      })

      if (result.effects?.status?.status !== 'success') {
        throw new Error(`Transaction failed: ${result.effects?.status?.error}`)
      }

      // Extract deployment request event
      const deploymentEvent = result.events?.find(
        (event: any) => event.type === DEXSTA_EVENTS.TOKEN_MODULE_DEPLOYMENT_REQUESTED
      )

      if (!deploymentEvent) {
        throw new Error('Deployment request event not found')
      }

      return {
        success: true,
        signature: result.digest,
        eventData: deploymentEvent.parsedJson
      }

    } catch (error) {
      console.error('Error requesting token creation:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to request token creation'
      }
    }
  }

  /**
   * Step 2: Handle module deployment (frontend generates and deploys module)
   */
  private async handleModuleDeployment(eventData: any): Promise<TokenCreationResult & { moduleInfo?: any }> {
    try {
      // Generate module code from template
      const moduleCode = await this.generateModuleFromTemplate(eventData)

      // Deploy module to Sui network
      const deploymentResult = await this.deployModule(moduleCode, eventData)

      return {
        success: true,
        moduleInfo: deploymentResult
      }

    } catch (error) {
      console.error('Error deploying module:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to deploy token module'
      }
    }
  }

  /**
   * Generate module code from template
   */
  private async generateModuleFromTemplate(eventData: any): Promise<string> {
    try {
      // Fetch template file
      const templateResponse = await fetch(TEMPLATE_CONSTANTS.TEMPLATE_PATH)
      if (!templateResponse.ok) {
        throw new Error('Failed to fetch template file')
      }

      let templateCode = await templateResponse.text()

      // Replace placeholders with actual values
      const moduleAddress = this.provider?.address || '0x0'
      const moduleName = `${eventData.token_name.toLowerCase().replace(/[^a-z0-9]/g, '_')}_token`
      const structName = eventData.token_symbol.toUpperCase()

      templateCode = templateCode
        .replace(new RegExp(TEMPLATE_CONSTANTS.PLACEHOLDERS.MODULE_ADDRESS, 'g'), moduleAddress)
        .replace(new RegExp(TEMPLATE_CONSTANTS.PLACEHOLDERS.MODULE_NAME, 'g'), moduleName)
        .replace(new RegExp(TEMPLATE_CONSTANTS.PLACEHOLDERS.STRUCT_NAME, 'g'), structName)

      return templateCode

    } catch (error) {
      throw new Error(`Failed to generate module code: ${error}`)
    }
  }

  /**
   * Deploy module to Sui network
   */
  private async deployModule(moduleCode: string, eventData: any): Promise<any> {
    try {
      // Note: This is a simplified version. In practice, you would:
      // 1. Compile the Move code
      // 2. Deploy the compiled bytecode
      // 3. Call the module's init function

      // For now, we'll simulate the deployment and return mock data
      // In a real implementation, you'd use Sui CLI or SDK to deploy

      const moduleAddress = this.provider?.address || '0x0'
      const moduleName = `${eventData.token_name.toLowerCase().replace(/[^a-z0-9]/g, '_')}_token`
      const structName = eventData.token_symbol.toUpperCase()

      // Simulate deployment delay
      await new Promise(resolve => setTimeout(resolve, 2000))

      return {
        moduleAddress,
        moduleName,
        tokenType: `${moduleAddress}::${moduleName}::${structName}`,
        treasuryCapId: '0x' + '1'.repeat(64), // Mock ID
        metadataId: '0x' + '2'.repeat(64), // Mock ID
      }

    } catch (error) {
      throw new Error(`Failed to deploy module: ${error}`)
    }
  }

  /**
   * Step 3: Complete token creation with deployed module info
   */
  private async completeTokenCreation(
    tokenData: TokenCreationData,
    moduleInfo: any
  ): Promise<TokenCreationResult> {
    try {
      const tx = new Transaction()

      // Call complete_token_creation function
      tx.moveCall({
        target: DEXSTA_FUNCTIONS.TOKEN.COMPLETE_CREATION,
        arguments: [
          tx.object(TOKEN_REGISTRY_ID),
          tx.pure.string(tokenData.symbol),
          tx.pure.address(moduleInfo.moduleAddress),
          tx.pure.string(moduleInfo.moduleName),
          tx.pure.string(moduleInfo.tokenType),
          tx.pure.id(moduleInfo.treasuryCapId),
          tx.pure.id(moduleInfo.metadataId),
          tx.pure.u64(TOTAL_SUPPLY),
          tx.pure.string(tokenData.website || ''),
          tx.pure.string(tokenData.twitter || ''),
          tx.pure.string(tokenData.telegram || ''),
          tx.pure.string(tokenData.tiktok || ''),
          tx.pure.u16(tokenData.buyFeeBps),
          tx.pure.u16(tokenData.sellFeeBps),
          tx.pure.u16(tokenData.maxWalletPercentage),
          tx.pure.u64(0), // link_to_label
          tx.pure.u64(0), // xft_id
        ],
      })

      tx.setGasBudget(DEFAULT_GAS_BUDGET)

      const result = await this.provider!.signAndExecuteTransaction({
        transaction: tx,
        options: {
          showEffects: true,
          showEvents: true,
          showObjectChanges: true,
        },
      })

      if (result.effects?.status?.status !== 'success') {
        throw new Error(`Transaction failed: ${result.effects?.status?.error}`)
      }

      // Extract created token pool object
      const createdObjects = result.objectChanges?.filter(
        (change: any) => change.type === 'created'
      )

      const tokenPoolObject = createdObjects?.find(
        (obj: any) => obj.objectType?.includes('TokenPool')
      )

      if (!tokenPoolObject) {
        throw new Error('Token pool object not found in transaction result')
      }

      return {
        success: true,
        tokenObjectId: tokenPoolObject.objectId,
        signature: result.digest,
      }

    } catch (error) {
      console.error('Error completing token creation:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to complete token creation'
      }
    }
  }

  /**
   * Buy tokens using SUI
   */
  async buyTokens(tokenObjectId: string, suiAmount: number): Promise<BuyTokensResult> {
    try {
      if (!this.provider) {
        throw new Error('Wallet not connected')
      }

      const suiMist = suiToMist(suiAmount)
      const tx = new Transaction()

      // Split SUI coin for the purchase
      const [coin] = tx.splitCoins(tx.gas, [suiMist])

      // Call the buy_tokens function
      tx.moveCall({
        target: DEXSTA_FUNCTIONS.TOKEN.BUY,
        arguments: [
          tx.object(tokenObjectId),
          coin,
          tx.pure.u64(0), // minimum tokens out (calculated by contract)
        ],
      })

      tx.setGasBudget(DEFAULT_GAS_BUDGET)

      const result = await this.provider.signAndExecuteTransaction({
        transaction: tx,
        options: {
          showEffects: true,
          showEvents: true,
        },
      })

      if (result.effects?.status?.status !== 'success') {
        throw new Error(`Transaction failed: ${result.effects?.status?.error}`)
      }

      // Extract tokens received from events
      const tokenSwapEvent = result.events?.find(
        (event: any) => event.type?.includes('TokenSwapped')
      )

      const tokensReceived = tokenSwapEvent?.parsedJson?.tokens_out || BigInt(0)

      return {
        success: true,
        signature: result.digest,
        tokensReceived: BigInt(tokensReceived),
        suiSpent: suiMist
      }

    } catch (error) {
      console.error('Error buying tokens:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Sell tokens for SUI
   */
  async sellTokens(tokenObjectId: string, tokenAmount: bigint): Promise<BuyTokensResult> {
    try {
      if (!this.provider) {
        throw new Error('Wallet not connected')
      }

      const tx = new Transaction()

      // Call the sell_tokens function
      tx.moveCall({
        target: DEXSTA_FUNCTIONS.TOKEN.SELL,
        arguments: [
          tx.object(tokenObjectId),
          tx.pure.u64(tokenAmount),
          tx.pure.u64(0), // minimum SUI out (calculated by contract)
        ],
      })

      tx.setGasBudget(DEFAULT_GAS_BUDGET)

      const result = await this.provider.signAndExecuteTransaction({
        transaction: tx,
        options: {
          showEffects: true,
          showEvents: true,
        },
      })

      if (result.effects?.status?.status !== 'success') {
        throw new Error(`Transaction failed: ${result.effects?.status?.error}`)
      }

      // Extract SUI received from events
      const tokenSwapEvent = result.events?.find(
        (event: any) => event.type?.includes('TokenSwapped')
      )

      const suiReceived = tokenSwapEvent?.parsedJson?.sui_out || BigInt(0)

      return {
        success: true,
        signature: result.digest,
        tokensReceived: BigInt(0), // We sold tokens, so 0 tokens received
        suiSpent: BigInt(0) // We received SUI, so 0 SUI spent
      }

    } catch (error) {
      console.error('Error selling tokens:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Get token information
   */
  async getTokenInfo(tokenObjectId: string): Promise<TokenInfo | null> {
    try {
      const tokenObject = await this.client.getObject({
        id: tokenObjectId,
        options: {
          showContent: true,
        },
      })

      if (!tokenObject.data?.content || tokenObject.data.content.dataType !== 'moveObject') {
        return null
      }

      const fields = (tokenObject.data.content as any).fields

      return {
        objectId: tokenObjectId,
        creator: fields.creator,
        name: fields.name,
        symbol: fields.symbol,
        description: fields.description,
        imageUrl: fields.icon_url || fields.image_url, // Contract uses icon_url
        totalSupply: BigInt(fields.total_supply),
        circulatingSupply: BigInt(fields.total_supply - fields.token_reserve), // Calculate from reserves
        liquiditySuiAmount: BigInt(fields.sui_reserve?.value || 0), // SUI reserve balance
        creatorFeeBps: fields.buy_fee_bps || 0, // Contract uses buy_fee_bps
        rewardPotBalance: BigInt(fields.reward_pot?.value || 0), // Reward pot balance
        rewardGoal: BigInt(fields.reward_goal || 0),
        isMigrated: fields.has_migrated || false // Contract uses has_migrated
      }

    } catch (error) {
      console.error('Error fetching token info:', error)
      return null
    }
  }

  /**
   * Get all tokens created by a user
   */
  async getUserTokens(userAddress: string): Promise<TokenInfo[]> {
    try {
      // Query for objects owned by the user with TokenData type
      const ownedObjects = await this.client.getOwnedObjects({
        owner: userAddress,
        filter: {
          StructType: `${TOKEN_PACKAGE_ID_DEXSTA}::token::TokenData`
        },
        options: {
          showContent: true,
        },
      })

      const tokens: TokenInfo[] = []

      for (const obj of ownedObjects.data) {
        if (obj.data?.content && obj.data.content.dataType === 'moveObject') {
          const fields = (obj.data.content as any).fields
          tokens.push({
            objectId: obj.data.objectId,
            creator: fields.creator,
            name: fields.name,
            symbol: fields.symbol,
            description: fields.description,
            imageUrl: fields.icon_url || fields.image_url, // Contract uses icon_url
            totalSupply: BigInt(fields.total_supply),
            circulatingSupply: BigInt(fields.total_supply - fields.token_reserve), // Calculate from reserves
            liquiditySuiAmount: BigInt(fields.sui_reserve?.value || 0), // SUI reserve balance
            creatorFeeBps: fields.buy_fee_bps || 0, // Contract uses buy_fee_bps
            rewardPotBalance: BigInt(fields.reward_pot?.value || 0), // Reward pot balance
            rewardGoal: BigInt(fields.reward_goal || 0),
            isMigrated: fields.has_migrated || false // Contract uses has_migrated
          })
        }
      }

      return tokens

    } catch (error) {
      console.error('Error fetching user tokens:', error)
      return []
    }
  }
}

// Export singleton instance
let tokenServiceInstance: TokenService | null = null

export function getTokenService(networkUrl?: string): TokenService {
  if (!tokenServiceInstance) {
    tokenServiceInstance = new TokenService(networkUrl)
  }
  return tokenServiceInstance
}
