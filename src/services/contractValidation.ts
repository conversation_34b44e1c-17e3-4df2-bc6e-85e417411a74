/**
 * Contract Validation Service
 * 
 * This service defines the exact validation logic that must be implemented
 * in the Sui smart contracts to prevent unauthorized token creation.
 * 
 * Two validation layers:
 * 1. Frontend validation - Real-time user feedback (this file)
 * 2. Contract validation - Security enforcement (in Sui Move contracts)
 */

export interface LabelValidationResult {
  isValid: boolean
  exists: boolean
  notExpired: boolean
  ownedByUser: boolean
  error?: string
}

export interface OperatorLicenseValidationResult {
  isValid: boolean
  exists: boolean
  linkedToLabel: boolean
  ownedByUser: boolean
  notExpired: boolean
  isActive: boolean
  hasMintRole: boolean
  error?: string
}

export interface NFTValidationResult {
  isValid: boolean
  exists: boolean
  linkedToLabel: boolean
  validFormat: boolean
  error?: string
}

/**
 * CONTRACT VALIDATION REQUIREMENTS
 * 
 * These checks MUST be implemented in the Sui smart contract mint functions
 * to prevent unauthorized token creation and ensure security.
 */

export class ContractValidationRequirements {
  /**
   * LABEL VALIDATION (for token linking)
   * 
   * Contract function: mint_token_with_label()
   * Required checks in smart contract:
   */
  static validateLabelOwnership = {
    description: "Validate user owns the label they're linking to",
    checks: [
      "1. Label exists in global registry (getLabel(labelId) returns Some)",
      "2. Label is not expired (label.expiration > tx_context::epoch(ctx))",
      "3. User owns the label (label.owner == tx_context::sender(ctx))"
    ],
    contractCode: `
      // In mint_token_with_label function:
      let label = label_registry::get_label(label_id);
      assert!(option::is_some(&label), ELabelNotFound);
      
      let label_obj = option::extract(&mut label);
      assert!(label_obj.expiration > tx_context::epoch(ctx), ELabelExpired);
      assert!(label_obj.owner == tx_context::sender(ctx), ENotLabelOwner);
    `
  }

  /**
   * OPERATOR LICENSE VALIDATION (for minting under others' labels)
   * 
   * Contract function: mint_token_with_operator_license()
   * Required checks in smart contract:
   */
  static validateOperatorLicense = {
    description: "Validate operator license for minting under others' labels",
    checks: [
      "1. Operator license exists (getLicense(licenseId) returns Some)",
      "2. License is linked to target label (license.linked_label_id == labelId)",
      "3. User owns the license (license.owner == tx_context::sender(ctx))",
      "4. License is not expired (license.expiration > tx_context::epoch(ctx))",
      "5. Operator is active (license.status == ACTIVE)",
      "6. Operator has MINT role (license.role == 1)"
    ],
    contractCode: `
      // In mint_token_with_operator_license function:
      let license = operator_registry::get_license(license_id);
      assert!(option::is_some(&license), ELicenseNotFound);
      
      let license_obj = option::extract(&mut license);
      assert!(license_obj.linked_label_id == label_id, ELicenseNotLinkedToLabel);
      assert!(license_obj.owner == tx_context::sender(ctx), ENotLicenseOwner);
      assert!(license_obj.expiration > tx_context::epoch(ctx), ELicenseExpired);
      assert!(license_obj.status == ACTIVE, EOperatorInactive);
      assert!(license_obj.role == 1, EInsufficientPermissions); // 1 = MINT role
    `
  }

  /**
   * PRIVATE POOL NFT VALIDATION (for NFT-gated trading)
   * 
   * Contract function: create_private_pool()
   * Required checks in smart contract:
   */
  static validatePrivatePoolNFT = {
    description: "Validate NFT for private pool gating",
    checks: [
      "1. NFT exists in XFT registry (getXFT(nftId) returns Some)",
      "2. NFT is linked to the same label as the token (xft.linked_label_id == token.linked_label_id)",
      "3. NFT object ID format is valid (proper Sui object ID)"
    ],
    contractCode: `
      // In create_private_pool function:
      let xft = xft_registry::get_xft(nft_id);
      assert!(option::is_some(&xft), ENFTNotFound);
      
      let xft_obj = option::extract(&mut xft);
      assert!(xft_obj.linked_label_id == token.linked_label_id, ENFTNotLinkedToLabel);
      
      // Object ID validation is handled by Sui runtime
    `
  }

  /**
   * MIGRATION VALIDATION (when moving to Raydium/other DEX)
   * 
   * Contract function: migrate_to_external_pool()
   * Required data to pass:
   */
  static migrationRequirements = {
    description: "Data that must be passed during pool migration",
    requiredData: [
      "1. Label link (if token is linked to a label)",
      "2. NFT ID (if pool is private/NFT-gated)",
      "3. Operator license ID (if token was created with operator license)",
      "4. Original creator address",
      "5. Fee structure (buy_fee, sell_fee)"
    ],
    contractCode: `
      // Migration data structure:
      struct MigrationData {
        linked_label_id: Option<u64>,
        required_nft_id: Option<address>,
        operator_license_id: Option<u64>,
        original_creator: address,
        buy_fee_bps: u64,
        sell_fee_bps: u64
      }
    `
  }

  /**
   * OWNERSHIP TRANSFER VALIDATION (when changing token ownership)
   * 
   * Contract function: transfer_token_ownership()
   * Required re-validation:
   */
  static ownershipTransferValidation = {
    description: "Re-validate all permissions when ownership changes",
    checks: [
      "1. If linking to new label: validate new owner owns the new label",
      "2. If swapping NFT for private pool: validate new NFT is linked to label",
      "3. If using new operator license: validate all operator license requirements",
      "4. Maintain audit trail of ownership changes"
    ],
    contractCode: `
      // In transfer_token_ownership function:
      if (new_label_id != old_label_id) {
        // Re-validate label ownership with new owner
        validate_label_ownership(new_owner, new_label_id);
      }
      
      if (new_nft_id != old_nft_id && is_private_pool) {
        // Re-validate NFT linking
        validate_nft_linking(new_nft_id, token.linked_label_id);
      }
      
      // Emit ownership transfer event for audit trail
      event::emit(TokenOwnershipTransferred {
        token_id,
        old_owner,
        new_owner,
        timestamp: tx_context::epoch(ctx)
      });
    `
  }
}

/**
 * ERROR CODES for smart contract validation
 * 
 * These should be defined as constants in the Sui Move contracts
 */
export const CONTRACT_ERROR_CODES = {
  // Label validation errors
  ELabelNotFound: "Label does not exist in registry",
  ELabelExpired: "Label has expired",
  ENotLabelOwner: "Caller does not own the label",
  
  // Operator license errors
  ELicenseNotFound: "Operator license does not exist",
  ELicenseNotLinkedToLabel: "License is not linked to the specified label",
  ENotLicenseOwner: "Caller does not own the operator license",
  ELicenseExpired: "Operator license has expired",
  EOperatorInactive: "Operator license is inactive",
  EInsufficientPermissions: "Operator does not have MINT permission",
  
  // NFT validation errors
  ENFTNotFound: "NFT does not exist in registry",
  ENFTNotLinkedToLabel: "NFT is not linked to the specified label",
  EInvalidNFTFormat: "Invalid NFT object ID format",
  
  // General errors
  EUnauthorized: "Unauthorized operation",
  EInvalidInput: "Invalid input parameters"
} as const

/**
 * SMART CONTRACT IMPLEMENTATION REQUIREMENTS
 *
 * The following functions MUST be implemented in the Sui Move contracts:
 */

export const REQUIRED_CONTRACT_FUNCTIONS = {
  // Token creation functions with validation
  CREATE_TOKEN_WITH_LABEL: {
    function: "create_token_with_label",
    description: "Create token linked to a label (requires label ownership)",
    parameters: [
      "token_registry: &mut TokenRegistry",
      "name: String",
      "symbol: String",
      "description: String",
      "icon_url: String",
      "website: String",
      "twitter: String",
      "telegram: String",
      "tiktok: String",
      "total_supply: u64",
      "decimals: u8",
      "buy_fee_bps: u64",
      "sell_fee_bps: u64",
      "label_id: String",
      "payment: Coin<SUI>",
      "ctx: &mut TxContext"
    ],
    validation: [
      "Validate label exists in XFT registry",
      "Validate label is not expired",
      "Validate caller owns the label",
      "Store label link in token metadata"
    ]
  },

  CREATE_TOKEN_WITH_OPERATOR_LICENSE: {
    function: "create_token_with_operator_license",
    description: "Create token using operator license (mint under others' labels)",
    parameters: [
      "token_registry: &mut TokenRegistry",
      "name: String",
      "symbol: String",
      "description: String",
      "icon_url: String",
      "website: String",
      "twitter: String",
      "telegram: String",
      "tiktok: String",
      "total_supply: u64",
      "decimals: u8",
      "buy_fee_bps: u64",
      "sell_fee_bps: u64",
      "label_id: String",
      "operator_license_id: String",
      "payment: Coin<SUI>",
      "ctx: &mut TxContext"
    ],
    validation: [
      "Validate operator license exists",
      "Validate license is linked to specified label",
      "Validate caller owns the license",
      "Validate license is not expired",
      "Validate operator is active",
      "Validate operator has MINT role (role == 1)",
      "Store both label link and operator license in token metadata"
    ]
  },

  CREATE_PRIVATE_POOL: {
    function: "create_private_pool_with_nft_gating",
    description: "Create NFT-gated private pool",
    parameters: [
      "pool_registry: &mut PoolRegistry",
      "token_info_id: ID",
      "required_nft_id: String",
      "payment: Coin<SUI>",
      "ctx: &mut TxContext"
    ],
    validation: [
      "Validate NFT exists in XFT registry",
      "Validate NFT is linked to same label as token",
      "Validate NFT object ID format",
      "Store NFT requirement in pool metadata"
    ]
  },

  MIGRATE_TO_EXTERNAL_POOL: {
    function: "migrate_to_external_pool",
    description: "Migrate token to external DEX with validation data",
    parameters: [
      "token_info_id: ID",
      "external_pool_address: address",
      "migration_data: MigrationData",
      "ctx: &mut TxContext"
    ],
    validation: [
      "Validate caller owns the token",
      "Pass all validation data to external pool",
      "Emit migration event with all metadata"
    ]
  },

  TRANSFER_TOKEN_OWNERSHIP: {
    function: "transfer_token_ownership_with_revalidation",
    description: "Transfer ownership with re-validation of permissions",
    parameters: [
      "token_info_id: ID",
      "new_owner: address",
      "new_label_id: Option<String>",
      "new_nft_id: Option<String>",
      "new_operator_license_id: Option<String>",
      "ctx: &mut TxContext"
    ],
    validation: [
      "If new_label_id provided: validate new owner owns new label",
      "If new_nft_id provided: validate NFT is linked to label",
      "If new_operator_license_id provided: validate all operator requirements",
      "Emit ownership transfer event for audit trail"
    ]
  }
}

/**
 * GETTER FUNCTIONS FOR FRONTEND VALIDATION
 *
 * These functions should be implemented for real-time validation in the frontend:
 */

export const REQUIRED_GETTER_FUNCTIONS = {
  // Label validation getters
  GET_LABEL_BY_ID: "get_label_by_global_id(label_id: String): Option<Label>",
  GET_LABEL_OWNER: "get_label_owner(label_id: String): Option<address>",
  IS_LABEL_EXPIRED: "is_label_expired(label_id: String): bool",

  // Operator license validation getters
  GET_LICENSE_BY_ID: "get_license_by_global_id(license_id: String): Option<OperatorLicense>",
  GET_LICENSE_OWNER: "get_license_owner(license_id: String): Option<address>",
  GET_LICENSE_LABEL_ID: "get_license_label_id(license_id: String): Option<String>",
  IS_LICENSE_EXPIRED: "is_license_expired(license_id: String): bool",
  IS_LICENSE_ACTIVE: "is_license_active(license_id: String): bool",
  GET_LICENSE_ROLE: "get_license_role(license_id: String): Option<u8>",

  // NFT validation getters
  GET_NFT_BY_ID: "get_nft_by_global_id(nft_id: String): Option<XFT>",
  GET_NFT_LABEL_ID: "get_nft_label_id(nft_id: String): Option<String>",
  NFT_EXISTS: "nft_exists(nft_id: String): bool"
}

/**
 * SECURITY NOTES:
 *
 * 1. All validation MUST happen in the smart contract, not just frontend
 * 2. Frontend validation is for UX only - contracts must be bulletproof
 * 3. Use assert! statements in Move contracts for validation
 * 4. Always validate ownership before allowing operations
 * 5. Check expiration dates for time-sensitive objects
 * 6. Validate linking relationships between objects
 * 7. Emit events for all ownership changes for audit trails
 * 8. Never trust frontend data - always re-validate in contracts
 * 9. Use proper error codes for different validation failures
 * 10. Implement getter functions for frontend real-time validation
 */
