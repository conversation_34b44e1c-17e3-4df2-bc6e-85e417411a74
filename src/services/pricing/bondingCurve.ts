/**
 * Bonding Curve Pricing Calculations
 *
 * This file implements the exact same pricing formulas used in the Sui contracts
 * to calculate token prices based on supply and demand using a power curve.
 */

// Import constants from centralized location
import {
  TOKEN_DECIMALS,
  SCALE_FACTOR,
  INITIAL_PRICE_SCALED,
  MIN_PRICE_SCALED,
  MAX_PRICE_SCALED,
  REFERENCE_AMOUNT_DIVISOR,
  POWER_LAW_EXPONENT_SCALED,
  REFERENCE_TRADE_SIZE_DIVISOR,
  MIST_PER_SUI,
  TOKENS_PER_UNIT
} from '@/constants/contracts'

// Re-export for backward compatibility
export {
  TOKEN_DECIMALS,
  SCALE_FACTOR,
  INITIAL_PRICE_SCALED,
  MIN_PRICE_SCALED,
  MAX_PRICE_SCALED,
  REFERENCE_AMOUNT_DIVISOR,
  POWER_LAW_EXPONENT_SCALED,
  REFERENCE_TRADE_SIZE_DIVISOR,
  MIST_PER_SUI,
  TO<PERSON>NS_PER_UNIT
}

// Legacy exports for backward compatibility
export const LAMPORTS_PER_SOL = MIST_PER_SUI

/**
 * Integer square root function (matches contract implementation)
 */
function integerSqrt(value: bigint): bigint {
  if (value === 0n) return 0n
  
  let x = value
  let y = (x + 1n) / 2n
  
  while (y < x) {
    x = y
    y = (x + value / x) / 2n
  }
  
  return x
}

/**
 * Power function implementation (matches contract exactly)
 */
function powerFunction(base: bigint, exponent: bigint): bigint {
  // For exponent = 2.0 (2_000_000), use base * base
  if (exponent === 2_000_000n) {
    return (base * base) / 1_000_000n
  }
  
  // For exponent = 1.8 (1_800_000), use approximation: base^1.8 ≈ base^1.5 * base^0.3
  if (exponent === 1_800_000n) {
    // Approximate base^1.8 as (base^1.5 * base^0.3)
    // base^1.5 = sqrt(base) * base
    const sqrtBase = integerSqrt(base * 1_000_000n)
    const base15 = (sqrtBase * base) / 1_000_000n
    
    // base^0.3 ≈ fifth_root(base) (approximation)
    const base03 = integerSqrt(integerSqrt(integerSqrt(base * 1_000_000n * 1_000_000n)))
    
    return (base15 * base03) / 1_000_000n
  }
  
  // For exponent = 1.5 (1_500_000), use sqrt(base) * base
  if (exponent === 1_500_000n) {
    const sqrtBase = integerSqrt(base)
    return (sqrtBase * base) / 1_000_000n
  }
  
  // For exponent = 1.2 (1_200_000), use approximation
  if (exponent === 1_200_000n) {
    const sqrtBase = integerSqrt(base * 1_000_000n)
    return (sqrtBase * base) / 1_000_000_000n
  }
  
  // Default case - linear
  return base
}

/**
 * Calculate current token price based on circulating supply
 */
export function calculateCurrentPrice(
  circulatingSupply: bigint,
  totalSupply: bigint
): bigint {
  const referenceAmount = totalSupply / REFERENCE_AMOUNT_DIVISOR
  const ratioScaled = (circulatingSupply * 1_000_000n) / referenceAmount
  const powerResult = powerFunction(ratioScaled, POWER_LAW_EXPONENT_SCALED)
  const currentPrice = (INITIAL_PRICE_SCALED * (1_000_000n + powerResult)) / 1_000_000n
  
  // Clamp to min/max bounds
  if (currentPrice < MIN_PRICE_SCALED) return MIN_PRICE_SCALED
  if (currentPrice > MAX_PRICE_SCALED) return MAX_PRICE_SCALED
  
  return currentPrice
}

/**
 * Calculate tokens received for a given SUI amount (matches contract exactly)
 */
export function calculateTokensForSui(
  suiAmount: bigint,
  currentPrice: bigint,
  circulatingSupply: bigint,
  totalSupply: bigint
): bigint {
  // For small purchases (< 0.1% of supply in SUI value), use simple division
  const referenceTradeSize = totalSupply / REFERENCE_TRADE_SIZE_DIVISOR
  const referenceSui = (referenceTradeSize * currentPrice) / SCALE_FACTOR

  if (suiAmount <= referenceSui) {
    return (suiAmount * SCALE_FACTOR) / currentPrice
  }

  // For larger purchases, simulate buying in chunks to account for price impact
  const chunks = 10n
  const suiPerChunk = suiAmount / chunks
  let remainingSui = suiAmount % chunks // Handle remainder

  let totalTokens = 0n
  let tempCirculating = circulatingSupply

  // Process each chunk
  for (let i = 0n; i < chunks; i++) {
    if (suiPerChunk === 0n) break

    // Calculate current price based on temp_circulating
    const referenceAmount = totalSupply / REFERENCE_AMOUNT_DIVISOR
    const ratioScaled = (tempCirculating * 1_000_000n) / referenceAmount
    const powerResult = powerFunction(ratioScaled, POWER_LAW_EXPONENT_SCALED)
    let chunkPrice = (INITIAL_PRICE_SCALED * (1_000_000n + powerResult)) / 1_000_000n

    // Clamp price
    if (chunkPrice < MIN_PRICE_SCALED) chunkPrice = MIN_PRICE_SCALED
    if (chunkPrice > MAX_PRICE_SCALED) chunkPrice = MAX_PRICE_SCALED

    const chunkTokens = (suiPerChunk * SCALE_FACTOR) / chunkPrice

    totalTokens += chunkTokens
    tempCirculating += chunkTokens
  }

  // Handle any remaining SUI
  if (remainingSui > 0n) {
    const referenceAmount = totalSupply / REFERENCE_AMOUNT_DIVISOR
    const ratioScaled = (tempCirculating * 1_000_000n) / referenceAmount
    const powerResult = powerFunction(ratioScaled, POWER_LAW_EXPONENT_SCALED)
    let finalPrice = (INITIAL_PRICE_SCALED * (1_000_000n + powerResult)) / 1_000_000n

    // Clamp price
    if (finalPrice < MIN_PRICE_SCALED) finalPrice = MIN_PRICE_SCALED
    if (finalPrice > MAX_PRICE_SCALED) finalPrice = MAX_PRICE_SCALED

    const finalTokens = (remainingSui * SCALE_FACTOR) / finalPrice
    totalTokens += finalTokens
  }

  return totalTokens
}

// Legacy function for backward compatibility
export function calculateTokensForSol(
  solAmount: bigint,
  currentPrice: bigint,
  circulatingSupply: bigint,
  totalSupply: bigint
): bigint {
  return calculateTokensForSui(solAmount, currentPrice, circulatingSupply, totalSupply)
}

/**
 * Calculate SUI required for a given token amount (matches contract exactly)
 */
export function calculateSuiForTokens(
  tokensRequested: bigint,
  currentPrice: bigint,
  circulatingSupply: bigint,
  totalSupply: bigint
): bigint {
  // For small purchases, use simple multiplication
  const referenceTradeSize = totalSupply / REFERENCE_TRADE_SIZE_DIVISOR

  if (tokensRequested <= referenceTradeSize) {
    return (tokensRequested * currentPrice) / SCALE_FACTOR
  }

  // For larger purchases, simulate buying in chunks
  const chunks = 10n
  const tokensPerChunk = tokensRequested / chunks
  const remainingTokens = tokensRequested % chunks

  let totalSui = 0n
  let tempCirculating = circulatingSupply

  // Process each chunk
  for (let i = 0n; i < chunks; i++) {
    if (tokensPerChunk === 0n) break

    // Calculate current price based on temp_circulating
    const referenceAmount = totalSupply / REFERENCE_AMOUNT_DIVISOR
    const ratioScaled = (tempCirculating * 1_000_000n) / referenceAmount
    const powerResult = powerFunction(ratioScaled, POWER_LAW_EXPONENT_SCALED)
    let chunkPrice = (INITIAL_PRICE_SCALED * (1_000_000n + powerResult)) / 1_000_000n

    // Clamp price
    if (chunkPrice < MIN_PRICE_SCALED) chunkPrice = MIN_PRICE_SCALED
    if (chunkPrice > MAX_PRICE_SCALED) chunkPrice = MAX_PRICE_SCALED

    const chunkSui = (tokensPerChunk * chunkPrice) / SCALE_FACTOR

    totalSui += chunkSui
    tempCirculating += tokensPerChunk
  }

  // Handle any remaining tokens
  if (remainingTokens > 0n) {
    const referenceAmount = totalSupply / REFERENCE_AMOUNT_DIVISOR
    const ratioScaled = (tempCirculating * 1_000_000n) / referenceAmount
    const powerResult = powerFunction(ratioScaled, POWER_LAW_EXPONENT_SCALED)
    let finalPrice = (INITIAL_PRICE_SCALED * (1_000_000n + powerResult)) / 1_000_000n

    // Clamp price
    if (finalPrice < MIN_PRICE_SCALED) finalPrice = MIN_PRICE_SCALED
    if (finalPrice > MAX_PRICE_SCALED) finalPrice = MAX_PRICE_SCALED

    const finalSui = (remainingTokens * finalPrice) / SCALE_FACTOR
    totalSui += finalSui
  }

  return totalSui
}

// Legacy function for backward compatibility
export function calculateSolForTokens(
  tokensRequested: bigint,
  currentPrice: bigint,
  circulatingSupply: bigint,
  totalSupply: bigint
): bigint {
  return calculateSuiForTokens(tokensRequested, currentPrice, circulatingSupply, totalSupply)
}

/**
 * Utility functions for UI display
 */

// Convert MIST to SUI for display
export function mistToSui(mist: bigint): number {
  return Number(mist) / Number(MIST_PER_SUI)
}

// Convert SUI to MIST
export function suiToMist(sui: number): bigint {
  return BigInt(Math.floor(sui * Number(MIST_PER_SUI)))
}

// Legacy functions for backward compatibility
export function lamportsToSol(lamports: bigint): number {
  return mistToSui(lamports)
}

export function solToLamports(sol: number): bigint {
  return suiToMist(sol)
}

// Convert token units to human readable
export function tokensToHuman(tokens: bigint): number {
  return Number(tokens) / Number(TOKENS_PER_UNIT)
}

// Convert human readable to token units
export function humanToTokens(human: number): bigint {
  return BigInt(Math.floor(human * Number(TOKENS_PER_UNIT)))
}

// Format price for display (price is in scaled format)
export function formatPrice(priceScaled: bigint): number {
  return Number(priceScaled) / Number(SCALE_FACTOR)
}

// Calculate price impact percentage for SUI
export function calculatePriceImpact(
  suiAmount: bigint,
  circulatingSupply: bigint,
  totalSupply: bigint
): number {
  const currentPrice = calculateCurrentPrice(circulatingSupply, totalSupply)
  const tokensOut = calculateTokensForSui(suiAmount, currentPrice, circulatingSupply, totalSupply)
  const newSupply = circulatingSupply + tokensOut
  const newPrice = calculateCurrentPrice(newSupply, totalSupply)

  const priceImpact = Number(newPrice - currentPrice) / Number(currentPrice)
  return priceImpact * 100 // Return as percentage
}

// Legacy function for backward compatibility
export function calculatePriceImpactSol(
  solAmount: bigint,
  circulatingSupply: bigint,
  totalSupply: bigint
): number {
  return calculatePriceImpact(solAmount, circulatingSupply, totalSupply)
}
