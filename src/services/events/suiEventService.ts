import { SuiClient, SuiEvent, EventId } from '@mysten/sui/client'
import {
  TOKEN_PACKAGE_ID_DEXSTA,
  POOL_PACKAGE_ID_DEXSTA,
  TOKEN_ADMIN_PACKAGE_ID,
  POOL_ADMIN_PACKAGE_ID,
  getSuiNetworkUrl,
  DEFAULT_NETWORK
} from '@/constants/contracts'

export interface TokenEvent {
  type: 'TokenCreated' | 'TokenSwapped' | 'TokenMigrated' | 'RewardClaimed'
  tokenAddress: string
  data: any
  timestamp: number
  transactionDigest: string
}

export interface PoolEvent {
  type: 'PoolCreated' | 'LiquidityAdded' | 'LiquidityRemoved' | 'PoolSwap'
  poolAddress: string
  tokenAddress: string
  data: any
  timestamp: number
  transactionDigest: string
}

export interface AdminEvent {
  type: 'PlatformSettingsUpdated' | 'AdminAdded' | 'AdminRemoved' | 'PlatformPaused'
  contractType: 'token' | 'pool'
  data: any
  timestamp: number
  transactionDigest: string
}

export type DexstaEvent = TokenEvent | PoolEvent | AdminEvent

export interface EventSubscription {
  id: string
  eventTypes: string[]
  callback: (event: DexstaEvent) => void
  isActive: boolean
}

export class SuiEventService {
  private client: SuiClient
  private subscriptions: Map<string, EventSubscription> = new Map()
  private isListening: boolean = false
  private eventCursor: EventId | null = null
  private pollInterval: NodeJS.Timeout | null = null

  constructor(networkUrl?: string) {
    const url = networkUrl || getSuiNetworkUrl(DEFAULT_NETWORK)
    this.client = new SuiClient({ url })
  }

  /**
   * Start listening for events
   */
  async startEventListening() {
    if (this.isListening) {
      console.warn('Event listening is already active')
      return
    }

    this.isListening = true
    console.log('Starting Sui event synchronization...')

    // Start polling for events
    this.pollInterval = setInterval(() => {
      this.pollEvents()
    }, 2000) // Poll every 2 seconds

    // Initial poll
    await this.pollEvents()
  }

  /**
   * Stop listening for events
   */
  stopEventListening() {
    if (!this.isListening) return

    this.isListening = false
    
    if (this.pollInterval) {
      clearInterval(this.pollInterval)
      this.pollInterval = null
    }

    console.log('Stopped Sui event synchronization')
  }

  /**
   * Subscribe to specific event types
   */
  subscribe(
    eventTypes: string[],
    callback: (event: DexstaEvent) => void
  ): string {
    const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    this.subscriptions.set(subscriptionId, {
      id: subscriptionId,
      eventTypes,
      callback,
      isActive: true
    })

    return subscriptionId
  }

  /**
   * Unsubscribe from events
   */
  unsubscribe(subscriptionId: string) {
    const subscription = this.subscriptions.get(subscriptionId)
    if (subscription) {
      subscription.isActive = false
      this.subscriptions.delete(subscriptionId)
    }
  }

  /**
   * Poll for new events
   */
  private async pollEvents() {
    try {
      // Query events from all Dexsta packages
      const eventQueries = [
        this.queryPackageEvents(TOKEN_PACKAGE_ID_DEXSTA),
        this.queryPackageEvents(POOL_PACKAGE_ID_DEXSTA),
        this.queryPackageEvents(TOKEN_ADMIN_PACKAGE_ID),
        this.queryPackageEvents(POOL_ADMIN_PACKAGE_ID)
      ]

      const eventResults = await Promise.all(eventQueries)
      const allEvents = eventResults.flat()

      // Sort events by timestamp
      allEvents.sort((a, b) => a.timestampMs - b.timestampMs)

      // Process new events
      for (const suiEvent of allEvents) {
        await this.processEvent(suiEvent)
      }

    } catch (error) {
      console.error('Error polling events:', error)
    }
  }

  /**
   * Query events from a specific package
   */
  private async queryPackageEvents(packageId: string): Promise<SuiEvent[]> {
    try {
      const response = await this.client.queryEvents({
        query: { Package: packageId },
        cursor: this.eventCursor,
        limit: 50,
        order: 'ascending'
      })

      // Update cursor for next query
      if (response.data.length > 0) {
        this.eventCursor = response.nextCursor
      }

      return response.data
    } catch (error) {
      console.error(`Error querying events for package ${packageId}:`, error)
      return []
    }
  }

  /**
   * Process a Sui event and convert to Dexsta event
   */
  private async processEvent(suiEvent: SuiEvent) {
    try {
      const dexstaEvent = this.convertSuiEventToDexstaEvent(suiEvent)
      if (!dexstaEvent) return

      // Notify all relevant subscriptions
      for (const subscription of this.subscriptions.values()) {
        if (!subscription.isActive) continue

        const shouldNotify = subscription.eventTypes.length === 0 || 
                           subscription.eventTypes.includes(dexstaEvent.type)

        if (shouldNotify) {
          try {
            subscription.callback(dexstaEvent)
          } catch (error) {
            console.error('Error in event callback:', error)
          }
        }
      }

    } catch (error) {
      console.error('Error processing event:', error)
    }
  }

  /**
   * Convert Sui event to Dexsta event format
   */
  private convertSuiEventToDexstaEvent(suiEvent: SuiEvent): DexstaEvent | null {
    const eventType = suiEvent.type
    const packageId = suiEvent.packageId
    const parsedJson = suiEvent.parsedJson as any

    // Token events
    if (packageId === TOKEN_PACKAGE_ID_DEXSTA) {
      if (eventType.includes('TokenCreated')) {
        return {
          type: 'TokenCreated',
          tokenAddress: parsedJson.token_address,
          data: {
            creator: parsedJson.creator,
            name: parsedJson.name,
            symbol: parsedJson.symbol,
            totalSupply: parsedJson.total_supply,
            initialSuiAmount: parsedJson.initial_sui_amount
          },
          timestamp: parseInt(suiEvent.timestampMs),
          transactionDigest: suiEvent.id.txDigest
        } as TokenEvent
      }

      if (eventType.includes('TokenSwapped')) {
        return {
          type: 'TokenSwapped',
          tokenAddress: parsedJson.token_address,
          data: {
            trader: parsedJson.trader,
            tokenAmount: parsedJson.token_amount,
            suiAmount: parsedJson.sui_amount,
            isBuy: parsedJson.is_buy,
            price: parsedJson.price_scaled
          },
          timestamp: parseInt(suiEvent.timestampMs),
          transactionDigest: suiEvent.id.txDigest
        } as TokenEvent
      }

      if (eventType.includes('RewardClaimed')) {
        return {
          type: 'RewardClaimed',
          tokenAddress: parsedJson.token_address,
          data: {
            winner: parsedJson.winner,
            amount: parsedJson.amount,
            tradeCount: parsedJson.trade_count,
            newGoal: parsedJson.new_goal
          },
          timestamp: parseInt(suiEvent.timestampMs),
          transactionDigest: suiEvent.id.txDigest
        } as TokenEvent
      }
    }

    // Pool events
    if (packageId === POOL_PACKAGE_ID_DEXSTA) {
      if (eventType.includes('PoolCreated')) {
        return {
          type: 'PoolCreated',
          poolAddress: parsedJson.pool_address,
          tokenAddress: parsedJson.token_address,
          data: {
            creator: parsedJson.creator,
            tokenSymbol: parsedJson.token_symbol,
            initialTokenAmount: parsedJson.initial_token_amount,
            initialSuiAmount: parsedJson.initial_sui_amount
          },
          timestamp: parseInt(suiEvent.timestampMs),
          transactionDigest: suiEvent.id.txDigest
        } as PoolEvent
      }

      if (eventType.includes('LiquidityAdded')) {
        return {
          type: 'LiquidityAdded',
          poolAddress: parsedJson.pool_address,
          tokenAddress: parsedJson.token_address,
          data: {
            provider: parsedJson.provider,
            tokenAmount: parsedJson.token_amount,
            suiAmount: parsedJson.sui_amount,
            lpTokens: parsedJson.lp_tokens
          },
          timestamp: parseInt(suiEvent.timestampMs),
          transactionDigest: suiEvent.id.txDigest
        } as PoolEvent
      }
    }

    // Admin events
    if (packageId === TOKEN_ADMIN_PACKAGE_ID || packageId === POOL_ADMIN_PACKAGE_ID) {
      if (eventType.includes('PlatformSettingsUpdated')) {
        return {
          type: 'PlatformSettingsUpdated',
          contractType: packageId === TOKEN_ADMIN_PACKAGE_ID ? 'token' : 'pool',
          data: {
            admin: parsedJson.admin,
            settings: parsedJson.settings
          },
          timestamp: parseInt(suiEvent.timestampMs),
          transactionDigest: suiEvent.id.txDigest
        } as AdminEvent
      }
    }

    return null
  }

  /**
   * Get recent events for a specific token
   */
  async getTokenEvents(tokenAddress: string, limit: number = 50): Promise<TokenEvent[]> {
    try {
      const events = await this.client.queryEvents({
        query: { 
          MoveEventType: `${TOKEN_PACKAGE_ID_DEXSTA}::token::TokenSwapped`
        },
        limit,
        order: 'descending'
      })

      return events.data
        .map(event => this.convertSuiEventToDexstaEvent(event))
        .filter((event): event is TokenEvent => 
          event !== null && 
          event.type === 'TokenSwapped' && 
          event.tokenAddress === tokenAddress
        )

    } catch (error) {
      console.error('Error fetching token events:', error)
      return []
    }
  }

  /**
   * Get recent events for a specific pool
   */
  async getPoolEvents(poolAddress: string, limit: number = 50): Promise<PoolEvent[]> {
    try {
      const events = await this.client.queryEvents({
        query: { 
          MoveEventType: `${POOL_PACKAGE_ID_DEXSTA}::pool::LiquidityAdded`
        },
        limit,
        order: 'descending'
      })

      return events.data
        .map(event => this.convertSuiEventToDexstaEvent(event))
        .filter((event): event is PoolEvent => 
          event !== null && 
          'poolAddress' in event && 
          event.poolAddress === poolAddress
        )

    } catch (error) {
      console.error('Error fetching pool events:', error)
      return []
    }
  }
}

// Export singleton instance
let eventServiceInstance: SuiEventService | null = null

export function getSuiEventService(networkUrl?: string): SuiEventService {
  if (!eventServiceInstance) {
    eventServiceInstance = new SuiEventService(networkUrl)
  }
  return eventServiceInstance
}

export function resetSuiEventService() {
  if (eventServiceInstance) {
    eventServiceInstance.stopEventListening()
    eventServiceInstance = null
  }
}
