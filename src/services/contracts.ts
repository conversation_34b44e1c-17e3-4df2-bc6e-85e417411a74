import { SuiClient } from '@mysten/sui.js/client';
import { Transaction } from '@mysten/sui/transactions';
import { WalletContextState } from '@suiet/wallet-kit';

// Contract addresses - Update these after deployment
export const CONTRACT_ADDRESSES = {
  PACKAGE_ID: process.env.NEXT_PUBLIC_PACKAGE_ID || '',
  TOKEN_REGISTRY: process.env.NEXT_PUBLIC_TOKEN_REGISTRY || '',
  POOL_REGISTRY: process.env.NEXT_PUBLIC_POOL_REGISTRY || '',
  NFT_REGISTRY: process.env.NEXT_PUBLIC_NFT_REGISTRY || '',
};

// Contract service class
export class DexstaContractService {
  private client: SuiClient;
  private wallet: WalletContextState;

  constructor(client: SuiClient, wallet: WalletContextState) {
    this.client = client;
    this.wallet = wallet;
  }

  // ========== TOKEN FUNCTIONS ==========

  async createToken(params: {
    name: string;
    symbol: string;
    description: string;
    iconUrl: string;
    website: string;
    twitter: string;
    telegram: string;
    tiktok: string;
    totalSupply: string;
    buyFeeBps: number;
    sellFeeBps: number;
    suiAmount: string; // For payment
  }) {
    const tx = new Transaction();
    
    // Split SUI for payment
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(params.suiAmount)]);
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::simple_token::create_token_simple`,
      arguments: [
        tx.object(CONTRACT_ADDRESSES.TOKEN_REGISTRY),
        tx.pure(params.name),
        tx.pure(params.symbol),
        tx.pure(params.description),
        tx.pure(params.iconUrl),
        tx.pure(params.website),
        tx.pure(params.twitter),
        tx.pure(params.telegram),
        tx.pure(params.tiktok),
        tx.pure(params.totalSupply),
        tx.pure(9), // decimals
        tx.pure(params.buyFeeBps),
        tx.pure(params.sellFeeBps),
        coin,
      ],
    });

    return await this.wallet.signAndExecuteTransaction({
      transaction: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  async createPrivateToken(params: {
    name: string;
    symbol: string;
    description: string;
    iconUrl: string;
    website: string;
    twitter: string;
    telegram: string;
    tiktok: string;
    totalSupply: string;
    buyFeeBps: number;
    sellFeeBps: number;
    requiredNftId: string;
    suiAmount: string;
  }) {
    const tx = new TransactionBlock();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(params.suiAmount)]);
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::simple_token::create_token_with_nft_gating`,
      arguments: [
        tx.object(CONTRACT_ADDRESSES.TOKEN_REGISTRY),
        tx.pure(params.name),
        tx.pure(params.symbol),
        tx.pure(params.description),
        tx.pure(params.iconUrl),
        tx.pure(params.website),
        tx.pure(params.twitter),
        tx.pure(params.telegram),
        tx.pure(params.tiktok),
        tx.pure(params.totalSupply),
        tx.pure(9),
        tx.pure(params.buyFeeBps),
        tx.pure(params.sellFeeBps),
        tx.pure([params.requiredNftId]), // Option<u64>
        coin,
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  async buyTokens(tokenInfoId: string, suiAmount: string) {
    const tx = new TransactionBlock();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(suiAmount)]);
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::simple_token::swap_sui_for_tokens`,
      arguments: [
        tx.object(tokenInfoId),
        coin,
        tx.pure('0'), // min_tokens_out (slippage protection)
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  async sellTokens(tokenInfoId: string, tokenAmount: string, minSuiOut: string = '0') {
    const tx = new TransactionBlock();
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::simple_token::swap_tokens_for_sui`,
      arguments: [
        tx.object(tokenInfoId),
        tx.pure(tokenAmount),
        tx.pure(minSuiOut),
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  async transferTokenOwnership(tokenInfoId: string, newOwner: string) {
    const tx = new TransactionBlock();
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::simple_token::transfer_ownership`,
      arguments: [
        tx.object(CONTRACT_ADDRESSES.TOKEN_REGISTRY),
        tx.object(tokenInfoId),
        tx.pure(newOwner),
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  async updateTokenFees(tokenInfoId: string, buyFeeBps: number, sellFeeBps: number) {
    const tx = new TransactionBlock();
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::simple_token::update_fees`,
      arguments: [
        tx.object(tokenInfoId),
        tx.pure(buyFeeBps),
        tx.pure(sellFeeBps),
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  async updateTokenSocials(tokenInfoId: string, socials: {
    website: string;
    twitter: string;
    telegram: string;
    tiktok: string;
  }) {
    const tx = new TransactionBlock();
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::simple_token::update_social_links`,
      arguments: [
        tx.object(tokenInfoId),
        tx.pure(socials.website),
        tx.pure(socials.twitter),
        tx.pure(socials.telegram),
        tx.pure(socials.tiktok),
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  async donateToTokenRewardPot(tokenInfoId: string, suiAmount: string) {
    const tx = new TransactionBlock();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(suiAmount)]);
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::simple_token::add_to_reward_pot`,
      arguments: [
        tx.object(tokenInfoId),
        coin,
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  // ========== POOL FUNCTIONS ==========
  // Note: Pools are automatically created during token migration
  // No manual pool creation needed - maintains single pool per token

  async buyFromPool(poolId: string, suiAmount: string) {
    const tx = new TransactionBlock();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(suiAmount)]);
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::pool::swap_sui_for_tokens`,
      arguments: [
        tx.object(poolId),
        coin,
        tx.pure('0'), // min_tokens_out
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  async sellToPool(poolId: string, tokenAmount: string, minSuiOut: string = '0') {
    const tx = new TransactionBlock();
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::pool::swap_tokens_for_sui`,
      arguments: [
        tx.object(poolId),
        tx.pure(tokenAmount),
        tx.pure(minSuiOut),
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  async donateToPoolRewardPot(poolId: string, suiAmount: string) {
    const tx = new TransactionBlock();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(suiAmount)]);
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::pool::add_to_reward_pot`,
      arguments: [
        tx.object(poolId),
        coin,
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  // ========== NFT FUNCTIONS ==========

  async mintOneOfOneNFT(params: {
    name: string;
    description: string;
    imageUrl: string;
    suiAmount: string;
  }) {
    const tx = new TransactionBlock();
    
    const [coin] = tx.splitCoins(tx.gas, [tx.pure(params.suiAmount)]);
    
    tx.moveCall({
      target: `${CONTRACT_ADDRESSES.PACKAGE_ID}::dexsta_nft::mint_one_of_one`,
      arguments: [
        tx.object(CONTRACT_ADDRESSES.NFT_REGISTRY),
        tx.pure(params.name),
        tx.pure(params.description),
        tx.pure(params.imageUrl),
        coin,
      ],
    });

    return await this.wallet.signAndExecuteTransactionBlock({
      transactionBlock: tx,
      options: {
        showEffects: true,
        showEvents: true,
      },
    });
  }

  // ========== QUERY FUNCTIONS ==========

  async getTokenInfo(tokenInfoId: string) {
    return await this.client.getObject({
      id: tokenInfoId,
      options: {
        showContent: true,
        showType: true,
      },
    });
  }

  async getPoolInfo(poolId: string) {
    return await this.client.getObject({
      id: poolId,
      options: {
        showContent: true,
        showType: true,
      },
    });
  }
}

// Helper function to create contract service instance
export const createContractService = (client: SuiClient, wallet: WalletContextState) => {
  return new DexstaContractService(client, wallet);
};
