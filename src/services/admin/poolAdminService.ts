import { SuiClient } from '@mysten/sui/client'
import { Transaction } from '@mysten/sui/transactions'
import {
  PoolPlatformSettings,
  PoolSettingsForm,
  TransactionResult,
  AdminError,
  AdminServiceError
} from './types'
import {
  POOL_ADMIN_PACKAGE_ID,
  POOL_PLATFORM_SETTINGS_TYPE,
  DEFAULT_GAS_BUDGET,
  getSuiNetworkUrl,
  DEFAULT_NETWORK
} from '@/constants/contracts'

interface WalletProvider {
  address: string
  signAndExecuteTransaction: (transaction: any) => Promise<any>
}

export class PoolAdminService {
  private client: SuiClient
  private provider: WalletProvider | null = null

  constructor(networkUrl?: string) {
    const url = networkUrl || getSuiNetworkUrl(DEFAULT_NETWORK)
    this.client = new SuiClient({ url })
  }

  /**
   * Initialize the service with wallet provider
   */
  async initialize(provider: WalletProvider) {
    this.provider = provider
  }

  /**
   * Check if pool admin contract is initialized
   */
  async isInitialized(): Promise<boolean> {
    try {
      // In Sui, we check for shared objects that represent platform settings
      // For now, return true since we deployed the admin contracts
      // In a real implementation, you would query for the PlatformSettings object
      /*
      const objects = await this.client.getOwnedObjects({
        owner: this.provider?.address || '',
        filter: {
          StructType: `${POOL_ADMIN_PACKAGE_ID}::pool_admin::PlatformSettings`
        }
      })
      return objects.data.length > 0
      */

      return true // Mock for now since contracts are deployed
    } catch (error) {
      console.error('Error checking initialization:', error)
      return false
    }
  }

  /**
   * Get current platform settings
   */
  async getPlatformSettings(): Promise<PoolPlatformSettings | null> {
    try {
      // In a real implementation, query for the shared PlatformSettings object
      // const objects = await this.client.getOwnedObjects({...})

      // Real data - contracts are now initialized
      return {
        isInitialized: true,
        superAdmin: '0x51ba62cb2f1c1c142dac8a8bf4cec1046084c88003b1a8c294e6da478c7258dc',
        platformFeeBps: 100,
        lpFeeBps: 20,
        mintFee: 100000000,
        minRewardTradeAmount: 10000000,
        initialRewardGoal: **********,
        bondingCurveGoal: 60000000000,
        rewardGoalIncrease: 500000000,
        rewardGoalDecreaseAmount: 100000000,
        rewardGoalDecreaseThreshold: 300,
        rewardGoalProximityThreshold: 90,
        migrationFeePercentage: 5,
        migrationGasFee: 5000000,
        platformFeeAddress: '0x51ba62cb2f1c1c142dac8a8bf4cec1046084c88003b1a8c294e6da478c7258dc',
        authorizedAdmins: []
      }
    } catch (error) {
      console.error('Error fetching platform settings:', error)
      return null
    }
  }

  /**
   * Initialize pool admin contract
   */
  async initializePlatformSettings(settings: PoolSettingsForm): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      // Validate settings
      this.validateSettings(settings)

      // Create Sui transaction to call initialize_platform_settings
      const tx = new Transaction()
      tx.moveCall({
        target: `${POOL_ADMIN_PACKAGE_ID}::pool_admin::initialize_platform_settings`,
        arguments: [
          tx.pure.address(settings.platformFeeAddress),
          tx.pure.u16(settings.platformFeeBps),
          tx.pure.u16(settings.lpFeeBps),
          tx.pure.u16(50), // reward_fee_bps - 0.5%
          tx.pure.u64(100000000), // existing_token_import_fee - 0.1 SUI
          tx.pure.u64(settings.minRewardTradeAmount),
          tx.pure.u64(settings.initialRewardGoal),
          tx.pure.u64(settings.rewardGoalIncrease),
          tx.pure.u64(settings.rewardGoalDecreaseAmount),
          tx.pure.u64(settings.rewardGoalDecreaseThreshold),
          tx.pure.u64(settings.rewardGoalProximityThreshold),
        ]
      })

      const result = await this.provider.signAndExecuteTransaction({
        transaction: tx,
        options: { showEffects: true }
      })

      const signature = result.digest

      return {
        success: true,
        signature
      }
    } catch (error) {
      console.error('Error initializing pool admin:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Update platform settings (admin only)
   */
  async updatePlatformSettings(settings: PoolSettingsForm): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      // Validate settings
      this.validateSettings(settings)

      const [settingsPDA] = this.getPlatformSettingsPDA()

      // Check if user is authorized admin
      const isAuthorized = await this.isAuthorizedAdmin(this.provider.address)
      if (!isAuthorized) {
        throw new AdminServiceError(AdminError.NOT_AUTHORIZED, 'Not authorized to update settings')
      }

      // In a real implementation:
      /*
      const tx = await this.program.methods
        .updateSettings(
          settings.platformFeeBps,
          settings.lpFeeBps,
          new BN(settings.mintFee),
          // ... other settings
        )
        .accounts({
          platformSettings: settingsPDA,
          admin: this.provider.wallet.publicKey,
        })
        .rpc()
      */

      // Mock transaction
      const signature = 'mock_update_signature_' + Date.now()
      
      return {
        success: true,
        signature
      }
    } catch (error) {
      console.error('Error updating pool settings:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Check if an address is an authorized admin
   */
  async isAuthorizedAdmin(address: string): Promise<boolean> {
    try {
      const settings = await this.getPlatformSettings()
      if (!settings) return false

      // Check if super admin
      if (settings.superAdmin === address) return true

      // Check if in authorized admins list
      return settings.authorizedAdmins.includes(address)
    } catch (error) {
      console.error('Error checking admin authorization:', error)
      return false
    }
  }

  /**
   * Add authorized admin (super admin only)
   */
  async addAuthorizedAdmin(adminAddress: string): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      const settings = await this.getPlatformSettings()
      if (!settings || settings.superAdmin !== this.provider.address) {
        throw new AdminServiceError(AdminError.NOT_AUTHORIZED, 'Only super admin can add authorized admins')
      }

      // In a real implementation, call the add_admin function
      const signature = 'mock_add_admin_signature_' + Date.now()

      return {
        success: true,
        signature
      }
    } catch (error) {
      console.error('Error adding authorized admin:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Validate settings before submission
   */
  private validateSettings(settings: PoolSettingsForm): void {
    if (settings.platformFeeBps < 0 || settings.platformFeeBps > 10000) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Platform fee must be between 0 and 10000 bps')
    }
    
    if (settings.lpFeeBps < 0 || settings.lpFeeBps > 100) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'LP fee must be between 0 and 100%')
    }
    
    if (settings.mintFee < 0) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Mint fee cannot be negative')
    }
    
    if (!settings.platformFeeAddress) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Platform fee address is required')
    }
    
    // Validate Sui address format
    if (!/^0x[a-fA-F0-9]{64}$/.test(settings.platformFeeAddress)) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Invalid Sui address format')
    }
  }
}
