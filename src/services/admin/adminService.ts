import { SuiClient } from '@mysten/sui/client'
import { PoolAdminService } from './poolAdminService'
import { TokenAdminService } from './tokenAdminService'
import {
  AdminStatus,
  TransactionResult,
  PoolSettingsForm,
  TokenSettingsForm,
  AdminError,
  AdminServiceError
} from './types'
import {
  getSuiNetworkUrl,
  DEFAULT_NETWORK
} from '@/constants/contracts'

interface WalletProvider {
  address: string
  signAndExecuteTransaction: (transaction: any) => Promise<any>
}

/**
 * Main admin service that coordinates both Pool and Token admin services
 */
export class AdminService {
  private poolAdminService: PoolAdminService
  private tokenAdminService: TokenAdminService
  private provider: WalletProvider | null = null

  constructor(networkUrl?: string) {
    this.poolAdminService = new PoolAdminService(networkUrl)
    this.tokenAdminService = new TokenAdminService(networkUrl)
  }

  /**
   * Initialize the service with wallet provider
   */
  async initialize(provider: WalletProvider) {
    this.provider = provider
    await this.poolAdminService.initialize(provider)
    await this.tokenAdminService.initialize(provider)
  }

  /**
   * Get comprehensive admin status
   */
  async getAdminStatus(): Promise<AdminStatus> {
    try {
      const walletConnected = this.provider !== null
      let isAdmin = false
      let isSuperAdmin = false
      let poolInitialized = false
      let tokenInitialized = false

      if (walletConnected && this.provider) {
        // Check admin status for both contracts
        const [poolAdmin, tokenAdmin, poolInit, tokenInit] = await Promise.all([
          this.poolAdminService.isAuthorizedAdmin(this.provider.address),
          this.tokenAdminService.isAuthorizedAdmin(this.provider.address),
          this.poolAdminService.isInitialized(),
          this.tokenAdminService.isInitialized()
        ])

        isAdmin = poolAdmin || tokenAdmin
        poolInitialized = poolInit
        tokenInitialized = tokenInit

        // Check if super admin for either contract
        const [poolSettings, tokenSettings] = await Promise.all([
          this.poolAdminService.getPlatformSettings(),
          this.tokenAdminService.getPlatformSettings()
        ])

        isSuperAdmin = (poolSettings?.superAdmin === this.provider.address) ||
                      (tokenSettings?.superAdmin === this.provider.address) ||
                      false
      }

      return {
        walletConnected,
        isAdmin,
        isSuperAdmin,
        poolInitialized,
        tokenInitialized
      }
    } catch (error) {
      console.error('Error getting admin status:', error)
      return {
        walletConnected: false,
        isAdmin: false,
        isSuperAdmin: false,
        poolInitialized: false,
        tokenInitialized: false
      }
    }
  }

  /**
   * Initialize pool admin contract
   */
  async initializePoolContract(settings: PoolSettingsForm): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      return await this.poolAdminService.initializePlatformSettings(settings)
    } catch (error) {
      console.error('Error initializing pool contract:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Initialize token admin contract
   */
  async initializeTokenContract(settings: TokenSettingsForm): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      return await this.tokenAdminService.initializePlatformSettings(settings)
    } catch (error) {
      console.error('Error initializing token contract:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Update pool settings
   */
  async updatePoolSettings(settings: PoolSettingsForm): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      return await this.poolAdminService.updatePlatformSettings(settings)
    } catch (error) {
      console.error('Error updating pool settings:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Update token settings
   */
  async updateTokenSettings(settings: TokenSettingsForm): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      return await this.tokenAdminService.updatePlatformSettings(settings)
    } catch (error) {
      console.error('Error updating token settings:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Add authorized admin to pool contract
   */
  async addPoolAdmin(adminAddress: string): Promise<TransactionResult> {
    return await this.poolAdminService.addAuthorizedAdmin(adminAddress)
  }

  /**
   * Add authorized admin to token contract
   */
  async addTokenAdmin(adminAddress: string): Promise<TransactionResult> {
    return await this.tokenAdminService.addAuthorizedAdmin(adminAddress)
  }

  /**
   * Get pool platform settings
   */
  async getPoolSettings() {
    return await this.poolAdminService.getPlatformSettings()
  }

  /**
   * Get token platform settings
   */
  async getTokenSettings() {
    return await this.tokenAdminService.getPlatformSettings()
  }

  /**
   * Check if wallet is authorized for pool admin
   */
  async isPoolAdmin(address: string): Promise<boolean> {
    return await this.poolAdminService.isAuthorizedAdmin(address)
  }

  /**
   * Check if wallet is authorized for token admin
   */
  async isTokenAdmin(address: string): Promise<boolean> {
    return await this.tokenAdminService.isAuthorizedAdmin(address)
  }

  /**
   * Check if the current wallet is the deployer (super admin)
   */
  isDeployerWallet(address: string): boolean {
    const deployerAddress = '0x7e1b89254c8f898b1a835a7819cfb5a4e1de6a186fcfcbd39a07a4d52c060c5e'
    return address === deployerAddress
  }

  /**
   * Validate Sui address
   */
  static isValidSuiAddress(address: string): boolean {
    try {
      // Basic Sui address validation - should be 64 characters hex string starting with 0x
      return /^0x[a-fA-F0-9]{64}$/.test(address)
    } catch {
      return false
    }
  }

  /**
   * Format MIST to SUI
   */
  static formatSUI(mist: number): string {
    return (mist / 1_000_000_000).toFixed(3) + ' SUI'
  }

  /**
   * Format basis points to percentage
   */
  static formatBPS(bps: number): string {
    return (bps / 100).toFixed(2) + '%'
  }

  /**
   * Convert SUI to MIST
   */
  static suiToMist(sui: number): number {
    return Math.floor(sui * 1_000_000_000)
  }

  /**
   * Convert percentage to basis points
   */
  static percentageToBPS(percentage: number): number {
    return Math.floor(percentage * 100)
  }


}

// Export singleton instance
let adminServiceInstance: AdminService | null = null

export const getAdminService = (networkUrl?: string): AdminService => {
  if (!adminServiceInstance) {
    adminServiceInstance = new AdminService(networkUrl)
  }
  return adminServiceInstance
}

export const resetAdminService = () => {
  adminServiceInstance = null
}
