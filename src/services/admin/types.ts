// Pool Admin Types
export interface PoolPlatformSettings {
  isInitialized: boolean
  superAdmin: string
  platformFeeBps: number
  lpFeeBps: number
  mintFee: number
  minRewardTradeAmount: number
  initialRewardGoal: number
  bondingCurveGoal: number
  rewardGoalIncrease: number
  rewardGoalDecreaseAmount: number
  rewardGoalDecreaseThreshold: number
  rewardGoalProximityThreshold: number
  migrationFeePercentage: number
  migrationGasFee: number
  platformFeeAddress: string
  authorizedAdmins: string[]
}

// Token Admin Types
export interface TokenPlatformSettings {
  isInitialized: boolean
  superAdmin: string
  platformFeeBps: number
  rewardFeeBps: number
  mintFee: number
  minRewardTradeAmount: number
  initialRewardGoal: number
  bondingCurveGoal: number
  rewardGoalIncrease: number
  rewardGoalDecreaseAmount: number
  rewardGoalDecreaseThreshold: number
  rewardGoalProximityThreshold: number
  migrationFeePercentage: number
  migrationGasFee: number
  platformFeeAddress: string
  authorizedAdmins: string[]
}

// Form Types for UI
export interface PoolSettingsForm {
  platformFeeBps: number
  lpFeeBps: number
  mintFee: number
  minRewardTradeAmount: number
  initialRewardGoal: number
  bondingCurveGoal: number
  rewardGoalIncrease: number
  rewardGoalDecreaseAmount: number
  rewardGoalDecreaseThreshold: number
  rewardGoalProximityThreshold: number
  migrationFeePercentage: number
  migrationGasFee: number
  platformFeeAddress: string
}

export interface TokenSettingsForm {
  platformFeeBps: number
  rewardFeeBps: number
  mintFee: number
  minRewardTradeAmount: number
  initialRewardGoal: number
  bondingCurveGoal: number
  rewardGoalIncrease: number
  rewardGoalDecreaseAmount: number
  rewardGoalDecreaseThreshold: number
  rewardGoalProximityThreshold: number
  migrationFeePercentage: number
  migrationGasFee: number
  platformFeeAddress: string
}

// Transaction Result Types
export interface TransactionResult {
  success: boolean
  signature?: string
  error?: string
}

// Admin Status Types
export interface AdminStatus {
  walletConnected: boolean
  isAdmin: boolean
  isSuperAdmin: boolean
  poolInitialized: boolean
  tokenInitialized: boolean
}

// Import contract addresses from constants
export {
  POOL_ADMIN_PROGRAM_ID,
  TOKEN_ADMIN_PROGRAM_ID,
  POOL_PLATFORM_SETTINGS_SEED,
  TOKEN_PLATFORM_SETTINGS_SEED
} from '@/constants/contracts'

// Error Types
export enum AdminError {
  WALLET_NOT_CONNECTED = 'WALLET_NOT_CONNECTED',
  NOT_AUTHORIZED = 'NOT_AUTHORIZED',
  ALREADY_INITIALIZED = 'ALREADY_INITIALIZED',
  INVALID_SETTINGS = 'INVALID_SETTINGS',
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR'
}

export class AdminServiceError extends Error {
  constructor(
    public code: AdminError,
    message: string,
    public details?: any
  ) {
    super(message)
    this.name = 'AdminServiceError'
  }
}
