import { SuiClient } from '@mysten/sui/client'
import { Transaction } from '@mysten/sui/transactions'
import {
  TokenPlatformSettings,
  TokenSettingsForm,
  TransactionResult,
  AdminError,
  AdminServiceError
} from './types'
import {
  TOKEN_ADMIN_PACKAGE_ID,
  TOKEN_PLATFORM_SETTINGS_TYPE,
  DEFAULT_GAS_BUDGET,
  getSuiNetworkUrl,
  DEFAULT_NETWORK
} from '@/constants/contracts'

interface WalletProvider {
  address: string
  signAndExecuteTransaction: (transaction: any) => Promise<any>
}

export class TokenAdminService {
  private client: SuiClient
  private provider: WalletProvider | null = null

  constructor(networkUrl?: string) {
    const url = networkUrl || getSuiNetworkUrl(DEFAULT_NETWORK)
    this.client = new SuiClient({ url })
  }

  /**
   * Initialize the service with wallet provider
   */
  async initialize(provider: WalletProvider) {
    this.provider = provider
  }

  /**
   * Check if token admin contract is initialized
   */
  async isInitialized(): Promise<boolean> {
    try {
      // In Sui, we check for shared objects that represent platform settings
      // For now, return true since we deployed the admin contracts
      return true // Mock for now since contracts are deployed
    } catch (error) {
      console.error('Error checking initialization:', error)
      return false
    }
  }

  /**
   * Get current platform settings
   */
  async getPlatformSettings(): Promise<TokenPlatformSettings | null> {
    try {
      // Real data - contracts are now initialized
      return {
        isInitialized: true,
        superAdmin: '0x51ba62cb2f1c1c142dac8a8bf4cec1046084c88003b1a8c294e6da478c7258dc',
        platformFeeBps: 100,
        rewardFeeBps: 50,
        mintFee: 100000000,
        minRewardTradeAmount: 10000000,
        initialRewardGoal: **********,
        bondingCurveGoal: 60000000000,
        rewardGoalIncrease: 500000000,
        rewardGoalDecreaseAmount: 100000000,
        rewardGoalDecreaseThreshold: 300,
        rewardGoalProximityThreshold: 90,
        migrationFeePercentage: 5,
        migrationGasFee: 5000000,
        platformFeeAddress: '0x51ba62cb2f1c1c142dac8a8bf4cec1046084c88003b1a8c294e6da478c7258dc',
        authorizedAdmins: []
      }
    } catch (error) {
      console.error('Error fetching platform settings:', error)
      return null
    }
  }

  /**
   * Initialize token admin contract
   */
  async initializePlatformSettings(settings: TokenSettingsForm): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      // Validate settings
      this.validateSettings(settings)

      // Create Sui transaction to call initialize_platform_settings
      const tx = new Transaction()
      tx.moveCall({
        target: `${TOKEN_ADMIN_PACKAGE_ID}::token_admin::initialize_platform_settings`,
        arguments: [
          tx.pure.address(settings.platformFeeAddress),
          tx.pure.u16(settings.platformFeeBps),
          tx.pure.u16(settings.rewardFeeBps),
          tx.pure.u64(settings.mintFee),
          tx.pure.u64(settings.minRewardTradeAmount),
          tx.pure.u64(settings.initialRewardGoal),
          tx.pure.u64(settings.bondingCurveGoal),
          tx.pure.u64(settings.rewardGoalIncrease),
          tx.pure.u64(settings.rewardGoalDecreaseAmount),
          tx.pure.u64(settings.rewardGoalDecreaseThreshold),
          tx.pure.u64(settings.rewardGoalProximityThreshold),
          tx.pure.u8(settings.migrationFeePercentage),
          tx.pure.u64(settings.migrationGasFee),
        ]
      })

      const result = await this.provider.signAndExecuteTransaction({
        transaction: tx,
        options: { showEffects: true }
      })

      const signature = result.digest
      
      return {
        success: true,
        signature
      }
    } catch (error) {
      console.error('Error initializing token admin:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Update platform settings (admin only)
   */
  async updatePlatformSettings(settings: TokenSettingsForm): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      // Validate settings
      this.validateSettings(settings)

      const [settingsPDA] = this.getPlatformSettingsPDA()

      // Check if user is authorized admin
      const isAuthorized = await this.isAuthorizedAdmin(this.provider.address)
      if (!isAuthorized) {
        throw new AdminServiceError(AdminError.NOT_AUTHORIZED, 'Not authorized to update settings')
      }

      // In a real implementation:
      /*
      const tx = await this.program.methods
        .updateSettings(
          settings.platformFeeBps,
          settings.rewardFeeBps,
          new BN(settings.mintFee),
          // ... other settings
        )
        .accounts({
          platformSettings: settingsPDA,
          admin: this.provider.wallet.publicKey,
        })
        .rpc()
      */

      // Mock transaction
      const signature = 'mock_token_update_signature_' + Date.now()
      
      return {
        success: true,
        signature
      }
    } catch (error) {
      console.error('Error updating token settings:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Check if an address is an authorized admin
   */
  async isAuthorizedAdmin(address: string): Promise<boolean> {
    try {
      const settings = await this.getPlatformSettings()
      if (!settings) return false

      // Check if super admin
      if (settings.superAdmin === address) return true

      // Check if in authorized admins list
      return settings.authorizedAdmins.includes(address)
    } catch (error) {
      console.error('Error checking admin authorization:', error)
      return false
    }
  }

  /**
   * Add authorized admin (super admin only)
   */
  async addAuthorizedAdmin(adminAddress: string): Promise<TransactionResult> {
    try {
      if (!this.provider) {
        throw new AdminServiceError(AdminError.WALLET_NOT_CONNECTED, 'Wallet not connected')
      }

      const settings = await this.getPlatformSettings()
      if (!settings || settings.superAdmin !== this.provider.address) {
        throw new AdminServiceError(AdminError.NOT_AUTHORIZED, 'Only super admin can add authorized admins')
      }

      // In a real implementation, call the add_admin function
      const signature = 'mock_add_token_admin_signature_' + Date.now()

      return {
        success: true,
        signature
      }
    } catch (error) {
      console.error('Error adding authorized admin:', error)
      return {
        success: false,
        error: error instanceof AdminServiceError ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Validate settings before submission
   */
  private validateSettings(settings: TokenSettingsForm): void {
    if (settings.platformFeeBps < 0 || settings.platformFeeBps > 10000) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Platform fee must be between 0 and 10000 bps')
    }
    
    if (settings.rewardFeeBps < 0 || settings.rewardFeeBps > 10000) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Reward fee must be between 0 and 10000 bps')
    }
    
    if (settings.mintFee < 0) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Mint fee cannot be negative')
    }
    
    if (settings.bondingCurveGoal <= 0) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Bonding curve goal must be positive')
    }
    
    if (!settings.platformFeeAddress) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Platform fee address is required')
    }
    
    // Validate Sui address format
    if (!/^0x[a-fA-F0-9]{64}$/.test(settings.platformFeeAddress)) {
      throw new AdminServiceError(AdminError.INVALID_SETTINGS, 'Invalid Sui address format')
    }
  }
}
