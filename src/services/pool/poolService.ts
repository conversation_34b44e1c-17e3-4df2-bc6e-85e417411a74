import { SuiClient } from '@mysten/sui/client'
import { Transaction } from '@mysten/sui/transactions'
import {
  POOL_PACKAGE_ID_DEXSTA,
  POOL_ADMIN_PACKAGE_ID,
  POOL_DATA_TYPE,
  LP_TOKEN_TYPE,
  DEFAULT_GAS_BUDGET,
  getSuiNetworkUrl,
  DEFAULT_NETWORK,
  suiToMist,
  mistToSui
} from '@/constants/contracts'

interface WalletProvider {
  address: string
  signAndExecuteTransaction: (transaction: any) => Promise<any>
}

export interface PoolInfo {
  objectId: string
  tokenObjectId: string
  tokenSymbol: string
  tokenReserves: bigint
  suiReserves: bigint
  lpTokenSupply: bigint
  buyFeeBps: number
  sellFeeBps: number
  isActive: boolean
  creator: string
  createdAt: number
}

export interface LiquidityPosition {
  poolObjectId: string
  lpTokenAmount: bigint
  sharePercentage: number
  tokenValue: bigint
  suiValue: bigint
}

export interface PoolCreationResult {
  success: boolean
  poolObjectId?: string
  signature?: string
  error?: string
}

export interface LiquidityResult {
  success: boolean
  signature?: string
  lpTokensReceived?: bigint
  suiReceived?: bigint
  tokensReceived?: bigint
  error?: string
}

export class PoolService {
  private client: SuiClient
  private provider: WalletProvider | null = null

  constructor(networkUrl?: string) {
    const url = networkUrl || getSuiNetworkUrl(DEFAULT_NETWORK)
    this.client = new SuiClient({ url })
  }

  /**
   * Initialize the service with wallet provider
   */
  async initialize(provider: WalletProvider) {
    this.provider = provider
  }

  /**
   * Create a new liquidity pool for a token
   */
  async createPool(
    tokenObjectId: string,
    initialTokenAmount: bigint,
    initialSuiAmount: number,
    buyFeeBps: number,
    sellFeeBps: number
  ): Promise<PoolCreationResult> {
    try {
      if (!this.provider) {
        throw new Error('Wallet not connected')
      }

      const tx = new Transaction()
      const suiMist = suiToMist(initialSuiAmount)

      // Split SUI coin for initial liquidity
      const [suiCoin] = tx.splitCoins(tx.gas, [suiMist])

      // Call the create_pool function from the Sui Move contract
      tx.moveCall({
        target: `${POOL_PACKAGE_ID_DEXSTA}::pool::create_pool`,
        arguments: [
          tx.object(tokenObjectId),
          tx.pure.u64(initialTokenAmount),
          suiCoin,
          tx.pure.u16(buyFeeBps),
          tx.pure.u16(sellFeeBps),
        ],
      })

      tx.setGasBudget(DEFAULT_GAS_BUDGET)

      const result = await this.provider.signAndExecuteTransaction({
        transaction: tx,
        options: {
          showEffects: true,
          showObjectChanges: true,
        },
      })

      if (result.effects?.status?.status !== 'success') {
        throw new Error(`Transaction failed: ${result.effects?.status?.error}`)
      }

      // Extract created pool object ID from effects
      const createdObjects = result.objectChanges?.filter(
        (change: any) => change.type === 'created'
      )
      
      const poolObject = createdObjects?.find(
        (obj: any) => obj.objectType?.includes('PoolData')
      )

      if (!poolObject) {
        throw new Error('Pool object not found in transaction result')
      }

      return {
        success: true,
        poolObjectId: poolObject.objectId,
        signature: result.digest
      }

    } catch (error) {
      console.error('Error creating pool:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Add liquidity to an existing pool
   */
  async addLiquidity(
    poolObjectId: string,
    suiAmount: number,
    minLpTokens: bigint = BigInt(0)
  ): Promise<LiquidityResult> {
    try {
      if (!this.provider) {
        throw new Error('Wallet not connected')
      }

      const tx = new Transaction()
      const suiMist = suiToMist(suiAmount)

      // Split SUI coin for liquidity
      const [suiCoin] = tx.splitCoins(tx.gas, [suiMist])

      // Call the add_liquidity function
      tx.moveCall({
        target: `${POOL_PACKAGE_ID_DEXSTA}::pool::add_liquidity`,
        arguments: [
          tx.object(poolObjectId),
          suiCoin,
          tx.pure.u64(minLpTokens),
        ],
      })

      tx.setGasBudget(DEFAULT_GAS_BUDGET)

      const result = await this.provider.signAndExecuteTransaction({
        transaction: tx,
        options: {
          showEffects: true,
          showEvents: true,
        },
      })

      if (result.effects?.status?.status !== 'success') {
        throw new Error(`Transaction failed: ${result.effects?.status?.error}`)
      }

      // Extract LP tokens received from events
      const liquidityEvent = result.events?.find(
        (event: any) => event.type?.includes('LiquidityAdded')
      )

      const lpTokensReceived = liquidityEvent?.parsedJson?.lp_tokens || BigInt(0)

      return {
        success: true,
        signature: result.digest,
        lpTokensReceived: BigInt(lpTokensReceived)
      }

    } catch (error) {
      console.error('Error adding liquidity:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Remove liquidity from a pool
   */
  async removeLiquidity(
    poolObjectId: string,
    lpTokenAmount: bigint,
    minSuiOut: bigint = BigInt(0),
    minTokensOut: bigint = BigInt(0)
  ): Promise<LiquidityResult> {
    try {
      if (!this.provider) {
        throw new Error('Wallet not connected')
      }

      const tx = new Transaction()

      // Call the remove_liquidity function
      tx.moveCall({
        target: `${POOL_PACKAGE_ID_DEXSTA}::pool::remove_liquidity`,
        arguments: [
          tx.object(poolObjectId),
          tx.pure.u64(lpTokenAmount),
          tx.pure.u64(minSuiOut),
          tx.pure.u64(minTokensOut),
        ],
      })

      tx.setGasBudget(DEFAULT_GAS_BUDGET)

      const result = await this.provider.signAndExecuteTransaction({
        transaction: tx,
        options: {
          showEffects: true,
          showEvents: true,
        },
      })

      if (result.effects?.status?.status !== 'success') {
        throw new Error(`Transaction failed: ${result.effects?.status?.error}`)
      }

      // Extract amounts received from events
      const liquidityEvent = result.events?.find(
        (event: any) => event.type?.includes('LiquidityRemoved')
      )

      const suiReceived = liquidityEvent?.parsedJson?.sui_out || BigInt(0)
      const tokensReceived = liquidityEvent?.parsedJson?.tokens_out || BigInt(0)

      return {
        success: true,
        signature: result.digest,
        suiReceived: BigInt(suiReceived),
        tokensReceived: BigInt(tokensReceived)
      }

    } catch (error) {
      console.error('Error removing liquidity:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Get pool information
   */
  async getPoolInfo(poolObjectId: string): Promise<PoolInfo | null> {
    try {
      const poolObject = await this.client.getObject({
        id: poolObjectId,
        options: {
          showContent: true,
        },
      })

      if (!poolObject.data?.content || poolObject.data.content.dataType !== 'moveObject') {
        return null
      }

      const fields = (poolObject.data.content as any).fields

      return {
        objectId: poolObjectId,
        tokenObjectId: fields.token_object_id,
        tokenSymbol: fields.token_symbol,
        tokenReserves: BigInt(fields.token_reserves),
        suiReserves: BigInt(fields.sui_reserves),
        lpTokenSupply: BigInt(fields.lp_token_supply),
        buyFeeBps: fields.buy_fee_bps,
        sellFeeBps: fields.sell_fee_bps,
        isActive: fields.is_active,
        creator: fields.creator,
        createdAt: fields.created_at
      }

    } catch (error) {
      console.error('Error fetching pool info:', error)
      return null
    }
  }

  /**
   * Get all pools
   */
  async getAllPools(): Promise<PoolInfo[]> {
    try {
      // Query for all pool objects
      const poolObjects = await this.client.getOwnedObjects({
        owner: POOL_PACKAGE_ID_DEXSTA, // Pools are owned by the package
        filter: {
          StructType: `${POOL_PACKAGE_ID_DEXSTA}::pool::PoolData`
        },
        options: {
          showContent: true,
        },
      })

      const pools: PoolInfo[] = []

      for (const obj of poolObjects.data) {
        if (obj.data?.content && obj.data.content.dataType === 'moveObject') {
          const fields = (obj.data.content as any).fields
          pools.push({
            objectId: obj.data.objectId,
            tokenObjectId: fields.token_object_id,
            tokenSymbol: fields.token_symbol,
            tokenReserves: BigInt(fields.token_reserves),
            suiReserves: BigInt(fields.sui_reserves),
            lpTokenSupply: BigInt(fields.lp_token_supply),
            buyFeeBps: fields.buy_fee_bps,
            sellFeeBps: fields.sell_fee_bps,
            isActive: fields.is_active,
            creator: fields.creator,
            createdAt: fields.created_at
          })
        }
      }

      return pools

    } catch (error) {
      console.error('Error fetching pools:', error)
      return []
    }
  }

  /**
   * Get user's liquidity positions
   */
  async getUserLiquidityPositions(userAddress: string): Promise<LiquidityPosition[]> {
    try {
      // Query for LP tokens owned by the user
      const lpTokens = await this.client.getOwnedObjects({
        owner: userAddress,
        filter: {
          StructType: `${POOL_PACKAGE_ID_DEXSTA}::pool::LPToken`
        },
        options: {
          showContent: true,
        },
      })

      const positions: LiquidityPosition[] = []

      for (const token of lpTokens.data) {
        if (token.data?.content && token.data.content.dataType === 'moveObject') {
          const fields = (token.data.content as any).fields
          const poolObjectId = fields.pool_id
          const lpTokenAmount = BigInt(fields.amount)

          // Get pool info to calculate values
          const poolInfo = await this.getPoolInfo(poolObjectId)
          if (poolInfo) {
            const sharePercentage = Number(lpTokenAmount * BigInt(100)) / Number(poolInfo.lpTokenSupply)
            const tokenValue = (poolInfo.tokenReserves * lpTokenAmount) / poolInfo.lpTokenSupply
            const suiValue = (poolInfo.suiReserves * lpTokenAmount) / poolInfo.lpTokenSupply

            positions.push({
              poolObjectId,
              lpTokenAmount,
              sharePercentage,
              tokenValue,
              suiValue
            })
          }
        }
      }

      return positions

    } catch (error) {
      console.error('Error fetching user liquidity positions:', error)
      return []
    }
  }
}

// Export singleton instance
let poolServiceInstance: PoolService | null = null

export function getPoolService(networkUrl?: string): PoolService {
  if (!poolServiceInstance) {
    poolServiceInstance = new PoolService(networkUrl)
  }
  return poolServiceInstance
}
