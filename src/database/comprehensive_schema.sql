-- Comprehensive Supabase Database Schema for Dexsta Platform
-- Covers all contracts: token_admin, tokens, pools, XFTs, labels, operators, marketplace
-- Includes all form data, user settings, and complete contract ecosystem
-- Run these commands in your Supabase SQL editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- CORE USER & ADMIN TABLES
-- =====================================================

-- Users Table
-- Stores user profiles and settings
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_address VARCHAR(66) UNIQUE NOT NULL, -- Sui address
    username VARCHAR(50),
    email VARCHAR(255),
    avatar_url TEXT,
    bio TEXT,
    twitter_handle VARCHAR(50),
    discord_handle VARCHAR(50),
    telegram_handle VARCHAR(50),
    website_url TEXT,
    
    -- Trading preferences
    default_slippage_bps INTEGER DEFAULT 100, -- 1% = 100 basis points
    quick_buy_amount_1 BIGINT DEFAULT 100000000, -- 0.1 SUI in MIST
    quick_buy_amount_2 BIGINT DEFAULT 1000000000, -- 1 SUI in MIST
    quick_buy_amount_3 BIGINT DEFAULT 5000000000, -- 5 SUI in MIST
    auto_approve_transactions BOOLEAN DEFAULT false,
    show_price_in_usd BOOLEAN DEFAULT true,
    enable_notifications BOOLEAN DEFAULT true,
    enable_sound_effects BOOLEAN DEFAULT true,
    
    -- Privacy settings
    profile_visibility VARCHAR(20) DEFAULT 'public' CHECK (profile_visibility IN ('public', 'private', 'friends')),
    show_trading_history BOOLEAN DEFAULT true,
    show_portfolio_value BOOLEAN DEFAULT false,
    
    -- Platform stats
    total_trades INTEGER DEFAULT 0,
    total_volume_sui BIGINT DEFAULT 0,
    total_rewards_earned BIGINT DEFAULT 0,
    favorite_tokens TEXT[], -- Array of token addresses

    -- XFT Bank & LTV rating
    current_ltv_rating DECIMAL(5,2) DEFAULT 45.00, -- Current LTV rating (45% start)
    total_loans_taken INTEGER DEFAULT 0,
    successful_repayments INTEGER DEFAULT 0,
    defaults INTEGER DEFAULT 0,
    total_borrowed_sui BIGINT DEFAULT 0,
    total_repaid_sui BIGINT DEFAULT 0,

    -- Show Love history
    total_love_given BIGINT DEFAULT 0, -- Total SUI given as love
    total_love_received BIGINT DEFAULT 0, -- Total SUI received as love
    love_interactions_given INTEGER DEFAULT 0, -- Number of times showed love
    love_interactions_received INTEGER DEFAULT 0, -- Number of times received love
    xfts_loved BIGINT[], -- Array of XFT global IDs user showed love to

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_active_at TIMESTAMPTZ DEFAULT NOW(),
    is_verified BOOLEAN DEFAULT false,
    is_banned BOOLEAN DEFAULT false
);

-- Admins Table
-- Platform administrators and their permissions
CREATE TABLE IF NOT EXISTS admins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    wallet_address VARCHAR(66) NOT NULL,
    admin_level INTEGER NOT NULL DEFAULT 1, -- 1=Admin, 2=Super Admin, 3=Owner
    permissions JSONB DEFAULT '{}', -- Flexible permissions object
    
    -- Admin capabilities
    can_manage_users BOOLEAN DEFAULT false,
    can_manage_tokens BOOLEAN DEFAULT false,
    can_manage_pools BOOLEAN DEFAULT false,
    can_manage_labels BOOLEAN DEFAULT false,
    can_manage_marketplace BOOLEAN DEFAULT false,
    can_manage_operators BOOLEAN DEFAULT false,
    can_view_analytics BOOLEAN DEFAULT true,
    can_moderate_content BOOLEAN DEFAULT false,
    can_manage_fees BOOLEAN DEFAULT false,
    can_pause_platform BOOLEAN DEFAULT false,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMPTZ
);

-- =====================================================
-- TOKEN & POOL TABLES
-- =====================================================

-- Tokens Table
-- Complete token information from creation forms
CREATE TABLE IF NOT EXISTS tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token_address VARCHAR(66) UNIQUE NOT NULL,
    global_id BIGINT UNIQUE, -- From Fire Registry
    
    -- Basic token info (from create form)
    name VARCHAR(255) NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    description TEXT,
    image_url TEXT,
    website_url TEXT,
    twitter_url TEXT,
    telegram_url TEXT,
    discord_url TEXT,
    
    -- Token economics
    total_supply BIGINT NOT NULL,
    decimals INTEGER NOT NULL DEFAULT 6,
    creator_address VARCHAR(66) NOT NULL,
    owner_address VARCHAR(66) NOT NULL, -- Can be transferred
    
    -- Pool configuration
    private_pool BOOLEAN DEFAULT false,
    max_wallet_percentage INTEGER DEFAULT 0, -- 0 = no limit
    initial_liquidity_sui BIGINT DEFAULT 0,
    bonding_curve_type VARCHAR(50) DEFAULT 'linear',
    
    -- Label linking (optimized for performance)
    linked_label_global_id BIGINT,
    linked_label_object_id VARCHAR(66),
    linked_label_creator VARCHAR(66),
    
    -- Fee configuration
    buy_fee_bps INTEGER DEFAULT 100, -- 1% = 100 basis points
    sell_fee_bps INTEGER DEFAULT 100, -- 1% = 100 basis points
    platform_fee_bps INTEGER DEFAULT 300, -- 3% = 300 basis points
    
    -- Trading game settings
    reward_pot_enabled BOOLEAN DEFAULT true,
    initial_reward_goal INTEGER DEFAULT 50,
    current_reward_goal INTEGER DEFAULT 50,
    current_trade_count INTEGER DEFAULT 0,
    total_reward_distributed BIGINT DEFAULT 0,
    
    -- Current state
    current_price_scaled BIGINT DEFAULT 0,
    market_cap_sui BIGINT DEFAULT 0,
    total_volume_sui BIGINT DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    holder_count INTEGER DEFAULT 0,
    
    -- Migration status
    migration_status VARCHAR(20) DEFAULT 'pre-migration' CHECK (migration_status IN ('pre-migration', 'migrated', 'failed')),
    migration_tx_hash VARCHAR(66),
    migrated_at TIMESTAMPTZ,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false
);

-- Pool Admin Settings Table
-- Pool configuration and admin settings
CREATE TABLE IF NOT EXISTS pool_admin_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token_address VARCHAR(66) UNIQUE NOT NULL REFERENCES tokens(token_address),
    
    -- Pool parameters
    platform_fee_bps INTEGER DEFAULT 300,
    reward_fee_bps INTEGER DEFAULT 200,
    mint_fee BIGINT DEFAULT 100000000, -- 0.1 SUI in MIST
    min_reward_trade_amount BIGINT DEFAULT 10000000, -- 0.01 SUI
    
    -- Bonding curve settings
    bonding_curve_goal BIGINT DEFAULT 85000000000000, -- 85,000 SUI in MIST
    reward_goal_increase INTEGER DEFAULT 10,
    reward_goal_decrease_amount INTEGER DEFAULT 5,
    reward_goal_decrease_threshold INTEGER DEFAULT 10,
    reward_goal_proximity_threshold INTEGER DEFAULT 5,
    
    -- Migration settings
    migration_fee_percentage INTEGER DEFAULT 3,
    migration_gas_fee BIGINT DEFAULT 50000000, -- 0.05 SUI
    
    -- Status
    is_paused BOOLEAN DEFAULT false,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- LABEL & XFT TABLES
-- =====================================================

-- Labels Table
-- All label types and their information
CREATE TABLE IF NOT EXISTS labels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    label_object_id VARCHAR(66) UNIQUE NOT NULL,
    global_id BIGINT UNIQUE NOT NULL,
    
    -- Label classification
    label_type INTEGER NOT NULL CHECK (label_type IN (1, 2, 3, 4, 5, 6)),
    -- 1=Lead Label, 2=Profile Label, 3=Tags Label, 4=Chapters Label, 5=Label Operator License, 6=Label Marketplace License
    
    -- Basic info (from create form)
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    banner_url TEXT,
    website_url TEXT,
    social_links JSONB DEFAULT '{}', -- Flexible social media links
    
    -- Creator info
    creator_address VARCHAR(66) NOT NULL,
    owner_address VARCHAR(66) NOT NULL, -- Can be transferred
    
    -- Label configuration
    is_transferable BOOLEAN DEFAULT true,
    is_public BOOLEAN DEFAULT true,
    max_supply INTEGER, -- NULL for unlimited
    current_supply INTEGER DEFAULT 0,
    
    -- Expiration settings
    expires_at TIMESTAMPTZ,
    is_expired BOOLEAN DEFAULT false,
    
    -- License-specific fields (for operator/marketplace licenses)
    linked_to_label_id BIGINT, -- For licenses linked to main labels
    commission_fee_bps INTEGER DEFAULT 0, -- For marketplace licenses
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false
);

-- XFTs Table
-- All XFT NFTs and their properties
CREATE TABLE IF NOT EXISTS xfts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    xft_object_id VARCHAR(66) UNIQUE NOT NULL,
    global_id BIGINT UNIQUE NOT NULL,
    
    -- Basic NFT info (from create form)
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    animation_url TEXT,
    external_url TEXT,
    
    -- XFT classification
    xft_type VARCHAR(20) NOT NULL CHECK (xft_type IN ('1-of-1', 'limited-edition', 'generative')),
    edition_number INTEGER, -- For limited editions
    total_editions INTEGER, -- For limited editions
    
    -- Creator info
    creator_address VARCHAR(66) NOT NULL,
    owner_address VARCHAR(66) NOT NULL,
    minter_address VARCHAR(66), -- Who actually minted (could be operator)
    
    -- Label linking
    linked_label_global_id BIGINT REFERENCES labels(global_id),
    linked_label_object_id VARCHAR(66),
    
    -- Asset storage (for 1-of-1 XFTs)
    stored_assets JSONB DEFAULT '[]', -- Array of stored asset objects
    vault_locked BOOLEAN DEFAULT false,
    vault_unlock_date TIMESTAMPTZ,
    
    -- Time locking
    is_time_locked BOOLEAN DEFAULT false,
    unlock_timestamp BIGINT, -- Custom unlock timestamp
    
    -- Marketplace info
    is_for_sale BOOLEAN DEFAULT false,
    sale_price BIGINT,
    sale_currency VARCHAR(10) DEFAULT 'SUI',
    marketplace_license_id BIGINT, -- If sold under marketplace license
    
    -- Generative collection info
    collection_id UUID, -- Links to generative collections
    generation_seed VARCHAR(255), -- For reproducible generation
    traits JSONB DEFAULT '{}', -- NFT traits/attributes
    rarity_score DECIMAL(10,4),

    -- Show Love tracking
    total_love_received BIGINT DEFAULT 0, -- Total SUI received as love
    total_love_count INTEGER DEFAULT 0, -- Number of love interactions
    unique_lovers INTEGER DEFAULT 0, -- Number of unique users who showed love
    last_love_received TIMESTAMPTZ, -- When last love was received

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false
);

-- Operators Table
-- Label operators and their permissions
CREATE TABLE IF NOT EXISTS operators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    operator_address VARCHAR(66) NOT NULL,
    label_global_id BIGINT NOT NULL REFERENCES labels(global_id),
    license_object_id VARCHAR(66), -- Operator license XFT

    -- Operator permissions
    role INTEGER NOT NULL DEFAULT 1, -- 1=MINT, 2=MANAGE, 3=SUPER
    can_mint BOOLEAN DEFAULT true,
    can_manage_operators BOOLEAN DEFAULT false,
    can_withdraw BOOLEAN DEFAULT false,
    can_update_metadata BOOLEAN DEFAULT false,

    -- Status
    is_active BOOLEAN DEFAULT true,
    assigned_at TIMESTAMPTZ DEFAULT NOW(),
    assigned_by VARCHAR(66), -- Who assigned this operator

    -- License expiration
    license_expires_at TIMESTAMPTZ,
    is_license_expired BOOLEAN DEFAULT false,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- MARKETPLACE TABLES
-- =====================================================

-- Marketplace Listings Table
-- XFT marketplace listings
CREATE TABLE IF NOT EXISTS marketplace_listings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    listing_id BIGINT UNIQUE NOT NULL, -- On-chain listing ID

    -- Item info
    xft_object_id VARCHAR(66) NOT NULL REFERENCES xfts(xft_object_id),
    seller_address VARCHAR(66) NOT NULL,

    -- Pricing
    price BIGINT NOT NULL, -- Price in MIST
    currency VARCHAR(10) DEFAULT 'SUI',

    -- Label association
    listed_under_label_id BIGINT REFERENCES labels(global_id),
    marketplace_license_id BIGINT, -- If using marketplace license
    commission_fee_bps INTEGER DEFAULT 0,

    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'sold', 'cancelled', 'expired')),
    expires_at TIMESTAMPTZ,

    -- Sale info
    buyer_address VARCHAR(66),
    sold_at TIMESTAMPTZ,
    sold_price BIGINT,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Generative Collections Table
-- Generative XFT collections
CREATE TABLE IF NOT EXISTS generative_collections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    collection_object_id VARCHAR(66) UNIQUE NOT NULL,
    global_id BIGINT UNIQUE NOT NULL,

    -- Collection info (from create form)
    name VARCHAR(255) NOT NULL,
    description TEXT,
    base_image_url TEXT,

    -- Creator info
    creator_address VARCHAR(66) NOT NULL,
    owner_address VARCHAR(66) NOT NULL,

    -- Label linking
    linked_label_global_id BIGINT REFERENCES labels(global_id),

    -- Generation settings
    max_supply INTEGER NOT NULL,
    current_supply INTEGER DEFAULT 0,
    generation_algorithm VARCHAR(50) DEFAULT 'random',
    trait_layers JSONB DEFAULT '[]', -- Array of trait layer definitions
    rarity_weights JSONB DEFAULT '{}', -- Rarity weights for traits

    -- Pricing
    mint_price BIGINT DEFAULT 0, -- Price per mint in MIST

    -- Status
    is_active BOOLEAN DEFAULT true,
    is_revealed BOOLEAN DEFAULT false,
    reveal_date TIMESTAMPTZ,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- XFT BANK SYSTEM
-- =====================================================

-- XFT Bank Table
-- Tracks the overall bank state and SUI balance
CREATE TABLE IF NOT EXISTS xft_bank (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Bank state
    total_sui_balance BIGINT DEFAULT 0, -- Total SUI in the bank vault
    total_loans_issued BIGINT DEFAULT 0, -- Total number of loans issued
    total_loans_pending BIGINT DEFAULT 0, -- Number of active loans
    total_defaults BIGINT DEFAULT 0, -- Number of defaulted loans

    -- Financial metrics
    total_loan_amount_issued BIGINT DEFAULT 0, -- Total SUI loaned out (lifetime)
    total_loan_amount_outstanding BIGINT DEFAULT 0, -- Current outstanding loan amount
    total_defaults_amount BIGINT DEFAULT 0, -- Total SUI lost to defaults
    total_interest_earned BIGINT DEFAULT 0, -- Total interest collected

    -- Risk metrics
    default_rate_percentage DECIMAL(5,2) DEFAULT 0.00, -- Default rate as percentage
    average_ltv_ratio DECIMAL(5,2) DEFAULT 45.00, -- Average LTV across all users

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Loans Table
-- Individual loan records for users
CREATE TABLE IF NOT EXISTS user_loans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    borrower_address VARCHAR(66) NOT NULL,
    xft_object_id VARCHAR(66) NOT NULL, -- XFT used as collateral

    -- Loan details
    loan_amount BIGINT NOT NULL, -- Amount borrowed in MIST
    collateral_value BIGINT NOT NULL, -- XFT value at time of loan
    ltv_ratio DECIMAL(5,2) NOT NULL, -- Loan-to-value ratio used
    interest_rate_bps INTEGER DEFAULT 500, -- 5% = 500 basis points

    -- Loan status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'repaid', 'defaulted', 'liquidated')),
    loan_date TIMESTAMPTZ DEFAULT NOW(),
    due_date TIMESTAMPTZ NOT NULL,
    repaid_date TIMESTAMPTZ,

    -- Repayment tracking
    amount_repaid BIGINT DEFAULT 0,
    interest_paid BIGINT DEFAULT 0,
    remaining_balance BIGINT NOT NULL,

    -- Default tracking
    days_overdue INTEGER DEFAULT 0,
    default_date TIMESTAMPTZ,
    liquidation_date TIMESTAMPTZ,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User LTV Ratings Table
-- Dynamic LTV ratings for each user
CREATE TABLE IF NOT EXISTS user_ltv_ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_address VARCHAR(66) UNIQUE NOT NULL,

    -- LTV rating
    current_ltv_percentage DECIMAL(5,2) DEFAULT 45.00, -- Starts at 45%
    max_ltv_percentage DECIMAL(5,2) DEFAULT 95.00, -- Maximum possible (95%)
    min_ltv_percentage DECIMAL(5,2) DEFAULT 15.00, -- Minimum possible (15%)

    -- Performance tracking
    successful_repayments INTEGER DEFAULT 0,
    total_loans INTEGER DEFAULT 0,
    defaults INTEGER DEFAULT 0,

    -- Rating history
    last_rating_increase TIMESTAMPTZ,
    last_rating_decrease TIMESTAMPTZ,
    rating_change_reason VARCHAR(100), -- 'successful_repayment', 'default', 'manual_adjustment'

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SHOW LOVE SYSTEM
-- =====================================================

-- XFT Love History Table
-- Tracks all "Show Love" interactions
CREATE TABLE IF NOT EXISTS xft_love_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Love interaction details
    giver_address VARCHAR(66) NOT NULL, -- User who showed love
    receiver_xft_object_id VARCHAR(66) NOT NULL, -- XFT that received love
    receiver_address VARCHAR(66) NOT NULL, -- Owner of the XFT

    -- Love amount
    love_amount BIGINT NOT NULL, -- Amount transferred in MIST
    giver_ltv_rating DECIMAL(5,2) NOT NULL, -- Giver's LTV at time of love

    -- Requirements validation
    giver_vault_locked BOOLEAN NOT NULL, -- Was giver's vault locked?
    receiver_vault_locked BOOLEAN NOT NULL, -- Was receiver's vault locked?

    -- Transaction details
    transaction_hash VARCHAR(66) NOT NULL,
    block_number BIGINT NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Prevent duplicate love from same user to same XFT
    UNIQUE(giver_address, receiver_xft_object_id)
);

-- XFT Love Summary Table
-- Aggregated love statistics for each XFT
CREATE TABLE IF NOT EXISTS xft_love_summary (
    xft_object_id VARCHAR(66) PRIMARY KEY,

    -- Love statistics
    total_love_received BIGINT DEFAULT 0, -- Total SUI received as love
    total_love_count INTEGER DEFAULT 0, -- Number of love interactions
    unique_lovers INTEGER DEFAULT 0, -- Number of unique users who showed love

    -- Love metrics
    average_love_amount BIGINT DEFAULT 0, -- Average love amount
    highest_love_amount BIGINT DEFAULT 0, -- Highest single love amount
    last_love_received TIMESTAMPTZ, -- When last love was received
    first_love_received TIMESTAMPTZ, -- When first love was received

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- COMPREHENSIVE EVENT TABLES
-- =====================================================

-- Token Events Table
-- Blockchain events for tokens
CREATE TABLE IF NOT EXISTS token_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('token_created', 'token_swapped', 'label_linked', 'ownership_transferred', 'pool_created', 'migration_completed')),
    token_address VARCHAR(66) NOT NULL,
    transaction_hash VARCHAR(66) NOT NULL,
    block_number BIGINT NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,

    -- Token creation specific fields
    creator_address VARCHAR(66),
    token_name VARCHAR(255),
    token_symbol VARCHAR(50),
    total_supply BIGINT,

    -- Trading specific fields
    trader_address VARCHAR(66),
    sui_amount BIGINT,
    token_amount BIGINT,
    is_buy BOOLEAN,
    price_scaled BIGINT,
    virtual_pool_sui_balance BIGINT,
    virtual_pool_token_balance BIGINT,

    -- Label linking specific fields
    linked_label_global_id BIGINT,
    linked_label_object_id VARCHAR(66),
    linked_label_creator VARCHAR(66),

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Label Events Table
-- Label and operator-related events
CREATE TABLE IF NOT EXISTS label_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('label_created', 'label_linked', 'label_unlinked', 'operator_added', 'operator_removed', 'license_created', 'license_expired', 'label_transferred')),
    label_global_id BIGINT NOT NULL,
    label_object_id VARCHAR(66) NOT NULL,
    transaction_hash VARCHAR(66) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,

    -- Label events
    creator_address VARCHAR(66),
    owner_address VARCHAR(66),
    label_type INTEGER,

    -- Operator events
    operator_address VARCHAR(66),
    operator_role INTEGER,
    assigned_by VARCHAR(66),

    -- License events
    license_object_id VARCHAR(66),
    linked_to_label_id BIGINT,
    commission_fee_bps INTEGER,

    -- Token linking events
    token_address VARCHAR(66),

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- XFT Events Table
-- XFT creation, transfer, and modification events
CREATE TABLE IF NOT EXISTS xft_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('xft_created', 'xft_transferred', 'xft_wrapped', 'vault_locked', 'vault_unlocked', 'assets_deposited', 'assets_withdrawn', 'xft_burned', 'love_received')),
    xft_object_id VARCHAR(66) NOT NULL,
    global_id BIGINT,
    transaction_hash VARCHAR(66) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,

    -- Creation events
    creator_address VARCHAR(66),
    minter_address VARCHAR(66),
    xft_type VARCHAR(20),
    linked_label_global_id BIGINT,

    -- Transfer events
    from_address VARCHAR(66),
    to_address VARCHAR(66),

    -- Vault events
    assets_deposited JSONB,
    assets_withdrawn JSONB,
    unlock_timestamp BIGINT,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Marketplace Events Table
-- Marketplace-related blockchain events
CREATE TABLE IF NOT EXISTS marketplace_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('item_listed', 'item_sold', 'item_cancelled', 'license_created', 'license_used', 'commission_paid')),
    transaction_hash VARCHAR(66) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,

    -- Listing events
    listing_id BIGINT,
    xft_object_id VARCHAR(66),
    seller_address VARCHAR(66),
    buyer_address VARCHAR(66),
    price BIGINT,

    -- License events
    license_object_id VARCHAR(66),
    label_global_id BIGINT,
    license_holder VARCHAR(66),
    commission_fee_bps INTEGER,
    commission_amount BIGINT,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bank Events Table
-- XFT Bank loan and love events
CREATE TABLE IF NOT EXISTS bank_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('loan_issued', 'loan_repaid', 'loan_defaulted', 'loan_liquidated', 'love_given', 'ltv_updated')),
    transaction_hash VARCHAR(66) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,

    -- Loan events
    borrower_address VARCHAR(66),
    lender_address VARCHAR(66), -- Bank address
    xft_object_id VARCHAR(66), -- Collateral XFT
    loan_amount BIGINT,
    collateral_value BIGINT,
    ltv_ratio DECIMAL(5,2),
    interest_rate_bps INTEGER,

    -- Love events
    giver_address VARCHAR(66),
    receiver_address VARCHAR(66),
    receiver_xft_object_id VARCHAR(66),
    love_amount BIGINT,
    giver_ltv_rating DECIMAL(5,2),

    -- LTV update events
    user_address VARCHAR(66),
    old_ltv_rating DECIMAL(5,2),
    new_ltv_rating DECIMAL(5,2),
    rating_change_reason VARCHAR(100),

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Trading Events Table
-- Detailed trading information for analytics
CREATE TABLE IF NOT EXISTS trading_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token_address VARCHAR(66) NOT NULL,
    trader_address VARCHAR(66) NOT NULL,
    transaction_hash VARCHAR(66) NOT NULL,
    block_number BIGINT NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,

    -- Trade details
    sui_amount BIGINT NOT NULL, -- Amount in MIST
    token_amount BIGINT NOT NULL,
    is_buy BOOLEAN NOT NULL,
    price_scaled BIGINT NOT NULL,

    -- Fee breakdown
    platform_fee BIGINT,
    buy_fee BIGINT,
    sell_fee BIGINT,
    reward_contribution BIGINT,

    -- Pool state after trade
    virtual_pool_sui_balance BIGINT NOT NULL,
    virtual_pool_token_balance BIGINT NOT NULL,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Reward Events Table
-- Tracks reward distributions
CREATE TABLE IF NOT EXISTS reward_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token_address VARCHAR(66) NOT NULL,
    winner_address VARCHAR(66) NOT NULL,
    reward_amount BIGINT NOT NULL, -- Amount in MIST
    trade_count BIGINT NOT NULL,
    new_reward_goal BIGINT NOT NULL,
    transaction_hash VARCHAR(66) NOT NULL,
    block_number BIGINT NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SUMMARY & ANALYTICS TABLES
-- =====================================================

-- Token Summary Table
-- Aggregated token information for quick queries
CREATE TABLE IF NOT EXISTS token_summary (
    token_address VARCHAR(66) PRIMARY KEY,
    token_name VARCHAR(255) NOT NULL,
    token_symbol VARCHAR(50) NOT NULL,
    creator_address VARCHAR(66) NOT NULL,

    -- Label information
    linked_label_global_id BIGINT,
    linked_label_object_id VARCHAR(66),
    linked_label_creator VARCHAR(66),

    -- Trading statistics
    total_trades BIGINT DEFAULT 0,
    total_volume_sui BIGINT DEFAULT 0, -- Total SUI volume in MIST
    total_volume_tokens BIGINT DEFAULT 0,
    current_price_scaled BIGINT DEFAULT 0,

    -- Pool state
    current_sui_reserve BIGINT DEFAULT 0,
    current_token_reserve BIGINT DEFAULT 0,

    -- Reward information
    current_trade_count BIGINT DEFAULT 0,
    current_reward_goal BIGINT DEFAULT 0,
    total_rewards_distributed BIGINT DEFAULT 0,

    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Portfolio Table
-- User token holdings and portfolio tracking
CREATE TABLE IF NOT EXISTS user_portfolios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_address VARCHAR(66) NOT NULL,
    token_address VARCHAR(66) NOT NULL,

    -- Holdings
    token_balance BIGINT DEFAULT 0,
    average_buy_price BIGINT DEFAULT 0, -- Weighted average
    total_invested_sui BIGINT DEFAULT 0,
    unrealized_pnl BIGINT DEFAULT 0,
    realized_pnl BIGINT DEFAULT 0,

    -- Trading stats
    total_buys INTEGER DEFAULT 0,
    total_sells INTEGER DEFAULT 0,
    total_buy_volume BIGINT DEFAULT 0,
    total_sell_volume BIGINT DEFAULT 0,

    -- Timestamps
    first_purchase_at TIMESTAMPTZ,
    last_trade_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(user_address, token_address)
);

-- Platform Analytics Table
-- Daily/hourly platform statistics
CREATE TABLE IF NOT EXISTS platform_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    hour INTEGER, -- NULL for daily stats, 0-23 for hourly

    -- Trading metrics
    total_trades INTEGER DEFAULT 0,
    total_volume_sui BIGINT DEFAULT 0,
    unique_traders INTEGER DEFAULT 0,
    new_tokens_created INTEGER DEFAULT 0,

    -- User metrics
    new_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,

    -- XFT metrics
    new_xfts_created INTEGER DEFAULT 0,
    xft_trades INTEGER DEFAULT 0,
    xft_volume_sui BIGINT DEFAULT 0,

    -- Label metrics
    new_labels_created INTEGER DEFAULT 0,
    label_linkings INTEGER DEFAULT 0,

    -- Marketplace metrics
    marketplace_listings INTEGER DEFAULT 0,
    marketplace_sales INTEGER DEFAULT 0,
    marketplace_volume_sui BIGINT DEFAULT 0,

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(date, hour)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User indexes
CREATE INDEX IF NOT EXISTS idx_users_wallet_address ON users(wallet_address);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at DESC);

-- Token indexes
CREATE INDEX IF NOT EXISTS idx_tokens_creator_address ON tokens(creator_address);
CREATE INDEX IF NOT EXISTS idx_tokens_symbol ON tokens(symbol);
CREATE INDEX IF NOT EXISTS idx_tokens_created_at ON tokens(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_tokens_is_featured ON tokens(is_featured);
CREATE INDEX IF NOT EXISTS idx_tokens_migration_status ON tokens(migration_status);
CREATE INDEX IF NOT EXISTS idx_tokens_linked_label ON tokens(linked_label_global_id);

-- Label indexes
CREATE INDEX IF NOT EXISTS idx_labels_creator_address ON labels(creator_address);
CREATE INDEX IF NOT EXISTS idx_labels_label_type ON labels(label_type);
CREATE INDEX IF NOT EXISTS idx_labels_global_id ON labels(global_id);
CREATE INDEX IF NOT EXISTS idx_labels_created_at ON labels(created_at DESC);

-- XFT indexes
CREATE INDEX IF NOT EXISTS idx_xfts_creator_address ON xfts(creator_address);
CREATE INDEX IF NOT EXISTS idx_xfts_owner_address ON xfts(owner_address);
CREATE INDEX IF NOT EXISTS idx_xfts_xft_type ON xfts(xft_type);
CREATE INDEX IF NOT EXISTS idx_xfts_linked_label ON xfts(linked_label_global_id);
CREATE INDEX IF NOT EXISTS idx_xfts_is_for_sale ON xfts(is_for_sale);

-- Operator indexes
CREATE INDEX IF NOT EXISTS idx_operators_operator_address ON operators(operator_address);
CREATE INDEX IF NOT EXISTS idx_operators_label_global_id ON operators(label_global_id);
CREATE INDEX IF NOT EXISTS idx_operators_is_active ON operators(is_active);

-- Marketplace indexes
CREATE INDEX IF NOT EXISTS idx_marketplace_listings_seller ON marketplace_listings(seller_address);
CREATE INDEX IF NOT EXISTS idx_marketplace_listings_status ON marketplace_listings(status);
CREATE INDEX IF NOT EXISTS idx_marketplace_listings_label ON marketplace_listings(listed_under_label_id);

-- Event indexes
CREATE INDEX IF NOT EXISTS idx_token_events_token_address ON token_events(token_address);
CREATE INDEX IF NOT EXISTS idx_token_events_timestamp ON token_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_token_events_event_type ON token_events(event_type);
CREATE INDEX IF NOT EXISTS idx_token_events_transaction_hash ON token_events(transaction_hash);

CREATE INDEX IF NOT EXISTS idx_label_events_label_global_id ON label_events(label_global_id);
CREATE INDEX IF NOT EXISTS idx_label_events_timestamp ON label_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_label_events_event_type ON label_events(event_type);

CREATE INDEX IF NOT EXISTS idx_xft_events_xft_object_id ON xft_events(xft_object_id);
CREATE INDEX IF NOT EXISTS idx_xft_events_timestamp ON xft_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_xft_events_event_type ON xft_events(event_type);

CREATE INDEX IF NOT EXISTS idx_trading_events_token_address ON trading_events(token_address);
CREATE INDEX IF NOT EXISTS idx_trading_events_trader_address ON trading_events(trader_address);
CREATE INDEX IF NOT EXISTS idx_trading_events_timestamp ON trading_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_trading_events_is_buy ON trading_events(is_buy);

CREATE INDEX IF NOT EXISTS idx_marketplace_events_timestamp ON marketplace_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_marketplace_events_event_type ON marketplace_events(event_type);

CREATE INDEX IF NOT EXISTS idx_reward_events_token_address ON reward_events(token_address);
CREATE INDEX IF NOT EXISTS idx_reward_events_winner_address ON reward_events(winner_address);
CREATE INDEX IF NOT EXISTS idx_reward_events_timestamp ON reward_events(timestamp DESC);

-- Portfolio indexes
CREATE INDEX IF NOT EXISTS idx_user_portfolios_user_address ON user_portfolios(user_address);
CREATE INDEX IF NOT EXISTS idx_user_portfolios_token_address ON user_portfolios(token_address);
CREATE INDEX IF NOT EXISTS idx_user_portfolios_last_trade ON user_portfolios(last_trade_at DESC);

-- Analytics indexes
CREATE INDEX IF NOT EXISTS idx_platform_analytics_date ON platform_analytics(date DESC);
CREATE INDEX IF NOT EXISTS idx_platform_analytics_date_hour ON platform_analytics(date, hour);

-- Bank indexes
CREATE INDEX IF NOT EXISTS idx_user_loans_borrower_address ON user_loans(borrower_address);
CREATE INDEX IF NOT EXISTS idx_user_loans_xft_object_id ON user_loans(xft_object_id);
CREATE INDEX IF NOT EXISTS idx_user_loans_status ON user_loans(status);
CREATE INDEX IF NOT EXISTS idx_user_loans_due_date ON user_loans(due_date);
CREATE INDEX IF NOT EXISTS idx_user_ltv_ratings_user_address ON user_ltv_ratings(user_address);

-- Love indexes
CREATE INDEX IF NOT EXISTS idx_xft_love_history_giver_address ON xft_love_history(giver_address);
CREATE INDEX IF NOT EXISTS idx_xft_love_history_receiver_xft ON xft_love_history(receiver_xft_object_id);
CREATE INDEX IF NOT EXISTS idx_xft_love_history_receiver_address ON xft_love_history(receiver_address);
CREATE INDEX IF NOT EXISTS idx_xft_love_history_timestamp ON xft_love_history(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_xft_love_summary_xft_object_id ON xft_love_summary(xft_object_id);

-- Bank event indexes
CREATE INDEX IF NOT EXISTS idx_bank_events_event_type ON bank_events(event_type);
CREATE INDEX IF NOT EXISTS idx_bank_events_timestamp ON bank_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_bank_events_borrower_address ON bank_events(borrower_address);
CREATE INDEX IF NOT EXISTS idx_bank_events_giver_address ON bank_events(giver_address);
CREATE INDEX IF NOT EXISTS idx_bank_events_user_address ON bank_events(user_address);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tokens_updated_at BEFORE UPDATE ON tokens FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_labels_updated_at BEFORE UPDATE ON labels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_xfts_updated_at BEFORE UPDATE ON xfts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_operators_updated_at BEFORE UPDATE ON operators FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_marketplace_listings_updated_at BEFORE UPDATE ON marketplace_listings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_token_events_updated_at BEFORE UPDATE ON token_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_label_events_updated_at BEFORE UPDATE ON label_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_xft_events_updated_at BEFORE UPDATE ON xft_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_trading_events_updated_at BEFORE UPDATE ON trading_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_marketplace_events_updated_at BEFORE UPDATE ON marketplace_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reward_events_updated_at BEFORE UPDATE ON reward_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_token_summary_updated_at BEFORE UPDATE ON token_summary FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_portfolios_updated_at BEFORE UPDATE ON user_portfolios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_loans_updated_at BEFORE UPDATE ON user_loans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_ltv_ratings_updated_at BEFORE UPDATE ON user_ltv_ratings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_xft_love_history_updated_at BEFORE UPDATE ON xft_love_history FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_xft_love_summary_updated_at BEFORE UPDATE ON xft_love_summary FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_xft_bank_updated_at BEFORE UPDATE ON xft_bank FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bank_events_updated_at BEFORE UPDATE ON bank_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update token summary on trading events
CREATE OR REPLACE FUNCTION update_token_summary_on_trade()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO token_summary (
        token_address,
        token_name,
        token_symbol,
        creator_address,
        total_trades,
        total_volume_sui,
        total_volume_tokens,
        current_price_scaled,
        current_sui_reserve,
        current_token_reserve
    ) VALUES (
        NEW.token_address,
        'Unknown', -- Will be updated when token_created event is processed
        'UNK',
        '0x0',
        1,
        NEW.sui_amount,
        NEW.token_amount,
        NEW.price_scaled,
        NEW.virtual_pool_sui_balance,
        NEW.virtual_pool_token_balance
    )
    ON CONFLICT (token_address) DO UPDATE SET
        total_trades = token_summary.total_trades + 1,
        total_volume_sui = token_summary.total_volume_sui + NEW.sui_amount,
        total_volume_tokens = token_summary.total_volume_tokens + NEW.token_amount,
        current_price_scaled = NEW.price_scaled,
        current_sui_reserve = NEW.virtual_pool_sui_balance,
        current_token_reserve = NEW.virtual_pool_token_balance,
        updated_at = NOW();

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to update token summary on trading events
CREATE TRIGGER update_token_summary_on_trading_event
    AFTER INSERT ON trading_events
    FOR EACH ROW
    EXECUTE FUNCTION update_token_summary_on_trade();

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE pool_admin_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE labels ENABLE ROW LEVEL SECURITY;
ALTER TABLE xfts ENABLE ROW LEVEL SECURITY;
ALTER TABLE operators ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_listings ENABLE ROW LEVEL SECURITY;
ALTER TABLE generative_collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE token_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE label_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE xft_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketplace_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE reward_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE token_summary ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_analytics ENABLE ROW LEVEL SECURITY;

-- Public read access policies
CREATE POLICY "Allow public read access on tokens" ON tokens FOR SELECT USING (true);
CREATE POLICY "Allow public read access on labels" ON labels FOR SELECT USING (true);
CREATE POLICY "Allow public read access on xfts" ON xfts FOR SELECT USING (true);
CREATE POLICY "Allow public read access on marketplace_listings" ON marketplace_listings FOR SELECT USING (true);
CREATE POLICY "Allow public read access on generative_collections" ON generative_collections FOR SELECT USING (true);
CREATE POLICY "Allow public read access on token_events" ON token_events FOR SELECT USING (true);
CREATE POLICY "Allow public read access on label_events" ON label_events FOR SELECT USING (true);
CREATE POLICY "Allow public read access on xft_events" ON xft_events FOR SELECT USING (true);
CREATE POLICY "Allow public read access on marketplace_events" ON marketplace_events FOR SELECT USING (true);
CREATE POLICY "Allow public read access on trading_events" ON trading_events FOR SELECT USING (true);
CREATE POLICY "Allow public read access on reward_events" ON reward_events FOR SELECT USING (true);
CREATE POLICY "Allow public read access on token_summary" ON token_summary FOR SELECT USING (true);
CREATE POLICY "Allow public read access on platform_analytics" ON platform_analytics FOR SELECT USING (true);

-- Web3 policies - No Supabase auth required, public read access
CREATE POLICY "Allow public read access on users" ON users FOR SELECT USING (true);
CREATE POLICY "Allow public read access on user_portfolios" ON user_portfolios FOR SELECT USING (true);
CREATE POLICY "Allow public read access on user_loans" ON user_loans FOR SELECT USING (true);
CREATE POLICY "Allow public read access on user_ltv_ratings" ON user_ltv_ratings FOR SELECT USING (true);
CREATE POLICY "Allow public read access on xft_love_history" ON xft_love_history FOR SELECT USING (true);
CREATE POLICY "Allow public read access on xft_love_summary" ON xft_love_summary FOR SELECT USING (true);
CREATE POLICY "Allow public read access on xft_bank" ON xft_bank FOR SELECT USING (true);

-- Admin policies - public read for transparency
CREATE POLICY "Allow public read access on admins" ON admins FOR SELECT USING (true);

-- Service role policies for event synchronization
CREATE POLICY "Allow service role insert on all event tables" ON token_events FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role insert on label_events" ON label_events FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role insert on xft_events" ON xft_events FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role insert on marketplace_events" ON marketplace_events FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role insert on trading_events" ON trading_events FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role insert on reward_events" ON reward_events FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role insert on token_summary" ON token_summary FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role update on token_summary" ON token_summary FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role insert on user_portfolios" ON user_portfolios FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role update on user_portfolios" ON user_portfolios FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role insert on platform_analytics" ON platform_analytics FOR INSERT WITH CHECK (true);

-- Web3 insert/update policies - Service role handles validation via smart contracts
CREATE POLICY "Allow service role to insert/update tokens" ON tokens FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update tokens" ON tokens FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role to insert/update labels" ON labels FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update labels" ON labels FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role to insert/update xfts" ON xfts FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update xfts" ON xfts FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role to insert/update listings" ON marketplace_listings FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update listings" ON marketplace_listings FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role to insert/update users" ON users FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update users" ON users FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role to insert/update loans" ON user_loans FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update loans" ON user_loans FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role to insert/update ltv_ratings" ON user_ltv_ratings FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update ltv_ratings" ON user_ltv_ratings FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role to insert/update love_history" ON xft_love_history FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update love_history" ON xft_love_history FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role to insert/update love_summary" ON xft_love_summary FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update love_summary" ON xft_love_summary FOR UPDATE WITH CHECK (true);
CREATE POLICY "Allow service role to insert/update bank" ON xft_bank FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow service role to update bank" ON xft_bank FOR UPDATE WITH CHECK (true);
