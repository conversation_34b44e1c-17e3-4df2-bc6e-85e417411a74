'use client'

import { useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { useWallet } from '@/contexts/WalletContext'
import { PLATFORM_ADMIN_ADDRESS } from '@/constants/contracts'
import {
  Home,
  ArrowUpDown,
  Plus,
  Users,
  Wallet,
  Shield
} from 'lucide-react'

interface NavItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  color: string
  description: string
}

const navItems: NavItem[] = [
  {
    id: 'home',
    label: 'Home',
    icon: Home,
    href: '/',
    color: 'from-indigo-400 to-purple-500',
    description: 'Home page'
  },
  {
    id: 'swap',
    label: 'Swap',
    icon: ArrowUpDown,
    href: '/swap',
    color: 'from-blue-400 to-indigo-500',
    description: 'Trade tokens'
  },
  {
    id: 'create',
    label: 'Create',
    icon: Plus,
    href: '/create',
    color: 'from-emerald-400 to-teal-500',
    description: 'Launch token'
  },
  {
    id: 'bank',
    label: 'Bank',
    icon: Users,
    href: '/bank',
    color: 'from-purple-400 to-pink-500',
    description: 'All Vault lending'
  },
  {
    id: 'nft',
    label: 'XFT',
    icon: Wallet,
    href: '/nft',
    color: 'from-orange-400 to-red-500',
    description: 'NFT 2.0 system'
  },
  {
    id: 'admin',
    label: 'Admin',
    icon: Shield,
    href: '/admin',
    color: 'from-red-400 to-rose-500',
    description: 'Platform admin'
  }
]

export function DesktopFloatingNav() {
  const router = useRouter()
  const pathname = usePathname()
  const { account } = useWallet()
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  // Check if current user is admin
  const isAdmin = account?.address === PLATFORM_ADMIN_ADDRESS

  const handleNavigation = (href: string) => {
    router.push(href)
  }

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <>
      {/* Desktop Only - Hidden on Mobile */}
      <div className="hidden lg:block fixed right-6 top-1/2 transform -translate-y-1/2 z-50">
        <motion.div
          initial={{ opacity: 0, x: 100 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-3"
        >
          {navItems
            .filter(item => item.id !== 'admin' || isAdmin) // Only show admin button for admins
            .map((item, index) => {
              const Icon = item.icon
              const active = isActive(item.href)

              return (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="relative group"
                onMouseEnter={() => setHoveredItem(item.id)}
                onMouseLeave={() => setHoveredItem(null)}
              >
                      {/* Tooltip */}
                      <AnimatePresence>
                        {hoveredItem === item.id && (
                          <motion.div
                            initial={{ opacity: 0, x: 10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: 10 }}
                            className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2"
                          >
                            <div className="bg-gray-900/95 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap border border-gray-700/50">
                              <div className="font-medium">{item.label}</div>
                              <div className="text-xs text-gray-400">{item.description}</div>
                              {/* Arrow */}
                              <div className="absolute left-full top-1/2 transform -translate-y-1/2">
                                <div className="w-0 h-0 border-l-4 border-l-gray-900/95 border-t-2 border-t-transparent border-b-2 border-b-transparent"></div>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>

                {/* Nav Button */}
                <motion.button
                  onClick={() => handleNavigation(item.href)}
                  className={`
                    w-14 h-14 rounded-full flex items-center justify-center
                    transition-all duration-300 shadow-lg hover:shadow-xl backdrop-blur-sm
                    ${active
                      ? `bg-gradient-to-br ${item.color} text-white shadow-2xl`
                      : 'bg-gray-900/70 text-gray-300 hover:text-white border border-gray-600/50 hover:border-gray-500/50'
                    }
                  `}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Icon className="w-6 h-6" />
                </motion.button>

                {/* Active Indicator */}
                {active && (
                  <motion.div
                    layoutId="activeIndicator"
                    className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-full shadow-lg"
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  />
                )}
              </motion.div>
            )
          })}
        </motion.div>
      </div>
    </>
  )
}
