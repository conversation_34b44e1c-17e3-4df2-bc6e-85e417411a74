'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { initializeEventSystem } from '@/lib/eventListener'

interface EventContextType {
  isListening: boolean
  error: string | null
  startListening: () => Promise<void>
  stopListening: () => void
}

const EventContext = createContext<EventContextType | undefined>(undefined)

export function useEventSystem() {
  const context = useContext(EventContext)
  if (context === undefined) {
    throw new Error('useEventSystem must be used within an EventProvider')
  }
  return context
}

interface EventProviderProps {
  children: ReactNode
}

export function EventProvider({ children }: EventProviderProps) {
  const [isListening, setIsListening] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [unsubscribe, setUnsubscribe] = useState<(() => void) | null>(null)

  const startListening = async () => {
    try {
      setError(null)
      console.log('Starting event system...')
      
      const unsubscribeFn = await initializeEventSystem()
      setUnsubscribe(() => unsubscribeFn)
      setIsListening(true)
      
      console.log('Event system started successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start event system'
      setError(errorMessage)
      console.error('Failed to start event system:', err)
    }
  }

  const stopListening = () => {
    if (unsubscribe) {
      unsubscribe()
      setUnsubscribe(null)
      setIsListening(false)
      console.log('Event system stopped')
    }
  }

  // Auto-start event listening when component mounts
  useEffect(() => {
    startListening()

    // Cleanup on unmount
    return () => {
      stopListening()
    }
  }, [])

  // Handle page visibility changes to restart event listening if needed
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !isListening && !error) {
        console.log('Page became visible, restarting event system...')
        startListening()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [isListening, error])

  const value: EventContextType = {
    isListening,
    error,
    startListening,
    stopListening
  }

  return (
    <EventContext.Provider value={value}>
      {children}
    </EventContext.Provider>
  )
}
