'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { useWallet } from '@suiet/wallet-kit'
import { getPoolService } from '@/services/pool/poolService'
import { Plus, Droplets, AlertCircle, CheckCircle } from 'lucide-react'

interface CreatePoolProps {
  tokenObjectId: string
  tokenSymbol: string
  onPoolCreated?: (poolObjectId: string) => void
  className?: string
}

export function CreatePool({ tokenObjectId, tokenSymbol, onPoolCreated, className }: CreatePoolProps) {
  const { connected, account } = useWallet()
  const poolService = getPoolService()
  
  const [formData, setFormData] = useState({
    initialTokenAmount: '',
    initialSuiAmount: '',
    buyFeeBps: '100', // 1% default
    sellFeeBps: '100', // 1% default
  })
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string; poolObjectId?: string } | null>(null)

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleCreatePool = async () => {
    if (!connected || !account) return

    setIsSubmitting(true)
    setResult(null)

    try {
      await poolService.initialize({
        address: account.address,
        signAndExecuteTransaction: account.signAndExecuteTransaction
      })

      const tokenAmount = BigInt(Math.floor(parseFloat(formData.initialTokenAmount) * 1e9)) // Convert to proper decimals
      const suiAmount = parseFloat(formData.initialSuiAmount)
      const buyFeeBps = parseInt(formData.buyFeeBps)
      const sellFeeBps = parseInt(formData.sellFeeBps)

      const result = await poolService.createPool(
        tokenObjectId,
        tokenAmount,
        suiAmount,
        buyFeeBps,
        sellFeeBps
      )

      if (result.success && result.poolObjectId) {
        setResult({ 
          success: true, 
          message: 'Pool created successfully!',
          poolObjectId: result.poolObjectId
        })
        
        // Reset form
        setFormData({
          initialTokenAmount: '',
          initialSuiAmount: '',
          buyFeeBps: '100',
          sellFeeBps: '100',
        })

        // Notify parent component
        onPoolCreated?.(result.poolObjectId)
      } else {
        setResult({ success: false, message: result.error || 'Failed to create pool' })
      }
    } catch (error) {
      setResult({ success: false, message: 'Transaction failed' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const isFormValid = formData.initialTokenAmount && 
                     formData.initialSuiAmount && 
                     parseFloat(formData.initialTokenAmount) > 0 && 
                     parseFloat(formData.initialSuiAmount) > 0 &&
                     connected

  return (
    <Card variant="glass" padding="lg" className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
            <Plus className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white font-heading">Create Liquidity Pool</h3>
            <p className="text-sm text-gray-400">Launch AMM trading for {tokenSymbol}</p>
          </div>
        </div>

        {/* Form */}
        <div className="space-y-4">
          {/* Initial Token Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Initial {tokenSymbol} Amount
            </label>
            <input
              type="number"
              value={formData.initialTokenAmount}
              onChange={(e) => handleInputChange('initialTokenAmount', e.target.value)}
              placeholder="1000000"
              step="1"
              min="0"
              className="w-full px-4 py-3 rounded-xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
            />
            <p className="text-xs text-gray-400 mt-1">
              Amount of {tokenSymbol} tokens to add to the pool
            </p>
          </div>

          {/* Initial SUI Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Initial SUI Amount
            </label>
            <input
              type="number"
              value={formData.initialSuiAmount}
              onChange={(e) => handleInputChange('initialSuiAmount', e.target.value)}
              placeholder="10.0"
              step="0.001"
              min="0"
              className="w-full px-4 py-3 rounded-xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
            />
            <p className="text-xs text-gray-400 mt-1">
              Amount of SUI to add to the pool
            </p>
          </div>

          {/* Trading Fees */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Buy Fee (bps)
              </label>
              <input
                type="number"
                value={formData.buyFeeBps}
                onChange={(e) => handleInputChange('buyFeeBps', e.target.value)}
                placeholder="100"
                min="0"
                max="1000"
                className="w-full px-4 py-3 rounded-xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Sell Fee (bps)
              </label>
              <input
                type="number"
                value={formData.sellFeeBps}
                onChange={(e) => handleInputChange('sellFeeBps', e.target.value)}
                placeholder="100"
                min="0"
                max="1000"
                className="w-full px-4 py-3 rounded-xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
              />
            </div>
          </div>
          <p className="text-xs text-gray-400">
            Trading fees in basis points (100 bps = 1%). Max 10%.
          </p>
        </div>

        {/* Pool Info */}
        <div className="bg-blue-400/10 border border-blue-400/20 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <Droplets className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-gray-300">
              <p className="font-medium text-blue-400 mb-1">Pool Creation</p>
              <p>Creating a liquidity pool enables AMM trading for your token. You'll receive LP tokens representing your share of the pool.</p>
            </div>
          </div>
        </div>

        {/* Wallet Connection Warning */}
        {!connected && (
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-gray-300">
                <p className="font-medium text-yellow-500 mb-1">Wallet Not Connected</p>
                <p>Please connect your Sui wallet to create a liquidity pool.</p>
              </div>
            </div>
          </div>
        )}

        {/* Result Message */}
        {result && (
          <div className={`p-4 rounded-xl border flex items-start space-x-3 ${
            result.success 
              ? 'bg-green-500/10 border-green-500/20'
              : 'bg-red-500/10 border-red-500/20'
          }`}>
            {result.success ? (
              <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
            ) : (
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            )}
            <div className="text-sm">
              <p className={`font-medium mb-1 ${
                result.success ? 'text-green-500' : 'text-red-500'
              }`}>
                {result.success ? 'Pool Created!' : 'Creation Failed'}
              </p>
              <p className="text-gray-300">{result.message}</p>
              {result.success && result.poolObjectId && (
                <p className="text-xs text-gray-400 mt-2">
                  Pool ID: {result.poolObjectId.slice(0, 8)}...{result.poolObjectId.slice(-8)}
                </p>
              )}
            </div>
          </div>
        )}

        {/* Create Button */}
        <Button
          variant="gradient"
          size="lg"
          className="w-full"
          onClick={handleCreatePool}
          isLoading={isSubmitting}
          disabled={!isFormValid}
        >
          {isSubmitting ? 'Creating Pool...' : connected ? 'Create Pool' : 'Connect Wallet First'}
        </Button>

        {/* Fee Breakdown */}
        {formData.initialSuiAmount && (
          <div className="text-xs text-gray-400 space-y-1">
            <p>Estimated costs:</p>
            <div className="flex justify-between">
              <span>Initial SUI:</span>
              <span>{formData.initialSuiAmount} SUI</span>
            </div>
            <div className="flex justify-between">
              <span>Gas fee:</span>
              <span>~0.01 SUI</span>
            </div>
            <div className="flex justify-between font-medium">
              <span>Total:</span>
              <span>{(parseFloat(formData.initialSuiAmount || '0') + 0.01).toFixed(3)} SUI</span>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}
