'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { useWallet } from '@suiet/wallet-kit'
import { getPoolService, PoolInfo, LiquidityPosition } from '@/services/pool/poolService'
import { mistToSui, suiToMist } from '@/constants/contracts'
import { Plus, Minus, Droplets, TrendingUp, Users, DollarSign } from 'lucide-react'

interface LiquidityPoolProps {
  tokenObjectId: string
  tokenSymbol: string
  className?: string
}

export function LiquidityPool({ tokenObjectId, tokenSymbol, className }: LiquidityPoolProps) {
  const { connected, account } = useWallet()
  const poolService = getPoolService()
  
  const [poolInfo, setPoolInfo] = useState<PoolInfo | null>(null)
  const [userPosition, setUserPosition] = useState<LiquidityPosition | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'add' | 'remove'>('add')
  
  // Form states
  const [suiAmount, setSuiAmount] = useState('')
  const [lpTokenAmount, setLpTokenAmount] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  // Load pool info and user position
  useEffect(() => {
    const loadPoolData = async () => {
      setIsLoading(true)
      try {
        // Get all pools and find the one for this token
        const allPools = await poolService.getAllPools()
        const pool = allPools.find(p => p.tokenObjectId === tokenObjectId)
        
        if (pool) {
          setPoolInfo(pool)
          
          // Get user position if connected
          if (connected && account) {
            const positions = await poolService.getUserLiquidityPositions(account.address)
            const position = positions.find(p => p.poolObjectId === pool.objectId)
            setUserPosition(position || null)
          }
        }
      } catch (error) {
        console.error('Error loading pool data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadPoolData()
  }, [tokenObjectId, connected, account, poolService])

  const handleAddLiquidity = async () => {
    if (!connected || !account || !poolInfo || !suiAmount) return

    setIsSubmitting(true)
    setResult(null)

    try {
      await poolService.initialize({
        address: account.address,
        signAndExecuteTransaction: account.signAndExecuteTransaction
      })

      const result = await poolService.addLiquidity(
        poolInfo.objectId,
        parseFloat(suiAmount),
        BigInt(0) // No minimum LP tokens for now
      )

      if (result.success) {
        setResult({ success: true, message: 'Liquidity added successfully!' })
        setSuiAmount('')
        // Refresh pool data
        setTimeout(() => window.location.reload(), 2000)
      } else {
        setResult({ success: false, message: result.error || 'Failed to add liquidity' })
      }
    } catch (error) {
      setResult({ success: false, message: 'Transaction failed' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleRemoveLiquidity = async () => {
    if (!connected || !account || !poolInfo || !lpTokenAmount || !userPosition) return

    setIsSubmitting(true)
    setResult(null)

    try {
      await poolService.initialize({
        address: account.address,
        signAndExecuteTransaction: account.signAndExecuteTransaction
      })

      const lpAmount = BigInt(Math.floor(parseFloat(lpTokenAmount) * 1e9)) // Convert to proper decimals
      
      const result = await poolService.removeLiquidity(
        poolInfo.objectId,
        lpAmount,
        BigInt(0), // No minimum SUI out for now
        BigInt(0)  // No minimum tokens out for now
      )

      if (result.success) {
        setResult({ success: true, message: 'Liquidity removed successfully!' })
        setLpTokenAmount('')
        // Refresh pool data
        setTimeout(() => window.location.reload(), 2000)
      } else {
        setResult({ success: false, message: result.error || 'Failed to remove liquidity' })
      }
    } catch (error) {
      setResult({ success: false, message: 'Transaction failed' })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <Card variant="glass" padding="lg" className={className}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-700 rounded w-1/3"></div>
          <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          <div className="h-20 bg-gray-700 rounded"></div>
        </div>
      </Card>
    )
  }

  if (!poolInfo) {
    return (
      <Card variant="glass" padding="lg" className={className}>
        <div className="text-center py-8">
          <Droplets className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">No Liquidity Pool</h3>
          <p className="text-gray-400 text-sm">
            This token doesn't have a liquidity pool yet.
          </p>
        </div>
      </Card>
    )
  }

  const suiReserves = mistToSui(Number(poolInfo.suiReserves))
  const tokenReserves = Number(poolInfo.tokenReserves) / 1e9 // Assuming 9 decimals
  const totalLiquidity = suiReserves * 2 // Rough estimate
  const userShare = userPosition ? userPosition.sharePercentage : 0

  return (
    <Card variant="glass" padding="lg" className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-400 to-cyan-600 flex items-center justify-center">
              <Droplets className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-white font-heading">Liquidity Pool</h3>
              <p className="text-sm text-gray-400">{tokenSymbol}/SUI</p>
            </div>
          </div>
        </div>

        {/* Pool Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 rounded-xl bg-blue-400/10 border border-blue-400/20">
            <DollarSign className="w-5 h-5 text-blue-400 mx-auto mb-1" />
            <p className="text-sm font-bold text-white">{totalLiquidity.toFixed(2)} SUI</p>
            <p className="text-xs text-gray-400">Total Liquidity</p>
          </div>
          <div className="text-center p-3 rounded-xl bg-cyan-400/10 border border-cyan-400/20">
            <TrendingUp className="w-5 h-5 text-cyan-400 mx-auto mb-1" />
            <p className="text-sm font-bold text-white">{userShare.toFixed(2)}%</p>
            <p className="text-xs text-gray-400">Your Share</p>
          </div>
        </div>

        {/* Pool Composition */}
        <div className="space-y-3">
          <h4 className="text-sm font-semibold text-gray-300">Pool Composition</h4>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">SUI</span>
              <span className="text-sm font-medium text-white">{suiReserves.toFixed(6)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-400">{tokenSymbol}</span>
              <span className="text-sm font-medium text-white">{tokenReserves.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* User Position */}
        {userPosition && (
          <div className="bg-emerald-400/10 border border-emerald-400/20 rounded-xl p-4">
            <h4 className="text-sm font-semibold text-emerald-300 mb-3">Your Position</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">LP Tokens</span>
                <span className="text-white">{(Number(userPosition.lpTokenAmount) / 1e9).toFixed(6)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">SUI Value</span>
                <span className="text-white">{mistToSui(Number(userPosition.suiValue)).toFixed(6)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Token Value</span>
                <span className="text-white">{(Number(userPosition.tokenValue) / 1e9).toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Action Tabs */}
        {connected && (
          <>
            <div className="flex space-x-1 bg-gray-800/50 rounded-xl p-1">
              <button
                onClick={() => setActiveTab('add')}
                className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg text-sm font-medium transition-all ${
                  activeTab === 'add'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Plus className="w-4 h-4" />
                <span>Add</span>
              </button>
              <button
                onClick={() => setActiveTab('remove')}
                className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg text-sm font-medium transition-all ${
                  activeTab === 'remove'
                    ? 'bg-red-500 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
                disabled={!userPosition}
              >
                <Minus className="w-4 h-4" />
                <span>Remove</span>
              </button>
            </div>

            {/* Add Liquidity Form */}
            {activeTab === 'add' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    SUI Amount
                  </label>
                  <input
                    type="number"
                    value={suiAmount}
                    onChange={(e) => setSuiAmount(e.target.value)}
                    placeholder="0.0"
                    step="0.001"
                    min="0"
                    className="w-full px-4 py-3 rounded-xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                  />
                </div>
                
                <Button
                  variant="gradient"
                  size="lg"
                  className="w-full"
                  onClick={handleAddLiquidity}
                  isLoading={isSubmitting}
                  disabled={!suiAmount || parseFloat(suiAmount) <= 0}
                >
                  Add Liquidity
                </Button>
              </div>
            )}

            {/* Remove Liquidity Form */}
            {activeTab === 'remove' && userPosition && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    LP Token Amount
                  </label>
                  <input
                    type="number"
                    value={lpTokenAmount}
                    onChange={(e) => setLpTokenAmount(e.target.value)}
                    placeholder="0.0"
                    step="0.000001"
                    min="0"
                    max={(Number(userPosition.lpTokenAmount) / 1e9).toString()}
                    className="w-full px-4 py-3 rounded-xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500/50"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Max: {(Number(userPosition.lpTokenAmount) / 1e9).toFixed(6)} LP tokens
                  </p>
                </div>
                
                <Button
                  variant="outline"
                  size="lg"
                  className="w-full border-red-500 text-red-400 hover:bg-red-500/10"
                  onClick={handleRemoveLiquidity}
                  isLoading={isSubmitting}
                  disabled={!lpTokenAmount || parseFloat(lpTokenAmount) <= 0}
                >
                  Remove Liquidity
                </Button>
              </div>
            )}

            {/* Result Message */}
            {result && (
              <div className={`p-4 rounded-xl border ${
                result.success 
                  ? 'bg-green-500/10 border-green-500/20 text-green-400'
                  : 'bg-red-500/10 border-red-500/20 text-red-400'
              }`}>
                <p className="text-sm">{result.message}</p>
              </div>
            )}
          </>
        )}

        {/* Connect Wallet CTA */}
        {!connected && (
          <div className="text-center py-4">
            <p className="text-gray-400 text-sm mb-3">
              Connect your wallet to manage liquidity
            </p>
            <Button variant="outline" size="sm">
              Connect Wallet
            </Button>
          </div>
        )}
      </div>
    </Card>
  )
}
