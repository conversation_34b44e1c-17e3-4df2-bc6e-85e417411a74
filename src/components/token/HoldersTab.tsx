'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { Crown, User, Search, ArrowRight } from 'lucide-react'
import { cn, truncateAddress, formatNumber } from '@/lib/utils'
import Link from 'next/link'

interface TokenData {
  id: string
  name: string
  symbol: string
  totalSupply: number
  creator: {
    name: string
    address: string
    avatar: string
  }
}

interface HoldersTabProps {
  token: TokenData
}

interface Holder {
  address: string
  name?: string
  avatar?: string
  balance: number
  percentage: number
  isCreator?: boolean
  rank: number
}

export function HoldersTab({ token }: HoldersTabProps) {
  const [searchQuery, setSearchQuery] = useState('')
  
  // Mock holders data - in real app this would come from blockchain
  const holders: Holder[] = [
    {
      address: token.creator.address,
      name: token.creator.name,
      avatar: token.creator.avatar,
      balance: 150000000, // 15% of total supply
      percentage: 15.0,
      isCreator: true,
      rank: 1
    },
    {
      address: 'B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0A1',
      name: 'DiamondHands',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
      balance: 85000000, // 8.5%
      percentage: 8.5,
      rank: 2
    },
    {
      address: 'C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0A1B2',
      name: 'CryptoWhale',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      balance: 72000000, // 7.2%
      percentage: 7.2,
      rank: 3
    },
    {
      address: 'D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0A1B2C3',
      name: 'TokenAnalyst',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=40&h=40&fit=crop&crop=face',
      balance: 45000000, // 4.5%
      percentage: 4.5,
      rank: 4
    },
    {
      address: 'E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0A1B2C3D4',
      balance: 38000000, // 3.8%
      percentage: 3.8,
      rank: 5
    },
    {
      address: 'F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0A1B2C3D4E5',
      balance: 32000000, // 3.2%
      percentage: 3.2,
      rank: 6
    },
    {
      address: 'G7H8I9J0K1L2M3N4O5P6Q7R8S9T0A1B2C3D4E5F6',
      balance: 28000000, // 2.8%
      percentage: 2.8,
      rank: 7
    },
    {
      address: 'H8I9J0K1L2M3N4O5P6Q7R8S9T0A1B2C3D4E5F6G7',
      balance: 25000000, // 2.5%
      percentage: 2.5,
      rank: 8
    },
    {
      address: 'I9J0K1L2M3N4O5P6Q7R8S9T0A1B2C3D4E5F6G7H8',
      balance: 22000000, // 2.2%
      percentage: 2.2,
      rank: 9
    },
    {
      address: 'J0K1L2M3N4O5P6Q7R8S9T0A1B2C3D4E5F6G7H8I9',
      balance: 18000000, // 1.8%
      percentage: 1.8,
      rank: 10
    }
  ]

  const filteredHolders = holders.filter(holder => 
    holder.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
    holder.name?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const totalHolders = 2847 // From token data
  const topHoldersPercentage = holders.slice(0, 10).reduce((sum, holder) => sum + holder.percentage, 0)

  return (
    <div className="space-y-6">
      {/* Holders Overview */}
      <Card variant="glass" padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white font-heading">Holders Overview</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 rounded-xl bg-gray-800/30">
              <p className="text-2xl font-bold text-white">{formatNumber(totalHolders, 0)}</p>
              <p className="text-sm text-gray-400">Total Holders</p>
            </div>
            <div className="text-center p-4 rounded-xl bg-gray-800/30">
              <p className="text-2xl font-bold text-white">{topHoldersPercentage.toFixed(1)}%</p>
              <p className="text-sm text-gray-400">Top 10 Holdings</p>
            </div>
          </div>

          {/* Creator Holdings */}
          <div className="p-4 rounded-xl bg-yellow-400/10 border border-yellow-400/30">
            <div className="flex items-center space-x-2 mb-2">
              <Crown className="w-4 h-4 text-yellow-400" />
              <span className="text-sm font-medium text-yellow-300">Creator Holdings</span>
            </div>
            <p className="text-white">
              <span className="font-bold">{holders[0].percentage}%</span> of total supply
              <span className="text-gray-400 ml-2">
                ({formatNumber(holders[0].balance, 0)} {token.symbol})
              </span>
            </p>
          </div>
        </div>
      </Card>

      {/* Search */}
      <Card variant="glass" padding="md">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search holders by address or name..."
            className="w-full pl-10 pr-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
          />
        </div>
      </Card>

      {/* Holders List */}
      <Card variant="glass" padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white font-heading">Top Holders</h3>
          
          <div className="space-y-3">
            {filteredHolders.map((holder, index) => (
              <motion.div
                key={holder.address}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <Link href={`/profile/${holder.address}`}>
                  <div className="flex items-center space-x-3 p-4 rounded-xl bg-gray-800/30 hover:bg-gray-700/30 transition-colors cursor-pointer">
                    {/* Rank */}
                    <div className="flex-shrink-0 w-8 text-center">
                      {holder.rank <= 3 ? (
                        <div className={cn(
                          "w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold",
                          holder.rank === 1 && "bg-yellow-400 text-black",
                          holder.rank === 2 && "bg-gray-300 text-black",
                          holder.rank === 3 && "bg-orange-400 text-black"
                        )}>
                          {holder.rank}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm font-medium">#{holder.rank}</span>
                      )}
                    </div>

                    {/* Avatar */}
                    <div className="flex-shrink-0">
                      {holder.avatar ? (
                        <img
                          src={holder.avatar}
                          alt={holder.name || 'Holder'}
                          className="w-10 h-10 rounded-full object-cover border-2 border-gray-600/40"
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center border-2 border-gray-600/40">
                          <User className="w-5 h-5 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Holder Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        {holder.name && (
                          <span className="font-medium text-white">{holder.name}</span>
                        )}
                        {holder.isCreator && (
                          <Crown className="w-4 h-4 text-yellow-400" />
                        )}
                      </div>
                      <p className="text-sm text-gray-400 font-mono">
                        {truncateAddress(holder.address)}
                      </p>
                    </div>

                    {/* Holdings */}
                    <div className="text-right flex-shrink-0">
                      <p className="font-semibold text-white">
                        {holder.percentage.toFixed(2)}%
                      </p>
                      <p className="text-xs text-gray-400">
                        {formatNumber(holder.balance, 0)} {token.symbol}
                      </p>
                    </div>

                    {/* Arrow */}
                    <ArrowRight className="w-4 h-4 text-gray-400 flex-shrink-0" />
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>

          {filteredHolders.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-400">No holders found matching your search.</p>
            </div>
          )}

          {/* Load More */}
          {filteredHolders.length > 0 && searchQuery === '' && (
            <div className="text-center pt-4">
              <Button variant="glass" size="md">
                Load More Holders
              </Button>
            </div>
          )}
        </div>
      </Card>
    </div>
  )
}
