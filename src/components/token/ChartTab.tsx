'use client'

import { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { ArrowUpDown, TrendingUp, Settings } from 'lucide-react'
import { cn, formatNumber } from '@/lib/utils'

interface TokenData {
  id: string
  name: string
  symbol: string
  price: number
  priceChange24h: number
  volume24h: number
  marketCap: number
  holders: number
  image: string
}

interface ChartTabProps {
  token: TokenData
}

export function ChartTab({ token }: ChartTabProps) {
  const [swapAmount, setSwapAmount] = useState('')
  const [swapDirection, setSwapDirection] = useState<'buy' | 'sell'>('buy')
  const [showChart, setShowChart] = useState(true)
  const [showSlippageSettings, setShowSlippageSettings] = useState(false)
  const [slippage, setSlippage] = useState(0.5) // Default 0.5% slippage
  const inputRef = useRef<HTMLInputElement>(null)

  // Auto-focus the input field when component mounts or swap direction changes
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [swapDirection])

  // Mock chart data points for demonstration
  const chartPoints = Array.from({ length: 50 }, (_, i) => {
    const basePrice = token.price
    const variation = (Math.sin(i * 0.3) + Math.random() * 0.4 - 0.2) * basePrice * 0.1
    return basePrice + variation
  })

  const maxPrice = Math.max(...chartPoints)
  const minPrice = Math.min(...chartPoints)

  return (
    <div className="space-y-6">
      {/* Chart Toggle & Container */}
      <div className="space-y-4">
        {/* Chart Toggle Button */}
        <div className="flex justify-center">
          <button
            onClick={() => setShowChart(!showChart)}
            className="px-4 py-2 rounded-xl bg-gray-800/50 border border-gray-600/30 text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200 text-sm font-medium"
          >
            {showChart ? 'Hide Chart' : 'Show Chart'}
          </button>
        </div>

        {/* Chart Container */}
        {showChart && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="relative h-64 bg-gray-900/50 rounded-xl overflow-hidden"
          >
            {/* Chart Placeholder Image */}
            <img
              src="https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=400&fit=crop&crop=center"
              alt="Trading Chart Placeholder"
              className="w-full h-full object-cover opacity-60"
            />

            {/* Overlay Text */}
            <div className="absolute inset-0 flex items-center justify-center bg-black/40">
              <div className="text-center space-y-2">
                <p className="text-white font-semibold">Chart Integration Coming Soon</p>
                <p className="text-gray-300 text-sm">TradingView, Dexscreener, or custom chart plugin</p>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Compact Swap Section */}
      <Card variant="glass" padding="md">
        <div className="space-y-3">
          {/* Compact Header with Buy/Sell Toggle */}
          <div className="flex items-center justify-between">
            <div className="flex space-x-1 bg-gray-800/50 rounded-lg p-1">
              <button
                onClick={() => setSwapDirection('buy')}
                className={cn(
                  "px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200",
                  swapDirection === 'buy'
                    ? "bg-emerald-400/20 text-emerald-300"
                    : "text-gray-400 hover:text-white"
                )}
              >
                Buy
              </button>
              <button
                onClick={() => setSwapDirection('sell')}
                className={cn(
                  "px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200",
                  swapDirection === 'sell'
                    ? "bg-red-400/20 text-red-300"
                    : "text-gray-400 hover:text-white"
                )}
              >
                Sell
              </button>
            </div>

            <Button
              variant="glass"
              size="sm"
              onClick={() => setShowSlippageSettings(!showSlippageSettings)}
              className={cn(
                "transition-colors",
                showSlippageSettings && "bg-emerald-400/20 border-emerald-400/50"
              )}
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>

          {/* Slippage Settings */}
          {showSlippageSettings && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="p-3 rounded-xl bg-gray-800/30 border border-gray-600/20 space-y-3"
            >
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-400">Slippage Tolerance</span>
                <span className="text-sm text-white font-medium">{slippage}%</span>
              </div>

              <div className="flex space-x-2">
                {[0.1, 0.5, 1.0, 3.0].map((value) => (
                  <button
                    key={value}
                    onClick={() => setSlippage(value)}
                    className={cn(
                      "flex-1 py-1.5 px-2 rounded-lg text-xs font-medium transition-all duration-200",
                      slippage === value
                        ? "bg-emerald-400/20 text-emerald-300 border border-emerald-400/50"
                        : "bg-gray-700/50 text-gray-400 border border-gray-600/30 hover:bg-gray-600/50"
                    )}
                  >
                    {value}%
                  </button>
                ))}
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={slippage}
                  onChange={(e) => setSlippage(parseFloat(e.target.value) || 0)}
                  step="0.1"
                  min="0.1"
                  max="50"
                  className="flex-1 p-2 rounded-lg bg-gray-700/50 border border-gray-600/30 text-white text-sm focus:outline-none focus:ring-1 focus:ring-emerald-400/50"
                  placeholder="Custom"
                />
                <span className="text-xs text-gray-400">%</span>
              </div>
            </motion.div>
          )}

          {/* Compact Swap Form */}
          <div className="space-y-2">
            {/* Input */}
            <div className="relative">
              <input
                ref={inputRef}
                type="number"
                value={swapAmount}
                onChange={(e) => setSwapAmount(e.target.value)}
                placeholder="0.00"
                className="w-full p-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400/50"
                autoFocus
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                {swapDirection === 'buy' ? 'SOL' : token.symbol}
              </div>
            </div>

            {/* Output Preview */}
            <div className="p-3 rounded-xl bg-gray-800/30 border border-gray-600/20">
              <div className="flex justify-between items-center">
                <span className="text-white text-sm">
                  ≈ {swapAmount ? formatNumber(parseFloat(swapAmount) * (swapDirection === 'buy' ? 1/token.price : token.price), 4) : '0.00'}
                </span>
                <span className="text-gray-400 text-sm">
                  {swapDirection === 'buy' ? token.symbol : 'SOL'}
                </span>
              </div>
            </div>
          </div>

          {/* Quick Amount Buttons */}
          <div className="grid grid-cols-4 gap-1.5">
            {swapDirection === 'buy'
              ? ['0.1', '0.5', '1', '5'].map((amount) => (
                  <button
                    key={amount}
                    onClick={() => setSwapAmount(amount)}
                    className="py-1.5 px-2 rounded-lg bg-gray-800/50 border border-gray-600/30 text-gray-300 text-xs hover:bg-gray-700/50 transition-colors"
                  >
                    {amount}
                  </button>
                ))
              : ['10%', '25%', '75%', '100%'].map((percentage) => (
                  <button
                    key={percentage}
                    onClick={() => {
                      // Mock user token balance - in real app this would come from wallet
                      const mockTokenBalance = 15000
                      const percentValue = parseInt(percentage.replace('%', ''))
                      const tokenAmount = (mockTokenBalance * percentValue / 100)
                      setSwapAmount(tokenAmount.toString())
                    }}
                    className="py-1.5 px-2 rounded-lg bg-gray-800/50 border border-gray-600/30 text-gray-300 text-xs hover:bg-gray-700/50 transition-colors"
                  >
                    {percentage}
                  </button>
                ))
            }
          </div>

          {/* Compact Swap Button */}
          <Button
            variant="gradient"
            size="md"
            className={cn(
              "w-full py-3 font-bold",
              swapDirection === 'buy'
                ? "bg-gradient-to-r from-emerald-400 to-emerald-600 hover:from-emerald-500 hover:to-emerald-700"
                : "bg-gradient-to-r from-red-400 to-red-600 hover:from-red-500 hover:to-red-700"
            )}
            disabled={!swapAmount}
          >
            {swapDirection === 'buy' ? 'Buy' : 'Sell'} {token.symbol}
          </Button>
        </div>
      </Card>
    </div>
  )
}
