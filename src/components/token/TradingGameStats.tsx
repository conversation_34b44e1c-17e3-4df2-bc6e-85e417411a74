'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/shared/Card'
// Helper function for formatting SUI
const formatSUI = (amount: number): string => {
  if (amount < 0.001) {
    return `${(amount * 1000).toFixed(3)} mSUI`
  }
  return `${amount.toFixed(6)} SUI`
}
import { Trophy, Target, Users, Timer, Coins, TrendingUp } from 'lucide-react'

interface TradingGameStatsProps {
  tokenInfo: any
  isMigrated: boolean
}

export function TradingGameStats({ tokenInfo, isMigrated }: TradingGameStatsProps) {
  const [timeLeft, setTimeLeft] = useState(0)
  const [currentTradeCount, setCurrentTradeCount] = useState(0)

  // Mock trading game data (in real implementation, this would come from contract)
  const rewardPotBalance = Number(tokenInfo.rewardPotBalance) / 1e9 // Convert to SUI
  const rewardGoal = Number(tokenInfo.rewardGoal) / 1e9 // Convert to SUI
  const targetTradeCount = 150 // Mock target (Xth trade wins)
  const recentTrades = 127 // Mock current trade count
  const lastWinner = "0x8kF2...9mN3" // Mock last winner (Sui address format)
  const lastWinAmount = 2.5 // Mock last win amount in SUI

  // Calculate progress to next reward
  const progressToReward = Math.min((recentTrades / targetTradeCount) * 100, 100)
  const tradesRemaining = Math.max(targetTradeCount - recentTrades, 0)

  // Mock countdown timer (5 minutes for goal decrease)
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 0) {
          return 300 // Reset to 5 minutes
        }
        return prev - 1
      })
    }, 1000)

    // Initialize with random time
    setTimeLeft(Math.floor(Math.random() * 300) + 60)

    return () => clearInterval(timer)
  }, [])

  // Update trade count periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTradeCount(prev => {
        const newCount = recentTrades + Math.floor(Math.random() * 3)
        return newCount > targetTradeCount ? targetTradeCount : newCount
      })
    }, 10000) // Update every 10 seconds

    setCurrentTradeCount(recentTrades)
    return () => clearInterval(interval)
  }, [recentTrades, targetTradeCount])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <Card variant="glass" padding="lg" className="border border-purple-400/30 bg-purple-400/5">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-400 to-pink-600 flex items-center justify-center">
              <Trophy className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-white font-heading">Trading Game</h3>
              <p className="text-sm text-gray-400">
                {isMigrated ? 'AMM Trading Rewards' : 'Bonding Curve Rewards'}
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-xl font-bold text-purple-400">
              {formatSUI(rewardPotBalance)}
            </div>
            <div className="text-xs text-gray-400">Reward Pot</div>
          </div>
        </div>

        {/* Current Game Progress */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-400">Progress to Next Reward</span>
            <span className="text-sm font-medium text-white">
              {currentTradeCount}/{targetTradeCount} trades
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="relative">
            <div className="w-full h-2 bg-gray-800/50 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-purple-400 to-pink-600 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${progressToReward}%` }}
              />
            </div>
            
            {/* Winner marker */}
            <div className="absolute right-0 top-0 transform translate-x-1/2 -translate-y-0.5">
              <div className="w-1 h-3 bg-yellow-400 rounded-full" />
            </div>
          </div>
          
          <div className="flex justify-between text-xs">
            <span className="text-gray-500">Start</span>
            <span className="text-yellow-400 font-medium">🏆 Winner</span>
          </div>
        </div>

        {/* Game Stats Grid */}
        <div className="grid grid-cols-3 gap-4">
          {/* Trades Remaining */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-2">
              <Target className="w-4 h-4 text-orange-400" />
              <span className="text-xs text-gray-400 font-medium">Remaining</span>
            </div>
            <div className="text-sm font-bold text-orange-400">
              {tradesRemaining} trades
            </div>
          </div>

          {/* Active Traders */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-2">
              <Users className="w-4 h-4 text-blue-400" />
              <span className="text-xs text-gray-400 font-medium">Traders</span>
            </div>
            <div className="text-sm font-bold text-blue-400">
              {Math.floor(Math.random() * 50) + 20}
            </div>
          </div>

          {/* Goal Timer */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-2">
              <Timer className="w-4 h-4 text-red-400" />
              <span className="text-xs text-gray-400 font-medium">Timer</span>
            </div>
            <div className="text-sm font-bold text-red-400">
              {formatTime(timeLeft)}
            </div>
          </div>
        </div>

        {/* Game Mechanics */}
        <div className="bg-gray-800/30 rounded-xl p-4 space-y-3">
          <h4 className="text-sm font-semibold text-gray-300">How It Works</h4>
          <div className="space-y-2 text-xs text-gray-400">
            <div className="flex items-start space-x-2">
              <Coins className="w-3 h-3 text-purple-400 flex-shrink-0 mt-0.5" />
              <span>50% of creator fees go into the reward pot</span>
            </div>
            <div className="flex items-start space-x-2">
              <Trophy className="w-3 h-3 text-yellow-400 flex-shrink-0 mt-0.5" />
              <span>Every {targetTradeCount}th trader wins the entire pot</span>
            </div>
            <div className="flex items-start space-x-2">
              <TrendingUp className="w-3 h-3 text-emerald-400 flex-shrink-0 mt-0.5" />
              <span>Goal increases on win, decreases after 5min inactivity</span>
            </div>
            <div className="flex items-start space-x-2">
              <Users className="w-3 h-3 text-blue-400 flex-shrink-0 mt-0.5" />
              <span>Community can add to the pot anytime</span>
            </div>
          </div>
        </div>

        {/* Recent Winner */}
        {lastWinner && (
          <div className="bg-yellow-400/10 border border-yellow-400/30 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-semibold text-yellow-300 mb-1">
                  🎉 Recent Winner
                </div>
                <div className="text-xs text-gray-300">
                  {lastWinner} won {formatSUI(lastWinAmount)}
                </div>
              </div>
              <div className="text-2xl">🏆</div>
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div className="text-center">
          <p className="text-sm text-gray-400 mb-2">
            Trade now for a chance to win the pot!
          </p>
          <div className="text-xs text-purple-400">
            {tradesRemaining > 0
              ? `Only ${tradesRemaining} trades until next winner`
              : 'Next trade wins the pot! 🎯'
            }
          </div>
        </div>
      </div>
    </Card>
  )
}
