'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { 
  ExternalLink, 
  Copy, 
  User, 
  Globe, 
  MessageCircle, 
  Music, 
  Video,
  Crown,
  ArrowRight,
  Check
} from 'lucide-react'
import { cn, truncateAddress, formatNumber, getRelativeTime } from '@/lib/utils'
import Link from 'next/link'

interface TokenData {
  id: string
  name: string
  symbol: string
  address: string
  price: number
  totalSupply: number
  description: string
  creator: {
    name: string
    address: string
    avatar: string
  }
  socials: {
    website?: string
    twitter?: string
    telegram?: string
    tiktok?: string
  }
  launchedAt: Date
}

interface InfoTabProps {
  token: TokenData
}

export function InfoTab({ token }: InfoTabProps) {
  const [showTakeoverForm, setShowTakeoverForm] = useState(false)
  const [takeoverReason, setTakeoverReason] = useState('')
  const [copiedAddress, setCopiedAddress] = useState(false)
  
  // Mock: Check if current user is the creator (in real app, this would come from auth)
  const isCreator = false // Set to true to test creator view

  const handleCopyAddress = async () => {
    try {
      await navigator.clipboard.writeText(token.address)
      setCopiedAddress(true)
      setTimeout(() => setCopiedAddress(false), 2000)
    } catch (err) {
      console.error('Failed to copy address:', err)
    }
  }

  const handleTakeoverSubmit = () => {
    // Handle takeover request submission
    console.log('Takeover request:', takeoverReason)
    setShowTakeoverForm(false)
    setTakeoverReason('')
  }

  const socialIcons = {
    website: Globe,
    twitter: MessageCircle,
    telegram: MessageCircle,
    tiktok: Video
  }

  return (
    <div className="space-y-6">
      {/* Token Details */}
      <Card variant="glass" padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white font-heading">Token Details</h3>
          
          {/* Description */}
          <div className="space-y-2">
            <p className="text-sm text-gray-400">Description</p>
            <p className="text-gray-200 leading-relaxed">{token.description}</p>
          </div>

          {/* Contract Address */}
          <div className="space-y-2">
            <p className="text-sm text-gray-400">Contract Address</p>
            <div className="flex items-center space-x-2">
              <code className="flex-1 p-3 rounded-lg bg-gray-800/50 text-gray-300 text-sm font-mono">
                {truncateAddress(token.address, 8)}
              </code>
              <Button
                variant="glass"
                size="sm"
                onClick={handleCopyAddress}
                className="flex-shrink-0"
              >
                {copiedAddress ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
          </div>

          {/* Token Stats */}
          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-700/50">
            <div>
              <p className="text-xs text-gray-400 mb-1">Total Supply</p>
              <p className="text-sm font-semibold text-white">{formatNumber(token.totalSupply, 0)}</p>
            </div>
            <div>
              <p className="text-xs text-gray-400 mb-1">Launched</p>
              <p className="text-sm font-semibold text-white">{getRelativeTime(token.launchedAt)}</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Creator Info */}
      <Card variant="glass" padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white font-heading">Creator</h3>
          
          <Link href={`/profile/${token.creator.address}`}>
            <div className="flex items-center space-x-3 p-3 rounded-xl bg-gray-800/30 hover:bg-gray-700/30 transition-colors cursor-pointer">
              <img
                src={token.creator.avatar}
                alt={token.creator.name}
                className="w-12 h-12 rounded-full object-cover border-2 border-gray-600/40"
              />
              <div className="flex-1">
                <p className="font-semibold text-white">{token.creator.name}</p>
                <p className="text-sm text-gray-400 font-mono">
                  {truncateAddress(token.creator.address)}
                </p>
              </div>
              <ArrowRight className="w-4 h-4 text-gray-400" />
            </div>
          </Link>
        </div>
      </Card>

      {/* Socials */}
      {Object.keys(token.socials).length > 0 && (
        <Card variant="glass" padding="lg">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white font-heading">Social Links</h3>
            
            <div className="space-y-2">
              {Object.entries(token.socials).map(([platform, url]) => {
                if (!url) return null
                const IconComponent = socialIcons[platform as keyof typeof socialIcons]
                
                return (
                  <a
                    key={platform}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-3 p-3 rounded-xl bg-gray-800/30 hover:bg-gray-700/30 transition-colors"
                  >
                    <IconComponent className="w-5 h-5 text-gray-400" />
                    <span className="flex-1 text-white capitalize">{platform}</span>
                    <ExternalLink className="w-4 h-4 text-gray-400" />
                  </a>
                )
              })}
            </div>
          </div>
        </Card>
      )}

      {/* Creator Actions or Community Takeover */}
      {isCreator ? (
        <Card variant="glass" padding="lg">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Crown className="w-5 h-5 text-yellow-400" />
              <h3 className="text-lg font-semibold text-white font-heading">Creator Controls</h3>
            </div>
            
            <p className="text-gray-400 text-sm">
              As the creator of this token, you can transfer ownership to another address.
            </p>

            <Button
              variant="gradient"
              size="md"
              className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600"
            >
              Transfer Token Ownership
            </Button>
          </div>
        </Card>
      ) : (
        <Card variant="glass" padding="lg">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white font-heading">Community Takeover</h3>
            
            <p className="text-gray-400 text-sm">
              Request to take over this token if you believe you can better serve the community.
            </p>

            {!showTakeoverForm ? (
              <Button
                variant="gradient"
                size="md"
                className="w-full bg-gradient-to-r from-purple-400 to-pink-500 hover:from-purple-500 hover:to-pink-600"
                onClick={() => setShowTakeoverForm(true)}
              >
                Request Community Takeover
              </Button>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm text-gray-400">Reason for Takeover Request</label>
                  <textarea
                    value={takeoverReason}
                    onChange={(e) => setTakeoverReason(e.target.value)}
                    placeholder="Explain why you should take over this token and how you plan to improve it..."
                    rows={4}
                    className="w-full p-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-400/50 resize-none"
                  />
                </div>
                
                <div className="flex space-x-3">
                  <Button
                    variant="glass"
                    size="md"
                    className="flex-1"
                    onClick={() => {
                      setShowTakeoverForm(false)
                      setTakeoverReason('')
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="gradient"
                    size="md"
                    className="flex-1 bg-gradient-to-r from-purple-400 to-pink-500 hover:from-purple-500 hover:to-pink-600"
                    onClick={handleTakeoverSubmit}
                    disabled={!takeoverReason.trim()}
                  >
                    Submit Request
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  )
}
