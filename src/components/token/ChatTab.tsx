'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { Send, Smile, Image, TrendingUp, TrendingDown } from 'lucide-react'
import { cn, truncateAddress, getRelativeTime } from '@/lib/utils'

interface TokenData {
  id: string
  name: string
  symbol: string
}

interface ChatTabProps {
  token: TokenData
}

interface ChatMessage {
  id: string
  user: {
    name: string
    address: string
    avatar: string
  }
  message: string
  timestamp: Date
  type: 'message' | 'trade' | 'system'
  tradeData?: {
    action: 'buy' | 'sell'
    amount: number
    price: number
  }
}

export function ChatTab({ token }: ChatTabProps) {
  const [message, setMessage] = useState('')
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      user: {
        name: 'Crypt<PERSON><PERSON><PERSON>',
        address: 'A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
      },
      message: 'This token is going to the moon! 🚀',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      type: 'message'
    },
    {
      id: '2',
      user: {
        name: 'DiamondHands',
        address: 'B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0A1',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face'
      },
      message: '',
      timestamp: new Date(Date.now() - 3 * 60 * 1000),
      type: 'trade',
      tradeData: {
        action: 'buy',
        amount: 1000,
        price: 0.0234
      }
    },
    {
      id: '3',
      user: {
        name: 'TokenAnalyst',
        address: 'C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0A1B2',
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=40&h=40&fit=crop&crop=face'
      },
      message: 'Great fundamentals on this project. The team is solid and the roadmap looks promising.',
      timestamp: new Date(Date.now() - 2 * 60 * 1000),
      type: 'message'
    },
    {
      id: '4',
      user: {
        name: 'MoonBoy',
        address: 'D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0A1B2C3',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=face'
      },
      message: 'When Binance listing? 👀',
      timestamp: new Date(Date.now() - 1 * 60 * 1000),
      type: 'message'
    }
  ])
  
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = () => {
    if (!message.trim()) return

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      user: {
        name: 'You',
        address: 'YOUR_WALLET_ADDRESS',
        avatar: 'https://images.unsplash.com/photo-1633332755192-727a05c4013d?w=40&h=40&fit=crop&crop=face'
      },
      message: message.trim(),
      timestamp: new Date(),
      type: 'message'
    }

    setMessages(prev => [...prev, newMessage])
    setMessage('')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const renderMessage = (msg: ChatMessage) => {
    if (msg.type === 'trade' && msg.tradeData) {
      return (
        <motion.div
          key={msg.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center space-x-3 p-3 rounded-xl bg-gray-800/30 border-l-4 border-emerald-400"
        >
          <img
            src={msg.user.avatar}
            alt={msg.user.name}
            className="w-8 h-8 rounded-full object-cover"
          />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-white">{msg.user.name}</span>
              <span className="text-xs text-gray-400">
                {truncateAddress(msg.user.address)}
              </span>
              {msg.tradeData.action === 'buy' ? (
                <TrendingUp className="w-4 h-4 text-emerald-400" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-400" />
              )}
            </div>
            <p className="text-sm text-gray-300">
              {msg.tradeData.action === 'buy' ? 'Bought' : 'Sold'} {msg.tradeData.amount.toLocaleString()} {token.symbol} 
              {' '}at ${msg.tradeData.price.toFixed(4)}
            </p>
          </div>
          <span className="text-xs text-gray-500">
            {getRelativeTime(msg.timestamp)}
          </span>
        </motion.div>
      )
    }

    return (
      <motion.div
        key={msg.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-start space-x-3 p-3"
      >
        <img
          src={msg.user.avatar}
          alt={msg.user.name}
          className="w-8 h-8 rounded-full object-cover flex-shrink-0"
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-white">{msg.user.name}</span>
            <span className="text-xs text-gray-400">
              {truncateAddress(msg.user.address)}
            </span>
            <span className="text-xs text-gray-500">
              {getRelativeTime(msg.timestamp)}
            </span>
          </div>
          <p className="text-gray-300 break-words">{msg.message}</p>
        </div>
      </motion.div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Chat Header */}
      <Card variant="glass" padding="md">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-white font-heading">
            {token.symbol} Community Chat
          </h3>
          <p className="text-sm text-gray-400 mt-1">
            Discuss {token.name} with the community
          </p>
        </div>
      </Card>

      {/* Chat Messages */}
      <Card variant="glass" padding="none">
        <div className="h-96 overflow-y-auto p-4 space-y-2">
          {messages.map(renderMessage)}
          <div ref={messagesEndRef} />
        </div>
        
        {/* Message Input */}
        <div className="border-t border-gray-700/50 p-4">
          <div className="flex items-end space-x-3">
            <div className="flex-1 relative">
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={`Share your thoughts about ${token.symbol}...`}
                rows={1}
                className="w-full p-3 pr-20 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 resize-none"
                style={{ minHeight: '44px', maxHeight: '120px' }}
              />
              
              {/* Message Actions */}
              <div className="absolute right-2 bottom-2 flex space-x-1">
                <button className="p-1.5 rounded-lg hover:bg-gray-700/50 transition-colors">
                  <Smile className="w-4 h-4 text-gray-400" />
                </button>
                <button className="p-1.5 rounded-lg hover:bg-gray-700/50 transition-colors">
                  <Image className="w-4 h-4 text-gray-400" />
                </button>
              </div>
            </div>
            
            <Button
              variant="gradient"
              size="md"
              onClick={handleSendMessage}
              disabled={!message.trim()}
              className="bg-gradient-to-r from-emerald-400 to-emerald-600 hover:from-emerald-500 hover:to-emerald-700 px-4"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
