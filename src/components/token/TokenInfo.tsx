'use client'

import { Card } from '@/components/shared/Card'
// Helper functions for formatting
const formatSOL = (amount: number): string => {
  if (amount < 0.001) {
    return `${(amount * 1000).toFixed(3)} mSOL`
  }
  return `${amount.toFixed(6)} SOL`
}

const formatTokens = (amount: number): string => {
  if (amount >= 1_000_000) {
    return `${(amount / 1_000_000).toFixed(2)}M`
  }
  if (amount >= 1_000) {
    return `${(amount / 1_000).toFixed(2)}K`
  }
  return amount.toFixed(2)
}
import { 
  User, 
  Globe, 
  Twitter, 
  MessageCircle, 
  ExternalLink, 
  Copy,
  Calendar,
  DollarSign,
  TrendingUp,
  Zap
} from 'lucide-react'

interface TokenInfoProps {
  tokenInfo: any
  currentPrice: number
  marketCap: number
  isMigrated: boolean
}

export function TokenInfo({ tokenInfo, currentPrice, marketCap, isMigrated }: TokenInfoProps) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // In a real implementation, show a toast notification
  }

  // Mock social links and additional data
  const socialLinks = {
    website: 'https://example.com',
    twitter: 'https://twitter.com/example',
    telegram: 'https://t.me/example'
  }

  const creationDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

  return (
    <div className="space-y-6">
      {/* Token Description */}
      <Card variant="glass" padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-bold text-white font-heading">About {tokenInfo.name}</h3>
          <p className="text-gray-300 leading-relaxed">
            {tokenInfo.description || 'No description provided for this token.'}
          </p>
          
          {/* Three dots separator */}
          <div className="flex justify-center">
            <div className="flex space-x-2">
              <div className="w-1 h-1 bg-gray-600 rounded-full" />
              <div className="w-1 h-1 bg-gray-600 rounded-full" />
              <div className="w-1 h-1 bg-gray-600 rounded-full" />
            </div>
          </div>
        </div>
      </Card>

      {/* Creator Information */}
      <Card variant="glass" padding="lg">
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <User className="w-5 h-5 text-blue-400" />
            <h3 className="text-lg font-bold text-white font-heading">Creator</h3>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Address</span>
              <div className="flex items-center space-x-2">
                <span className="text-white font-mono text-sm">
                  {tokenInfo.creator.toString().slice(0, 8)}...{tokenInfo.creator.toString().slice(-8)}
                </span>
                <button
                  onClick={() => copyToClipboard(tokenInfo.creator.toString())}
                  className="p-1 rounded hover:bg-gray-700/50 transition-colors"
                >
                  <Copy className="w-4 h-4 text-gray-400" />
                </button>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Created</span>
              <span className="text-white">
                {creationDate.toLocaleDateString()}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Creator Fee</span>
              <span className="text-white">
                {(tokenInfo.creatorFeeBps / 100).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      </Card>

      {/* Social Links */}
      <Card variant="glass" padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-bold text-white font-heading">Social Links</h3>
          
          <div className="space-y-3">
            {socialLinks.website && (
              <a
                href={socialLinks.website}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-between p-3 rounded-xl bg-gray-800/30 hover:bg-gray-700/30 transition-colors group"
              >
                <div className="flex items-center space-x-3">
                  <Globe className="w-5 h-5 text-blue-400" />
                  <span className="text-white">Website</span>
                </div>
                <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" />
              </a>
            )}
            
            {socialLinks.twitter && (
              <a
                href={socialLinks.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-between p-3 rounded-xl bg-gray-800/30 hover:bg-gray-700/30 transition-colors group"
              >
                <div className="flex items-center space-x-3">
                  <Twitter className="w-5 h-5 text-blue-400" />
                  <span className="text-white">Twitter</span>
                </div>
                <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" />
              </a>
            )}
            
            {socialLinks.telegram && (
              <a
                href={socialLinks.telegram}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-between p-3 rounded-xl bg-gray-800/30 hover:bg-gray-700/30 transition-colors group"
              >
                <div className="flex items-center space-x-3">
                  <MessageCircle className="w-5 h-5 text-blue-400" />
                  <span className="text-white">Telegram</span>
                </div>
                <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" />
              </a>
            )}
          </div>
        </div>
      </Card>

      {/* Token Statistics */}
      <Card variant="glass" padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-bold text-white font-heading">Token Statistics</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4 text-emerald-400" />
                <span className="text-gray-400 text-sm">Current Price</span>
              </div>
              <div className="text-white font-bold">
                {currentPrice.toFixed(8)} SOL
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-blue-400" />
                <span className="text-gray-400 text-sm">Market Cap</span>
              </div>
              <div className="text-white font-bold">
                {marketCap < 1000 
                  ? `${marketCap.toFixed(2)} SOL`
                  : `${(marketCap / 1000).toFixed(1)}K SOL`
                }
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-purple-400" />
                <span className="text-gray-400 text-sm">Total Supply</span>
              </div>
              <div className="text-white font-bold">
                {formatTokens(Number(tokenInfo.totalSupply) / 1e9)}
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-yellow-400" />
                <span className="text-gray-400 text-sm">Circulating</span>
              </div>
              <div className="text-white font-bold">
                {formatTokens(Number(tokenInfo.circulatingSupply) / 1e9)}
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Contract Information */}
      <Card variant="glass" padding="lg">
        <div className="space-y-4">
          <h3 className="text-lg font-bold text-white font-heading">Contract Information</h3>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Token Address</span>
              <div className="flex items-center space-x-2">
                <span className="text-white font-mono text-sm">
                  {tokenInfo.mint.toString().slice(0, 8)}...{tokenInfo.mint.toString().slice(-8)}
                </span>
                <button
                  onClick={() => copyToClipboard(tokenInfo.mint.toString())}
                  className="p-1 rounded hover:bg-gray-700/50 transition-colors"
                >
                  <Copy className="w-4 h-4 text-gray-400" />
                </button>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Decimals</span>
              <span className="text-white">9</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-400">Trading Mode</span>
              <span className={`font-medium ${isMigrated ? 'text-emerald-400' : 'text-blue-400'}`}>
                {isMigrated ? 'AMM Pool' : 'Bonding Curve'}
              </span>
            </div>
            
            {isMigrated && (
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Pool Liquidity</span>
                <span className="text-white">
                  {formatSOL(Number(tokenInfo.liquiditySolAmount) / 1e9)}
                </span>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  )
}
