'use client'

import { Card } from '@/components/shared/Card'
import { formatPrice, lamportsToSol, tokensToHuman } from '@/services/pricing/bondingCurve'

// Helper functions for formatting
const formatSOL = (amount: number): string => {
  if (amount < 0.001) {
    return `${(amount * 1000).toFixed(3)} mSOL`
  }
  return `${amount.toFixed(6)} SOL`
}

const formatTokens = (amount: number): string => {
  if (amount >= 1_000_000) {
    return `${(amount / 1_000_000).toFixed(2)}M`
  }
  if (amount >= 1_000) {
    return `${(amount / 1_000).toFixed(2)}K`
  }
  return amount.toFixed(2)
}
import { TrendingUp, TrendingDown, DollarSign, Users, Zap } from 'lucide-react'

interface TokenHeaderProps {
  tokenInfo: any
  currentPrice: number
  marketCap: number
  isMigrated: boolean
}

export function TokenHeader({ tokenInfo, currentPrice, marketCap, isMigrated }: TokenHeaderProps) {
  // Mock price change data (in real implementation, this would come from price history)
  const priceChange24h = 12.5 // +12.5%
  const volume24h = 45.2 // 45.2 SOL

  return (
    <Card variant="glass" padding="lg" className="relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-purple-400/5 to-emerald-400/10" />
      
      <div className="relative space-y-6">
        {/* Token Identity */}
        <div className="flex items-center space-x-4">
          {/* Token Image */}
          <div className="relative">
            <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-400 to-purple-600 flex items-center justify-center overflow-hidden">
              {tokenInfo.imageUrl ? (
                <img 
                  src={tokenInfo.imageUrl} 
                  alt={tokenInfo.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-2xl font-bold text-white">
                  {tokenInfo.symbol.charAt(0)}
                </span>
              )}
            </div>
            
            {/* Migration Status Badge */}
            {isMigrated && (
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-emerald-400 rounded-full flex items-center justify-center">
                <Zap className="w-3 h-3 text-white" />
              </div>
            )}
          </div>

          {/* Token Details */}
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-bold text-white font-heading">
                {tokenInfo.name}
              </h1>
              {isMigrated && (
                <span className="px-2 py-1 bg-emerald-400/20 text-emerald-300 text-xs font-medium rounded-full border border-emerald-400/30">
                  Migrated
                </span>
              )}
            </div>
            <p className="text-gray-400 font-medium">${tokenInfo.symbol}</p>
            <p className="text-sm text-gray-500 mt-1 line-clamp-2">
              {tokenInfo.description}
            </p>
          </div>
        </div>

        {/* Three Dots Separator */}
        <div className="flex justify-center">
          <div className="flex space-x-2">
            <div className="w-1 h-1 bg-gray-600 rounded-full" />
            <div className="w-1 h-1 bg-gray-600 rounded-full" />
            <div className="w-1 h-1 bg-gray-600 rounded-full" />
          </div>
        </div>

        {/* Price Information */}
        <div className="space-y-4">
          {/* Current Price */}
          <div className="text-center">
            <div className="text-3xl font-bold text-white font-heading">
              {formatPrice(currentPrice)}
            </div>
            <div className="flex items-center justify-center space-x-2 mt-1">
              {priceChange24h >= 0 ? (
                <TrendingUp className="w-4 h-4 text-emerald-400" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-400" />
              )}
              <span className={`text-sm font-medium ${
                priceChange24h >= 0 ? 'text-emerald-400' : 'text-red-400'
              }`}>
                {priceChange24h >= 0 ? '+' : ''}{priceChange24h.toFixed(2)}% (24h)
              </span>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-3 gap-4">
            {/* Market Cap */}
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <DollarSign className="w-4 h-4 text-gray-400" />
                <span className="text-xs text-gray-400 font-medium">Market Cap</span>
              </div>
              <div className="text-sm font-bold text-white">
                {marketCap < 1000 
                  ? `${marketCap.toFixed(2)} SOL`
                  : `${(marketCap / 1000).toFixed(1)}K SOL`
                }
              </div>
            </div>

            {/* Volume 24h */}
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <TrendingUp className="w-4 h-4 text-gray-400" />
                <span className="text-xs text-gray-400 font-medium">Volume 24h</span>
              </div>
              <div className="text-sm font-bold text-white">
                {volume24h.toFixed(1)} SOL
              </div>
            </div>

            {/* Holders */}
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <Users className="w-4 h-4 text-gray-400" />
                <span className="text-xs text-gray-400 font-medium">Holders</span>
              </div>
              <div className="text-sm font-bold text-white">
                {Math.floor(Math.random() * 500) + 100} {/* Mock data */}
              </div>
            </div>
          </div>
        </div>

        {/* Supply Information */}
        <div className="bg-gray-800/30 rounded-xl p-4 space-y-3">
          <h3 className="text-sm font-semibold text-gray-300">Supply Information</h3>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-400">Total Supply</p>
              <p className="text-white font-medium">
                {formatTokens(Number(tokenInfo.totalSupply) / 1e9)} {tokenInfo.symbol}
              </p>
            </div>
            
            <div>
              <p className="text-gray-400">Circulating</p>
              <p className="text-white font-medium">
                {formatTokens(Number(tokenInfo.circulatingSupply) / 1e9)} {tokenInfo.symbol}
              </p>
            </div>
            
            {isMigrated && (
              <>
                <div>
                  <p className="text-gray-400">Liquidity Pool</p>
                  <p className="text-white font-medium">
                    {formatSOL(Number(tokenInfo.liquiditySolAmount) / 1e9)}
                  </p>
                </div>
                
                <div>
                  <p className="text-gray-400">Pool Type</p>
                  <p className="text-emerald-400 font-medium">AMM Pool</p>
                </div>
              </>
            )}
            
            {!isMigrated && (
              <>
                <div>
                  <p className="text-gray-400">Bonding Curve</p>
                  <p className="text-blue-400 font-medium">Active</p>
                </div>
                
                <div>
                  <p className="text-gray-400">Creator Fee</p>
                  <p className="text-white font-medium">
                    {(tokenInfo.creatorFeeBps / 100).toFixed(1)}%
                  </p>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Creator Information */}
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-400">Created by</span>
          <span className="text-blue-400 font-medium">
            {tokenInfo.creator.toString().slice(0, 4)}...{tokenInfo.creator.toString().slice(-4)}
          </span>
        </div>
      </div>
    </Card>
  )
}
