'use client'

interface Tab {
  id: string
  label: string
  icon: string
}

interface TokenTabsProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
}

export function TokenTabs({ tabs, activeTab, onTabChange }: TokenTabsProps) {
  return (
    <div className="flex space-x-1 bg-gray-800/50 rounded-2xl p-1">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl font-medium transition-all duration-300 ${
            activeTab === tab.id
              ? "bg-gradient-primary text-white shadow-lg"
              : "text-gray-400 hover:text-white"
          }`}
        >
          <span>{tab.icon}</span>
          <span className="text-sm">{tab.label}</span>
        </button>
      ))}
    </div>
  )
}
