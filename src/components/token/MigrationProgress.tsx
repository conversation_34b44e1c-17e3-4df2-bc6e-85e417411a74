'use client'

import { Card } from '@/components/shared/Card'
// Helper function for formatting SOL
const formatSOL = (amount: number): string => {
  if (amount < 0.001) {
    return `${(amount * 1000).toFixed(3)} mSOL`
  }
  return `${amount.toFixed(6)} SOL`
}
import { DEFAULT_BONDING_CURVE_GOAL } from '@/constants/contracts'
import { Zap, Target, TrendingUp, Clock } from 'lucide-react'

interface MigrationProgressProps {
  tokenInfo: any
  currentPrice: number
}

export function MigrationProgress({ tokenInfo, currentPrice }: MigrationProgressProps) {
  // Calculate migration progress
  const migrationGoal = Number(DEFAULT_BONDING_CURVE_GOAL) / 1e9 // Convert to SOL
  const currentLiquidity = Number(tokenInfo.liquiditySolAmount) / 1e9 // Convert to SOL
  const progressPercentage = Math.min((currentLiquidity / migrationGoal) * 100, 100)
  const remainingSOL = Math.max(migrationGoal - currentLiquidity, 0)
  
  // Estimate time to migration (mock calculation)
  const dailyVolume = 15.5 // Mock daily volume in SOL
  const estimatedDays = remainingSOL > 0 ? Math.ceil(remainingSOL / (dailyVolume * 0.1)) : 0

  return (
    <Card variant="glass" padding="lg" className="border border-blue-400/30 bg-blue-400/5">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-400 to-purple-600 flex items-center justify-center">
              <Zap className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-white font-heading">Migration Progress</h3>
              <p className="text-sm text-gray-400">Progress to AMM liquidity pool</p>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-400">
              {progressPercentage.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-400">Complete</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Current Liquidity</span>
            <span className="text-white font-medium">{formatSOL(currentLiquidity)}</span>
          </div>
          
          <div className="relative">
            {/* Background bar */}
            <div className="w-full h-3 bg-gray-800/50 rounded-full overflow-hidden">
              {/* Progress fill */}
              <div 
                className="h-full bg-gradient-to-r from-blue-400 to-purple-600 rounded-full transition-all duration-1000 ease-out relative"
                style={{ width: `${progressPercentage}%` }}
              >
                {/* Animated shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
              </div>
            </div>
            
            {/* Goal marker */}
            <div className="absolute right-0 top-0 transform translate-x-1/2 -translate-y-1">
              <div className="w-2 h-5 bg-emerald-400 rounded-full" />
              <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-emerald-400 font-medium whitespace-nowrap">
                {formatSOL(migrationGoal)}
              </div>
            </div>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">0 SOL</span>
            <span className="text-emerald-400 font-medium">Migration Goal</span>
          </div>
        </div>

        {/* Migration Stats */}
        <div className="grid grid-cols-3 gap-4">
          {/* Remaining Amount */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-2">
              <Target className="w-4 h-4 text-orange-400" />
              <span className="text-xs text-gray-400 font-medium">Remaining</span>
            </div>
            <div className="text-sm font-bold text-orange-400">
              {formatSOL(remainingSOL)}
            </div>
          </div>

          {/* Current Price */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-2">
              <TrendingUp className="w-4 h-4 text-blue-400" />
              <span className="text-xs text-gray-400 font-medium">Price</span>
            </div>
            <div className="text-sm font-bold text-blue-400">
              {currentPrice.toFixed(8)} SOL
            </div>
          </div>

          {/* Estimated Time */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-2">
              <Clock className="w-4 h-4 text-purple-400" />
              <span className="text-xs text-gray-400 font-medium">ETA</span>
            </div>
            <div className="text-sm font-bold text-purple-400">
              {estimatedDays > 0 ? `${estimatedDays}d` : 'Soon'}
            </div>
          </div>
        </div>

        {/* Migration Benefits */}
        <div className="bg-emerald-400/10 border border-emerald-400/30 rounded-xl p-4">
          <h4 className="text-sm font-semibold text-emerald-300 mb-3">Migration Benefits</h4>
          <div className="space-y-2 text-xs text-gray-300">
            <div className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full" />
              <span>Unlimited liquidity and trading</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full" />
              <span>Lower slippage for large trades</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full" />
              <span>Open liquidity injection support</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full" />
              <span>Advanced trading features</span>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        {progressPercentage < 100 && (
          <div className="text-center">
            <p className="text-sm text-gray-400 mb-2">
              Help reach migration goal by trading!
            </p>
            <div className="text-xs text-blue-400">
              Every trade brings us closer to AMM migration
            </div>
          </div>
        )}

        {/* Migration Ready */}
        {progressPercentage >= 100 && (
          <div className="bg-emerald-400/20 border border-emerald-400/30 rounded-xl p-4 text-center">
            <div className="text-emerald-400 font-semibold mb-1">🎉 Ready for Migration!</div>
            <div className="text-xs text-gray-300">
              This token has reached the migration threshold and can be migrated to an AMM pool
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}
