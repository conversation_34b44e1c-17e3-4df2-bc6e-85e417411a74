'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { useBondingCurve } from '@/hooks/useBondingCurve'
import {
  Coins,
  DollarSign,
  TrendingUp,
  Info,
  AlertTriangle,
  Zap,
  Image as ImageIcon,
  Link,
  CheckCircle,
  XCircle,
  Loader2,
  ExternalLink
} from 'lucide-react'

interface TokenCreationFormProps {
  onSubmit: (tokenData: TokenCreationData) => Promise<void>
  isLoading?: boolean
}

export interface TokenCreationData {
  name: string
  symbol: string
  description: string
  imageUrl: string
  buyFeeBps: number
  sellFeeBps: number
  initialPurchaseAmount: number // SUI amount for initial purchase
  maxWalletPercentage: number // Max percentage of total supply per wallet (0 = no limit)
  linkedLabelId?: string // Optional label to link token to
  website?: string
  twitter?: string
  telegram?: string
  tiktok?: string
}

export function TokenCreationForm({ onSubmit, isLoading = false }: TokenCreationFormProps) {
  const [formData, setFormData] = useState<TokenCreationData>({
    name: '',
    symbol: '',
    description: '',
    imageUrl: '',
    buyFeeBps: 100, // 1% default
    sellFeeBps: 100, // 1% default
    initialPurchaseAmount: 0.1, // 0.1 SUI default
    maxWalletPercentage: 0, // 0 = no limit
    linkedLabelId: '',
    website: '',
    twitter: '',
    telegram: '',
    tiktok: ''
  })

  const [labelValidation, setLabelValidation] = useState<{
    isValid: boolean
    isLoading: boolean
    error?: string
    labelInfo?: {
      name: string
      owner: string
      isOneOfOne: boolean
      isExpired: boolean
      hasOperatorLicense: boolean
    }
  }>({
    isValid: false,
    isLoading: false
  })

  // Mock token info for pricing calculations (would come from actual token after creation)
  const mockTokenInfo = {
    mint: 'mock',
    totalSupply: BigInt(1_000_000_000) * BigInt(1_000_000_000), // 1B tokens with 9 decimals
    circulatingSupply: BigInt(0), // Start with 0 circulating
    liquiditySolAmount: BigInt(0) // Keep as SOL for backward compatibility with hook
  }

  const {
    getBuyQuote,
    calculateTokensForSol,
    formatSol,
    formatTokens,
    formatPrice,
    currentPrice,
    isValidBuyAmount
  } = useBondingCurve(mockTokenInfo)

  // Calculate initial purchase quote
  const initialQuote = getBuyQuote(formData.initialPurchaseAmount)

  // Validate label function
  const validateLabel = async (labelId: string) => {
    if (!labelId.trim()) {
      setLabelValidation({ isValid: false, isLoading: false })
      return
    }

    setLabelValidation({ isValid: false, isLoading: true })

    try {
      // Mock validation - in real implementation, this would call the blockchain
      // to check label ownership, expiration, and operator licenses

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Mock label data - replace with actual blockchain calls
      const mockLabelInfo = {
        name: `Label ${labelId}`,
        owner: '0x123...abc', // Would be actual owner address
        isOneOfOne: true, // Check if supply is 1
        isExpired: false, // Check if current time > expiration_time
        hasOperatorLicense: false // Check if user has valid operator license
      }

      // Validation logic
      const currentUserAddress = '0x123...abc' // Would come from wallet
      const isOwner = mockLabelInfo.owner === currentUserAddress
      const canUseLabel = isOwner || mockLabelInfo.hasOperatorLicense

      if (!mockLabelInfo.isOneOfOne) {
        setLabelValidation({
          isValid: false,
          isLoading: false,
          error: 'Only 1-of-1 labels can be linked to tokens',
          labelInfo: mockLabelInfo
        })
        return
      }

      if (mockLabelInfo.isExpired) {
        setLabelValidation({
          isValid: false,
          isLoading: false,
          error: 'Label registration has expired',
          labelInfo: mockLabelInfo
        })
        return
      }

      if (!canUseLabel) {
        setLabelValidation({
          isValid: false,
          isLoading: false,
          error: 'You must own this label or have an active operator license',
          labelInfo: mockLabelInfo
        })
        return
      }

      setLabelValidation({
        isValid: true,
        isLoading: false,
        labelInfo: mockLabelInfo
      })

    } catch (error) {
      setLabelValidation({
        isValid: false,
        isLoading: false,
        error: 'Failed to validate label. Please check the label ID.'
      })
    }
  }

  const handleInputChange = (field: keyof TokenCreationData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (isLoading) return
    
    await onSubmit(formData)
  }

  const isFormValid = formData.name && 
                     formData.symbol && 
                     formData.description && 
                     formData.initialPurchaseAmount > 0 &&
                     isValidBuyAmount(formData.initialPurchaseAmount)

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Token Details */}
      <Card variant="glass" padding="lg">
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <Coins className="w-6 h-6 text-blue-400" />
            <h3 className="text-xl font-bold text-white font-heading">Token Details</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Token Name</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-transparent"
                placeholder="My Awesome Token"
                maxLength={32}
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Symbol</label>
              <input
                type="text"
                value={formData.symbol}
                onChange={(e) => handleInputChange('symbol', e.target.value.toUpperCase())}
                className="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-transparent"
                placeholder="MAT"
                maxLength={10}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-transparent resize-none"
              placeholder="Describe your token and its purpose..."
              rows={3}
              maxLength={200}
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Image URL</label>
            <div className="flex items-center space-x-3">
              <ImageIcon className="w-5 h-5 text-gray-400" />
              <input
                type="url"
                value={formData.imageUrl}
                onChange={(e) => handleInputChange('imageUrl', e.target.value)}
                className="flex-1 px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-transparent"
                placeholder="https://example.com/token-image.png"
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Label Linking */}
      <Card variant="glass" padding="lg">
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <Link className="w-6 h-6 text-cyan-400" />
            <h3 className="text-xl font-bold text-white font-heading">Link to Label (Optional)</h3>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Label ID</label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.linkedLabelId}
                  onChange={(e) => {
                    handleInputChange('linkedLabelId', e.target.value)
                    if (e.target.value.trim()) {
                      validateLabel(e.target.value.trim())
                    } else {
                      setLabelValidation({ isValid: false, isLoading: false })
                    }
                  }}
                  className="w-full px-4 py-3 pr-12 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent"
                  placeholder="Enter label ID to link your token"
                />

                {/* Validation indicator */}
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {labelValidation.isLoading && (
                    <Loader2 className="w-5 h-5 text-gray-400 animate-spin" />
                  )}
                  {!labelValidation.isLoading && formData.linkedLabelId && labelValidation.isValid && (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  )}
                  {!labelValidation.isLoading && formData.linkedLabelId && !labelValidation.isValid && labelValidation.error && (
                    <XCircle className="w-5 h-5 text-red-400" />
                  )}
                </div>
              </div>

              {/* Validation feedback */}
              {formData.linkedLabelId && !labelValidation.isLoading && (
                <div className="mt-2">
                  {labelValidation.isValid && labelValidation.labelInfo && (
                    <div className="bg-green-400/10 border border-green-400/30 rounded-lg p-3">
                      <div className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="text-sm text-green-300 font-medium">Label Verified ✓</p>
                          <p className="text-xs text-gray-300 mt-1">
                            "{labelValidation.labelInfo.name}" - 1-of-1 Label
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {labelValidation.error && (
                    <div className="bg-red-400/10 border border-red-400/30 rounded-lg p-3">
                      <div className="flex items-start space-x-2">
                        <XCircle className="w-4 h-4 text-red-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="text-sm text-red-300 font-medium">Validation Error</p>
                          <p className="text-xs text-gray-300 mt-1">{labelValidation.error}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Social Links */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">Website</label>
                <input
                  type="url"
                  value={formData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  className="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent"
                  placeholder="https://yourproject.com"
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">Twitter</label>
                <input
                  type="text"
                  value={formData.twitter}
                  onChange={(e) => handleInputChange('twitter', e.target.value)}
                  className="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent"
                  placeholder="@yourproject"
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">Telegram</label>
                <input
                  type="text"
                  value={formData.telegram}
                  onChange={(e) => handleInputChange('telegram', e.target.value)}
                  className="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent"
                  placeholder="@yourproject"
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">TikTok</label>
                <input
                  type="text"
                  value={formData.tiktok}
                  onChange={(e) => handleInputChange('tiktok', e.target.value)}
                  className="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-transparent"
                  placeholder="@yourproject"
                />
              </div>
            </div>

            {/* Info about label linking */}
            <div className="bg-cyan-400/10 border border-cyan-400/30 rounded-xl p-4">
              <div className="flex items-start space-x-3">
                <Info className="w-5 h-5 text-cyan-400 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-cyan-300 font-medium">Label Linking Benefits</p>
                  <ul className="text-xs text-gray-300 mt-1 space-y-1">
                    <li>• Associate your token with a verified 1-of-1 label</li>
                    <li>• Build brand recognition and trust</li>
                    <li>• Enable cross-platform integration</li>
                    <li>• Transfer ownership along with token ownership</li>
                  </ul>
                  <p className="text-xs text-gray-400 mt-2">
                    Requirements: Must own the label or have an active operator license
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Fee Settings */}
      <Card variant="glass" padding="lg">
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <DollarSign className="w-6 h-6 text-emerald-400" />
            <h3 className="text-xl font-bold text-white font-heading">Fee Settings</h3>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Buy Fee</label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={formData.buyFeeBps / 100}
                  onChange={(e) => handleInputChange('buyFeeBps', parseFloat(e.target.value) * 100)}
                  className="flex-1 px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-transparent"
                  min="0"
                  max="5"
                  step="0.1"
                />
                <span className="text-sm text-gray-400">%</span>
              </div>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">Sell Fee</label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  value={formData.sellFeeBps / 100}
                  onChange={(e) => handleInputChange('sellFeeBps', parseFloat(e.target.value) * 100)}
                  className="flex-1 px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-transparent"
                  min="0"
                  max="5"
                  step="0.1"
                />
                <span className="text-sm text-gray-400">%</span>
              </div>
            </div>
          </div>

          {/* Max Wallet Limit */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">Max Wallet Limit</label>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                value={formData.maxWalletPercentage}
                onChange={(e) => handleInputChange('maxWalletPercentage', parseFloat(e.target.value) || 0)}
                className="flex-1 px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-transparent"
                min="0"
                max="100"
                step="0.1"
                placeholder="0 = No limit"
              />
              <span className="text-sm text-gray-400">% of supply</span>
            </div>
            <p className="text-xs text-gray-400">
              Maximum percentage of total supply any wallet can hold (0 = no limit)
            </p>
          </div>

          <div className="bg-blue-400/10 border border-blue-400/30 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <Info className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm text-blue-300 font-medium">Fee Information</p>
                <p className="text-xs text-gray-300 mt-1">
                  These fees are collected on each trade and go to you as the token creator. 
                  Lower fees may encourage more trading activity.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Initial Purchase */}
      <Card variant="glass" padding="lg">
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-6 h-6 text-purple-400" />
            <h3 className="text-xl font-bold text-white font-heading">Initial Purchase</h3>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">SOL Amount</label>
              <input
                type="number"
                value={formData.initialPurchaseAmount}
                onChange={(e) => handleInputChange('initialPurchaseAmount', parseFloat(e.target.value) || 0)}
                className="w-full px-4 py-3 rounded-xl bg-gray-800/50 border border-gray-600/30 text-white focus:outline-none focus:ring-2 focus:ring-purple-400/50 focus:border-transparent"
                min="0.001"
                max="100"
                step="0.001"
                placeholder="0.1"
              />
            </div>

            {/* Purchase Preview */}
            {formData.initialPurchaseAmount > 0 && (
              <div className="bg-purple-400/10 border border-purple-400/30 rounded-xl p-4 space-y-3">
                <h4 className="text-sm font-semibold text-purple-300">Purchase Preview</h4>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-400">You'll receive:</p>
                    <p className="text-white font-medium">
                      {formatTokens(initialQuote.tokenAmount)} {formData.symbol || 'tokens'}
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-gray-400">Price per token:</p>
                    <p className="text-white font-medium">
                      {formatPrice(initialQuote.pricePerToken)}
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-gray-400">Price impact:</p>
                    <p className={`font-medium ${initialQuote.priceImpact > 5 ? 'text-red-400' : 'text-emerald-400'}`}>
                      +{initialQuote.priceImpact.toFixed(2)}%
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-gray-400">Starting price:</p>
                    <p className="text-white font-medium">
                      {formatPrice(currentPrice)}
                    </p>
                  </div>
                </div>

                {initialQuote.priceImpact > 10 && (
                  <div className="flex items-start space-x-2 mt-3 p-3 bg-yellow-400/10 border border-yellow-400/30 rounded-lg">
                    <AlertTriangle className="w-4 h-4 text-yellow-400 flex-shrink-0 mt-0.5" />
                    <p className="text-xs text-yellow-300">
                      High price impact! Consider a smaller initial purchase.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Submit Button */}
      <Button
        type="submit"
        variant="gradient"
        size="lg"
        className="w-full bg-gradient-to-r from-blue-400 to-purple-600 hover:from-blue-500 hover:to-purple-700"
        disabled={!isFormValid || isLoading}
      >
        {isLoading ? (
          <>
            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
            Creating Token...
          </>
        ) : (
          <>
            <Zap className="w-5 h-5 mr-2" />
            Create Token & Buy {formatSol(formData.initialPurchaseAmount)}
          </>
        )}
      </Button>

      {/* Cost Breakdown */}
      <Card variant="glass" padding="md" className="border border-gray-600/30">
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">Token Creation Fee:</span>
            <span className="text-white">0.1 SOL</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Initial Purchase:</span>
            <span className="text-white">{formatSol(formData.initialPurchaseAmount)}</span>
          </div>
          <div className="border-t border-gray-600/30 pt-2 flex justify-between font-medium">
            <span className="text-gray-300">Total Cost:</span>
            <span className="text-white">{formatSol(0.1 + formData.initialPurchaseAmount)}</span>
          </div>
        </div>
      </Card>
    </form>
  )
}
