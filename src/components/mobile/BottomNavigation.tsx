'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { usePathname, useRouter } from 'next/navigation'
import {
  Compass,
  ArrowLeftRight,
  Plus,
  Bot,
  Image,
  Home,
  RefreshCw
} from 'lucide-react'
import { cn, triggerHaptic } from '@/lib/utils'
import { bottomNavSlideUp, buttonTap, pulseAnimation } from '@/lib/animations'
import { QuickSwapModal } from '@/components/shared/QuickSwapModal'

interface NavItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  isCenter?: boolean
}

interface NavItemExtended extends NavItem {
  emoji: string
  gradient: string
  hoverGradient: string
}

const navItems: NavItemExtended[] = [
  {
    id: 'discover',
    label: 'Discover',
    icon: Compass,
    href: '/discover',
    emoji: '🚀',
    gradient: 'from-purple-500 to-pink-500',
    hoverGradient: 'from-purple-400 to-pink-400'
  },
  {
    id: 'swap',
    label: 'Swap',
    icon: ArrowLeftRight,
    href: '/swap',
    emoji: '⚡',
    gradient: 'from-blue-500 to-cyan-500',
    hoverGradient: 'from-blue-400 to-cyan-400'
  },
  {
    id: 'create',
    label: 'Create',
    icon: Plus,
    href: '/create',
    isCenter: true,
    emoji: '✨',
    gradient: 'from-yellow-400 to-orange-500',
    hoverGradient: 'from-yellow-300 to-orange-400'
  },
  {
    id: 'bank',
    label: 'Bank',
    icon: Bot,
    href: '/bank',
    emoji: '🏦',
    gradient: 'from-blue-500 to-indigo-500',
    hoverGradient: 'from-blue-400 to-indigo-400'
  },
  {
    id: 'nft',
    label: 'XFT',
    icon: Image,
    href: '/nft',
    emoji: '🎨',
    gradient: 'from-indigo-500 to-purple-500',
    hoverGradient: 'from-indigo-400 to-purple-400'
  }
]

export function BottomNavigation() {
  const pathname = usePathname()
  const router = useRouter()
  const [showQuickSwap, setShowQuickSwap] = useState(false)

  const handleNavigation = (item: NavItem) => {
    triggerHaptic('light')
    router.push(item.href)
  }

  const handleCenterButtonClick = () => {
    triggerHaptic('light')

    // Check if we're on a token page
    if (pathname.startsWith('/token/')) {
      // Show quick swap modal
      setShowQuickSwap(true)
    } else if (pathname === '/') {
      // On home page, go to create
      router.push('/create')
    } else {
      // On other pages, go to home
      router.push('/')
    }
  }

  const handleQuickSwap = (settings: any) => {
    // Handle quick swap logic here
    console.log('Quick swap with settings:', settings)
    // In real app, this would execute the swap
  }

  const isActive = (href: string) => {
    if (href === '/') return pathname === '/'
    return pathname.startsWith(href)
  }

  const isOnHomePage = pathname === '/'
  const isOnTokenPage = pathname.startsWith('/token/')

  // Mock token data for quick swap - in real app this would come from the token page
  const currentToken = {
    symbol: 'SAVG',
    price: 0.0234
  }

  // Mock user token ownership - in real app this would come from wallet
  const userHasTokens = Math.random() > 0.5

  return (
    <motion.div
      variants={bottomNavSlideUp}
      initial="initial"
      animate="animate"
      className="fixed bottom-0 left-0 right-0 z-50 safe-bottom md:hidden"
    >
      {/* Center button positioned above nav bar */}
      <div className="relative">
        {navItems.map((item) => {
          if (item.isCenter) {
            // Dynamic icon based on current page
            let CenterIcon = Plus
            let buttonColor = "from-cyan-400 to-cyan-600"
            let borderColor = "border-cyan-300/50"
            let pulseColor = "shadow-cyan-400/30"

            if (isOnTokenPage) {
              CenterIcon = RefreshCw
              buttonColor = "from-yellow-400 to-orange-500"
              borderColor = "border-yellow-300/50"
              pulseColor = "shadow-yellow-400/30"
            } else if (!isOnHomePage) {
              CenterIcon = Home
            }

            return (
              <div key={item.id} className="absolute left-1/2 transform -translate-x-1/2 -top-8 z-10">
                {/* Multiple pulsing rings for more fun effect */}
                <motion.div
                  animate={{
                    scale: [1, 1.4, 1],
                    opacity: [0.6, 0, 0.6]
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className={cn(
                    "absolute inset-0 rounded-full",
                    `bg-gradient-to-br ${buttonColor}`,
                    pulseColor
                  )}
                />

                <motion.div
                  animate={{
                    scale: [1, 1.6, 1],
                    opacity: [0.3, 0, 0.3]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5
                  }}
                  className={cn(
                    "absolute inset-0 rounded-full",
                    `bg-gradient-to-br ${buttonColor}`,
                    pulseColor
                  )}
                />

                {/* Main center button with enhanced styling */}
                <motion.button
                  whileTap={{ scale: 0.9 }}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  onClick={handleCenterButtonClick}
                  className={cn(
                    "relative w-16 h-16 rounded-full shadow-2xl flex flex-col items-center justify-center border-2 z-10 overflow-hidden",
                    `bg-gradient-to-br ${buttonColor}`,
                    borderColor,
                    "hover:shadow-3xl transition-all duration-300"
                  )}
                >
                  {/* Background sparkle effect */}
                  <motion.div
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                    className="absolute inset-0 bg-gradient-to-r from-white/20 via-transparent to-white/20"
                  />

                  {/* Just the emoji */}
                  <div className="relative z-10 text-2xl">
                    {isOnTokenPage ? '🔄' : !isOnHomePage ? '🏠' : '✨'}
                  </div>
                </motion.button>
              </div>
            )
          }
          return null
        })}

        {/* Main navigation bar */}
        <div className="bg-gradient-to-r from-[#0A0A0F]/95 via-[#1A0A2E]/95 to-[#0A0A0F]/95 backdrop-blur-xl border-t border-purple-400/30 shadow-lg shadow-purple-500/10">
          <div className="px-6 py-3">
            <div className="flex items-center justify-between">
              {navItems.map((item) => {
                const Icon = item.icon
                const active = isActive(item.href)

                if (item.isCenter) {
                  // Empty space for center button with golden ratio width
                  return <div key={item.id} className="w-16" />
                }

                return (
                  <motion.button
                    key={item.id}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleNavigation(item)}
                    className="flex flex-col items-center justify-center space-y-1 p-2 group min-w-[60px]"
                  >
                    {/* Emoji with circular border for active state */}
                    <div className={cn(
                      "text-lg transition-all duration-300 group-hover:scale-110 w-10 h-10 rounded-full flex items-center justify-center",
                      active ? "border-2 border-green-400 bg-green-400/10" : ""
                    )}>
                      {item.emoji}
                    </div>

                    {/* Label */}
                    <span className={cn(
                      "text-xs font-medium transition-all duration-200 font-body",
                      active ? "text-white font-semibold" : "text-gray-400 group-hover:text-white"
                    )}>
                      {item.label}
                    </span>
                  </motion.button>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Swap Modal */}
      <QuickSwapModal
        isOpen={showQuickSwap}
        onClose={() => setShowQuickSwap(false)}
        tokenSymbol={currentToken.symbol}
        tokenPrice={currentToken.price}
        userHasTokens={userHasTokens}
        onQuickSwap={handleQuickSwap}
      />
    </motion.div>
  )
}
