'use client'

import { motion } from 'framer-motion'
import { useScrollDirection } from '@/hooks/useScrollDirection'
import { BottomNavigation } from './BottomNavigation'
import { MobileHeader } from './MobileHeader'
import { DesktopFloatingNav } from '../desktop/DesktopFloatingNav'
import { cn } from '@/lib/utils'
import { fadeInUp } from '@/lib/animations'

interface MobileLayoutProps {
  children: React.ReactNode
  showHeader?: boolean
  showBottomNav?: boolean
  headerTitle?: string
  headerActions?: React.ReactNode
  showBackButton?: boolean
  className?: string
}

export function MobileLayout({
  children,
  showHeader = true,
  showBottomNav = true,
  headerTitle,
  headerActions,
  showBackButton = false,
  className
}: MobileLayoutProps) {
  const { scrollDirection, isScrolled } = useScrollDirection()

  return (
    <div className="min-h-screen bg-dark-bg text-white overflow-x-hidden">
      {/* Background gradient */}
      <div className="fixed inset-0 gradient-cosmic pointer-events-none" />
      
      {/* Mobile Header */}
      {showHeader && (
        <MobileHeader
          title={headerTitle}
          actions={headerActions}
          showBackButton={showBackButton}
          isScrolled={isScrolled}
          isVisible={scrollDirection !== 'down'}
        />
      )}
      
      {/* Main Content */}
      <motion.main
        variants={fadeInUp}
        initial="initial"
        animate="animate"
        className={cn(
          "relative z-10 min-h-screen",
          showHeader && "mt-[66px]", // 66px margin-top for proper spacing
          showBottomNav && "pb-32 md:pb-6", // Extra space for elevated center button on mobile, normal padding on desktop
          "safe-top safe-bottom",
          className
        )}
      >
        <div className="mobile-container px-6 py-6">
          {children}
        </div>
      </motion.main>
      
      {/* Bottom Navigation */}
      {showBottomNav && <BottomNavigation />}

      {/* Desktop Floating Navigation */}
      <DesktopFloatingNav />
    </div>
  )
}
