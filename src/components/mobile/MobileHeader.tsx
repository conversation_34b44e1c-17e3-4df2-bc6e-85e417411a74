'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, Menu, Wallet, Settings, LogOut, X, User } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { cn, triggerHaptic, truncateAddress } from '@/lib/utils'
import { fadeInDown } from '@/lib/animations'
import { useWallet, ConnectButton } from '@suiet/wallet-kit'
import { Dropdown } from '@/components/shared/Dropdown'


interface MobileHeaderProps {
  title?: string
  showBackButton?: boolean
  actions?: React.ReactNode
  isScrolled?: boolean
  isVisible?: boolean
  onMenuClick?: () => void
}

export function MobileHeader({
  title = 'Dexsta',
  showBackButton = false,
  actions,
  isScrolled = false,
  isVisible = true,
  onMenuClick
}: MobileHeaderProps) {
  const router = useRouter()
  const { connected, account, disconnect } = useWallet()
  const [showMenuPanel, setShowMenuPanel] = useState(false)

  const handleBackClick = () => {
    triggerHaptic('light')
    router.back()
  }

  const handleMenuClick = () => {
    triggerHaptic('light')
    setShowMenuPanel(true)
    onMenuClick?.()
  }

  const handleDisconnectWallet = async () => {
    triggerHaptic('light')
    try {
      await disconnect()
    } catch (error) {
      console.error('Failed to disconnect wallet:', error)
    }
  }

  // Generate avatar from wallet address
  const walletAddress = account?.address
  const userAvatar = walletAddress ? `https://api.dicebear.com/7.x/avataaars/svg?seed=${walletAddress}` : null

  const dropdownItems = [
    {
      id: 'profile',
      label: 'Profile',
      icon: <User className="w-4 h-4" />,
      onClick: () => {
        triggerHaptic('light')
        router.push('/profile')
      }
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="w-4 h-4" />,
      onClick: () => {
        triggerHaptic('light')
        router.push('/settings')
      }
    },
    {
      id: 'disconnect',
      label: 'Disconnect',
      icon: <LogOut className="w-4 h-4" />,
      onClick: handleDisconnectWallet,
      variant: 'danger' as const
    }
  ]

  return (
    <motion.header
      variants={fadeInDown}
      initial="initial"
      animate={isVisible ? "animate" : "initial"}
      className={cn(
        "fixed top-0 left-0 right-0 z-40 transition-all duration-300",
        "safe-top",
        isScrolled ? "glass-dark shadow-lg" : "bg-transparent"
      )}
    >
      <div className="flex items-center justify-between h-16 px-4">
        {/* Left side */}
        <div className="flex items-center space-x-3">
          {showBackButton ? (
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={handleBackClick}
              className="p-2 rounded-full glass-button"
            >
              <ArrowLeft className="w-5 h-5" />
            </motion.button>
          ) : (
            <motion.button
              whileTap={{ scale: 0.95 }}
              onClick={handleMenuClick}
              className="p-2 rounded-full glass-button border border-emerald-400/30 hover:border-emerald-400/60"
            >
              <Menu className="w-5 h-5 text-emerald-400" />
            </motion.button>
          )}
        </div>

        {/* Center - Title/Logo */}
        <div className="flex-1 flex justify-center">
          <motion.h1
            className="text-2xl font-bold bg-gradient-to-r from-[#9945FF] via-[#14F195] to-[#00D4FF] bg-clip-text text-transparent"
            style={{ fontFamily: 'Matangi, cursive' }}
            whileTap={{ scale: 0.95 }}
          >
            dexsta
          </motion.h1>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-2">
          {actions || (
            connected ? (
              // User Avatar with Dropdown
              <Dropdown
                trigger={
                  <motion.div
                    whileTap={{ scale: 0.95 }}
                    className="w-10 h-10 rounded-full overflow-hidden border-2 border-emerald-400/60 cursor-pointer"
                  >
                    {userAvatar ? (
                      <div
                        className="w-full h-full bg-cover bg-center"
                        style={{ backgroundImage: `url(${userAvatar})` }}
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
                        <span className="text-white text-sm font-semibold">
                          {walletAddress ? truncateAddress(walletAddress, 2) : 'U'}
                        </span>
                      </div>
                    )}
                  </motion.div>
                }
                items={dropdownItems}
                align="right"
              />
            ) : (
              // Connect Wallet using Suiet ConnectButton (wrapped for React 19 compatibility)
              <div className="relative">
                <ConnectButton
                  style={{
                    padding: '8px',
                    borderRadius: '50%',
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(20, 241, 149, 0.3)',
                    color: 'rgb(20, 241, 149)',
                    minWidth: 'auto',
                    width: '40px',
                    height: '40px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  onConnectSuccess={(wallet) => {
                    console.log('Wallet connected:', wallet)
                    triggerHaptic('light')
                  }}
                  onConnectError={(error) => {
                    console.error('Wallet connection failed:', error)
                  }}
                >
                  <Wallet className="w-5 h-5" />
                </ConnectButton>
              </div>
            )
          )}
        </div>
      </div>



      {/* Menu Panel */}
      <AnimatePresence>
        {showMenuPanel && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex"
            style={{ background: 'rgba(10, 10, 15, 0.9)' }}
            onClick={() => setShowMenuPanel(false)}
          >
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="w-80 max-w-[85vw] h-full bg-gradient-to-br from-gray-900/95 to-gray-800/95 border-r-2 border-emerald-400/30 backdrop-blur-sm shadow-xl"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Panel Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
                <div>
                  <h2 className="text-xl font-bold text-white font-heading">Menu</h2>
                  <p className="text-sm text-gray-400">Explore Dexsta</p>
                </div>
                <button
                  onClick={() => setShowMenuPanel(false)}
                  className="p-2 rounded-full hover:bg-white/10 transition-colors"
                >
                  <X className="w-5 h-5 text-gray-400" />
                </button>
              </div>

              {/* Menu Items */}
              <div className="p-6 space-y-2">
                {[
                  { label: 'About Us', href: '/about', icon: '🏢' },
                  { label: 'Trading Fees', href: '/trading-fees', icon: '💰' },
                  { label: 'How to Launch a Token', href: '/how-to-launch', icon: '🚀' },
                  { label: 'Trading Game', href: '/trading-game', icon: '🎮' },
                  { label: 'Open Liquidity', href: '/open-liquidity', icon: '🌊' },
                  { label: 'Private Liquidity Pools', href: '/private-liquidity', icon: '🔒' },
                  { label: 'User Agreement', href: '/user-agreement', icon: '📋' },
                  { label: 'Privacy Policy', href: '/privacy-policy', icon: '🔐' },
                  { label: 'Support', href: '/support', icon: '💬' },
                  { label: 'Admin Panel', href: '/admin', icon: '⚙️' }
                ].map((item) => (
                  <motion.button
                    key={item.href}
                    onClick={() => {
                      router.push(item.href)
                      setShowMenuPanel(false)
                    }}
                    className="w-full flex items-center space-x-4 p-4 rounded-xl hover:bg-emerald-400/10 hover:border-emerald-400/30 transition-all duration-300 border border-transparent text-left"
                    whileHover={{ x: 4 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span className="text-2xl">{item.icon}</span>
                    <span className="text-white font-medium">{item.label}</span>
                  </motion.button>
                ))}
              </div>

              {/* Panel Footer */}
              <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-gray-700/50">
                <div className="text-center">
                  <p className="text-sm text-gray-400">Dexsta v1.0</p>
                  <p className="text-xs text-gray-500 mt-1">Built on Sui</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  )
}
