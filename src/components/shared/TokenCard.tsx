'use client'

import { motion } from 'framer-motion'
import { TrendingUp, TrendingDown, ExternalLink, ArrowUp, ArrowDown, Clock } from 'lucide-react'
import { Card } from './Card'
import { Button } from './Button'
import { cn, formatNumber, formatPercentage, getPriceChangeColor, generateTokenGradient, getRelativeTime } from '@/lib/utils'
import { staggerItem } from '@/lib/animations'
import { useRouter } from 'next/navigation'

interface TokenData {
  id: string
  name: string
  symbol: string
  price: number
  priceChange24h: number
  volume24h: number
  marketCap: number
  holders?: number // Number of token holders
  ath?: number
  priceHistory?: number[] // Last few price points for trend analysis
  launchedAt?: Date
  migrationStatus?: 'pre-migration' | 'migrated' // Migration status for border styling
  image?: string
  description?: string
  creator?: string
  createdAt?: Date
}

interface TokenCardProps {
  token: TokenData
  variant?: 'default' | 'featured' | 'compact'
  onTrade?: (token: TokenData) => void
  onView?: (token: TokenData) => void
  className?: string
}

export function TokenCard({
  token,
  variant = 'default',
  onTrade,
  onView,
  className
}: TokenCardProps) {
  const router = useRouter()
  const isPositive = token.priceChange24h >= 0
  const gradient = generateTokenGradient(token.symbol)

  const handleCardClick = () => {
    // Navigate to token page using the token ID as address
    router.push(`/token/${token.id}`)
  }

  // Calculate price trend arrows based on price history
  const getTrendArrows = () => {
    if (!token.priceHistory || token.priceHistory.length < 2) {
      return isPositive ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />
    }

    const recent = token.priceHistory.slice(-3)
    const isUptrend = recent.every((price, i) => i === 0 || price >= recent[i - 1])
    const isDowntrend = recent.every((price, i) => i === 0 || price <= recent[i - 1])

    if (isUptrend && recent.length >= 3) {
      return (
        <div className="flex">
          <ArrowUp className="w-3 h-3" />
          <ArrowUp className="w-3 h-3 -ml-1" />
        </div>
      )
    }
    if (isDowntrend && recent.length >= 3) {
      return (
        <div className="flex">
          <ArrowDown className="w-3 h-3" />
          <ArrowDown className="w-3 h-3 -ml-1" />
        </div>
      )
    }

    return isPositive ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />
  }

  // Get migration border styling - only migrated tokens get green border
  const getMigrationBorder = () => {
    if (token.migrationStatus === 'migrated') {
      return "!border-2 !border-emerald-400 shadow-lg shadow-emerald-400/30 ring-1 ring-emerald-400/20"
    }
    return "" // No border for pre-migration tokens
  }

  if (variant === 'compact') {
    return (
      <motion.div
        variants={staggerItem}
        className={cn("p-4 rounded-xl glass-card cursor-pointer", getMigrationBorder(), className)}
        onClick={handleCardClick}
      >
        {/* Top Row: Token Info + Price + Trend */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            {/* Token Avatar */}
            <div className="relative w-12 h-12 flex-shrink-0">
              {token.image ? (
                <img
                  src={token.image}
                  alt={token.name}
                  className="w-12 h-12 rounded-full object-cover border-2 border-gray-600/40"
                />
              ) : (
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-sm border-2 border-gray-600/40"
                  style={{ background: gradient }}
                >
                  {token.symbol.slice(0, 2).toUpperCase()}
                </div>
              )}
            </div>

            {/* Token Name & Symbol */}
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-white truncate font-heading text-base">{token.name}</h3>
              <p className="text-sm text-gray-400">{token.symbol}</p>
            </div>
          </div>

          {/* Price & Trend Arrows */}
          <div className="text-right flex-shrink-0">
            <div className="flex items-center space-x-2">
              <div className={cn("flex items-center", getPriceChangeColor(token.priceChange24h))}>
                {getTrendArrows()}
              </div>
              <div>
                <p className="font-semibold text-white text-base">${formatNumber(token.price, 4)}</p>
                <p className={cn("text-sm font-medium", getPriceChangeColor(token.priceChange24h))}>
                  {formatPercentage(token.priceChange24h)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Row: Market Data */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-3">
            {/* Market Cap */}
            <div>
              <span className="text-gray-400">MC </span>
              <span className="text-white font-medium">${formatNumber(token.marketCap)}</span>
            </div>

            {/* Holders */}
            {token.holders && (
              <div>
                <span className="text-gray-400">H </span>
                <span className="text-white font-medium">{formatNumber(token.holders, 0)}</span>
              </div>
            )}

            {/* ATH if available */}
            {token.ath && (
              <div>
                <span className="text-gray-400">ATH </span>
                <span className="text-white font-medium">${formatNumber(token.ath, 4)}</span>
              </div>
            )}
          </div>

          {/* Time Launched */}
          <div className="flex items-center space-x-1 text-gray-400">
            <Clock className="w-3 h-3" />
            <span className="text-xs">
              {token.launchedAt ? getRelativeTime(token.launchedAt) :
               token.createdAt ? getRelativeTime(token.createdAt) : '24m'}
            </span>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <Card
      variant="glass"
      interactive
      className={cn("overflow-hidden", className)}
      onClick={() => onView?.(token)}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          {/* Token Avatar */}
          <div 
            className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold"
            style={{ background: gradient }}
          >
            {token.symbol.slice(0, 2).toUpperCase()}
          </div>
          
          <div>
            <h3 className="font-semibold text-white font-heading">{token.name}</h3>
            <p className="text-gray-400">{token.symbol}</p>
          </div>
        </div>

        {/* Price Change Indicator */}
        <div className={cn(
          "flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium",
          isPositive ? "bg-primary-green/20 text-primary-green" : "bg-red-500/20 text-red-400"
        )}>
          {isPositive ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
          <span>{formatPercentage(token.priceChange24h)}</span>
        </div>
      </div>

      {/* Price */}
      <div className="mb-4">
        <div className="text-2xl font-bold text-white font-heading">
          ${formatNumber(token.price, 4)}
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <p className="text-xs text-gray-400 mb-1">Volume 24h</p>
          <p className="text-sm font-semibold text-white">${formatNumber(token.volume24h)}</p>
        </div>
        <div>
          <p className="text-xs text-gray-400 mb-1">Market Cap</p>
          <p className="text-sm font-semibold text-white">${formatNumber(token.marketCap)}</p>
        </div>
      </div>

      {/* Description */}
      {token.description && (
        <p className="text-sm text-gray-300 mb-4 line-clamp-2">
          {token.description}
        </p>
      )}

      {/* Actions */}
      <div className="flex space-x-2">
        <Button
          variant="gradient"
          size="sm"
          className="flex-1"
          onClick={(e) => {
            e.stopPropagation()
            onTrade?.(token)
          }}
        >
          Trade
        </Button>
        <Button
          variant="glass"
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            onView?.(token)
          }}
        >
          <ExternalLink className="w-4 h-4" />
        </Button>
      </div>
    </Card>
  )
}
