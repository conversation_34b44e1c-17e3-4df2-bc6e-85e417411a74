'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Check, TrendingUp, TrendingDown, Calendar, DollarSign, Users, Activity } from 'lucide-react'
import { Card } from './Card'
import { Button } from './Button'
import { cn } from '@/lib/utils'

export interface FilterOptions {
  sortBy: 'marketcap' | 'volume' | 'created' | 'lastTrade' | 'holders'
  sortOrder: 'highest' | 'lowest' | 'newest' | 'oldest'
}

interface FilterModalProps {
  isOpen: boolean
  onClose: () => void
  currentFilter: FilterOptions
  onApplyFilter: (filter: FilterOptions) => void
}

export function FilterModal({ isOpen, onClose, currentFilter, onApplyFilter }: FilterModalProps) {
  const [tempFilter, setTempFilter] = useState<FilterOptions>(currentFilter)

  const filterOptions = [
    {
      id: 'marketcap',
      label: 'Market Cap',
      icon: DollarSign,
      orders: [
        { value: 'highest', label: 'Highest First', icon: TrendingUp },
        { value: 'lowest', label: 'Lowest First', icon: TrendingDown }
      ]
    },
    {
      id: 'volume',
      label: 'Volume 24h',
      icon: Activity,
      orders: [
        { value: 'highest', label: 'Highest First', icon: TrendingUp },
        { value: 'lowest', label: 'Lowest First', icon: TrendingDown }
      ]
    },
    {
      id: 'created',
      label: 'Create Date',
      icon: Calendar,
      orders: [
        { value: 'newest', label: 'Newest First', icon: TrendingUp },
        { value: 'oldest', label: 'Oldest First', icon: TrendingDown }
      ]
    },
    {
      id: 'lastTrade',
      label: 'Last Trade',
      icon: Activity,
      orders: [
        { value: 'newest', label: 'Most Recent', icon: TrendingUp },
        { value: 'oldest', label: 'Least Recent', icon: TrendingDown }
      ]
    },
    {
      id: 'holders',
      label: 'Holders',
      icon: Users,
      orders: [
        { value: 'highest', label: 'Most Holders', icon: TrendingUp },
        { value: 'lowest', label: 'Least Holders', icon: TrendingDown }
      ]
    }
  ]

  const handleApply = () => {
    onApplyFilter(tempFilter)
    onClose()
  }

  const handleReset = () => {
    const defaultFilter: FilterOptions = { sortBy: 'marketcap', sortOrder: 'highest' }
    setTempFilter(defaultFilter)
    onApplyFilter(defaultFilter)
    onClose()
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-end justify-center p-4 pb-24"
          style={{ background: 'rgba(10, 10, 15, 0.9)' }}
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 50 }}
            className="w-full max-w-md bg-gradient-to-br from-gray-900/95 to-gray-800/95 border-2 border-emerald-400/30 rounded-2xl backdrop-blur-sm shadow-xl shadow-emerald-400/10"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
              <h3 className="text-xl font-bold text-white font-heading">Filter Tokens</h3>
              <button
                onClick={onClose}
                className="p-2 rounded-full hover:bg-white/10 transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>

            {/* Filter Options */}
            <div className="p-6 space-y-6">
              {filterOptions.map((option) => {
                const IconComponent = option.icon
                const isSelected = tempFilter.sortBy === option.id
                
                return (
                  <div key={option.id} className="space-y-3">
                    {/* Filter Category */}
                    <button
                      onClick={() => setTempFilter(prev => ({ 
                        ...prev, 
                        sortBy: option.id as FilterOptions['sortBy'],
                        sortOrder: option.orders[0].value as FilterOptions['sortOrder']
                      }))}
                      className={cn(
                        "w-full flex items-center space-x-3 p-3 rounded-xl transition-all duration-200",
                        isSelected 
                          ? "bg-emerald-400/20 border border-emerald-400/50" 
                          : "bg-white/5 border border-gray-600/30 hover:bg-white/10"
                      )}
                    >
                      <IconComponent className={cn("w-5 h-5", isSelected ? "text-emerald-400" : "text-gray-400")} />
                      <span className={cn("font-medium", isSelected ? "text-emerald-300" : "text-white")}>
                        {option.label}
                      </span>
                      {isSelected && <Check className="w-4 h-4 text-emerald-400 ml-auto" />}
                    </button>

                    {/* Sort Order Options */}
                    {isSelected && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        className="ml-8 space-y-2"
                      >
                        {option.orders.map((order) => {
                          const OrderIcon = order.icon
                          const isOrderSelected = tempFilter.sortOrder === order.value
                          
                          return (
                            <button
                              key={order.value}
                              onClick={() => setTempFilter(prev => ({ 
                                ...prev, 
                                sortOrder: order.value as FilterOptions['sortOrder']
                              }))}
                              className={cn(
                                "w-full flex items-center space-x-3 p-2 rounded-lg transition-all duration-200",
                                isOrderSelected 
                                  ? "bg-emerald-400/10 text-emerald-300" 
                                  : "text-gray-400 hover:text-white hover:bg-white/5"
                              )}
                            >
                              <OrderIcon className="w-4 h-4" />
                              <span className="text-sm">{order.label}</span>
                              {isOrderSelected && <Check className="w-3 h-3 ml-auto" />}
                            </button>
                          )
                        })}
                      </motion.div>
                    )}
                  </div>
                )
              })}
            </div>

            {/* Actions */}
            <div className="flex space-x-3 p-6 border-t border-gray-700/50">
              <Button
                variant="glass"
                size="md"
                className="flex-1"
                onClick={handleReset}
              >
                Reset
              </Button>
              <Button
                variant="gradient"
                size="md"
                className="flex-1 bg-gradient-to-r from-emerald-400 to-emerald-600"
                onClick={handleApply}
              >
                Apply Filter
              </Button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
