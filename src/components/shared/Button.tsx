'use client'

import { motion } from 'framer-motion'
import { forwardRef } from 'react'
import { cn, triggerHaptic } from '@/lib/utils'
import { buttonHover, buttonTap } from '@/lib/animations'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'glass' | 'gradient'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  isLoading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  haptic?: boolean
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    isLoading = false,
    leftIcon,
    rightIcon,
    haptic = true,
    children,
    onClick,
    disabled,
    ...props
  }, ref) => {
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (haptic) triggerHaptic('light')
      onClick?.(e)
    }

    const baseClasses = cn(
      "relative inline-flex items-center justify-center rounded-2xl font-medium transition-all duration-300",
      "focus:outline-none focus:ring-2 focus:ring-primary-purple/50 focus:ring-offset-2 focus:ring-offset-dark-bg",
      "disabled:opacity-50 disabled:cursor-not-allowed",
      "font-body"
    )

    const variants = {
      primary: "bg-primary-purple text-white hover:bg-primary-purple/90 shadow-lg hover:shadow-xl",
      secondary: "bg-dark-surface text-white border border-dark-border hover:bg-dark-border",
      ghost: "text-white hover:bg-white/10",
      glass: "glass-button text-white",
      gradient: "bg-gradient-primary text-white shadow-lg hover:shadow-xl"
    }

    const sizes = {
      sm: "px-4 py-2 text-sm min-h-[36px]",
      md: "px-6 py-3 text-base min-h-[44px]",
      lg: "px-8 py-4 text-lg min-h-[52px]",
      xl: "px-10 py-5 text-xl min-h-[60px]"
    }

    return (
      <motion.button
        ref={ref}
        whileHover={!disabled ? buttonHover : undefined}
        whileTap={!disabled ? buttonTap : undefined}
        className={cn(
          baseClasses,
          variants[variant],
          sizes[size],
          className
        )}
        onClick={handleClick}
        disabled={disabled || isLoading}
        {...props}
      >
        {/* Loading spinner */}
        {isLoading && (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
          >
            <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full" />
          </motion.div>
        )}

        {/* Content */}
        <span className={cn(
          "flex items-center space-x-2",
          isLoading && "opacity-0"
        )}>
          {leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}
          {children && <span>{children}</span>}
          {rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
        </span>
      </motion.button>
    )
  }
)

Button.displayName = "Button"

export { Button }
