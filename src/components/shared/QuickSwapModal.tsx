'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Settings, ArrowUpDown, Zap } from 'lucide-react'
import { Card } from './Card'
import { Button } from './Button'
import { cn, formatNumber } from '@/lib/utils'

interface QuickSwapSettings {
  solPercentageToBuy: number // % of SOL balance to use for buying
  tokenPercentageToSell: number // % of token balance to sell
}

interface QuickSwapModalProps {
  isOpen: boolean
  onClose: () => void
  tokenSymbol: string
  tokenPrice: number
  userHasTokens: boolean
  onQuickSwap: (settings: QuickSwapSettings) => void
}

export function QuickSwapModal({ 
  isOpen, 
  onClose, 
  tokenSymbol, 
  tokenPrice, 
  userHasTokens,
  onQuickSwap 
}: QuickSwapModalProps) {
  const [settings, setSettings] = useState<QuickSwapSettings>({
    solPercentageToBuy: 25, // Default 25% of SOL balance
    tokenPercentageToSell: 50 // Default 50% of token balance
  })
  const [showSettings, setShowSettings] = useState(false)

  // Mock user balances - in real app this would come from wallet
  const mockSolBalance = 2.5
  const mockTokenBalance = 15000

  const handleQuickSwap = () => {
    onQuickSwap(settings)
    onClose()
  }

  const swapAction = userHasTokens ? 'sell' : 'buy'
  const percentage = userHasTokens ? settings.tokenPercentageToSell : settings.solPercentageToBuy
  const amount = userHasTokens 
    ? (mockTokenBalance * percentage / 100)
    : (mockSolBalance * percentage / 100)

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-end justify-center p-4 pb-24"
          style={{ background: 'rgba(10, 10, 15, 0.9)' }}
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 50 }}
            className="w-full max-w-md bg-gradient-to-br from-gray-900/95 to-gray-800/95 border-2 border-emerald-400/30 rounded-2xl backdrop-blur-sm shadow-xl shadow-emerald-400/10"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5 text-emerald-400" />
                <h3 className="text-xl font-bold text-white font-heading">Quick Swap</h3>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className={cn(
                    "p-2 rounded-full transition-colors",
                    showSettings ? "bg-emerald-400/20 text-emerald-400" : "hover:bg-white/10 text-gray-400"
                  )}
                >
                  <Settings className="w-4 h-4" />
                </button>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-white/10 transition-colors"
                >
                  <X className="w-4 h-4 text-gray-400" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Quick Swap Preview */}
              <div className="text-center space-y-4">
                <div className="p-4 rounded-xl bg-gray-800/50 border border-gray-600/30">
                  <div className="space-y-2">
                    <p className="text-sm text-gray-400">
                      {userHasTokens ? 'Selling' : 'Buying'} {tokenSymbol}
                    </p>
                    <div className="flex items-center justify-center space-x-2">
                      <span className="text-2xl font-bold text-white">
                        {userHasTokens 
                          ? formatNumber(amount, 0) 
                          : formatNumber(amount, 2)
                        }
                      </span>
                      <span className="text-gray-400">
                        {userHasTokens ? tokenSymbol : 'SOL'}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500">
                      {percentage}% of your {userHasTokens ? tokenSymbol : 'SOL'} balance
                    </p>
                  </div>
                </div>

                {/* Swap Arrow */}
                <div className="flex justify-center">
                  <div className="p-2 rounded-full bg-emerald-400/20 border border-emerald-400/50">
                    <ArrowUpDown className="w-4 h-4 text-emerald-400" />
                  </div>
                </div>

                {/* Estimated Receive */}
                <div className="p-4 rounded-xl bg-gray-800/30 border border-gray-600/20">
                  <div className="space-y-2">
                    <p className="text-sm text-gray-400">
                      You'll receive ≈
                    </p>
                    <div className="flex items-center justify-center space-x-2">
                      <span className="text-xl font-bold text-emerald-300">
                        {userHasTokens 
                          ? formatNumber(amount * tokenPrice, 2)
                          : formatNumber(amount / tokenPrice, 0)
                        }
                      </span>
                      <span className="text-gray-400">
                        {userHasTokens ? 'SOL' : tokenSymbol}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Settings Panel */}
              {showSettings && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-4 border-t border-gray-700/50 pt-4"
                >
                  <h4 className="text-lg font-semibold text-white font-heading">Quick Swap Settings</h4>
                  
                  {/* Buy Settings */}
                  <div className="space-y-3">
                    <label className="text-sm text-gray-400">
                      SOL % to use for buying (when you don't own tokens)
                    </label>
                    <div className="flex items-center space-x-3">
                      <input
                        type="range"
                        min="5"
                        max="100"
                        step="5"
                        value={settings.solPercentageToBuy}
                        onChange={(e) => setSettings(prev => ({ 
                          ...prev, 
                          solPercentageToBuy: parseInt(e.target.value) 
                        }))}
                        className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <span className="text-white font-medium w-12 text-center">
                        {settings.solPercentageToBuy}%
                      </span>
                    </div>
                  </div>

                  {/* Sell Settings */}
                  <div className="space-y-3">
                    <label className="text-sm text-gray-400">
                      Token % to sell (when you own tokens)
                    </label>
                    <div className="flex items-center space-x-3">
                      <input
                        type="range"
                        min="5"
                        max="100"
                        step="5"
                        value={settings.tokenPercentageToSell}
                        onChange={(e) => setSettings(prev => ({ 
                          ...prev, 
                          tokenPercentageToSell: parseInt(e.target.value) 
                        }))}
                        className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <span className="text-white font-medium w-12 text-center">
                        {settings.tokenPercentageToSell}%
                      </span>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <Button
                  variant="glass"
                  size="md"
                  className="flex-1"
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <Button
                  variant="gradient"
                  size="md"
                  className={cn(
                    "flex-1 font-bold",
                    userHasTokens
                      ? "bg-gradient-to-r from-red-400 to-red-600 hover:from-red-500 hover:to-red-700"
                      : "bg-gradient-to-r from-emerald-400 to-emerald-600 hover:from-emerald-500 hover:to-emerald-700"
                  )}
                  onClick={handleQuickSwap}
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Quick {swapAction === 'buy' ? 'Buy' : 'Sell'}
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
