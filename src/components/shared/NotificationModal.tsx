'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCircle, XCircle, X, AlertTriangle, Info } from 'lucide-react'
import { Button } from './Button'
import { cn } from '@/lib/utils'
import { modalBackdrop, modalContent } from '@/lib/animations'

interface NotificationModalProps {
  isOpen: boolean
  onClose: () => void
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  autoClose?: boolean
  autoCloseDelay?: number
  actionButton?: {
    label: string
    onClick: () => void
  }
}

export function NotificationModal({
  isOpen,
  onClose,
  type,
  title,
  message,
  autoClose = false,
  autoCloseDelay = 3000,
  actionButton
}: NotificationModalProps) {
  // Auto close functionality
  React.useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onClose()
      }, autoCloseDelay)
      
      return () => clearTimeout(timer)
    }
  }, [isOpen, autoClose, autoCloseDelay, onClose])

  const getIcon = () => {
    switch (type) {
      case 'success':
        return (
          <motion.div
            initial={{ scale: 0 }}
            animate={{
              scale: [1, 1.1, 1],
            }}
            transition={{
              delay: 0.2,
              type: "spring",
              stiffness: 200,
              scale: {
                duration: 0.6,
                repeat: 2,
                ease: "easeInOut"
              }
            }}
          >
            <CheckCircle className="w-12 h-12 text-emerald-400" />
          </motion.div>
        )
      case 'error':
        return (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          >
            <XCircle className="w-12 h-12 text-red-400" />
          </motion.div>
        )
      case 'warning':
        return (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          >
            <AlertTriangle className="w-12 h-12 text-yellow-400" />
          </motion.div>
        )
      case 'info':
        return (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          >
            <Info className="w-12 h-12 text-blue-400" />
          </motion.div>
        )
    }
  }

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          border: 'border-emerald-400/30',
          shadow: 'shadow-emerald-400/10',
          gradient: 'from-emerald-900/20 to-emerald-800/20',
          titleColor: 'text-emerald-300',
          iconBg: 'bg-emerald-400/20'
        }
      case 'error':
        return {
          border: 'border-red-400/30',
          shadow: 'shadow-red-400/10',
          gradient: 'from-red-900/20 to-red-800/20',
          titleColor: 'text-red-300',
          iconBg: 'bg-red-400/20'
        }
      case 'warning':
        return {
          border: 'border-yellow-400/30',
          shadow: 'shadow-yellow-400/10',
          gradient: 'from-yellow-900/20 to-yellow-800/20',
          titleColor: 'text-yellow-300',
          iconBg: 'bg-yellow-400/20'
        }
      case 'info':
        return {
          border: 'border-blue-400/30',
          shadow: 'shadow-blue-400/10',
          gradient: 'from-blue-900/20 to-blue-800/20',
          titleColor: 'text-blue-300',
          iconBg: 'bg-blue-400/20'
        }
    }
  }

  const colors = getColors()

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          variants={modalBackdrop}
          initial="initial"
          animate="animate"
          exit="exit"
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          style={{ background: 'rgba(10, 10, 15, 0.9)' }}
          onClick={onClose}
        >
          <motion.div
            variants={modalContent}
            initial="initial"
            animate="animate"
            exit="exit"
            className={cn(
              "w-full max-w-md bg-gradient-to-br from-gray-900/95 to-gray-800/95 border-2 rounded-2xl backdrop-blur-sm shadow-xl",
              colors.border,
              colors.shadow
            )}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
              <div className="flex items-center space-x-3">
                <div className={cn("p-2 rounded-full", colors.iconBg)}>
                  {getIcon()}
                </div>
                <h2 className={cn("text-xl font-bold font-heading", colors.titleColor)}>
                  {title}
                </h2>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-gray-700/50 transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Message */}
              <div className={cn("p-4 rounded-xl border", colors.gradient, colors.border)}>
                <p className="text-gray-300 leading-relaxed">
                  {message}
                </p>
              </div>

              {/* Auto close indicator */}
              {autoClose && (
                <div className="text-center space-y-2">
                  <div className="w-full bg-gray-700/50 rounded-full h-1 overflow-hidden">
                    <motion.div
                      className={cn(
                        "h-full rounded-full",
                        type === 'success' ? 'bg-emerald-400' : 'bg-blue-400'
                      )}
                      initial={{ width: '100%' }}
                      animate={{ width: '0%' }}
                      transition={{ duration: autoCloseDelay / 1000, ease: 'linear' }}
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Auto-closing in {autoCloseDelay / 1000} seconds
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <Button
                  variant="glass"
                  size="md"
                  className="flex-1"
                  onClick={onClose}
                >
                  Close
                </Button>
                {actionButton && (
                  <Button
                    variant="gradient"
                    size="md"
                    className="flex-1"
                    onClick={() => {
                      actionButton.onClick()
                      onClose()
                    }}
                  >
                    {actionButton.label}
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
