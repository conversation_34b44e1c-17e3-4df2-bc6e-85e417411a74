'use client'

import { motion } from 'framer-motion'
import { forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { scaleIn } from '@/lib/animations'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'gradient' | 'elevated'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  interactive?: boolean
  animate?: boolean
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({
    className,
    variant = 'default',
    padding = 'md',
    interactive = false,
    animate = true,
    children,
    ...props
  }, ref) => {
    const baseClasses = cn(
      "rounded-2xl transition-all duration-300",
      interactive && "cursor-pointer hover:scale-[1.02] active:scale-[0.98]"
    )

    const variants = {
      default: "bg-dark-surface border border-dark-border",
      glass: "glass-card",
      gradient: "bg-gradient-to-br from-dark-surface to-dark-border border border-dark-border",
      elevated: "bg-dark-surface border border-dark-border shadow-2xl"
    }

    const paddings = {
      none: "",
      sm: "p-4",
      md: "p-6",
      lg: "p-8"
    }

    const MotionDiv = animate ? motion.div : 'div'
    const motionProps = animate ? {
      variants: scaleIn,
      initial: "initial",
      animate: "animate",
      whileHover: interactive ? { scale: 1.02 } : undefined,
      whileTap: interactive ? { scale: 0.98 } : undefined
    } : {}

    return (
      <MotionDiv
        ref={ref}
        className={cn(
          baseClasses,
          variants[variant],
          paddings[padding],
          className
        )}
        {...motionProps}
        {...props}
      >
        {children}
      </MotionDiv>
    )
  }
)

Card.displayName = "Card"

export { Card }
