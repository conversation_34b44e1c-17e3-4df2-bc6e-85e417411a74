'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { 
  ChevronDown,
  ChevronRight,
  Coins,
  Shield,
  Calendar,
  Lock,
  TrendingUp,
  Zap,
  Plus,
  Sparkles,
  DollarSign,
  Users,
  Clock,
  Wallet,
  Star,
  Target,
  ArrowRight
} from 'lucide-react'

interface XFTInfoSectionProps {
  onCreateClick: () => void
}

interface AccordionItemProps {
  title: string
  icon: React.ReactNode
  color: string
  children: React.ReactNode
  isOpen: boolean
  onToggle: () => void
}

function AccordionItem({ title, icon, color, children, isOpen, onToggle }: AccordionItemProps) {
  return (
    <Card variant="glass" padding="none" className={`border ${color} overflow-hidden`}>
      <button
        onClick={onToggle}
        className="w-full p-4 flex items-center justify-between hover:bg-white/5 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${color.replace('border-', 'bg-').replace('/20', '/20')}`}>
            {icon}
          </div>
          <h3 className="text-lg font-semibold text-white font-heading">{title}</h3>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 90 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronRight className="w-5 h-5 text-gray-400" />
        </motion.div>
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="p-4 pt-0 border-t border-gray-700/30">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  )
}

export function XFTInfoSection({ onCreateClick }: XFTInfoSectionProps) {
  const [activeAccordion, setActiveAccordion] = useState<string | null>('overview')
  const [activeTab, setActiveTab] = useState<'features' | 'benefits' | 'examples'>('features')

  const toggleAccordion = (id: string) => {
    setActiveAccordion(activeAccordion === id ? null : id)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center space-x-2 mb-3">
          <Sparkles className="w-8 h-8 text-primary-purple" />
          <h2 className="text-3xl font-bold text-white font-heading">XFT NFTs</h2>
          <Sparkles className="w-8 h-8 text-primary-purple" />
        </div>
        <p className="text-gray-300 text-lg mb-2">NFT 2.0 - Beyond Simple Collectibles</p>
        <p className="text-gray-400 text-sm">Revolutionary NFTs that earn, store, and unlock value</p>
      </div>

      {/* Quick Overview Accordion */}
      <AccordionItem
        title="What Makes XFTs Revolutionary?"
        icon={<Zap className="w-5 h-5 text-yellow-400" />}
        color="border-yellow-500/20"
        isOpen={activeAccordion === 'overview'}
        onToggle={() => toggleAccordion('overview')}
      >
        <div className="space-y-4">
          <p className="text-gray-300 leading-relaxed">
            XFTs (eXtended Functionality Tokens) are the next evolution of NFTs. Unlike traditional NFTs that are just images, 
            XFTs are <span className="text-yellow-400 font-semibold">productive digital assets</span> that can:
          </p>
          
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center space-x-3 p-3 bg-yellow-500/10 rounded-lg">
              <DollarSign className="w-5 h-5 text-yellow-400 flex-shrink-0" />
              <span className="text-gray-200">Earn money automatically from trading fees</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-blue-500/10 rounded-lg">
              <Wallet className="w-5 h-5 text-blue-400 flex-shrink-0" />
              <span className="text-gray-200">Store and manage multiple cryptocurrencies</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-purple-500/10 rounded-lg">
              <Shield className="w-5 h-5 text-purple-400 flex-shrink-0" />
              <span className="text-gray-200">Control access to exclusive features</span>
            </div>
          </div>
        </div>
      </AccordionItem>

      {/* Tabbed Content */}
      <Card variant="glass" padding="none">
        {/* Tab Navigation */}
        <div className="flex border-b border-gray-700/30">
          {[
            { id: 'features', label: 'Core Features', icon: Star },
            { id: 'benefits', label: 'Benefits', icon: TrendingUp },
            { id: 'examples', label: 'Use Cases', icon: Target }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 transition-colors ${
                activeTab === tab.id
                  ? 'bg-primary-purple/20 text-primary-purple border-b-2 border-primary-purple'
                  : 'text-gray-400 hover:text-gray-200 hover:bg-white/5'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span className="text-sm font-medium">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="p-4">
          <AnimatePresence mode="wait">
            {activeTab === 'features' && (
              <motion.div
                key="features"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <div className="space-y-3">
                  <div className="flex items-start space-x-3 p-3 bg-cyan-500/10 rounded-lg">
                    <Shield className="w-5 h-5 text-cyan-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-cyan-400 mb-1">Private Pool Access</h4>
                      <p className="text-gray-300 text-sm">Gate exclusive trading pools with NFT ownership</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3 p-3 bg-green-500/10 rounded-lg">
                    <Coins className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-green-400 mb-1">Asset Storage</h4>
                      <p className="text-gray-300 text-sm">Store SUI and tokens directly inside your NFT</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3 p-3 bg-purple-500/10 rounded-lg">
                    <Calendar className="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-purple-400 mb-1">Event Integration</h4>
                      <p className="text-gray-300 text-sm">Use as tickets with revenue sharing to token liquidity</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3 p-3 bg-yellow-500/10 rounded-lg">
                    <Lock className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-yellow-400 mb-1">Time-Locking</h4>
                      <p className="text-gray-300 text-sm">Lock assets for investment discipline and future value</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'benefits' && (
              <motion.div
                key="benefits"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <div className="space-y-3">
                  <div className="p-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg border border-green-500/20">
                    <h4 className="font-semibold text-green-400 mb-2 flex items-center">
                      <DollarSign className="w-4 h-4 mr-2" />
                      Passive Income Generation
                    </h4>
                    <p className="text-gray-300 text-sm">Your NFTs automatically earn fees from platform activity, creating sustainable income streams.</p>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg border border-purple-500/20">
                    <h4 className="font-semibold text-purple-400 mb-2 flex items-center">
                      <Users className="w-4 h-4 mr-2" />
                      Community Building
                    </h4>
                    <p className="text-gray-300 text-sm">Create exclusive communities around your tokens with NFT-gated access and shared benefits.</p>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-lg border border-cyan-500/20">
                    <h4 className="font-semibold text-cyan-400 mb-2 flex items-center">
                      <Wallet className="w-4 h-4 mr-2" />
                      Portfolio Management
                    </h4>
                    <p className="text-gray-300 text-sm">Manage multiple assets in one place with built-in storage and time-locking features.</p>
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'examples' && (
              <motion.div
                key="examples"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <div className="space-y-3">
                  <div className="p-4 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-red-500/10 rounded-lg border border-purple-500/20">
                    <h4 className="font-semibold text-purple-400 mb-2">🎫 Concert Ticket NFT</h4>
                    <p className="text-gray-300 text-sm mb-2">Mint NFTs as concert tickets. 10% of ticket sales automatically flow back to your token's liquidity pool.</p>
                    <p className="text-gray-400 text-xs">Result: Stronger token price + exclusive fan experiences</p>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-br from-green-500/10 via-emerald-500/10 to-teal-500/10 rounded-lg border border-green-500/20">
                    <h4 className="font-semibold text-green-400 mb-2">💼 Investment Portfolio NFT</h4>
                    <p className="text-gray-300 text-sm mb-2">Store 5 SUI + 1000 USDC + various tokens in one NFT. Lock for 6 months, then sell the entire portfolio.</p>
                    <p className="text-gray-400 text-xs">Result: Simplified portfolio trading + forced HODLing</p>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-br from-cyan-500/10 via-blue-500/10 to-indigo-500/10 rounded-lg border border-cyan-500/20">
                    <h4 className="font-semibold text-cyan-400 mb-2">🏆 VIP Trading Access</h4>
                    <p className="text-gray-300 text-sm mb-2">Create exclusive trading pools only accessible to your NFT holders. Premium tokens, early access, special features.</p>
                    <p className="text-gray-400 text-xs">Result: Premium community + recurring value for holders</p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </Card>

      {/* How It Works Accordion */}
      <AccordionItem
        title="How Do XFTs Work?"
        icon={<Target className="w-5 h-5 text-blue-400" />}
        color="border-blue-500/20"
        isOpen={activeAccordion === 'how-it-works'}
        onToggle={() => toggleAccordion('how-it-works')}
      >
        <div className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-primary-purple rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0 mt-0.5">1</div>
              <div>
                <h4 className="font-semibold text-white mb-1">Create Your XFT</h4>
                <p className="text-gray-300 text-sm">Mint an NFT with custom supply and features. 1-of-1 NFTs can store assets.</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-primary-purple rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0 mt-0.5">2</div>
              <div>
                <h4 className="font-semibold text-white mb-1">Add Utility</h4>
                <p className="text-gray-300 text-sm">Connect to private pools, store assets, or use as event tickets.</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-primary-purple rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0 mt-0.5">3</div>
              <div>
                <h4 className="font-semibold text-white mb-1">Earn & Grow</h4>
                <p className="text-gray-300 text-sm">Your NFT automatically earns fees and grows in value through utility.</p>
              </div>
            </div>
          </div>

          <div className="p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
            <p className="text-blue-400 text-sm font-medium">💡 Pro Tip</p>
            <p className="text-gray-300 text-sm mt-1">
              Start with a simple access NFT, then expand utility as your community grows!
            </p>
          </div>
        </div>
      </AccordionItem>

      {/* XFT Ecosystem Accordion */}
      <AccordionItem
        title="Complete XFT Ecosystem"
        icon={<Users className="w-5 h-5 text-indigo-400" />}
        color="border-indigo-500/20"
        isOpen={activeAccordion === 'ecosystem'}
        onToggle={() => toggleAccordion('ecosystem')}
      >
        <div className="space-y-4">
          <p className="text-gray-300 text-sm leading-relaxed mb-4">
            XFTs are part of a complete ecosystem that includes labels, licenses, and financial services:
          </p>

          <div className="space-y-3">
            <div className="p-4 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg border border-blue-500/20">
              <h4 className="font-semibold text-blue-400 mb-2 flex items-center">
                <Target className="w-4 h-4 mr-2" />
                Labels - Text-Based XFTs
              </h4>
              <p className="text-gray-300 text-sm mb-2">
                Domain-like namespaces that organize and categorize your XFTs. Think of them as collections or albums.
              </p>
              <div className="text-xs text-gray-400 space-y-1">
                <div>• Lead Labels: Your main collection (1-of-1)</div>
                <div>• Profile Labels: Personal identity labels</div>
                <div>• Tag Labels: Limited transfer labels for professionals</div>
                <div>• Chapter Labels: Limited edition text-based NFTs</div>
              </div>
            </div>

            <div className="p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg border border-purple-500/20">
              <h4 className="font-semibold text-purple-400 mb-2 flex items-center">
                <Users className="w-4 h-4 mr-2" />
                Operator Licenses
              </h4>
              <p className="text-gray-300 text-sm mb-2">
                Time-limited permissions that allow team members to mint and manage XFTs under your label.
              </p>
              <div className="text-xs text-gray-400 space-y-1">
                <div>• Super Operators: Full management permissions</div>
                <div>• Regular Operators: Limited minting permissions</div>
                <div>• Transferable licenses with expiration dates</div>
                <div>• Perfect for teams and collaborations</div>
              </div>
            </div>

            <div className="p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-500/20">
              <h4 className="font-semibold text-green-400 mb-2 flex items-center">
                <Star className="w-4 h-4 mr-2" />
                Marketplace Licenses
              </h4>
              <p className="text-gray-300 text-sm mb-2">
                Allow artists and creators to sell their XFTs under your label with automatic fee distribution.
              </p>
              <div className="text-xs text-gray-400 space-y-1">
                <div>• Enable others to sell under your brand</div>
                <div>• Automatic royalty collection</div>
                <div>• Build marketplace communities</div>
                <div>• Revenue sharing with label owners</div>
              </div>
            </div>

            <div className="p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20">
              <h4 className="font-semibold text-yellow-400 mb-2 flex items-center">
                <DollarSign className="w-4 h-4 mr-2" />
                0% Interest Banking
              </h4>
              <p className="text-gray-300 text-sm mb-2">
                Borrow SUI against your XFT assets with 0% interest loans. Your XFTs work as collateral.
              </p>
              <div className="text-xs text-gray-400 space-y-1">
                <div>• Collateralize XFTs for instant SUI loans</div>
                <div>• 0% interest - only platform fees</div>
                <div>• Dynamic LTV based on asset value</div>
                <div>• Unlock liquidity without selling</div>
              </div>
            </div>
          </div>
        </div>
      </AccordionItem>

      {/* Technical Details Accordion */}
      <AccordionItem
        title="Technical Innovation"
        icon={<Sparkles className="w-5 h-5 text-pink-400" />}
        color="border-pink-500/20"
        isOpen={activeAccordion === 'technical'}
        onToggle={() => toggleAccordion('technical')}
      >
        <div className="space-y-4">
          <p className="text-gray-300 text-sm leading-relaxed">
            XFTs are built on advanced smart contract architecture that enables:
          </p>

          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center space-x-3 p-3 bg-pink-500/10 rounded-lg">
              <div className="w-2 h-2 bg-pink-400 rounded-full flex-shrink-0"></div>
              <span className="text-gray-200 text-sm"><strong>Asset Storage:</strong> SUI's object-in-object capability</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-pink-500/10 rounded-lg">
              <div className="w-2 h-2 bg-pink-400 rounded-full flex-shrink-0"></div>
              <span className="text-gray-200 text-sm"><strong>Time Locks:</strong> Blockchain-enforced release dates</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-pink-500/10 rounded-lg">
              <div className="w-2 h-2 bg-pink-400 rounded-full flex-shrink-0"></div>
              <span className="text-gray-200 text-sm"><strong>Fee Distribution:</strong> Automatic revenue sharing</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-pink-500/10 rounded-lg">
              <div className="w-2 h-2 bg-pink-400 rounded-full flex-shrink-0"></div>
              <span className="text-gray-200 text-sm"><strong>Access Control:</strong> Programmable permissions</span>
            </div>
          </div>
        </div>
      </AccordionItem>

      {/* Call to Action */}
      <div className="text-center pt-6">
        <Button
          variant="primary"
          size="lg"
          onClick={onCreateClick}
          className="px-8 py-4 bg-gradient-to-r from-primary-purple via-primary-pink to-primary-purple bg-size-200 animate-gradient"
        >
          <Plus className="w-5 h-5 mr-2" />
          Create Your First XFT
        </Button>
        <p className="text-gray-400 text-sm mt-3">
          Join the NFT 2.0 revolution on Dexsta
        </p>
      </div>

      {/* Stats Section */}
      <div className="grid grid-cols-2 gap-4 pt-6">
        <Card variant="glass" padding="md" className="text-center">
          <TrendingUp className="w-8 h-8 text-primary-purple mx-auto mb-2" />
          <p className="text-2xl font-bold text-white">∞</p>
          <p className="text-gray-400 text-sm">Utility Possibilities</p>
        </Card>
        <Card variant="glass" padding="md" className="text-center">
          <Shield className="w-8 h-8 text-cyan-400 mx-auto mb-2" />
          <p className="text-2xl font-bold text-white">100%</p>
          <p className="text-gray-400 text-sm">Secure & Decentralized</p>
        </Card>
      </div>

      {/* Bottom CTA */}
      <Card variant="glass" padding="lg" className="border border-primary-purple/20 bg-gradient-to-br from-primary-purple/5 to-primary-pink/5">
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-2">
            <Sparkles className="w-6 h-6 text-primary-purple" />
            <h3 className="text-xl font-bold text-white font-heading">Ready to Build the Future?</h3>
            <Sparkles className="w-6 h-6 text-primary-purple" />
          </div>
          <p className="text-gray-300">
            XFTs aren't just the next evolution of NFTs—they're the foundation of a new digital economy where every asset is productive.
          </p>
          <Button
            variant="gradient"
            size="md"
            onClick={onCreateClick}
            className="bg-gradient-to-r from-primary-purple to-primary-pink hover:from-primary-purple/80 hover:to-primary-pink/80"
          >
            Start Creating Now
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </Card>
    </div>
  )
}
