'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useWallet } from '@suiet/wallet-kit'
import { Card } from '@/components/shared/Card'
import { Button } from '@/components/shared/Button'
import { NotificationModal } from '@/components/shared/NotificationModal'
import { useContracts } from '@/hooks/useContracts'
import { XFT_CONSTANTS } from '@/constants/contracts'
import {
  ImageIcon,
  Tag,
  Plus,
  Sparkles,
  Search,
  ArrowRight,
  Info,
  Shield,
  Clock,
  DollarSign,
  Eye,
  Upload,
  Trash2
} from 'lucide-react'

type CreateType = 'choose' | 'label' | 'nft' | 'wrap' | 'generative'

interface LabelType {
  id: number
  name: string
  description: string
  icon: string
  isOneOfOne: boolean
}

const labelTypes: LabelType[] = [
  {
    id: 1,
    name: "Lead Label",
    description: "Your main collection or album that XFTs are linked to. Funds from sales are held in the label wallet.",
    icon: "🏷️",
    isOneOfOne: true
  },
  {
    id: 2,
    name: "Profile Label", 
    description: "Personal identity label for your profile. Store all your XFTs under this label.",
    icon: "👤",
    isOneOfOne: true
  },
  {
    id: 3,
    name: "Tags Label",
    description: "Limited transfer labels. Perfect for teachers, consultants, and professionals.",
    icon: "🏷️",
    isOneOfOne: false
  },
  {
    id: 4,
    name: "Chapters Label",
    description: "Limited edition text-based XFTs. Perfect for literature, poems, stories, and recipes.",
    icon: "📖",
    isOneOfOne: false
  },
  {
    id: 5,
    name: "Operator License",
    description: "Allow team members to mint and sell XFTs under your label.",
    icon: "👥",
    isOneOfOne: false
  },
  {
    id: 6,
    name: "Marketplace License",
    description: "Allow artists and creators to sell their XFTs under your label.",
    icon: "🛒",
    isOneOfOne: false
  }
]

export function XFTCreateSection() {
  // Wallet and contract hooks
  const { connected, account } = useWallet()
  const { createLabel, mintXFT, wrapXFT, loading } = useContracts()

  // UI state
  const [createType, setCreateType] = useState<CreateType>('choose')
  const [selectedLabelType, setSelectedLabelType] = useState<LabelType | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResult, setSearchResult] = useState<'available' | 'taken' | null>(null)
  const [isSearching, setIsSearching] = useState(false)
  const [labelImage, setLabelImage] = useState<string | null>(null)
  const [selectedColor, setSelectedColor] = useState<string>('#34326E')
  const [fontSize, setFontSize] = useState<number>(28)
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false)
  const [registrationYears, setRegistrationYears] = useState<number>(1)
  const [attributes, setAttributes] = useState<Array<{trait_type: string, value: string}>>([])
  const [attributeMethod, setAttributeMethod] = useState<'manual' | 'json'>('manual')
  const [jsonAttributes, setJsonAttributes] = useState<string>('')
  const [isLocked, setIsLocked] = useState<boolean>(false)
  const [lockDuration, setLockDuration] = useState<number>(30)

  // Form data state
  const [labelFormData, setLabelFormData] = useState({
    name: '',
    title: '',
    description: '',
    supply: 1,
  })

  const [xftFormData, setXftFormData] = useState({
    name: '',
    description: '',
    imageUrl: '',
    supply: 1,
    labelGlobalId: '',
    operatorLicenseGlobalId: '',
    ipfsHash: '',
  })

  const [wrapFormData, setWrapFormData] = useState({
    xftId: '',
  })

  // Loading states
  const [isCreating, setIsCreating] = useState(false)

  // Modal state for notifications
  const [notificationModal, setNotificationModal] = useState<{
    isOpen: boolean
    type: 'success' | 'error'
    title: string
    message: string
  }>({
    isOpen: false,
    type: 'success',
    title: '',
    message: ''
  })

  const colors = [
    '#34326E', '#68316E', '#316E69', '#6E3C31', '#6E3161',
    '#BF0B99', '#D426AF', '#8026D4', '#9A4FBD', '#497B8C',
    '#8B8C49', '#2F709E', '#2F5A9E', '#2A52DE', '#2A78DE',
    '#E3204A', '#26060D', '#1A0207', '#1A0106', '#038250'
  ]

  const addAttribute = () => {
    setAttributes([...attributes, { trait_type: '', value: '' }])
  }

  const removeAttribute = (index: number) => {
    setAttributes(attributes.filter((_, i) => i !== index))
  }

  const updateAttribute = (index: number, field: 'trait_type' | 'value', value: string) => {
    const updated = [...attributes]
    updated[index][field] = value
    setAttributes(updated)
  }

  const validateJsonAttributes = (json: string) => {
    try {
      const parsed = JSON.parse(json)
      if (Array.isArray(parsed)) {
        return parsed.every(attr =>
          typeof attr === 'object' &&
          attr.trait_type &&
          attr.value
        )
      }
      return false
    } catch {
      return false
    }
  }

  const generateLabelImage = (text: string, bgColor: string = selectedColor, textSize: number = fontSize) => {
    // Generate SVG with better styling to look like an NFT card - only show the text
    setLabelImage(`data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${bgColor.replace('#', '%23')};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${bgColor.replace('#', '%23')}CC;stop-opacity:1" />
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="%23000000" flood-opacity="0.3"/>
        </filter>
      </defs>
      <rect width="400" height="400" fill="url(%23grad)" rx="20" />
      <rect x="20" y="20" width="360" height="360" fill="none" stroke="white" stroke-width="2" stroke-opacity="0.2" rx="15" />
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${textSize}" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle" filter="url(%23shadow)">${text}</text>
    </svg>`)
  }

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!searchQuery.trim()) return

    setIsSearching(true)
    
    // Simulate API call
    setTimeout(() => {
      // For demo: labels with "taken" are unavailable
      const isTaken = searchQuery.toLowerCase().includes('taken')
      setSearchResult(isTaken ? 'taken' : 'available')

      if (!isTaken) {
        generateLabelImage(searchQuery)
      }

      setIsSearching(false)
    }, 1500)
  }

  const handleSelectLabelType = (labelType: LabelType) => {
    setSelectedLabelType(labelType)
    setShowCreateForm(true)
  }

  // Handler functions for contract interactions
  const handleCreateLabel = async () => {
    if (!connected || !account) {
      setNotificationModal({
        isOpen: true,
        type: 'error',
        title: 'Wallet Not Connected',
        message: 'Please connect your wallet first to create a label.'
      })
      return
    }

    if (!searchQuery.trim()) {
      setNotificationModal({
        isOpen: true,
        type: 'error',
        title: 'Missing Label Name',
        message: 'Please enter a label name before creating.'
      })
      return
    }

    setIsCreating(true)
    try {
      const durationMs = registrationYears * 365 * 24 * 60 * 60 * 1000 // Convert years to milliseconds
      const suiAmount = (XFT_CONSTANTS.LABEL_MINT_FEE + (registrationYears * 0.01 * **********)).toString() // Base fee + yearly fee

      await createLabel({
        name: searchQuery,
        title: searchQuery,
        description: `${selectedLabelType?.name} - ${searchQuery}`,
        imageUrl: labelImage || '',
        labelType: selectedLabelType?.id || 1,
        supply: selectedLabelType?.isOneOfOne ? 1 : 100,
        durationMs,
        suiAmount,
      })

      setNotificationModal({
        isOpen: true,
        type: 'success',
        title: 'Label Created Successfully! 🎉',
        message: `Your "${searchQuery}" label has been created and is ready to use for XFT minting.`
      })
      setCreateType('choose')
      setShowCreateForm(false)
      setSearchResult(null)
      setSearchQuery('')
    } catch (error) {
      console.error('Label creation failed:', error)
      setNotificationModal({
        isOpen: true,
        type: 'error',
        title: 'Label Creation Failed 😞',
        message: `Failed to create label "${searchQuery}": ${error instanceof Error ? error.message : 'Unknown error occurred'}. Please try again.`
      })
    } finally {
      setIsCreating(false)
    }
  }

  const handleMintXFT = async () => {
    if (!connected || !account) {
      setNotificationModal({
        isOpen: true,
        type: 'error',
        title: 'Wallet Not Connected',
        message: 'Please connect your wallet first to mint an XFT.'
      })
      return
    }

    if (!xftFormData.name || !xftFormData.description || !xftFormData.labelGlobalId) {
      setNotificationModal({
        isOpen: true,
        type: 'error',
        title: 'Missing Required Fields',
        message: 'Please fill in all required fields: name, description, and label ID.'
      })
      return
    }

    setIsCreating(true)
    try {
      const finalAttributes = attributeMethod === 'json' && jsonAttributes
        ? JSON.parse(jsonAttributes).map((attr: any) => `${attr.trait_type}:${attr.value}`)
        : attributes.filter(attr => attr.trait_type && attr.value).map(attr => `${attr.trait_type}:${attr.value}`)

      await mintXFT({
        name: xftFormData.name,
        description: xftFormData.description,
        imageUrl: xftFormData.imageUrl,
        supply: xftFormData.supply,
        labelGlobalId: parseInt(xftFormData.labelGlobalId),
        operatorLicenseGlobalId: xftFormData.operatorLicenseGlobalId ? parseInt(xftFormData.operatorLicenseGlobalId) : undefined,
        attributes: finalAttributes,
        ipfsHash: xftFormData.ipfsHash || '',
        suiAmount: XFT_CONSTANTS.XFT_MINT_FEE.toString(),
      })

      setNotificationModal({
        isOpen: true,
        type: 'success',
        title: 'XFT Minted Successfully! 🎉',
        message: `Your "${xftFormData.name}" XFT has been minted and is now available in your wallet.`
      })
      setCreateType('choose')
    } catch (error) {
      console.error('XFT minting failed:', error)
      setNotificationModal({
        isOpen: true,
        type: 'error',
        title: 'XFT Minting Failed 😞',
        message: `Failed to mint XFT "${xftFormData.name}": ${error instanceof Error ? error.message : 'Unknown error occurred'}. Please try again.`
      })
    } finally {
      setIsCreating(false)
    }
  }

  const handleWrapXFT = async () => {
    if (!connected || !account) {
      setNotificationModal({
        isOpen: true,
        type: 'error',
        title: 'Wallet Not Connected',
        message: 'Please connect your wallet first to wrap an XFT.'
      })
      return
    }

    if (!wrapFormData.xftId) {
      setNotificationModal({
        isOpen: true,
        type: 'error',
        title: 'Missing XFT ID',
        message: 'Please enter an XFT ID to wrap.'
      })
      return
    }

    setIsCreating(true)
    try {
      await wrapXFT({
        xftId: wrapFormData.xftId,
      })

      setNotificationModal({
        isOpen: true,
        type: 'success',
        title: 'XFT Wrapped Successfully! 🎉',
        message: `XFT ${wrapFormData.xftId} has been wrapped and converted to a 1-of-1 for asset storage.`
      })
      setCreateType('choose')
      setWrapFormData({ xftId: '' })
    } catch (error) {
      console.error('XFT wrapping failed:', error)
      setNotificationModal({
        isOpen: true,
        type: 'error',
        title: 'XFT Wrapping Failed 😞',
        message: `Failed to wrap XFT ${wrapFormData.xftId}: ${error instanceof Error ? error.message : 'Unknown error occurred'}. Please try again.`
      })
    } finally {
      setIsCreating(false)
    }
  }

  if (createType === 'choose') {
    return (
      <>
        <div className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-white mb-3 font-heading">Create XFT</h2>
          <p className="text-gray-300 text-lg">Choose what you want to create</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {/* Create Label Option */}
          <Card
            variant="glass"
            padding="md"
            className="border border-blue-500/20 cursor-pointer hover:border-blue-500/40 transition-all group"
            onClick={() => setCreateType('label')}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className="p-4 bg-blue-500/20 rounded-xl group-hover:bg-blue-500/30 transition-colors">
                <Tag className="w-8 h-8 text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white font-heading">Create Label</h3>
                <p className="text-gray-400 text-sm">Text-based XFTs for organization</p>
              </div>
            </div>
          </Card>

          {/* Mint XFT Option */}
          <Card
            variant="glass"
            padding="md"
            className="border border-purple-500/20 cursor-pointer hover:border-purple-500/40 transition-all group"
            onClick={() => setCreateType('nft')}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className="p-4 bg-purple-500/20 rounded-xl group-hover:bg-purple-500/30 transition-colors">
                <ImageIcon className="w-8 h-8 text-purple-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white font-heading">Mint XFT</h3>
                <p className="text-gray-400 text-sm">Enhanced NFTs with utility features</p>
              </div>
            </div>
          </Card>

          {/* Mint Generative Option */}
          <Card
            variant="glass"
            padding="md"
            className="border border-cyan-500/20 cursor-pointer hover:border-cyan-500/40 transition-all group"
            onClick={() => setCreateType('generative')}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className="p-4 bg-cyan-500/20 rounded-xl group-hover:bg-cyan-500/30 transition-colors">
                <Sparkles className="w-8 h-8 text-cyan-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white font-heading">Mint Generative</h3>
                <p className="text-gray-400 text-sm">Create generative collections</p>
              </div>
            </div>
          </Card>

          {/* Wrap XFT Option */}
          <Card
            variant="glass"
            padding="md"
            className="border border-green-500/20 cursor-pointer hover:border-green-500/40 transition-all group"
            onClick={() => setCreateType('wrap')}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className="p-4 bg-green-500/20 rounded-xl group-hover:bg-green-500/30 transition-colors">
                <Shield className="w-8 h-8 text-green-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white font-heading">Wrap XFT</h3>
                <p className="text-gray-400 text-sm">Convert to 1-of-1 for asset storage</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Quick Info */}
        <Card variant="glass" padding="sm" className="border border-cyan-500/20 bg-cyan-500/5">
          <div className="flex items-center space-x-2">
            <Info className="w-4 h-4 text-cyan-400 flex-shrink-0" />
            <p className="text-gray-300 text-sm">
              <strong>New to XFTs?</strong> Start with a Lead Label to organize your collection
            </p>
          </div>
        </Card>
        </div>

        {/* Notification Modal */}
        <NotificationModal
          isOpen={notificationModal.isOpen}
          onClose={() => setNotificationModal(prev => ({ ...prev, isOpen: false }))}
          type={notificationModal.type}
          title={notificationModal.title}
          message={notificationModal.message}
          autoClose={notificationModal.type === 'success'}
          autoCloseDelay={4000}
          actionButton={notificationModal.type === 'error' ? {
            label: 'Try Again',
            onClick: () => setNotificationModal(prev => ({ ...prev, isOpen: false }))
          } : undefined}
        />
      </>
    )
  }

  if (createType === 'label') {
    return (
      <div className="space-y-6">
        {/* Header with Back Button */}
        <div className="flex items-center space-x-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCreateType('choose')}
            className="flex items-center space-x-2"
          >
            <ArrowRight className="w-4 h-4 rotate-180" />
            <span>Back</span>
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-white font-heading">Create Label</h2>
            <p className="text-gray-400">Text-based XFTs for organization</p>
          </div>
        </div>

        {/* Search and Selection Cards - Hide when showing create form */}
        {!showCreateForm && (
          <>
            {/* Search for Label */}
            <Card variant="glass" padding="lg">
              <h3 className="text-lg font-semibold text-white mb-4 font-heading">Search for Label</h3>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex space-x-3">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Enter label name (e.g., 'Best Pizza NY')"
                className="flex-1 px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                required
              />
              <Button
                type="submit"
                variant="primary"
                size="md"
                disabled={isSearching}
                className="px-6"
              >
                {isSearching ? (
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                ) : (
                  <Search className="w-5 h-5" />
                )}
              </Button>
            </div>

            <div className="text-xs text-gray-400 space-y-1">
              <p className="font-medium">Special character meanings:</p>
              <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                <div>* - identity/profile</div>
                <div>[ - label operator</div>
                <div># - social/marketplace</div>
                <div>! - male</div>
                <div>@ - contract</div>
                <div>$ - business</div>
                <div>^ - travel</div>
                <div>& - tickets/gaming</div>
              </div>
            </div>
          </form>
        </Card>

        {/* Search Results */}
        <AnimatePresence>
          {searchResult === 'available' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <Card variant="glass" padding="lg" className="border border-green-500/20">
                <div className="flex items-center space-x-2 mb-6">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-green-400">Label Available: "{searchQuery}"</h3>
                </div>

                {/* Label Preview Card */}
                <div className="mb-6">
                  <div className="flex flex-col items-center">
                    {/* NFT Card Preview */}
                    <div className="w-64 h-64 rounded-2xl overflow-hidden border-2 border-gray-600/50 shadow-2xl mb-4">
                      {labelImage ? (
                        <img
                          src={labelImage}
                          alt={searchQuery}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                          <Tag className="w-16 h-16 text-gray-600" />
                        </div>
                      )}
                    </div>

                    {/* Customization Controls */}
                    <div className="space-y-4">
                      {/* Color Selection */}
                      <div className="space-y-2">
                        <p className="text-gray-300 text-sm text-center">Background Color:</p>
                        <div className="grid grid-cols-10 gap-2 max-w-md mx-auto">
                          {colors.map((color) => (
                            <button
                              key={color}
                              onClick={() => {
                                setSelectedColor(color)
                                generateLabelImage(searchQuery, color, fontSize)
                              }}
                              className={`w-6 h-6 rounded-full border-2 transition-all ${
                                selectedColor === color
                                  ? 'border-white scale-110'
                                  : 'border-gray-600 hover:border-gray-400'
                              }`}
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>

                      {/* Font Size Control */}
                      <div className="space-y-2">
                        <p className="text-gray-300 text-sm text-center">Font Size: {fontSize}px</p>
                        <div className="flex items-center space-x-4 max-w-xs mx-auto">
                          <span className="text-gray-400 text-xs">Small</span>
                          <input
                            type="range"
                            min="16"
                            max="80"
                            value={fontSize}
                            onChange={(e) => {
                              const newSize = parseInt(e.target.value)
                              setFontSize(newSize)
                              generateLabelImage(searchQuery, selectedColor, newSize)
                            }}
                            className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                          />
                          <span className="text-gray-400 text-xs">Large</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <p className="text-gray-300 mb-6">Choose the type of label you want to create:</p>
                
                <div className="space-y-3">
                  {labelTypes.map((labelType) => (
                    <Button
                      key={labelType.id}
                      variant="ghost"
                      onClick={() => handleSelectLabelType(labelType)}
                      className="w-full justify-start p-4 h-auto border border-gray-700 hover:border-blue-500/50"
                    >
                      <div className="flex items-start space-x-3 text-left">
                        <span className="text-2xl">{labelType.icon}</span>
                        <div className="flex-1">
                          <h4 className="font-semibold text-white mb-1">{labelType.name}</h4>
                          <p className="text-gray-400 text-sm">{labelType.description}</p>
                          {labelType.isOneOfOne && (
                            <span className="inline-block mt-1 px-2 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full">
                              1-of-1 Only
                            </span>
                          )}
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </Card>
            </motion.div>
          )}

          {searchResult === 'taken' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <Card variant="glass" padding="lg" className="border border-red-500/20">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-red-400">Label Taken</h3>
                </div>
                <p className="text-gray-300">
                  This label is already taken. Please try searching for a different label name.
                </p>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
        </>
        )}

        {/* Show Create Form Only When Label Type Selected */}
        <AnimatePresence>
          {showCreateForm && selectedLabelType && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <Card variant="glass" padding="lg" className="border border-blue-500/20">
                {/* Header with Back Button */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowCreateForm(false)}
                      className="flex items-center space-x-2"
                    >
                      <ArrowRight className="w-4 h-4 rotate-180" />
                      <span>Back</span>
                    </Button>
                    <div>
                      <h3 className="text-lg font-semibold text-white font-heading">
                        Create {selectedLabelType.name}
                      </h3>
                      <p className="text-gray-400 text-sm">{selectedLabelType.description}</p>
                    </div>
                  </div>
                </div>

                {/* Label Preview in Form */}
                {labelImage && (
                  <div className="flex justify-center mb-6">
                    <div className="w-48 h-48 rounded-2xl overflow-hidden border-2 border-gray-600/50 shadow-xl">
                      <img
                        src={labelImage}
                        alt={searchQuery}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                )}

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Label Name</label>
                    <input
                      type="text"
                      value={searchQuery}
                      readOnly
                      className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-gray-800/50 text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">Description</label>
                    <textarea
                      placeholder="Tell a cool story about why this label is important to you"
                      rows={3}
                      className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 resize-none"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-white mb-2">Registration Years</label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={registrationYears}
                        onChange={(e) => setRegistrationYears(parseInt(e.target.value) || 1)}
                        className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                      />
                      <p className="text-gray-400 text-xs mt-1">1 SUI per year</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-white mb-2">Quantity</label>
                      <input
                        type="number"
                        min="1"
                        max={selectedLabelType.isOneOfOne ? 1 : 100}
                        defaultValue="1"
                        disabled={selectedLabelType.isOneOfOne}
                        className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 disabled:opacity-50"
                      />
                      {selectedLabelType.isOneOfOne && (
                        <p className="text-gray-400 text-xs mt-1">Must be 1-of-1</p>
                      )}
                    </div>
                  </div>

                  {/* Pricing Display */}
                  <Card variant="glass" padding="md" className="border border-green-500/20 bg-green-500/5">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">Registration ({registrationYears} year{registrationYears > 1 ? 's' : ''})</span>
                        <span className="text-green-400 font-semibold">{registrationYears}.0 SUI</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">Platform Fee</span>
                        <span className="text-green-400 font-semibold">0.01 SUI</span>
                      </div>
                      <div className="border-t border-gray-700 pt-2">
                        <div className="flex items-center justify-between">
                          <span className="text-white font-semibold">Total</span>
                          <span className="text-green-400 font-bold text-lg">{(registrationYears + 0.01).toFixed(2)} SUI</span>
                        </div>
                      </div>
                    </div>
                  </Card>

                  <Button
                    variant="primary"
                    size="lg"
                    className="w-full py-4"
                    onClick={handleCreateLabel}
                    disabled={isCreating || !connected}
                  >
                    {isCreating ? (
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                    ) : (
                      <Plus className="w-5 h-5 mr-2" />
                    )}
                    {isCreating ? 'Creating...' : `Create ${selectedLabelType.name} • ${(registrationYears + 0.01).toFixed(2)} SUI`}
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Notification Modal */}
        <NotificationModal
          isOpen={notificationModal.isOpen}
          onClose={() => setNotificationModal(prev => ({ ...prev, isOpen: false }))}
          type={notificationModal.type}
          title={notificationModal.title}
          message={notificationModal.message}
          autoClose={notificationModal.type === 'success'}
          autoCloseDelay={4000}
          actionButton={notificationModal.type === 'error' ? {
            label: 'Try Again',
            onClick: () => setNotificationModal(prev => ({ ...prev, isOpen: false }))
          } : undefined}
        />
      </div>
    )
  }

  if (createType === 'nft') {
    return (
      <div className="space-y-6">
        {/* Header with Back Button */}
        <div className="flex items-center space-x-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCreateType('choose')}
            className="flex items-center space-x-2"
          >
            <ArrowRight className="w-4 h-4 rotate-180" />
            <span>Back</span>
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-white font-heading">Mint XFT</h2>
            <p className="text-gray-400">Enhanced NFTs with utility features</p>
          </div>
        </div>

        {/* XFT Mint Form */}
        <Card variant="glass" padding="lg">
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-white mb-2">XFT Name *</label>
              <input
                type="text"
                placeholder="VIP Access Pass"
                value={xftFormData.name}
                onChange={(e) => setXftFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Description *</label>
              <textarea
                placeholder="Describe your XFT's utility and features..."
                rows={3}
                value={xftFormData.description}
                onChange={(e) => setXftFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 resize-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Supply *</label>
              <input
                type="number"
                min="1"
                max="10000"
                value={xftFormData.supply}
                onChange={(e) => setXftFormData(prev => ({ ...prev, supply: parseInt(e.target.value) || 1 }))}
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50"
              />
              <p className="text-gray-400 text-xs mt-1">Only 1-of-1 XFTs can store assets</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">Label ID *</label>
                <input
                  type="text"
                  placeholder="Enter label ID"
                  value={xftFormData.labelGlobalId}
                  onChange={(e) => setXftFormData(prev => ({ ...prev, labelGlobalId: e.target.value }))}
                  className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                  required
                />
                <p className="text-gray-400 text-xs mt-1">XFT will be linked to this label</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-white mb-2">Operator ID (Optional)</label>
                <input
                  type="text"
                  placeholder="Your operator license"
                  value={xftFormData.operatorLicenseGlobalId}
                  onChange={(e) => setXftFormData(prev => ({ ...prev, operatorLicenseGlobalId: e.target.value }))}
                  className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                />
                <p className="text-gray-400 text-xs mt-1">Required if minting under someone else's label</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">XFT Image *</label>
              <div className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-600 rounded-2xl cursor-pointer hover:border-purple-500/50 transition-colors glass-card">
                <Upload className="w-8 h-8 text-gray-400 mb-2" />
                <p className="text-gray-400 text-sm">Click to upload image</p>
                <p className="text-gray-500 text-xs">PNG, JPG, GIF up to 10MB</p>
              </div>
            </div>

            {/* Lock Options */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="lockAssets"
                  checked={isLocked}
                  onChange={(e) => setIsLocked(e.target.checked)}
                  className="w-4 h-4 text-purple-600 bg-transparent border-gray-600 rounded focus:ring-purple-500"
                />
                <label htmlFor="lockAssets" className="text-white font-medium">
                  Lock assets for investment discipline
                </label>
              </div>

              {isLocked && (
                <div>
                  <label className="block text-sm font-medium text-white mb-2">Lock Duration (days)</label>
                  <select
                    value={lockDuration}
                    onChange={(e) => setLockDuration(parseInt(e.target.value))}
                    className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                  >
                    <option value={7} className="bg-gray-800">7 days</option>
                    <option value={30} className="bg-gray-800">30 days</option>
                    <option value={90} className="bg-gray-800">90 days (3 months)</option>
                    <option value={180} className="bg-gray-800">180 days (6 months)</option>
                    <option value={365} className="bg-gray-800">365 days (1 year)</option>
                  </select>
                  <p className="text-gray-400 text-xs mt-1">
                    Assets will be locked until {new Date(Date.now() + lockDuration * 24 * 60 * 60 * 1000).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>

            {/* Attributes Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-white">Attributes (Optional)</h3>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => setAttributeMethod('manual')}
                    className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                      attributeMethod === 'manual'
                        ? 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                        : 'bg-gray-800/50 text-gray-400 border border-gray-600/30'
                    }`}
                  >
                    Manual
                  </button>
                  <button
                    type="button"
                    onClick={() => setAttributeMethod('json')}
                    className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                      attributeMethod === 'json'
                        ? 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                        : 'bg-gray-800/50 text-gray-400 border border-gray-600/30'
                    }`}
                  >
                    JSON
                  </button>
                </div>
              </div>

              {attributeMethod === 'manual' ? (
                <div className="space-y-3">
                  {attributes.map((attr, index) => (
                    <div key={index} className="flex space-x-3 items-end">
                      <div className="flex-1">
                        <input
                          type="text"
                          placeholder="Trait type (e.g., Rarity)"
                          value={attr.trait_type}
                          onChange={(e) => updateAttribute(index, 'trait_type', e.target.value)}
                          className="w-full px-3 py-2 rounded-xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                        />
                      </div>
                      <div className="flex-1">
                        <input
                          type="text"
                          placeholder="Value (e.g., Legendary)"
                          value={attr.value}
                          onChange={(e) => updateAttribute(index, 'value', e.target.value)}
                          className="w-full px-3 py-2 rounded-xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                        />
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAttribute(index)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={addAttribute}
                    className="w-full border border-gray-600"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Attribute
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  <textarea
                    placeholder="Paste your JSON attributes here..."
                    value={jsonAttributes}
                    onChange={(e) => setJsonAttributes(e.target.value)}
                    rows={6}
                    className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 resize-none font-mono text-sm"
                  />
                  {jsonAttributes && !validateJsonAttributes(jsonAttributes) && (
                    <p className="text-red-400 text-sm">Invalid JSON format. Please check the structure below.</p>
                  )}
                </div>
              )}
            </div>

            <Button
              variant="primary"
              size="lg"
              className="w-full py-4"
              onClick={handleMintXFT}
              disabled={isCreating || !connected}
            >
              {isCreating ? (
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
              ) : (
                <Plus className="w-5 h-5 mr-2" />
              )}
              {isCreating ? 'Minting...' : 'Mint XFT • 0.05 SUI'}
            </Button>
          </div>
        </Card>

        {/* Helper Section */}
        <Card variant="glass" padding="lg" className="border border-blue-500/20 bg-blue-500/5">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Info className="w-5 h-5 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">XFT Creation Guide</h3>
            </div>

            <div className="space-y-4">
              {/* JSON Structure */}
              <div>
                <h4 className="text-white font-medium mb-2">JSON Attributes Structure:</h4>
                <div className="p-3 bg-gray-800/50 rounded-xl">
                  <pre className="text-gray-300 text-xs overflow-x-auto">
{`[
  {
    "trait_type": "Rarity",
    "value": "Legendary"
  },
  {
    "trait_type": "Access Level",
    "value": "VIP"
  },
  {
    "trait_type": "Power",
    "value": "Elite"
  }
]`}
                  </pre>
                </div>
              </div>

              {/* Tips */}
              <div>
                <h4 className="text-white font-medium mb-2">Tips:</h4>
                <div className="space-y-2 text-sm text-gray-300">
                  <div className="flex items-start space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <p><strong>1-of-1 XFTs</strong> can store assets like SUI and tokens</p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <p><strong>Asset locking</strong> prevents emotional selling and enforces investment discipline</p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <p><strong>Attributes</strong> define traits and rarity for your XFT</p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <p><strong>Label linking</strong> organizes XFTs into collections</p>
                  </div>
                </div>
              </div>

              {/* Lock Duration Guide */}
              <div>
                <h4 className="text-white font-medium mb-2">Lock Duration Guide:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="p-2 bg-gray-800/30 rounded-lg">
                    <p className="text-yellow-400 font-medium">7-30 days</p>
                    <p className="text-gray-400">Short-term discipline</p>
                  </div>
                  <div className="p-2 bg-gray-800/30 rounded-lg">
                    <p className="text-orange-400 font-medium">90-180 days</p>
                    <p className="text-gray-400">Medium-term investment</p>
                  </div>
                  <div className="p-2 bg-gray-800/30 rounded-lg">
                    <p className="text-red-400 font-medium">365+ days</p>
                    <p className="text-gray-400">Long-term holding</p>
                  </div>
                  <div className="p-2 bg-gray-800/30 rounded-lg">
                    <p className="text-green-400 font-medium">No lock</p>
                    <p className="text-gray-400">Flexible access</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Notification Modal */}
        <NotificationModal
          isOpen={notificationModal.isOpen}
          onClose={() => setNotificationModal(prev => ({ ...prev, isOpen: false }))}
          type={notificationModal.type}
          title={notificationModal.title}
          message={notificationModal.message}
          autoClose={notificationModal.type === 'success'}
          autoCloseDelay={4000}
          actionButton={notificationModal.type === 'error' ? {
            label: 'Try Again',
            onClick: () => setNotificationModal(prev => ({ ...prev, isOpen: false }))
          } : undefined}
        />
      </div>
    )
  }

  if (createType === 'generative') {
    return (
      <div className="space-y-6">
        {/* Header with Back Button */}
        <div className="flex items-center space-x-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCreateType('choose')}
            className="flex items-center space-x-2"
          >
            <ArrowRight className="w-4 h-4 rotate-180" />
            <span>Back</span>
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-white font-heading">Mint Generative Collection</h2>
            <p className="text-gray-400">Create a generative XFT collection</p>
          </div>
        </div>

        {/* Generative Mint Form */}
        <Card variant="glass" padding="lg">
          <div className="space-y-6">
            <div className="p-4 bg-cyan-500/10 border border-cyan-500/20 rounded-xl">
              <h3 className="font-semibold text-cyan-400 mb-2">Generative Collections</h3>
              <p className="text-gray-300 text-sm mb-2">
                Create a collection of unique XFTs with programmatically generated traits and metadata.
              </p>
              <div className="text-xs text-gray-400 space-y-1">
                <div>• Must be linked to a main label</div>
                <div>• Images stored on IPFS for decentralization</div>
                <div>• Each XFT has unique traits and rarity</div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Collection Name *</label>
              <input
                type="text"
                placeholder="Cosmic Cats Collection"
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500/50"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Description *</label>
              <textarea
                placeholder="Describe your generative collection..."
                rows={3}
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 resize-none"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">Collection Size *</label>
                <input
                  type="number"
                  min="10"
                  max="10000"
                  placeholder="1000"
                  className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500/50"
                  required
                />
                <p className="text-gray-400 text-xs mt-1">Number of unique XFTs to generate</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-white mb-2">Royalty Fee (%)</label>
                <input
                  type="number"
                  min="0"
                  max="15"
                  defaultValue="5"
                  className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white focus:outline-none focus:ring-2 focus:ring-cyan-500/50"
                />
                <p className="text-gray-400 text-xs mt-1">Royalty on secondary sales</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Main Label ID *</label>
              <input
                type="text"
                placeholder="Enter main label ID"
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500/50"
                required
              />
              <p className="text-gray-400 text-xs mt-1">Collection will be linked to this label</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">IPFS Base URL *</label>
              <input
                type="url"
                placeholder="ipfs://QmYourHashHere/"
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500/50"
                required
              />
              <p className="text-gray-400 text-xs mt-1">IPFS URL where your generated images are stored</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">Metadata JSON URL *</label>
              <input
                type="url"
                placeholder="ipfs://QmYourMetadataHashHere/"
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500/50"
                required
              />
              <p className="text-gray-400 text-xs mt-1">IPFS URL for metadata JSON files</p>
            </div>

            <Button
              variant="primary"
              size="lg"
              className="w-full py-4 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
            >
              <Sparkles className="w-5 h-5 mr-2" />
              Create Generative Collection • 0.1 SUI
            </Button>
          </div>
        </Card>

        {/* Notification Modal */}
        <NotificationModal
          isOpen={notificationModal.isOpen}
          onClose={() => setNotificationModal(prev => ({ ...prev, isOpen: false }))}
          type={notificationModal.type}
          title={notificationModal.title}
          message={notificationModal.message}
          autoClose={notificationModal.type === 'success'}
          autoCloseDelay={4000}
          actionButton={notificationModal.type === 'error' ? {
            label: 'Try Again',
            onClick: () => setNotificationModal(prev => ({ ...prev, isOpen: false }))
          } : undefined}
        />
      </div>
    )
  }

  if (createType === 'wrap') {
    return (
      <div className="space-y-6">
        {/* Header with Back Button */}
        <div className="flex items-center space-x-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCreateType('choose')}
            className="flex items-center space-x-2"
          >
            <ArrowRight className="w-4 h-4 rotate-180" />
            <span>Back</span>
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-white font-heading">Wrap XFT</h2>
            <p className="text-gray-400">Convert limited edition to 1-of-1 for asset storage</p>
          </div>
        </div>

        {/* Wrap XFT Form */}
        <Card variant="glass" padding="lg">
          <div className="space-y-6">
            <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-xl">
              <h3 className="font-semibold text-green-400 mb-2">What is XFT Wrapping?</h3>
              <p className="text-gray-300 text-sm mb-2">
                Wrapping converts a limited edition XFT into a unique 1-of-1 XFT that can store assets like SUI and tokens.
              </p>
              <div className="text-xs text-gray-400 space-y-1">
                <div>• Original XFT is burned in the process</div>
                <div>• New 1-of-1 XFT gains asset storage capability</div>
                <div>• Keeps original name and description</div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-white mb-2">XFT ID to Wrap *</label>
              <input
                type="text"
                placeholder="Enter XFT ID to wrap (e.g., 0x123...)"
                value={wrapFormData.xftId}
                onChange={(e) => setWrapFormData(prev => ({ ...prev, xftId: e.target.value }))}
                className="w-full px-4 py-3 rounded-2xl glass-card border-0 bg-transparent text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500/50"
                required
              />
              <p className="text-gray-400 text-xs mt-1">Must be a limited edition XFT you own</p>
            </div>

            <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
              <div className="flex items-start space-x-2">
                <Info className="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-yellow-400 text-sm mb-1">Important Notice</h4>
                  <p className="text-gray-300 text-xs">
                    This action is irreversible. Your original limited edition XFT will be permanently burned
                    and replaced with a new 1-of-1 XFT that can store assets.
                  </p>
                </div>
              </div>
            </div>

            <Button
              variant="primary"
              size="lg"
              className="w-full py-4 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
              onClick={handleWrapXFT}
              disabled={isCreating || !connected}
            >
              {isCreating ? (
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
              ) : (
                <Shield className="w-5 h-5 mr-2" />
              )}
              {isCreating ? 'Wrapping...' : 'Wrap XFT • Free'}
            </Button>
          </div>
        </Card>

        {/* Notification Modal */}
        <NotificationModal
          isOpen={notificationModal.isOpen}
          onClose={() => setNotificationModal(prev => ({ ...prev, isOpen: false }))}
          type={notificationModal.type}
          title={notificationModal.title}
          message={notificationModal.message}
          autoClose={notificationModal.type === 'success'}
          autoCloseDelay={4000}
          actionButton={notificationModal.type === 'error' ? {
            label: 'Try Again',
            onClick: () => setNotificationModal(prev => ({ ...prev, isOpen: false }))
          } : undefined}
        />
      </div>
    )
  }

  return null
}
