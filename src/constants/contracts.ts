/**
 * Dexsta Platform Contract Addresses - Sui Blockchain
 *
 * Update these addresses after deploying contracts to mainnet/testnet
 */

// ============================================================================
// ADMIN CONTRACTS
// ============================================================================

// Pool Admin Package - manages pool platform settings
export const POOL_ADMIN_PACKAGE_ID = '0x2db3d095d44f3f384c54bfc0d8dc7c780e6c1396c518fff6d458b3fbd5771e65' // ✅ DEPLOYED TO TESTNET

// Token Admin Package - manages token platform settings
export const TOKEN_ADMIN_PACKAGE_ID = '0x2db3d095d44f3f384c54bfc0d8dc7c780e6c1396c518fff6d458b3fbd5771e65' // ✅ DEPLOYED TO TESTNET

// Shared Platform Settings Objects (created during initialization)
export const POOL_PLATFORM_SETTINGS_ID = '0x33141502987854513b747d08e48e937e0612e470c12f7432f3c9fe1c33530a84' // ✅ INITIALIZED
export const TOKEN_PLATFORM_SETTINGS_ID = '0xa71568b23842e569da1ba22335b1e2b3ef0ab65820be8c4c317806fd1d0ff894' // ✅ INITIALIZED

// Super Admin Address (has AdminCap objects)
export const SUPER_ADMIN_ADDRESS = '0x51ba62cb2f1c1c142dac8a8bf4cec1046084c88003b1a8c294e6da478c7258dc' // ✅ INITIALIZED

// Admin Capability Object IDs (needed for admin functions)
export const TOKEN_ADMIN_CAP_ID = process.env.NEXT_PUBLIC_TOKEN_ADMIN_CAP || '0x0000000000000000000000000000000000000000000000000000000000000000' // TODO: Set after initialization
export const POOL_ADMIN_CAP_ID = process.env.NEXT_PUBLIC_POOL_ADMIN_CAP || '0x0000000000000000000000000000000000000000000000000000000000000000' // TODO: Set after initialization

// ============================================================================
// CORE CONTRACTS (NEW DEXSTA SYSTEM)
// ============================================================================

// Main Dexsta Package - Template-based token creation system
export const DEXSTA_PACKAGE_ID = process.env.NEXT_PUBLIC_PACKAGE_ID || '0x351ff7bca3c8ff3b9aa34b331daa1ea745ce5e4837e75e323c741c36092a67d3' // ✅ DEPLOYED TO DEVNET (TEMPLATE SYSTEM)

// Shared Registry Objects (created during deployment)
export const TOKEN_REGISTRY_ID = process.env.NEXT_PUBLIC_TOKEN_REGISTRY || '0x71170147b6830583a01a9a97d17fa6b7ca88266266891418fafa5faa108dec6a' // ✅ DEPLOYED TO DEVNET (TEMPLATE SYSTEM)
export const POOL_REGISTRY_ID = process.env.NEXT_PUBLIC_POOL_REGISTRY || '0x9cc077bcba4acafd5636f07358ac1e4d9ca60539d9cab1ed7e2051273316a1f7' // ✅ DEPLOYED TO DEVNET (TEMPLATE SYSTEM)
export const NFT_REGISTRY_ID = process.env.NEXT_PUBLIC_NFT_REGISTRY || '0xc987a4ce3d0d30ddb10e1b48f6912a9f571828054bbed578a6bffc9d1ca752cd' // ✅ DEPLOYED TO DEVNET (TEMPLATE SYSTEM)

// Platform Admin Address
export const PLATFORM_ADMIN_ADDRESS = process.env.NEXT_PUBLIC_PLATFORM_ADMIN || '0x107850bf3302019d71a699210d8fcf1789065700d3a53f2d6bd7322733c4d7fe' // ✅ SET TO DEPLOYMENT ACCOUNT

// Admin Platform Settings (Shared Objects)
export const TOKEN_PLATFORM_SETTINGS = process.env.NEXT_PUBLIC_TOKEN_PLATFORM_SETTINGS || '0x704055cd95bb0d66cbdc82cf749979a8bfae948a269f93a9f94a93b5c22f1f8c' // ✅ INITIALIZED
export const POOL_PLATFORM_SETTINGS = process.env.NEXT_PUBLIC_POOL_PLATFORM_SETTINGS || '0x6b1b4d01b6a0348fa29b97c408964757c08ffd7eabde8fa93a5aad0774755204' // ✅ INITIALIZED

// Admin Capabilities (Owned by Platform Admin)
export const TOKEN_ADMIN_CAP = process.env.NEXT_PUBLIC_TOKEN_ADMIN_CAP || '0x9a8a84f73c20e33f82f17c0a9b8a8f6b50e6d4c1a0fb3dbc41b8756e9b674f77' // ✅ INITIALIZED
export const POOL_ADMIN_CAP = process.env.NEXT_PUBLIC_POOL_ADMIN_CAP || '0xd01202bebec47abd788398d2e03f3a166a7311ad4f5aa67d50f708e351d5739f' // ✅ INITIALIZED

// ============================================================================
// LEGACY CONTRACTS (OLD SYSTEM - KEEP FOR REFERENCE)
// ============================================================================

// Main Platform Package - handles token creation, trading, bonding curve, and pools
export const MAIN_PLATFORM_PACKAGE_ID = '0x2bc2ff731577330a6b9f9f40d5c218b234527b7ee6b94ebbfc6526049eb6a262' // ✅ DEPLOYED TO DEVNET

// Token Package - handles token creation, trading, and bonding curve
export const TOKEN_PACKAGE_ID_DEXSTA = '0x2bc2ff731577330a6b9f9f40d5c218b234527b7ee6b94ebbfc6526049eb6a262' // ✅ DEPLOYED TO DEVNET

// Pool Package - handles liquidity pools and AMM functionality
export const POOL_PACKAGE_ID_DEXSTA = '0x2bc2ff731577330a6b9f9f40d5c218b234527b7ee6b94ebbfc6526049eb6a262' // ✅ DEPLOYED TO DEVNET

// ============================================================================
// SUI NETWORK CONFIGURATION
// ============================================================================

// Network URLs
export const SUI_NETWORKS = {
  mainnet: 'https://fullnode.mainnet.sui.io:443',
  testnet: 'https://fullnode.testnet.sui.io:443',
  devnet: 'https://fullnode.devnet.sui.io:443',
  localnet: 'http://127.0.0.1:9000'
} as const

// Default network
export const DEFAULT_NETWORK = 'testnet' as const

// ============================================================================
// NEW DEXSTA CONTRACT FUNCTIONS
// ============================================================================

// Contract function targets for new template-based system
export const DEXSTA_FUNCTIONS = {
  // Template-based token functions
  TOKEN: {
    // Step 1: Request token creation (triggers frontend module deployment)
    REQUEST_CREATION: `${DEXSTA_PACKAGE_ID}::dexsta_token::request_token_creation`,

    // Step 2: Complete token creation (after module deployment)
    COMPLETE_CREATION: `${DEXSTA_PACKAGE_ID}::dexsta_token::complete_token_creation`,

    // Legacy functions (still supported)
    BUY: `${DEXSTA_PACKAGE_ID}::dexsta_token::swap_sui_for_tokens`,
    SELL: `${DEXSTA_PACKAGE_ID}::dexsta_token::swap_tokens_for_sui`,
    TRANSFER_OWNERSHIP: `${DEXSTA_PACKAGE_ID}::dexsta_token::transfer_ownership`,
    UPDATE_FEES: `${DEXSTA_PACKAGE_ID}::dexsta_token::update_fees`,
    UPDATE_SOCIALS: `${DEXSTA_PACKAGE_ID}::dexsta_token::update_social_links`,
    DONATE_REWARD: `${DEXSTA_PACKAGE_ID}::dexsta_token::add_to_reward_pot`,
    GET_INFO: `${DEXSTA_PACKAGE_ID}::dexsta_token::get_token_pool_info`,
    GET_TEMPLATE_DATA: `${DEXSTA_PACKAGE_ID}::dexsta_token::generate_template_data`,
  },

  // Pool functions
  POOL: {
    CREATE: `${DEXSTA_PACKAGE_ID}::pool::create_pool`,
    CREATE_WITH_NFT: `${DEXSTA_PACKAGE_ID}::pool::create_pool_with_nft_gating`,
    BUY: `${DEXSTA_PACKAGE_ID}::pool::swap_sui_for_tokens`,
    SELL: `${DEXSTA_PACKAGE_ID}::pool::swap_tokens_for_sui`,
    TRANSFER_OWNERSHIP: `${DEXSTA_PACKAGE_ID}::pool::transfer_pool_ownership`,
    UPDATE_FEES: `${DEXSTA_PACKAGE_ID}::pool::update_pool_fees`,
    DONATE_REWARD: `${DEXSTA_PACKAGE_ID}::pool::add_to_reward_pot`,
    GET_INFO: `${DEXSTA_PACKAGE_ID}::pool::get_pool_info`,
  },

  // NFT functions
  NFT: {
    MINT_ONE_OF_ONE: `${DEXSTA_PACKAGE_ID}::dexsta_nft::mint_one_of_one`,
    CREATE_COLLECTION: `${DEXSTA_PACKAGE_ID}::dexsta_nft::create_collection`,
    MINT_FROM_COLLECTION: `${DEXSTA_PACKAGE_ID}::dexsta_nft::mint_from_collection`,
    STORE_SUI: `${DEXSTA_PACKAGE_ID}::dexsta_nft::store_sui_in_nft`,
    WITHDRAW_SUI: `${DEXSTA_PACKAGE_ID}::dexsta_nft::withdraw_sui_from_nft`,
    LOCK_NFT: `${DEXSTA_PACKAGE_ID}::dexsta_nft::lock_nft`,
    GET_INFO: `${DEXSTA_PACKAGE_ID}::dexsta_nft::get_nft_info`,
    GET_ASSETS: `${DEXSTA_PACKAGE_ID}::dexsta_nft::get_nft_assets`,
  },
};

// Event types for parsing - Template-based system
export const DEXSTA_EVENTS = {
  // Template-based token events
  TOKEN_MODULE_DEPLOYMENT_REQUESTED: `${DEXSTA_PACKAGE_ID}::dexsta_token::TokenModuleDeploymentRequested`,
  TOKEN_CREATED: `${DEXSTA_PACKAGE_ID}::dexsta_token::TokenCreated`,
  TOKEN_SWAPPED: `${DEXSTA_PACKAGE_ID}::dexsta_token::TokenSwapped`,

  // Pool events
  POOL_CREATED: `${DEXSTA_PACKAGE_ID}::dexsta_pool::PoolCreated`,

  // NFT events
  NFT_MINTED: `${DEXSTA_PACKAGE_ID}::dexsta_nft::NFTMinted`,
  COLLECTION_CREATED: `${DEXSTA_PACKAGE_ID}::dexsta_nft::CollectionCreated`,
  ASSET_STORED: `${DEXSTA_PACKAGE_ID}::dexsta_nft::AssetStored`,
  NFT_LOCKED: `${DEXSTA_PACKAGE_ID}::dexsta_nft::NFTLocked`,
};

// ============================================================================
// OBJECT TYPE IDENTIFIERS
// ============================================================================

// Admin Object Types
export const POOL_PLATFORM_SETTINGS_TYPE = 'platform_settings::PlatformSettings'
export const TOKEN_PLATFORM_SETTINGS_TYPE = 'platform_settings::PlatformSettings'

// Token Object Types
export const TOKEN_DATA_TYPE = 'token::TokenData'
export const REWARD_POT_TYPE = 'token::RewardPot'

// Pool Object Types
export const POOL_DATA_TYPE = 'pool::PoolData'
export const LP_TOKEN_TYPE = 'pool::LPToken'

// ============================================================================
// TOKEN CONSTANTS
// ============================================================================

// Token Configuration
export const TOKEN_DECIMALS = 9
export const TOTAL_SUPPLY = BigInt(1_000_000_000) * (BigInt(10) ** BigInt(TOKEN_DECIMALS)) // 1B tokens

// Template-based Token Creation
export const TEMPLATE_CONSTANTS = {
  // Template file location
  TEMPLATE_PATH: '/example/template.move',

  // Template placeholders
  PLACEHOLDERS: {
    MODULE_ADDRESS: '{{token_module_address}}',
    MODULE_NAME: '{{token_module_name}}',
    STRUCT_NAME: '{{token_struct_name}}',
  },

  // Module naming rules
  MODULE_NAME_SUFFIX: '_token',
  MAX_MODULE_NAME_LENGTH: 32,

  // Deployment configuration
  DEFAULT_GAS_BUDGET: 100_000_000, // 0.1 SUI for module deployment
  DEPLOYMENT_TIMEOUT: 60000, // 60 seconds
}

// Fee Limits (in basis points)
export const MIN_FEE_BPS = 0      // 0%
export const MAX_FEE_BPS = 500    // 5%
export const DEFAULT_PLATFORM_FEE_BPS = 100  // 1%
export const DEFAULT_REWARD_FEE_BPS = 50     // 0.5%

// Trading Limits
export const MIN_SUI_TRADE = 0.001  // 0.001 SUI minimum
export const MAX_SUI_TRADE = 100    // 100 SUI maximum per transaction
export const MIN_TOKEN_CREATION_FEE = 100_000_000 // 0.1 SUI

// ============================================================================
// BONDING CURVE CONSTANTS
// ============================================================================

// Pricing Constants (must match contract exactly)
export const SCALE_FACTOR = BigInt(1_000_000_000_000) // 10^12 for precision
export const INITIAL_PRICE_SCALED = BigInt(23800)     // 0.00000001 SUI initial price
export const MIN_PRICE_SCALED = BigInt(10)            // 0.00000001 SUI minimum
export const MAX_PRICE_SCALED = BigInt(1_000_000_000_000) // 0.001 SUI maximum
export const REFERENCE_AMOUNT_DIVISOR = BigInt(100)   // 1% of total supply as reference
export const POWER_LAW_EXPONENT_SCALED = BigInt(1_800_000) // Exponent 1.8 for aggressive curve
export const REFERENCE_TRADE_SIZE_DIVISOR = BigInt(1000)   // 0.1% of supply for small trade threshold

// Conversion Constants
export const MIST_PER_SUI = BigInt(1_000_000_000) // SUI uses MIST (10^9)
export const TOKENS_PER_UNIT = BigInt(10) ** BigInt(TOKEN_DECIMALS)

// ============================================================================
// MIGRATION CONSTANTS
// ============================================================================

// Migration Thresholds
export const DEFAULT_BONDING_CURVE_GOAL = BigInt(60_000_000_000) // 60 SUI
export const DEFAULT_MIGRATION_FEE_PERCENTAGE = 5 // 5%
export const DEFAULT_MIGRATION_GAS_FEE = BigInt(5_000_000) // 0.005 SUI

// Price Tolerance for Migration
export const MIGRATION_PRICE_TOLERANCE_BPS = 100 // 1% tolerance

// ============================================================================
// REWARD GAME CONSTANTS
// ============================================================================

// Reward Configuration
export const DEFAULT_INITIAL_REWARD_GOAL = BigInt(1_000_000_000) // 1 SUI
export const DEFAULT_REWARD_GOAL_INCREASE = BigInt(500_000_000)  // 0.5 SUI
export const DEFAULT_REWARD_GOAL_DECREASE_AMOUNT = BigInt(100_000_000) // 0.1 SUI
export const DEFAULT_REWARD_GOAL_DECREASE_THRESHOLD = 300 // 5 minutes in seconds
export const DEFAULT_REWARD_GOAL_PROXIMITY_THRESHOLD = 90 // 90%
export const DEFAULT_MIN_REWARD_TRADE_AMOUNT = BigInt(10_000_000) // 0.01 SUI

// ============================================================================
// SUI TRANSACTION CONFIGURATION
// ============================================================================

// Gas Configuration
export const DEFAULT_GAS_BUDGET = 10_000_000 // 0.01 SUI
export const MAX_GAS_BUDGET = 100_000_000    // 0.1 SUI

// Transaction Configuration
export const DEFAULT_MAX_RETRIES = 3
export const DEFAULT_MAX_WAIT_SECONDS = 60
export const DEFAULT_POLL_INTERVAL = 1000 // 1 second

// ============================================================================
// ENVIRONMENT HELPERS
// ============================================================================

/**
 * Get contract addresses based on environment
 */
export function getContractAddresses(network: 'mainnet' | 'testnet' | 'devnet' | 'localnet') {
  switch (network) {
    case 'mainnet':
      return {
        poolAdmin: POOL_ADMIN_PACKAGE_ID,
        tokenAdmin: TOKEN_ADMIN_PACKAGE_ID,
        tokenPackage: TOKEN_PACKAGE_ID_DEXSTA,
        poolPackage: POOL_PACKAGE_ID_DEXSTA,
      }
    case 'testnet':
      // ✅ Testnet addresses deployed and working
      return {
        poolAdmin: POOL_ADMIN_PACKAGE_ID,
        tokenAdmin: TOKEN_ADMIN_PACKAGE_ID,
        mainPlatform: MAIN_PLATFORM_PACKAGE_ID,
        tokenPackage: TOKEN_PACKAGE_ID_DEXSTA,
        poolPackage: POOL_PACKAGE_ID_DEXSTA,
        tokenRegistry: TOKEN_REGISTRY_ID,
        poolRegistry: POOL_REGISTRY_ID,
      }
    case 'devnet':
      // ✅ Devnet addresses deployed and working
      return {
        poolAdmin: POOL_ADMIN_PACKAGE_ID,
        tokenAdmin: TOKEN_ADMIN_PACKAGE_ID,
        mainPlatform: MAIN_PLATFORM_PACKAGE_ID,
        tokenPackage: TOKEN_PACKAGE_ID_DEXSTA,
        poolPackage: POOL_PACKAGE_ID_DEXSTA,
        tokenRegistry: TOKEN_REGISTRY_ID,
        poolRegistry: POOL_REGISTRY_ID,
        nftRegistry: NFT_REGISTRY_ID,
      }
    case 'localnet':
      // TODO: Add localnet addresses for testing
      return {
        poolAdmin: POOL_ADMIN_PACKAGE_ID,
        tokenAdmin: TOKEN_ADMIN_PACKAGE_ID,
        tokenPackage: TOKEN_PACKAGE_ID_DEXSTA,
        poolPackage: POOL_PACKAGE_ID_DEXSTA,
      }
    default:
      throw new Error(`Unsupported network: ${network}`)
  }
}

/**
 * Get Sui network URL
 */
export function getSuiNetworkUrl(network: keyof typeof SUI_NETWORKS): string {
  return SUI_NETWORKS[network]
}

/**
 * Validate that all contract addresses are properly set
 */
export function validateContractAddresses() {
  const placeholderAddress = '0x0000000000000000000000000000000000000000000000000000000000000000'

  const addresses = [
    { name: 'POOL_ADMIN_PACKAGE_ID', address: POOL_ADMIN_PACKAGE_ID },
    { name: 'TOKEN_ADMIN_PACKAGE_ID', address: TOKEN_ADMIN_PACKAGE_ID },
    { name: 'MAIN_PLATFORM_PACKAGE_ID', address: MAIN_PLATFORM_PACKAGE_ID },
    { name: 'TOKEN_PACKAGE_ID_DEXSTA', address: TOKEN_PACKAGE_ID_DEXSTA },
    { name: 'POOL_PACKAGE_ID_DEXSTA', address: POOL_PACKAGE_ID_DEXSTA },
    { name: 'TOKEN_REGISTRY_ID', address: TOKEN_REGISTRY_ID },
    { name: 'POOL_REGISTRY_ID', address: POOL_REGISTRY_ID },
  ]

  const unsetAddresses = addresses.filter(addr => addr.address === placeholderAddress)

  if (unsetAddresses.length > 0) {
    console.warn('⚠️  The following contract addresses are not set:')
    unsetAddresses.forEach(addr => {
      console.warn(`   - ${addr.name}: ${addr.address}`)
    })
    console.warn('   Update src/constants/contracts.ts with deployed contract addresses')
    return false
  }

  console.log('✅ All contract addresses are properly configured')
  return true
}

/**
 * Format contract address for display
 */
export function formatContractAddress(address: string): string {
  return `${address.slice(0, 6)}...${address.slice(-4)}`
}

/**
 * Check if address is a placeholder
 */
export function isPlaceholderAddress(address: string): boolean {
  return address === '0x0000000000000000000000000000000000000000000000000000000000000000'
}

/**
 * Convert SUI to MIST
 */
export function suiToMist(sui: number): bigint {
  return BigInt(Math.floor(sui * 1_000_000_000))
}

/**
 * Convert MIST to SUI
 */
export function mistToSui(mist: bigint): number {
  return Number(mist) / 1_000_000_000
}

/**
 * Format SUI amount for display
 */
export function formatSuiAmount(amount: number | string | bigint): string {
  let numAmount: number

  if (typeof amount === 'bigint') {
    numAmount = mistToSui(amount)
  } else if (typeof amount === 'string') {
    numAmount = parseFloat(amount)
  } else {
    numAmount = amount
  }

  // Handle very small amounts
  if (numAmount < 0.000001) {
    return '< 0.000001 SUI'
  }

  // Format based on amount size
  if (numAmount >= 1000000) {
    return `${(numAmount / 1000000).toFixed(2)}M SUI`
  } else if (numAmount >= 1000) {
    return `${(numAmount / 1000).toFixed(2)}K SUI`
  } else if (numAmount >= 1) {
    return `${numAmount.toFixed(3)} SUI`
  } else if (numAmount >= 0.001) {
    return `${numAmount.toFixed(6)} SUI`
  } else {
    return `${numAmount.toFixed(9)} SUI`
  }
}

// ============================================================================
// XFT CONTRACTS (NFT 2.0 System)
// ============================================================================

// XFT Package IDs - Deployed to Devnet
export const XFT_PACKAGE_ID = '0x4dc8f30853631d23aef24cbf60797b13e70457241928d6d82290d54690cb9f42'

// Fire Registry Package and Object
export const FIRE_REGISTRY_PACKAGE_ID = 'fire_registry' // Package name for Fire Registry
export const FIRE_REGISTRY_ID = process.env.NEXT_PUBLIC_FIRE_REGISTRY || '0x0000000000000000000000000000000000000000000000000000000000000000' // TODO: Set after deployment

// XFT Registry Objects
export const XFT_LABEL_REGISTRY_ID = '0xc48e29105f078473d86e3e4dc2b5b667ce82f65833995e5febae401e78c930fc'
export const XFT_NFT_REGISTRY_ID = '0xc6a3d8b70b92ddf0977306c82fba50d195da7d31d68309aab6d10b4f3ae22baa'

// XFT Function Targets
export const XFT_FUNCTIONS = {
  // Label functions
  LABEL: {
    MINT_LABEL: `${XFT_PACKAGE_ID}::simple_label::mint_label`,
    VALIDATE_OWNERSHIP: `${XFT_PACKAGE_ID}::simple_label::validate_label_ownership`,
    // Getter functions for validation - these exist in the contracts
    GET_LABEL_OWNER: `${XFT_PACKAGE_ID}::simple_label::owner`,
    GET_LABEL_EXPIRATION: `${XFT_PACKAGE_ID}::simple_label::expiration_time`,
    IS_EXPIRED: `${XFT_PACKAGE_ID}::simple_label::is_expired`,
    GET_LABEL_NAME: `${XFT_PACKAGE_ID}::simple_label::name`,
    GET_LABEL_TYPE: `${XFT_PACKAGE_ID}::simple_label::label_type`,
  },
  // XFT functions
  XFT: {
    MINT_XFT: `${XFT_PACKAGE_ID}::simple_nft::mint_nft`,
    WRAP_XFT: `${XFT_PACKAGE_ID}::simple_nft::wrap_xft`,
    DEPOSIT_ASSETS: `${XFT_PACKAGE_ID}::simple_nft::deposit`,
    WITHDRAW_ASSETS: `${XFT_PACKAGE_ID}::simple_nft::withdraw`,
    // Getter functions for validation
    GET_NFT_OWNER: `${XFT_PACKAGE_ID}::simple_nft::owner`,
    GET_NFT_CREATOR: `${XFT_PACKAGE_ID}::simple_nft::creator`,
    GET_NFT_LABEL_ID: `${XFT_PACKAGE_ID}::simple_nft::label_global_id`, // Need to add this
    NFT_EXISTS: `${XFT_PACKAGE_ID}::simple_nft::nft_exists`, // Need to add this
  },
  // Operator functions
  OPERATOR: {
    GRANT_LICENSE: `${XFT_PACKAGE_ID}::simple_operator::grant_license`,
    MINT_AS_OPERATOR: `${XFT_PACKAGE_ID}::simple_nft::mint_as_operator`,
    // Getter functions for validation - these exist in the contracts
    GET_LICENSE_HOLDER: `${XFT_PACKAGE_ID}::simple_operator::holder`,
    GET_LICENSE_LABEL_ID: `${XFT_PACKAGE_ID}::simple_operator::label_id`,
    GET_LICENSE_ROLE: `${XFT_PACKAGE_ID}::simple_operator::role`,
    GET_LICENSE_EXPIRATION: `${XFT_PACKAGE_ID}::simple_operator::expiration_time`,
    IS_LICENSE_EXPIRED: `${XFT_PACKAGE_ID}::simple_operator::is_expired`,
    IS_VALID_OPERATOR: `${XFT_PACKAGE_ID}::simple_operator::is_valid_operator`,
  },
  // Bank functions
  BANK: {
    DEPOSIT: `${XFT_PACKAGE_ID}::simple_bank::deposit`,
    ISSUE_LOAN: `${XFT_PACKAGE_ID}::simple_bank::issue_loan`,
    REPAY_LOAN: `${XFT_PACKAGE_ID}::simple_bank::repay_loan`,
  },
  // Registry functions for validation
  REGISTRY: {
    GET_LABEL_BY_ID: `${XFT_PACKAGE_ID}::simple_label::get_label_by_global_id`, // Need to add this
    GET_NFT_BY_ID: `${XFT_PACKAGE_ID}::simple_nft::get_nft_by_global_id`, // Need to add this
  }
}

// XFT Constants
export const XFT_CONSTANTS = {
  LABEL_MINT_FEE: *********, // 0.1 SUI in MIST
  XFT_MINT_FEE: ********,    // 0.05 SUI in MIST
  MAX_SUPPLY: 10000,
  DEFAULT_DURATION_YEARS: 1,
}

// ============================================================================
// EXPORT ALL CONSTANTS
// ============================================================================

export const CONTRACT_ADDRESSES = {
  POOL_ADMIN_PACKAGE_ID,
  TOKEN_ADMIN_PACKAGE_ID,
  MAIN_PLATFORM_PACKAGE_ID,
  TOKEN_PACKAGE_ID_DEXSTA,
  POOL_PACKAGE_ID_DEXSTA,
  TOKEN_REGISTRY_ID,
  POOL_REGISTRY_ID,
  // XFT Contracts
  XFT_PACKAGE_ID,
  XFT_LABEL_REGISTRY_ID,
  XFT_NFT_REGISTRY_ID,
}

export const SUI_OBJECT_TYPES = {
  POOL_PLATFORM_SETTINGS_TYPE,
  TOKEN_PLATFORM_SETTINGS_TYPE,
  TOKEN_DATA_TYPE,
  REWARD_POT_TYPE,
  POOL_DATA_TYPE,
  LP_TOKEN_TYPE,
}

export const TOKEN_CONSTANTS = {
  TOKEN_DECIMALS,
  TOTAL_SUPPLY,
  MIN_FEE_BPS,
  MAX_FEE_BPS,
  DEFAULT_PLATFORM_FEE_BPS,
  DEFAULT_REWARD_FEE_BPS,
  MIN_SUI_TRADE,
  MAX_SUI_TRADE,
  MIN_TOKEN_CREATION_FEE,
}

export const XFT_EXPORTS = {
  XFT_FUNCTIONS,
  XFT_CONSTANTS,
}

export const BONDING_CURVE_CONSTANTS = {
  SCALE_FACTOR,
  INITIAL_PRICE_SCALED,
  MIN_PRICE_SCALED,
  MAX_PRICE_SCALED,
  REFERENCE_AMOUNT_DIVISOR,
  POWER_LAW_EXPONENT_SCALED,
  REFERENCE_TRADE_SIZE_DIVISOR,
  MIST_PER_SUI,
  TOKENS_PER_UNIT,
}

export const MIGRATION_CONSTANTS = {
  DEFAULT_BONDING_CURVE_GOAL,
  DEFAULT_MIGRATION_FEE_PERCENTAGE,
  DEFAULT_MIGRATION_GAS_FEE,
  MIGRATION_PRICE_TOLERANCE_BPS,
}

export const REWARD_GAME_CONSTANTS = {
  DEFAULT_INITIAL_REWARD_GOAL,
  DEFAULT_REWARD_GOAL_INCREASE,
  DEFAULT_REWARD_GOAL_DECREASE_AMOUNT,
  DEFAULT_REWARD_GOAL_DECREASE_THRESHOLD,
  DEFAULT_REWARD_GOAL_PROXIMITY_THRESHOLD,
  DEFAULT_MIN_REWARD_TRADE_AMOUNT,
}
