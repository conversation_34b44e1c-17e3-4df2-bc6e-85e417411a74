'use client'

import { ReactNode } from 'react'
import { WalletProvider } from '@suiet/wallet-kit'
import '@suiet/wallet-kit/style.css'

// Re-export wallet hooks from Suiet
export { useWallet } from '@suiet/wallet-kit'

interface WalletContextProviderProps {
  children: ReactNode
}

export function WalletContextProvider({ children }: WalletContextProviderProps) {
  return (
    <WalletProvider>
      {children}
    </WalletProvider>
  )
}
