// Haptic feedback utility for mobile devices
// Provides tactile feedback for user interactions

export type HapticType = 'light' | 'medium' | 'heavy' | 'selection' | 'impact' | 'notification'

/**
 * Trigger haptic feedback on supported devices
 * @param type - Type of haptic feedback to trigger
 */
export const triggerHaptic = (type: HapticType = 'light'): void => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') return

  try {
    // Check for iOS Safari haptic feedback
    if ('vibrate' in navigator) {
      switch (type) {
        case 'light':
          navigator.vibrate(10)
          break
        case 'medium':
          navigator.vibrate(20)
          break
        case 'heavy':
          navigator.vibrate(40)
          break
        case 'selection':
          navigator.vibrate(5)
          break
        case 'impact':
          navigator.vibrate(30)
          break
        case 'notification':
          navigator.vibrate([10, 50, 10])
          break
        default:
          navigator.vibrate(10)
      }
    }

    // Check for iOS Safari Taptic Engine (if available)
    // @ts-ignore - iOS Safari specific API
    if (window.DeviceMotionEvent && typeof DeviceMotionEvent.requestPermission === 'function') {
      // iOS device with haptic feedback support
      // Note: This requires user permission and is limited to user-initiated events
    }

    // Android Chrome haptic feedback
    if ('vibrate' in navigator && /Android/i.test(navigator.userAgent)) {
      switch (type) {
        case 'light':
          navigator.vibrate(25)
          break
        case 'medium':
          navigator.vibrate(50)
          break
        case 'heavy':
          navigator.vibrate(100)
          break
        case 'selection':
          navigator.vibrate(10)
          break
        case 'impact':
          navigator.vibrate(75)
          break
        case 'notification':
          navigator.vibrate([50, 100, 50])
          break
        default:
          navigator.vibrate(25)
      }
    }

  } catch (error) {
    // Silently fail if haptic feedback is not supported
    console.debug('Haptic feedback not supported:', error)
  }
}

/**
 * Check if haptic feedback is supported on the current device
 * @returns boolean indicating haptic support
 */
export const isHapticSupported = (): boolean => {
  if (typeof window === 'undefined') return false
  
  return 'vibrate' in navigator && typeof navigator.vibrate === 'function'
}

/**
 * Request permission for haptic feedback (iOS Safari)
 * @returns Promise<boolean> indicating if permission was granted
 */
export const requestHapticPermission = async (): Promise<boolean> => {
  if (typeof window === 'undefined') return false

  try {
    // @ts-ignore - iOS Safari specific API
    if (window.DeviceMotionEvent && typeof DeviceMotionEvent.requestPermission === 'function') {
      // @ts-ignore
      const permission = await DeviceMotionEvent.requestPermission()
      return permission === 'granted'
    }
    return true // Permission not required on other platforms
  } catch (error) {
    console.debug('Haptic permission request failed:', error)
    return false
  }
}

/**
 * Haptic feedback patterns for common UI interactions
 */
export const hapticPatterns = {
  // Button taps
  buttonTap: () => triggerHaptic('light'),
  
  // Navigation
  tabSwitch: () => triggerHaptic('selection'),
  pageTransition: () => triggerHaptic('medium'),
  
  // Actions
  success: () => triggerHaptic('notification'),
  error: () => triggerHaptic('heavy'),
  warning: () => triggerHaptic('medium'),
  
  // Trading
  buy: () => triggerHaptic('impact'),
  sell: () => triggerHaptic('impact'),
  tradeComplete: () => triggerHaptic('notification'),
  
  // UI feedback
  swipeRefresh: () => triggerHaptic('light'),
  longPress: () => triggerHaptic('heavy'),
  selection: () => triggerHaptic('selection'),
} as const

export default triggerHaptic
