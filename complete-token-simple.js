/**
 * Simple Token Completion Script
 * Completes token creation using existing template approach
 */

const { SuiClient, getFullnodeUrl } = require('@mysten/sui/client');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { Transaction } = require('@mysten/sui/transactions');
const { fromB64 } = require('@mysten/sui/utils');

// Configuration
const NETWORK = 'devnet';
const PACKAGE_ID = '0x75da1bbe12c141aa2269396420b17355847063e55f0211fe97b3969c49618bcf';
const TOKEN_REGISTRY = '0xe25b786282469e3e78571b9fed5cfe9c81203eac20bfaa82bae79611dd6cba68';
const PRIVATE_KEY = 'suiprivkey1qq96u4z3h5ze7ptnm69d45fugmutr499mwddfqt6009jzfgqypge6fmrzws';

async function completeTokenCreation() {
  console.log('🎯 Simple Token Completion');
  console.log('==========================');

  // Initialize client and keypair
  const client = new SuiClient({ url: getFullnodeUrl(NETWORK) });
  const keypair = Ed25519Keypair.fromSecretKey(PRIVATE_KEY);

  try {
    // Step 1: Find deployment events
    console.log('🔍 Finding deployment events...');
    
    const eventQuery = {
      MoveEventType: `${PACKAGE_ID}::dexsta_token::TokenModuleDeploymentRequested`
    };

    const events = await client.queryEvents({
      query: eventQuery,
      limit: 10,
      order: 'descending'
    });

    if (!events.data || events.data.length === 0) {
      console.log('⚠️ No deployment events found');
      return;
    }

    // Use the latest event
    const latestEvent = events.data[0];
    const eventData = latestEvent.parsedJson;
    
    console.log('📋 Processing token:', eventData.token_symbol);

    // Step 2: Complete token creation using template approach
    console.log('🎯 Completing token creation...');

    const tx = new Transaction();
    
    // Use the existing template module (your deployed contract)
    tx.moveCall({
      target: `${PACKAGE_ID}::dexsta_token::complete_token_creation`,
      arguments: [
        tx.object(TOKEN_REGISTRY),
        tx.pure.string(eventData.token_symbol),
        tx.pure.address(PACKAGE_ID), // Use main package as module address
        tx.pure.string('dexsta_token'), // Module name
        tx.pure.string(`${PACKAGE_ID}::dexsta_token::DEXSTA`), // Token type
        tx.pure.id('0x0'), // Treasury cap (placeholder)
        tx.pure.id('0x0'), // Coin metadata (placeholder)
        tx.pure.u64('1000000'), // Total supply
        tx.pure.string(''), // Website
        tx.pure.string(''), // Twitter
        tx.pure.string(''), // Telegram
        tx.pure.string(''), // TikTok
        tx.pure.u16(250), // Buy fee (2.5%)
        tx.pure.u16(250), // Sell fee (2.5%)
        tx.pure.u16(1000), // Max wallet (10%)
        tx.pure.u64(0), // Link to label
        tx.pure.u64(0), // XFT ID
      ],
    });

    const result = await client.signAndExecuteTransaction({
      transaction: tx,
      signer: keypair,
      options: {
        showEffects: true,
        showEvents: true,
      }
    });

    console.log('✅ Token creation completed!');
    console.log('📦 Transaction:', result.digest);
    console.log('🌐 View on explorer:', `https://suiexplorer.com/txblock/${result.digest}?network=devnet`);

    // Check for events
    if (result.events) {
      console.log('📋 Events emitted:');
      result.events.forEach((event, index) => {
        console.log(`  ${index + 1}. ${event.type}`);
        if (event.parsedJson) {
          console.log('     Data:', event.parsedJson);
        }
      });
    }

    console.log('\n🎉 Token is now live and tradeable!');
    console.log('💰 Users should now see tokens in their wallets');

  } catch (error) {
    console.error('❌ Token completion failed:', error);
  }
}

// Run the completion
completeTokenCreation().catch(console.error);
