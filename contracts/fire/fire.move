///
/// # Fire Registry - Central Contract Address Registry
///
/// This contract serves as the central registry for all XFT ecosystem contract addresses.
/// Instead of hardcoding contract addresses or creating cross-dependencies, all contracts
/// can query the Fire Registry to get the current addresses of other contracts.
///
/// ## Key Features
///
/// * Central registry for all contract addresses
/// * Admin-controlled updates for contract upgrades
/// * Version tracking for contract deployments
/// * Emergency pause functionality
/// * Event logging for all address changes
///
module fire_registry::fire {
    use std::string::{Self, String};
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::event;
    use sui::table::{Self, Table};
    use std::option::{Self, Option};

    // ===== Constants =====
    const CONTRACT_OPERATOR: vector<u8> = b"operator";
    const CONTRACT_LABEL: vector<u8> = b"label";
    const CONTRACT_NFT: vector<u8> = b"nft";
    const CONTRACT_BANK: vector<u8> = b"bank";
    const CONTRACT_MARKETPLACE: vector<u8> = b"marketplace";
    const CONTRACT_TOKEN: vector<u8> = b"token";
    const CONTRACT_POOL: vector<u8> = b"pool";

    // Item Types for Global Registry
    const ITEM_TYPE_LABEL: u64 = 1;
    const ITEM_TYPE_XFT: u64 = 2;
    const ITEM_TYPE_OPERATOR_LICENSE: u64 = 3;
    const ITEM_TYPE_MARKETPLACE_LICENSE: u64 = 4;

    // ===== Errors =====
    const ENotAdmin: u64 = 0;
    const EContractNotFound: u64 = 1;
    const ESystemPaused: u64 = 2;
    const EInvalidContractName: u64 = 3;
    const EInvalidItemType: u64 = 4;
    const EItemNotFound: u64 = 5;
    const EItemAlreadyRegistered: u64 = 6;

    // ===== Events =====
    public struct ContractRegistered has copy, drop {
        contract_name: String,
        package_address: address,
        registry_address: address,
        version: u64,
        admin: address,
    }

    public struct ContractUpdated has copy, drop {
        contract_name: String,
        old_package_address: address,
        new_package_address: address,
        old_registry_address: address,
        new_registry_address: address,
        old_version: u64,
        new_version: u64,
        admin: address,
    }

    public struct AdminChanged has copy, drop {
        old_admin: address,
        new_admin: address,
    }

    public struct SystemPaused has copy, drop {
        admin: address,
        reason: String,
    }

    public struct SystemUnpaused has copy, drop {
        admin: address,
    }

    public struct GlobalIDAssigned has copy, drop {
        entity_type: String, // "label", "xft", "operator_license", etc.
        entity_id: ID,       // Object ID of the entity
        global_id: u64,      // Sequential ID (#1, #2, #3, etc.)
        creator: address,
    }

    // ===== One-Time Witness =====
    public struct FIRE has drop {}

    // ===== Objects =====
    public struct FireRegistry has key {
        id: UID,
        admin: address,
        paused: bool,
        contracts: Table<String, ContractInfo>,
        contract_count: u64,
        version: u64,
        // External registry reference
        global_registry_id: Option<ID>,         // Reference to external GlobalRegistry
        // Fee configuration
        label_fee_per_year: u64,                // Fee for label registration per year
        marketplace_fee_bps: u64,               // Marketplace fee in basis points
        platform_label_id: Option<ID>,         // Platform label object ID for fee routing
    }

    public struct ContractInfo has store, drop {
        name: String,
        package_address: address,
        registry_address: address, // Shared object address for the contract
        version: u64,
        active: bool,
        deployed_at: u64,
        updated_at: u64,
    }

    // ===== Functions =====
    fun init(_witness: FIRE, ctx: &mut TxContext) {
        let registry = FireRegistry {
            id: object::new(ctx),
            admin: tx_context::sender(ctx),
            paused: false,
            contracts: table::new(ctx),
            contract_count: 0,
            version: 1,
            // External registry reference (set later)
            global_registry_id: option::none(),
            // Fee configuration
            label_fee_per_year: **********,     // 1 SUI per year default
            marketplace_fee_bps: 250,           // 2.5% default marketplace fee
            platform_label_id: option::none(),  // No platform label initially
        };

        transfer::share_object(registry);
    }

    // Register a new contract
    public fun register_contract(
        registry: &mut FireRegistry,
        name: String,
        package_address: address,
        registry_address: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAdmin);
        assert!(!registry.paused, ESystemPaused);

        let current_time = tx_context::epoch_timestamp_ms(ctx);
        
        let contract_info = ContractInfo {
            name,
            package_address,
            registry_address,
            version: 1,
            active: true,
            deployed_at: current_time,
            updated_at: current_time,
        };

        // Add or update contract
        if (table::contains(&registry.contracts, name)) {
            let old_info = table::remove(&mut registry.contracts, name);
            
            // Emit update event
            event::emit(ContractUpdated {
                contract_name: name,
                old_package_address: old_info.package_address,
                new_package_address: package_address,
                old_registry_address: old_info.registry_address,
                new_registry_address: registry_address,
                old_version: old_info.version,
                new_version: contract_info.version,
                admin: sender,
            });
        } else {
            registry.contract_count = registry.contract_count + 1;
            
            // Emit registration event
            event::emit(ContractRegistered {
                contract_name: name,
                package_address,
                registry_address,
                version: contract_info.version,
                admin: sender,
            });
        };

        table::add(&mut registry.contracts, name, contract_info);
    }

    // Update an existing contract (for upgrades)
    public fun update_contract(
        registry: &mut FireRegistry,
        name: String,
        new_package_address: address,
        mut new_registry_address: Option<address>, // Optional - registry might stay the same
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAdmin);
        assert!(!registry.paused, ESystemPaused);
        assert!(table::contains(&registry.contracts, name), EContractNotFound);

        let contract_info = table::borrow_mut(&mut registry.contracts, name);
        let old_package = contract_info.package_address;
        let old_registry = contract_info.registry_address;
        let old_version = contract_info.version;

        // Update contract info
        contract_info.package_address = new_package_address;
        if (option::is_some(&new_registry_address)) {
            contract_info.registry_address = option::extract(&mut new_registry_address);
        };
        contract_info.version = contract_info.version + 1;
        contract_info.updated_at = tx_context::epoch_timestamp_ms(ctx);

        // Emit update event
        event::emit(ContractUpdated {
            contract_name: name,
            old_package_address: old_package,
            new_package_address: new_package_address,
            old_registry_address: old_registry,
            new_registry_address: contract_info.registry_address,
            old_version,
            new_version: contract_info.version,
            admin: sender,
        });
    }

    // Get contract package address
    public fun get_package_address(registry: &FireRegistry, name: String): address {
        assert!(table::contains(&registry.contracts, name), EContractNotFound);
        let contract_info = table::borrow(&registry.contracts, name);
        assert!(contract_info.active, EContractNotFound);
        contract_info.package_address
    }

    // Get contract registry address (shared object)
    public fun get_registry_address(registry: &FireRegistry, name: String): address {
        assert!(table::contains(&registry.contracts, name), EContractNotFound);
        let contract_info = table::borrow(&registry.contracts, name);
        assert!(contract_info.active, EContractNotFound);
        contract_info.registry_address
    }

    // Get contract address (returns registry address as ID for shared objects)
    public fun get_contract_address(registry: &FireRegistry, name: String): Option<ID> {
        if (table::contains(&registry.contracts, name)) {
            let contract_info = table::borrow(&registry.contracts, name);
            if (contract_info.active) {
                option::some(object::id_from_address(contract_info.registry_address))
            } else {
                option::none()
            }
        } else {
            option::none()
        }
    }

    // Check if an address is a registered contract
    public fun is_contract_registered(registry: &FireRegistry, contract_address: address): bool {
        // Iterate through all contracts to see if any have this address
        // Note: This is a simple implementation. For better performance, we could maintain
        // a reverse lookup table from address to contract name

        // For now, check common contract addresses
        let xft_contract_opt = get_contract_address(registry, string::utf8(b"xft"));
        if (option::is_some(&xft_contract_opt)) {
            let xft_contract_id = option::extract(&mut xft_contract_opt);
            if (object::id_to_address(&xft_contract_id) == contract_address) {
                return true
            };
        };

        let label_contract_opt = get_contract_address(registry, string::utf8(b"label"));
        if (option::is_some(&label_contract_opt)) {
            let label_contract_id = option::extract(&mut label_contract_opt);
            if (object::id_to_address(&label_contract_id) == contract_address) {
                return true
            };
        };

        let marketplace_contract_opt = get_contract_address(registry, string::utf8(b"marketplace"));
        if (option::is_some(&marketplace_contract_opt)) {
            let marketplace_contract_id = option::extract(&mut marketplace_contract_opt);
            if (object::id_to_address(&marketplace_contract_id) == contract_address) {
                return true
            };
        };

        false
    }

    // Get full contract info
    public fun get_contract_info(registry: &FireRegistry, name: String): (address, address, u64, bool) {
        assert!(table::contains(&registry.contracts, name), EContractNotFound);
        let contract_info = table::borrow(&registry.contracts, name);
        (
            contract_info.package_address,
            contract_info.registry_address,
            contract_info.version,
            contract_info.active
        )
    }

    // Convenience functions for specific contracts
    public fun get_operator_addresses(registry: &FireRegistry): (address, address) {
        let name = string::utf8(CONTRACT_OPERATOR);
        (get_package_address(registry, name), get_registry_address(registry, name))
    }

    public fun get_label_addresses(registry: &FireRegistry): (address, address) {
        let name = string::utf8(CONTRACT_LABEL);
        (get_package_address(registry, name), get_registry_address(registry, name))
    }

    public fun get_nft_addresses(registry: &FireRegistry): (address, address) {
        let name = string::utf8(CONTRACT_NFT);
        (get_package_address(registry, name), get_registry_address(registry, name))
    }

    public fun get_bank_addresses(registry: &FireRegistry): (address, address) {
        let name = string::utf8(CONTRACT_BANK);
        (get_package_address(registry, name), get_registry_address(registry, name))
    }

    public fun get_marketplace_addresses(registry: &FireRegistry): (address, address) {
        let name = string::utf8(CONTRACT_MARKETPLACE);
        (get_package_address(registry, name), get_registry_address(registry, name))
    }

    public fun get_token_addresses(registry: &FireRegistry): (address, address) {
        let name = string::utf8(CONTRACT_TOKEN);
        (get_package_address(registry, name), get_registry_address(registry, name))
    }

    public fun get_pool_addresses(registry: &FireRegistry): (address, address) {
        let name = string::utf8(CONTRACT_POOL);
        (get_package_address(registry, name), get_registry_address(registry, name))
    }

    // Admin functions
    public fun change_admin(
        registry: &mut FireRegistry,
        new_admin: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAdmin);

        let old_admin = registry.admin;
        registry.admin = new_admin;

        event::emit(AdminChanged {
            old_admin,
            new_admin,
        });
    }

    public fun pause_system(
        registry: &mut FireRegistry,
        reason: String,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAdmin);

        registry.paused = true;

        event::emit(SystemPaused {
            admin: sender,
            reason,
        });
    }

    public fun unpause_system(
        registry: &mut FireRegistry,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAdmin);

        registry.paused = false;

        event::emit(SystemUnpaused {
            admin: sender,
        });
    }

    // View functions
    public fun admin(registry: &FireRegistry): address {
        registry.admin
    }

    public fun is_paused(registry: &FireRegistry): bool {
        registry.paused
    }

    public fun contract_count(registry: &FireRegistry): u64 {
        registry.contract_count
    }

    public fun version(registry: &FireRegistry): u64 {
        registry.version
    }

    public fun contract_exists(registry: &FireRegistry, name: String): bool {
        table::contains(&registry.contracts, name)
    }

    // ===== External Registry Management =====

    /// Set global registry ID (admin only)
    public fun set_global_registry_id(
        registry: &mut FireRegistry,
        global_registry_id: ID,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAdmin);
        registry.global_registry_id = option::some(global_registry_id);
    }

    /// Get global registry ID
    public fun get_global_registry_id(registry: &FireRegistry): Option<ID> {
        registry.global_registry_id
    }

    /// Set platform label ID (admin only)
    public fun set_platform_label_id(
        registry: &mut FireRegistry,
        platform_label_id: ID,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAdmin);
        registry.platform_label_id = option::some(platform_label_id);
    }

    /// Get platform label ID for fee routing
    public fun get_platform_label_id(registry: &FireRegistry): Option<ID> {
        registry.platform_label_id
    }



    // ===== Fee Configuration =====

    /// Get label fee per year
    public fun get_label_fee_per_year(registry: &FireRegistry): u64 {
        registry.label_fee_per_year
    }

    /// Get marketplace fee in basis points
    public fun get_marketplace_fee_bps(registry: &FireRegistry): u64 {
        registry.marketplace_fee_bps
    }

    /// Update label fee per year (admin only)
    public fun update_label_fee_per_year(
        registry: &mut FireRegistry,
        new_fee: u64,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAdmin);
        registry.label_fee_per_year = new_fee;
    }

    /// Update marketplace fee (admin only)
    public fun update_marketplace_fee_bps(
        registry: &mut FireRegistry,
        new_fee_bps: u64,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAdmin);
        registry.marketplace_fee_bps = new_fee_bps;
    }
}
