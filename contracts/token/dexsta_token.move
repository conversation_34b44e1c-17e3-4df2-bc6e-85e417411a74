module maji::dexsta_token {
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::coin::{Self, Coin, TreasuryCap, CoinMetadata};
    use sui::balance::{Self, Balance};
    use sui::sui::SUI;
    use sui::table::{Self, Table};
    use sui::event;
    use sui::url::{Self, Url};
    use std::string::{Self, String};
    use std::option::{Self, Option};
    use std::vector;

    // Error codes
    const ENotAuthorized: u64 = 0;
    const EInvalidParameter: u64 = 1;
    const EInsufficientBalance: u64 = 3;
    const ETokenAlreadyExists: u64 = 5;
    const EZeroLiquidity: u64 = 6;
    const ESlippageTooHigh: u64 = 7;
    const ETokenMigrated: u64 = 8;
    const ENotOwner: u64 = 9;

    // Constants
    const MINT_FEE: u64 = 100000000; // 0.1 SUI
    const INITIAL_REWARD_GOAL: u64 = 5;
    const BONDING_CURVE_GOAL: u64 = 100000000; // 0.1 SUI migration goal
    const MAX_TRADE_SIZE_SUI: u64 = 10000000000; // 10 SUI max per trade
    const MAX_TRADE_PERCENTAGE: u64 = 1000; // 10% of pool max per trade
    const DEFAULT_CURVE_AGGRESSIVENESS: u64 = 1000; // 1.0x
    const DEFAULT_VIRTUAL_LIQUIDITY_BUFFER: u64 = 1000000; // 0.001 SUI

    /// One-time witness for the Dexsta platform registry
    public struct DEXSTA_TOKEN has drop {}

    /// Template-based token creation system
    /// Each token gets its own module generated from template.move
    /// This enables unlimited wallet-recognizable tokens

    // Token pool data for trading - Each token has unique type from template
    public struct TokenPool has key, store {
        id: UID,
        // Token identification
        module_address: address, // Address where token module is deployed
        module_name: String, // Name of the deployed token module
        token_type: String, // Full type string (e.g., "0x123::dogecoin::DOGECOIN")
        treasury_cap_id: ID, // ID of the TreasuryCap<T> for this specific token
        metadata_id: ID, // ID of the CoinMetadata<T> for this specific token
        creator: address,
        owner: address,
        total_supply: u64,
        // Social links (additional metadata not in CoinMetadata)
        website: String,
        twitter: String,
        telegram: String,
        tiktok: String,
        // Virtual pool data for bonding curve
        token_reserve: u64,
        sui_reserve: Balance<SUI>,
        // Fee configuration
        buy_fee_bps: u16,
        sell_fee_bps: u16,
        fee_payout_address: address,
        // Trading reward data
        trade_count: u64,
        reward_goal: u64,
        last_reward_update: u64,
        reward_pot: Balance<SUI>,
        // Migration data
        bonding_curve_goal: u64,
        has_migrated: bool,
        first_goal_hit_post_migration: bool,
        // Private pool NFT gating
        required_nft_id: Option<u64>,
        // Label linking
        linked_label_id: Option<u64>,
        // Max wallet limit
        max_wallet_percentage: u16,
    }

    // Token registry to keep track of all token pools
    public struct TokenRegistry has key {
        id: UID,
        token_pools: Table<String, address>, // symbol -> token_pool address
        platform_admin: address,
        // Bonding curve parameters (admin-controlled)
        curve_aggressiveness: u64,
        virtual_liquidity_buffer: u64,
        // Fire Registry for contract validation
        fire_registry_address: Option<address>,
        // Token creation counter for unique identification
        token_counter: u64,
    }

    // Events
    public struct TokenCreated has copy, drop {
        name: String,
        symbol: String,
        description: String,
        creator: address,
        total_supply: u64,
        decimals: u8,
        timestamp: u64,
        token_pool_address: address,
        treasury_cap_id: ID,
        metadata_id: ID,
        module_address: address,
        module_name: String,
        token_type: String,
    }

    public struct TokenModuleDeploymentRequested has copy, drop {
        creator: address,
        token_name: String,
        token_symbol: String,
        module_name: String,
        template_data: String, // JSON with all template replacement data
        initial_purchase_amount: u64, // SUI amount for initial token minting
        timestamp: u64,
    }

    public struct TokenSwapped has copy, drop {
        token_pool_address: address,
        token_amount: u64,
        sui_amount: u64,
        is_buy: bool,
        trader: address,
        timestamp: u64,
        price_scaled: u64,
        virtual_pool_sui_balance: u64,
        virtual_pool_token_balance: u64,
    }

    // Initialize token registry
    fun init(_witness: DEXSTA_TOKEN, ctx: &mut TxContext) {
        let sender = tx_context::sender(ctx);

        let token_registry = TokenRegistry {
            id: object::new(ctx),
            token_pools: table::new(ctx),
            platform_admin: sender,
            curve_aggressiveness: DEFAULT_CURVE_AGGRESSIVENESS,
            virtual_liquidity_buffer: DEFAULT_VIRTUAL_LIQUIDITY_BUFFER,
            fire_registry_address: option::none(),
            token_counter: 0,
        };

        transfer::share_object(token_registry);
    }

    // Admin function to set Fire Registry address
    public entry fun set_fire_registry_address(
        registry: &mut TokenRegistry,
        fire_registry_address: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.platform_admin, ENotAuthorized);
        registry.fire_registry_address = option::some(fire_registry_address);
    }

    // UNLIMITED TOKEN CREATION using template-based module generation
    // Each token gets its own module with unique type for wallet recognition
    // Step 1: Request token creation (triggers frontend module deployment)

    public entry fun request_token_creation(
        registry: &mut TokenRegistry,
        name: vector<u8>,
        symbol: vector<u8>,
        description: vector<u8>,
        icon_url: vector<u8>,
        website: vector<u8>,
        twitter: vector<u8>,
        telegram: vector<u8>,
        tiktok: vector<u8>,
        total_supply: u64,
        decimals: u8,
        initial_buy_fee_bps: u16,
        initial_sell_fee_bps: u16,
        max_wallet_percentage: u16,
        // Optional validation parameters (0 = not used)
        _operator_id: u64,
        link_to_label: u64,
        xft_id: u64,
        payment: Coin<SUI>,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Convert bytes to strings
        let name_str = string::utf8(name);
        let symbol_str = string::utf8(symbol);
        let description_str = string::utf8(description);
        let website_str = string::utf8(website);
        let twitter_str = string::utf8(twitter);
        let telegram_str = string::utf8(telegram);
        let tiktok_str = string::utf8(tiktok);

        // Check if token with this symbol already exists
        assert!(!table::contains(&registry.token_pools, symbol_str), ETokenAlreadyExists);

        // Take mint fee and handle initial purchase
        let payment_value = coin::value(&payment);
        assert!(payment_value >= MINT_FEE, EInsufficientBalance);

        // Calculate initial purchase amount (everything above mint fee)
        let initial_purchase_amount = payment_value - MINT_FEE;

        // Transfer entire payment to platform admin (covers deployment costs)
        // Initial purchase amount is tracked in event for later token minting
        transfer::public_transfer(payment, registry.platform_admin);

        // Generate module name (sanitized token name)
        let mut module_name = name_str;
        string::append(&mut module_name, string::utf8(b"_token"));

        // Create template data for frontend deployment
        let mut template_data = string::utf8(b"{");
        string::append(&mut template_data, string::utf8(b"\"name\":\""));
        string::append(&mut template_data, name_str);
        string::append(&mut template_data, string::utf8(b"\",\"symbol\":\""));
        string::append(&mut template_data, symbol_str);
        string::append(&mut template_data, string::utf8(b"\",\"description\":\""));
        string::append(&mut template_data, description_str);
        string::append(&mut template_data, string::utf8(b"\",\"decimals\":"));
        // Note: Would need to convert decimals to string in real implementation
        string::append(&mut template_data, string::utf8(b"9"));
        string::append(&mut template_data, string::utf8(b",\"total_supply\":"));
        // Note: Would need to convert total_supply to string in real implementation
        string::append(&mut template_data, string::utf8(b"1000000"));
        string::append(&mut template_data, string::utf8(b"}"));

        // Emit deployment request event for frontend to handle
        event::emit(TokenModuleDeploymentRequested {
            creator: sender,
            token_name: name_str,
            token_symbol: symbol_str,
            module_name,
            template_data,
            initial_purchase_amount,
            timestamp: tx_context::epoch(ctx),
        });

        // Note: Token pool creation happens in complete_token_creation()
        // after frontend deploys the module and calls the init function
    }

    // Step 2: Complete token creation after module deployment
    // Called by frontend after deploying token module and calling its init function
    public entry fun complete_token_creation(
        registry: &mut TokenRegistry,
        token_symbol: String,
        module_address: address,
        module_name: String,
        token_type: String,
        treasury_cap_id: ID,
        metadata_id: ID,
        total_supply: u64,
        website: String,
        twitter: String,
        telegram: String,
        tiktok: String,
        initial_buy_fee_bps: u16,
        initial_sell_fee_bps: u16,
        max_wallet_percentage: u16,
        link_to_label: u64,
        xft_id: u64,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Verify token doesn't already exist
        assert!(!table::contains(&registry.token_pools, token_symbol), ETokenAlreadyExists);

        // Handle optional parameters
        let linked_label_option = if (link_to_label == 0) {
            option::none<u64>()
        } else {
            option::some(link_to_label)
        };

        let required_nft_option = if (xft_id == 0) {
            option::none<u64>()
        } else {
            option::some(xft_id)
        };

        // Create token pool with real token data
        let mut token_pool = TokenPool {
            id: object::new(ctx),
            module_address,
            module_name,
            token_type,
            treasury_cap_id,
            metadata_id,
            creator: sender,
            owner: sender,
            total_supply,
            website,
            twitter,
            telegram,
            tiktok,
            token_reserve: total_supply,
            sui_reserve: balance::zero<SUI>(),
            buy_fee_bps: initial_buy_fee_bps,
            sell_fee_bps: initial_sell_fee_bps,
            fee_payout_address: sender,
            trade_count: 0,
            reward_goal: INITIAL_REWARD_GOAL,
            last_reward_update: tx_context::epoch_timestamp_ms(ctx),
            reward_pot: balance::zero<SUI>(),
            bonding_curve_goal: BONDING_CURVE_GOAL,
            has_migrated: false,
            first_goal_hit_post_migration: false,
            required_nft_id: required_nft_option,
            linked_label_id: linked_label_option,
            max_wallet_percentage,
        };

        // Add token pool to registry
        let token_pool_address = object::id_address(&token_pool);
        table::add(&mut registry.token_pools, token_symbol, token_pool_address);

        // Increment token counter
        registry.token_counter = registry.token_counter + 1;

        // Share token pool object
        transfer::share_object(token_pool);

        // Emit completion event
        event::emit(TokenCreated {
            name: string::utf8(b""), // Will be filled by frontend
            symbol: token_symbol,
            description: string::utf8(b""), // Will be filled by frontend
            creator: sender,
            total_supply,
            decimals: 9, // Default, will be from template
            timestamp: tx_context::epoch(ctx),
            token_pool_address,
            treasury_cap_id,
            metadata_id,
            module_address,
            module_name,
            token_type,
        });
    }

    // Mint initial tokens to creator after token deployment
    public entry fun mint_initial_tokens<T>(
        token_pool: &mut TokenPool,
        treasury_cap: &mut TreasuryCap<T>,
        initial_sui_amount: u64,
        ctx: &mut TxContext
    ) {
        let creator = token_pool.creator;
        let sender = tx_context::sender(ctx);

        // Only creator or contract deployer can mint initial tokens
        assert!(sender == creator, EInvalidParameter);

        // Calculate initial tokens based on SUI amount (1 SUI = 100 tokens at start)
        let initial_tokens = (initial_sui_amount * 100) / 1_000_000_000;

        // Ensure we don't exceed available tokens
        assert!(initial_tokens <= token_pool.token_reserve, EInsufficientBalance);

        // Update token reserve
        token_pool.token_reserve = token_pool.token_reserve - initial_tokens;

        // Mint and transfer tokens to creator
        let token_coin = coin::mint(treasury_cap, initial_tokens, ctx);
        transfer::public_transfer(token_coin, creator);

        // Emit event for initial token distribution
        event::emit(TokenSwapped {
            token_pool_address: object::id_address(token_pool),
            token_amount: initial_tokens,
            sui_amount: initial_sui_amount,
            is_buy: true,
            trader: creator,
            timestamp: tx_context::epoch(ctx),
            price_scaled: if (initial_tokens > 0) { (initial_sui_amount * 1_000_000) / initial_tokens } else { 0 },
            virtual_pool_sui_balance: balance::value(&token_pool.sui_reserve),
            virtual_pool_token_balance: token_pool.token_reserve,
        });
    }

    // Calculate tokens out using bonding curve
    fun calculate_tokens_out(
        sui_in: u64,
        current_sui_reserve: u64,
        current_token_reserve: u64,
        curve_aggressiveness: u64,
        virtual_liquidity_buffer: u64
    ): u64 {
        // Add virtual liquidity to reduce price swings
        let virtual_sui_reserve = current_sui_reserve + virtual_liquidity_buffer;
        let virtual_token_reserve = current_token_reserve + ((virtual_liquidity_buffer * 100) / 1_000_000_000);

        // Initial pricing for new tokens
        if (current_sui_reserve == 0) {
            return (sui_in * 100) / 1_000_000_000 // 1 SUI = 100 tokens
        };

        // Apply curve aggressiveness
        let adjusted_sui_in = (sui_in * curve_aggressiveness) / 1000;

        // Calculate using constant product formula
        let k = (virtual_sui_reserve as u128) * (virtual_token_reserve as u128);
        let new_virtual_sui_reserve = virtual_sui_reserve + adjusted_sui_in;
        let new_virtual_token_reserve = k / (new_virtual_sui_reserve as u128);
        let tokens_out = virtual_token_reserve - (new_virtual_token_reserve as u64);

        // Ensure we don't exceed available tokens
        if (tokens_out > current_token_reserve) {
            current_token_reserve
        } else {
            tokens_out
        }
    }

    // Calculate SUI out using bonding curve
    fun calculate_sui_out(
        tokens_in: u64,
        current_sui_reserve: u64,
        current_token_reserve: u64,
        curve_aggressiveness: u64,
        virtual_liquidity_buffer: u64
    ): u64 {
        // Add virtual liquidity
        let virtual_sui_reserve = current_sui_reserve + virtual_liquidity_buffer;
        let virtual_token_reserve = current_token_reserve + ((virtual_liquidity_buffer * 100) / 1_000_000_000);

        // Avoid division by zero
        if (virtual_sui_reserve == 0 || virtual_token_reserve == 0) {
            return 0
        };

        // Apply curve aggressiveness
        let adjusted_tokens_in = (tokens_in * curve_aggressiveness) / 1000;

        // Calculate using constant product formula
        let k = (virtual_sui_reserve as u128) * (virtual_token_reserve as u128);
        let new_virtual_token_reserve = virtual_token_reserve + adjusted_tokens_in;
        let new_virtual_sui_reserve = k / (new_virtual_token_reserve as u128);
        let sui_out = virtual_sui_reserve - (new_virtual_sui_reserve as u64);

        // Ensure we don't exceed available SUI
        if (sui_out > current_sui_reserve) {
            current_sui_reserve
        } else {
            sui_out
        }
    }

    // Buy tokens with SUI - Production version with real token minting
    public entry fun buy_tokens<T>(
        registry: &TokenRegistry,
        token_pool: &mut TokenPool,
        treasury_cap: &mut TreasuryCap<T>,
        sui_payment: Coin<SUI>,
        min_tokens_out: u64,
        ctx: &mut TxContext
    ) {
        assert!(!token_pool.has_migrated, ETokenMigrated);

        let sui_in = coin::value(&sui_payment);
        assert!(sui_in > 0, EInvalidParameter);
        assert!(sui_in <= MAX_TRADE_SIZE_SUI, EInvalidParameter);

        let current_sui_reserve = balance::value(&token_pool.sui_reserve);
        let current_token_reserve = token_pool.token_reserve;

        // Calculate tokens to give
        let tokens_out = calculate_tokens_out(
            sui_in,
            current_sui_reserve,
            current_token_reserve,
            registry.curve_aggressiveness,
            registry.virtual_liquidity_buffer
        );

        assert!(tokens_out >= min_tokens_out, ESlippageTooHigh);
        assert!(tokens_out <= current_token_reserve, EInsufficientBalance);

        // Update reserves
        token_pool.token_reserve = token_pool.token_reserve - tokens_out;
        balance::join(&mut token_pool.sui_reserve, coin::into_balance(sui_payment));

        // Update trading stats
        token_pool.trade_count = token_pool.trade_count + 1;

        // PRODUCTION: Mint and transfer actual tokens to buyer
        let token_coin = coin::mint(treasury_cap, tokens_out, ctx);
        transfer::public_transfer(token_coin, tx_context::sender(ctx));

        // Emit swap event
        event::emit(TokenSwapped {
            token_pool_address: object::id_address(token_pool),
            token_amount: tokens_out,
            sui_amount: sui_in,
            is_buy: true,
            trader: tx_context::sender(ctx),
            timestamp: tx_context::epoch(ctx),
            price_scaled: if (tokens_out > 0) { (sui_in * 1_000_000) / tokens_out } else { 0 },
            virtual_pool_sui_balance: balance::value(&token_pool.sui_reserve),
            virtual_pool_token_balance: token_pool.token_reserve,
        });
    }

    // Get token pool info
    public fun get_token_pool_info(token_pool: &TokenPool): (
        address, // module_address
        String, // module_name
        String, // token_type
        address, // creator
        u64, // total_supply
        u64, // token_reserve
        u64, // sui_reserve
        u16, // buy_fee_bps
        u16, // sell_fee_bps
        bool // has_migrated
    ) {
        (
            token_pool.module_address,
            token_pool.module_name,
            token_pool.token_type,
            token_pool.creator,
            token_pool.total_supply,
            token_pool.token_reserve,
            balance::value(&token_pool.sui_reserve),
            token_pool.buy_fee_bps,
            token_pool.sell_fee_bps,
            token_pool.has_migrated
        )
    }

    // Get template data for frontend module generation
    public fun generate_template_data(
        name: String,
        symbol: String,
        description: String,
        icon_url: String,
        decimals: u8,
        creator_address: address
    ): String {
        // This would generate the template replacement data
        // For now, return a simple JSON string
        let mut template = string::utf8(b"{\"token_module_address\":\"");
        string::append(&mut template, string::utf8(b"0x")); // Would use actual address
        string::append(&mut template, string::utf8(b"\",\"token_module_name\":\""));
        string::append(&mut template, name);
        string::append(&mut template, string::utf8(b"_token"));
        string::append(&mut template, string::utf8(b"\",\"token_struct_name\":\""));
        string::append(&mut template, symbol);
        string::append(&mut template, string::utf8(b"\"}"));
        template
    }
}
