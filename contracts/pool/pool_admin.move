module maji::pool_admin {
    use sui::object::{Self, UID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::coin::{Self, Coin};
    use sui::sui::SUI;
    use sui::table::{Self, Table};
    use sui::event;
    use std::string::{Self, String};
    use std::vector;

    // Error codes
    const ENotAuthorized: u64 = 0;
    const EInvalidParameter: u64 = 1;
    const EAdminListFull: u64 = 2;
    const EPlatformPaused: u64 = 3;

    // Platform settings object that stores all configuration
    public struct PlatformSettings has key {
        id: UID,
        super_admin: address,
        admins: vector<address>,
        platform_fee_address: address,
        platform_fee_bps: u16,
        lp_fee_bps: u16,
        reward_fee_bps: u16, // Percentage of creator fee that goes to reward pot
        existing_token_import_fee: u64,
        min_reward_trade_amount: u64,
        initial_reward_goal: u64,
        reward_goal_increase: u64,
        reward_goal_decrease_amount: u64,
        reward_goal_decrease_threshold: u64,
        reward_goal_proximity_threshold: u64,
        is_paused: bool,
    }

    // Capability object that grants admin privileges
    public struct AdminCap has key {
        id: UID,
        admin: address,
    }

    // Events
    public struct PlatformConstantsUpdated has copy, drop {
        initial_reward_goal: u64,
        platform_fee_bps: u16,
        lp_fee_bps: u16,
        reward_fee_bps: u16,
        existing_token_import_fee: u64,
        min_reward_trade_amount: u64,
        reward_goal_increase: u64,
        reward_goal_decrease_amount: u64,
        reward_goal_decrease_threshold: u64,
        reward_goal_proximity_threshold: u64,
        admin: address,
        timestamp: u64,
    }

    // Initialize platform settings
    public fun initialize_platform_settings(
        platform_fee_address: address,
        platform_fee_bps: u16,
        lp_fee_bps: u16,
        reward_fee_bps: u16,
        existing_token_import_fee: u64,
        min_reward_trade_amount: u64,
        initial_reward_goal: u64,
        reward_goal_increase: u64,
        reward_goal_decrease_amount: u64,
        reward_goal_decrease_threshold: u64,
        reward_goal_proximity_threshold: u64,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Create platform settings object
        let platform_settings = PlatformSettings {
            id: object::new(ctx),
            super_admin: sender,
            admins: vector::singleton(sender), // Super admin is also an admin
            platform_fee_address,
            platform_fee_bps,
            lp_fee_bps,
            reward_fee_bps,
            existing_token_import_fee,
            min_reward_trade_amount,
            initial_reward_goal,
            reward_goal_increase,
            reward_goal_decrease_amount,
            reward_goal_decrease_threshold,
            reward_goal_proximity_threshold,
            is_paused: false,
        };

        // Create admin capability for the creator
        let admin_cap = AdminCap {
            id: object::new(ctx),
            admin: sender,
        };

        // Emit event
        event::emit(PlatformConstantsUpdated {
            initial_reward_goal,
            platform_fee_bps,
            lp_fee_bps,
            reward_fee_bps,
            existing_token_import_fee,
            min_reward_trade_amount,
            reward_goal_increase,
            reward_goal_decrease_amount,
            reward_goal_decrease_threshold,
            reward_goal_proximity_threshold,
            admin: sender,
            timestamp: tx_context::epoch(ctx),
        });

        // Transfer objects to the sender
        transfer::share_object(platform_settings);
        transfer::transfer(admin_cap, sender);
    }

    // Update platform settings
    public entry fun update_platform_constants(
        platform_settings: &mut PlatformSettings,
        _admin_cap: &AdminCap,
        platform_fee_address: address,
        platform_fee_bps: u16,
        lp_fee_bps: u16,
        reward_fee_bps: u16,
        existing_token_import_fee: u64,
        min_reward_trade_amount: u64,
        initial_reward_goal: u64,
        reward_goal_increase: u64,
        reward_goal_decrease_amount: u64,
        reward_goal_decrease_threshold: u64,
        reward_goal_proximity_threshold: u64,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if the sender is authorized
        assert!(
            sender == platform_settings.super_admin || vector::contains(&platform_settings.admins, &sender),
            ENotAuthorized
        );

        // Check if platform is paused
        assert!(!platform_settings.is_paused, EPlatformPaused);

        // Update platform settings
        platform_settings.platform_fee_address = platform_fee_address;
        platform_settings.platform_fee_bps = platform_fee_bps;
        platform_settings.lp_fee_bps = lp_fee_bps;
        platform_settings.reward_fee_bps = reward_fee_bps;
        platform_settings.existing_token_import_fee = existing_token_import_fee;
        platform_settings.min_reward_trade_amount = min_reward_trade_amount;
        platform_settings.initial_reward_goal = initial_reward_goal;
        platform_settings.reward_goal_increase = reward_goal_increase;
        platform_settings.reward_goal_decrease_amount = reward_goal_decrease_amount;
        platform_settings.reward_goal_decrease_threshold = reward_goal_decrease_threshold;
        platform_settings.reward_goal_proximity_threshold = reward_goal_proximity_threshold;

        // Emit event
        event::emit(PlatformConstantsUpdated {
            initial_reward_goal,
            platform_fee_bps,
            lp_fee_bps,
            reward_fee_bps,
            existing_token_import_fee,
            min_reward_trade_amount,
            reward_goal_increase,
            reward_goal_decrease_amount,
            reward_goal_decrease_threshold,
            reward_goal_proximity_threshold,
            admin: sender,
            timestamp: tx_context::epoch(ctx),
        });
    }

    // Add an admin
    public entry fun add_admin(
        platform_settings: &mut PlatformSettings,
        _admin_cap: &AdminCap,
        new_admin: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Only super admin can add admins
        assert!(sender == platform_settings.super_admin, ENotAuthorized);

        // Check if admin already exists
        if (!vector::contains(&platform_settings.admins, &new_admin)) {
            // Check if we have room for more admins
            let max_admins = 20; // Set a reasonable limit
            assert!(vector::length(&platform_settings.admins) < max_admins, EAdminListFull);

            // Add new admin
            vector::push_back(&mut platform_settings.admins, new_admin);

            // Create admin capability for the new admin
            let admin_cap = AdminCap {
                id: object::new(ctx),
                admin: new_admin,
            };

            // Transfer admin capability to the new admin
            transfer::transfer(admin_cap, new_admin);
        };
    }

    // Remove an admin
    public entry fun remove_admin(
        platform_settings: &mut PlatformSettings,
        _admin_cap: &AdminCap,
        admin_to_remove: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Only super admin can remove admins
        assert!(sender == platform_settings.super_admin, ENotAuthorized);

        // Find and remove the admin
        let (found, index) = vector::index_of(&platform_settings.admins, &admin_to_remove);
        if (found) {
            vector::remove(&mut platform_settings.admins, index);
        };
    }

    // Transfer super admin powers to a new address
    public entry fun transfer_super_admin(
        platform_settings: &mut PlatformSettings,
        _admin_cap: &AdminCap,
        new_super_admin: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Only current super admin can transfer super admin status
        assert!(sender == platform_settings.super_admin, ENotAuthorized);

        // Update super admin
        platform_settings.super_admin = new_super_admin;

        // Create admin capability for the new super admin
        let admin_cap = AdminCap {
            id: object::new(ctx),
            admin: new_super_admin,
        };

        // Transfer admin capability to the new super admin
        transfer::transfer(admin_cap, new_super_admin);
    }

    // Toggle platform pause status
    public entry fun toggle_pause(
        platform_settings: &mut PlatformSettings,
        _admin_cap: &AdminCap,
        new_pause_state: bool,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Verify the caller is authorized (either super admin or an admin)
        assert!(
            sender == platform_settings.super_admin || vector::contains(&platform_settings.admins, &sender),
            ENotAuthorized
        );

        // Update pause state
        platform_settings.is_paused = new_pause_state;
    }

    // Getters for platform settings
    public fun get_platform_fee_address(platform_settings: &PlatformSettings): address {
        platform_settings.platform_fee_address
    }

    public fun get_platform_fee_bps(platform_settings: &PlatformSettings): u16 {
        platform_settings.platform_fee_bps
    }

    public fun get_lp_fee_bps(platform_settings: &PlatformSettings): u16 {
        platform_settings.lp_fee_bps
    }

    public fun get_reward_fee_bps(platform_settings: &PlatformSettings): u16 {
        platform_settings.reward_fee_bps
    }

    public fun get_existing_token_import_fee(platform_settings: &PlatformSettings): u64 {
        platform_settings.existing_token_import_fee
    }

    public fun get_min_reward_trade_amount(platform_settings: &PlatformSettings): u64 {
        platform_settings.min_reward_trade_amount
    }

    public fun get_initial_reward_goal(platform_settings: &PlatformSettings): u64 {
        platform_settings.initial_reward_goal
    }

    public fun get_reward_goal_increase(platform_settings: &PlatformSettings): u64 {
        platform_settings.reward_goal_increase
    }

    public fun get_reward_goal_decrease_amount(platform_settings: &PlatformSettings): u64 {
        platform_settings.reward_goal_decrease_amount
    }

    public fun get_reward_goal_decrease_threshold(platform_settings: &PlatformSettings): u64 {
        platform_settings.reward_goal_decrease_threshold
    }

    public fun get_reward_goal_proximity_threshold(platform_settings: &PlatformSettings): u64 {
        platform_settings.reward_goal_proximity_threshold
    }

    public fun is_paused(platform_settings: &PlatformSettings): bool {
        platform_settings.is_paused
    }

    public fun is_admin(platform_settings: &PlatformSettings, addr: address): bool {
        addr == platform_settings.super_admin || vector::contains(&platform_settings.admins, &addr)
    }

    public fun is_super_admin(platform_settings: &PlatformSettings, addr: address): bool {
        addr == platform_settings.super_admin
    }
}