module maji::pool {
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::coin::{Self, Coin};
    use sui::balance::{Self, Balance};
    use sui::sui::SUI;
    use sui::table::{Self, Table};
    use sui::event;
    use sui::url::{Self, Url};
    use std::string::{Self, String};
    use std::option::{Self, Option};

    // Error codes
    const ENotAuthorized: u64 = 0;
    const EInvalidParameter: u64 = 1;
    const EInsufficientBalance: u64 = 3;
    const EPoolAlreadyExists: u64 = 5;
    const EZeroLiquidity: u64 = 6;
    const ESlippageTooHigh: u64 = 7;
    const ENotOwner: u64 = 9;
    const EInsufficientLiquidity: u64 = 8;
    // Validation error codes
    const ENFTNotFound: u64 = 18;
    const ENFTNotLinkedToLabel: u64 = 19;
    const EFireRegistryNotSet: u64 = 20;

    // Constants - Conservative amounts for testing
    const POOL_CREATION_FEE: u64 = 0; // Free pool creation for testing
    const INITIAL_REWARD_GOAL: u64 = 5; // Only 5 trades to hit reward goal
    const SCALE_FACTOR: u128 = 1_000_000_000_000; // 10^12
    
    // Fee constants
    const PLATFORM_FEE_BPS: u16 = 100; // 1% platform fee
    const MIN_LIQUIDITY: u64 = 10000000; // 0.01 SUI minimum virtual liquidity

    // MEV Protection Constants
    const MAX_TRADE_SIZE_SUI: u64 = 10000000000; // 10 SUI max per trade
    const MAX_TRADE_PERCENTAGE: u64 = 1000; // 10% of pool max per trade

    // Default bonding curve parameters
    const DEFAULT_CURVE_AGGRESSIVENESS: u64 = 1000; // 1.0x (normal curve)
    const DEFAULT_VIRTUAL_LIQUIDITY_BUFFER: u64 = 10000000; // 0.01 SUI

    // Pool information
    public struct LiquidityPool has key {
        id: UID,
        token_address: address,
        token_symbol: String,
        token_balance: u64,
        sui_balance: Balance<SUI>,
        lp_token_supply: u64,
        creator: address,
        owner: address, // Current owner (can be transferred)
        // Fee configuration
        buy_fee_bps: u16,
        sell_fee_bps: u16,
        fee_payout_address: address,
        // Trading reward data
        trade_count: u64,
        reward_goal: u64,
        last_reward_update: u64,
        reward_pot: Balance<SUI>,
        first_goal_hit_post_migration: bool, // Track if first goal was hit after migration
        // Private pool NFT gating
        required_nft_id: Option<u64>,
    }

    // LP token representing liquidity provider's share
    public struct LPToken has key, store {
        id: UID,
        pool_id: address,
        token_symbol: String,
        amount: u64,
    }

    // Pool registry to keep track of all pools
    public struct PoolRegistry has key {
        id: UID,
        pools: Table<String, address>, // symbol -> pool address
        platform_admin: address, // Platform admin who can override ownership
        // Bonding curve parameters (admin-controlled)
        curve_aggressiveness: u64, // Multiplier for curve steepness (1000 = 1.0x, 2000 = 2.0x)
        virtual_liquidity_buffer: u64, // Virtual liquidity to add for price stability
        // Fire Registry for contract validation
        fire_registry_address: Option<address>, // Address of Fire Registry for cross-contract calls
    }

    // Events
    public struct PoolCreated has copy, drop {
        pool_address: address,
        token_address: address,
        token_symbol: String,
        creator: address,
        initial_token_amount: u64,
        initial_sui_amount: u64,
        timestamp: u64,
    }

    public struct TokenSwapped has copy, drop {
        token_address: address,
        pool_address: address,
        token_amount: u64,
        sui_amount: u64,
        is_buy: bool,
        trader: address,
        timestamp: u64,
        price_scaled: u64,
        pool_sui_balance: u64,
        pool_token_balance: u64,
    }

    public struct LiquidityAdded has copy, drop {
        pool_address: address,
        provider: address,
        token_amount: u64,
        sui_amount: u64,
        lp_tokens_minted: u64,
        timestamp: u64,
    }

    public struct LiquidityRemoved has copy, drop {
        pool_address: address,
        provider: address,
        token_amount: u64,
        sui_amount: u64,
        lp_tokens_burned: u64,
        timestamp: u64,
    }

    // Initialize pool registry
    fun init(ctx: &mut TxContext) {
        let sender = tx_context::sender(ctx);
        let pool_registry = PoolRegistry {
            id: object::new(ctx),
            pools: table::new(ctx),
            platform_admin: sender, // Deployer becomes platform admin
            curve_aggressiveness: DEFAULT_CURVE_AGGRESSIVENESS,
            virtual_liquidity_buffer: DEFAULT_VIRTUAL_LIQUIDITY_BUFFER,
            fire_registry_address: option::none(), // Set later by admin
        };

        transfer::share_object(pool_registry);
    }

    // Admin function to set Fire Registry address
    public entry fun set_fire_registry_address(
        registry: &mut PoolRegistry,
        fire_registry_address: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.platform_admin, ENotAuthorized);
        registry.fire_registry_address = option::some(fire_registry_address);
    }

    // Calculate tokens out using open liquidity (actual pool SUI balance affects price)
    fun calculate_tokens_out(
        sui_in: u64,
        actual_sui_balance: u64,
        current_token_reserve: u64
    ): u64 {
        // OPEN LIQUIDITY: Use actual SUI balance of pool address for pricing
        // Anyone can send SUI directly to pool → impacts price immediately

        // Add minimum virtual liquidity to reduce price swings
        let virtual_sui_reserve = actual_sui_balance + MIN_LIQUIDITY;
        let virtual_token_reserve = current_token_reserve + (MIN_LIQUIDITY * 1_000_000); // 1M tokens per SUI

        // Avoid division by zero
        if (virtual_sui_reserve == 0 || virtual_token_reserve == 0) {
            // Initial pricing: 1 SUI = 1,000,000 tokens
            return sui_in * 1_000_000
        };

        // Calculate constant k with virtual reserves
        let k = (virtual_sui_reserve as u128) * (virtual_token_reserve as u128);

        // New SUI reserve after adding input
        let new_virtual_sui_reserve = virtual_sui_reserve + sui_in;

        // Calculate new token reserve using k = x * y
        let new_virtual_token_reserve = k / (new_virtual_sui_reserve as u128);

        // Tokens out = difference in virtual reserves
        let tokens_out = virtual_token_reserve - (new_virtual_token_reserve as u64);

        // Ensure we don't exceed available tokens
        if (tokens_out > current_token_reserve) {
            current_token_reserve
        } else {
            tokens_out
        }
    }

    // Calculate SUI out using open liquidity (actual pool SUI balance affects price)
    fun calculate_sui_out(
        tokens_in: u64,
        actual_sui_balance: u64,
        current_token_reserve: u64
    ): u64 {
        // OPEN LIQUIDITY: Use actual SUI balance of pool address for pricing

        // Add minimum virtual liquidity to reduce price swings
        let virtual_sui_reserve = actual_sui_balance + MIN_LIQUIDITY;
        let virtual_token_reserve = current_token_reserve + (MIN_LIQUIDITY * 1_000_000);

        // Avoid division by zero
        if (virtual_sui_reserve == 0 || virtual_token_reserve == 0) {
            return 0
        };

        // Calculate constant k with virtual reserves
        let k = (virtual_sui_reserve as u128) * (virtual_token_reserve as u128);

        // New token reserve after adding tokens back
        let new_virtual_token_reserve = virtual_token_reserve + tokens_in;

        // Calculate new SUI reserve using k = x * y
        let new_virtual_sui_reserve = k / (new_virtual_token_reserve as u128);

        // SUI out = difference in virtual reserves
        let sui_out = virtual_sui_reserve - (new_virtual_sui_reserve as u64);

        // Ensure we don't exceed available SUI
        if (sui_out > actual_sui_balance) {
            actual_sui_balance
        } else {
            sui_out
        }
    }

    // Create a new liquidity pool
    public entry fun create_pool(
        registry: &mut PoolRegistry,
        token_symbol: vector<u8>,
        token_address: address,
        initial_token_amount: u64,
        initial_sui: Coin<SUI>,
        buy_fee_bps: u16,
        sell_fee_bps: u16,
        ctx: &mut TxContext
    ) {
        create_pool_with_nft_gating(
            registry,
            token_symbol,
            token_address,
            initial_token_amount,
            initial_sui,
            buy_fee_bps,
            sell_fee_bps,
            option::none(), // No NFT gating
            ctx
        );
    }

    // Create a new liquidity pool with NFT gating (for private pool trading)
    public entry fun create_pool_with_nft_gating(
        registry: &mut PoolRegistry,
        token_symbol: vector<u8>,
        token_address: address,
        initial_token_amount: u64,
        initial_sui: Coin<SUI>,
        buy_fee_bps: u16,
        sell_fee_bps: u16,
        required_nft_id: Option<u64>,
        ctx: &mut TxContext
    ) {
        // Convert bytes to string
        let token_symbol_str = string::utf8(token_symbol);
        
        // Check if pool with this symbol already exists
        assert!(!table::contains(&registry.pools, token_symbol_str), EPoolAlreadyExists);
        
        // Validate parameters
        assert!(initial_token_amount > 0, EInvalidParameter);
        assert!(coin::value(&initial_sui) > 0, EInvalidParameter);
        assert!(buy_fee_bps <= 500, EInvalidParameter); // Max 5%
        assert!(sell_fee_bps <= 500, EInvalidParameter); // Max 5%
        
        let sender = tx_context::sender(ctx);
        let initial_sui_amount = coin::value(&initial_sui);
        
        // Calculate initial LP token supply (geometric mean)
        let lp_token_supply = ((initial_token_amount as u128) * (initial_sui_amount as u128));
        let lp_token_supply = ((lp_token_supply as u256) as u64); // Simplified for now
        
        // Convert SUI coin to balance
        let sui_balance = coin::into_balance(initial_sui);
        
        // Create the pool
        let pool = LiquidityPool {
            id: object::new(ctx),
            token_address,
            token_symbol: token_symbol_str,
            token_balance: initial_token_amount,
            sui_balance,
            lp_token_supply,
            creator: sender,
            owner: sender, // Initially, creator is the owner
            // Fee configuration
            buy_fee_bps,
            sell_fee_bps,
            fee_payout_address: sender, // Initially set to creator
            // Trading reward data
            trade_count: 0,
            reward_goal: INITIAL_REWARD_GOAL,
            last_reward_update: tx_context::epoch_timestamp_ms(ctx),
            reward_pot: balance::zero<SUI>(),
            first_goal_hit_post_migration: false,
            // Private pool NFT gating
            required_nft_id: required_nft_id,
        };
        
        let pool_address = object::id_address(&pool);
        
        // Create LP token for the creator
        let lp_token = LPToken {
            id: object::new(ctx),
            pool_id: pool_address,
            token_symbol: token_symbol_str,
            amount: lp_token_supply,
        };
        
        // Add pool to registry
        table::add(&mut registry.pools, token_symbol_str, pool_address);
        
        // Transfer LP token to creator
        transfer::transfer(lp_token, sender);
        
        // Share pool object
        transfer::share_object(pool);
        
        // Emit event
        event::emit(PoolCreated {
            pool_address,
            token_address,
            token_symbol: token_symbol_str,
            creator: sender,
            initial_token_amount,
            initial_sui_amount,
            timestamp: tx_context::epoch_timestamp_ms(ctx),
        });
    }

    // NFT ownership and label linking validation for private pools
    fun validate_nft_ownership_and_linking(
        registry: &PoolRegistry,
        required_nft_id: Option<u64>,
        linked_label_id: Option<String>,
        user_address: address
    ): bool {
        // If no NFT is required, allow access (public pool)
        if (option::is_none(&required_nft_id)) {
            return true
        };

        // If Fire Registry is not set, skip validation (for testing)
        if (option::is_none(&registry.fire_registry_address)) {
            return true
        };

        // TODO: Implement actual cross-contract call to XFT contract
        // let fire_registry_addr = *option::borrow(&registry.fire_registry_address);
        // let (nft_package, nft_registry) = fire_registry::get_nft_addresses(fire_registry_addr);
        // let nft_id = *option::borrow(&required_nft_id);
        //
        // Call XFT contract functions:
        // 1. Check if NFT exists: nft::exists(nft_registry, nft_id)
        // 2. Check ownership: nft::owner(nft_registry, nft_id) == user_address
        // 3. If label is linked, check NFT is linked to same label:
        //    nft::label_global_id(nft_registry, nft_id) == label_id

        // For now, return true as placeholder
        true
    }

    // OPEN LIQUIDITY FEATURE:
    // In the full implementation, this should query the actual SUI balance of the pool address
    // Anyone can send SUI directly to the pool address → impacts price immediately
    // Sending other tokens does nothing (only SUI affects pricing)
    // This allows artists/creators to direct revenue into token liquidity
    fun get_pool_sui_balance(pool: &LiquidityPool): u64 {
        // TODO: Replace with actual address balance query for true open liquidity
        // For now, using tracked balance (same as current behavior)
        balance::value(&pool.sui_balance)
    }

    // Swap SUI for tokens (buy tokens) with fee collection and reward system
    public entry fun swap_sui_for_tokens(
        registry: &PoolRegistry, // Added for validation
        pool: &mut LiquidityPool,
        sui_payment: Coin<SUI>,
        min_tokens_out: u64,
        ctx: &mut TxContext
    ) {
        // Get swap parameters
        let sui_in = coin::value(&sui_payment);
        assert!(sui_in > 0, EInvalidParameter);

        // MEV Protection: Limit transaction size
        assert!(sui_in <= MAX_TRADE_SIZE_SUI, EInvalidParameter);

        // MEV Protection: Limit percentage of pool per trade
        let actual_sui_balance = get_pool_sui_balance(pool);
        if (actual_sui_balance > 0) {
            let trade_percentage = (sui_in * 10000) / actual_sui_balance;
            assert!(trade_percentage <= MAX_TRADE_PERCENTAGE, EInvalidParameter);
        };

        // PRIVATE POOL VALIDATION: Check if user owns required NFT and it's linked to label
        let user_address = tx_context::sender(ctx);
        assert!(
            validate_nft_ownership_and_linking(
                registry,
                pool.required_nft_id,
                option::none(), // TODO: Get linked_label_id from token contract
                user_address
            ),
            ENotAuthorized
        );

        // Calculate and collect fees
        let platform_fee = (sui_in * (PLATFORM_FEE_BPS as u64)) / 10000;
        let creator_fee = (sui_in * (pool.buy_fee_bps as u64)) / 10000;
        let reward_contribution = creator_fee / 2; // 50% of creator fee goes to reward pot
        let actual_creator_fee = creator_fee - reward_contribution;
        let sui_for_tokens = sui_in - platform_fee - creator_fee;

        // OPEN LIQUIDITY: Get actual SUI balance of pool address for pricing
        // This includes any SUI sent directly to the pool (open liquidity feature)
        let actual_sui_balance = get_pool_sui_balance(pool);
        let current_token_reserve = pool.token_balance;

        // Calculate tokens out using open liquidity bonding curve (on net amount after fees)
        let tokens_out = calculate_tokens_out(sui_for_tokens, actual_sui_balance, current_token_reserve);

        // Check slippage
        assert!(tokens_out >= min_tokens_out, ESlippageTooHigh);

        // Update reserves - when buying, tokens leave the pool
        assert!(pool.token_balance >= tokens_out, EInsufficientBalance);
        pool.token_balance = pool.token_balance - tokens_out;

        // Split the payment and distribute fees
        let mut sui_balance = coin::into_balance(sui_payment);

        // Take platform fee
        if (platform_fee > 0) {
            let platform_fee_balance = balance::split(&mut sui_balance, platform_fee);
            let platform_fee_coin = coin::from_balance(platform_fee_balance, ctx);
            // For now, send to creator (in production, send to platform address)
            transfer::public_transfer(platform_fee_coin, pool.creator);
        };

        // Take creator fee
        if (actual_creator_fee > 0) {
            let creator_fee_balance = balance::split(&mut sui_balance, actual_creator_fee);
            let creator_fee_coin = coin::from_balance(creator_fee_balance, ctx);
            transfer::public_transfer(creator_fee_coin, pool.fee_payout_address);
        };

        // Add reward contribution to pot
        if (reward_contribution > 0) {
            let reward_balance = balance::split(&mut sui_balance, reward_contribution);
            balance::join(&mut pool.reward_pot, reward_balance);
        };

        // Add remaining SUI to reserves (for bonding curve)
        balance::join(&mut pool.sui_balance, sui_balance);

        // Update trade count FIRST (this should always work)
        // For pools: assume migrated status, only buys count until first goal hit, then buys & sells count
        let should_count_trade = !pool.first_goal_hit_post_migration;
        if (should_count_trade) {
            pool.trade_count = pool.trade_count + 1;
        };

        // Check if trade goal reached and auto-distribute reward
        if (should_count_trade && pool.trade_count >= pool.reward_goal) {
            let reward_amount = balance::value(&pool.reward_pot);

            if (reward_amount > 0) {
                // Transfer reward pot to the trader who hit the goal (current sender)
                let reward_balance = balance::withdraw_all(&mut pool.reward_pot);
                let reward_coin = coin::from_balance(reward_balance, ctx);
                transfer::public_transfer(reward_coin, tx_context::sender(ctx));

                // Reset trade count and increase goal by 50%
                pool.trade_count = 0;
                pool.reward_goal = pool.reward_goal + (pool.reward_goal / 2);
                pool.last_reward_update = tx_context::epoch_timestamp_ms(ctx);

                // Mark first goal hit (now sells will also count)
                if (!pool.first_goal_hit_post_migration) {
                    pool.first_goal_hit_post_migration = true;
                };
            };
        };

        // Emit event
        event::emit(TokenSwapped {
            token_address: pool.token_address,
            pool_address: object::id_address(pool),
            token_amount: tokens_out,
            sui_amount: sui_for_tokens,
            is_buy: true,
            trader: tx_context::sender(ctx),
            timestamp: tx_context::epoch_timestamp_ms(ctx),
            price_scaled: if (tokens_out > 0) { (sui_for_tokens * 1_000_000_000) / tokens_out } else { 0 },
            pool_sui_balance: balance::value(&pool.sui_balance),
            pool_token_balance: pool.token_balance,
        });
    }

    // Swap tokens for SUI (sell tokens) with fee collection and reward system
    public entry fun swap_tokens_for_sui(
        registry: &PoolRegistry, // Added for validation
        pool: &mut LiquidityPool,
        tokens_in: u64,
        min_sui_out: u64,
        ctx: &mut TxContext
    ) {
        // Get swap parameters
        assert!(tokens_in > 0, EInvalidParameter);

        // PRIVATE POOL VALIDATION: Check if user owns required NFT and it's linked to label
        let user_address = tx_context::sender(ctx);
        assert!(
            validate_nft_ownership_and_linking(
                registry,
                pool.required_nft_id,
                option::none(), // TODO: Get linked_label_id from token contract
                user_address
            ),
            ENotAuthorized
        );

        // OPEN LIQUIDITY: Get actual SUI balance of pool address for pricing
        // This includes any SUI sent directly to the pool (open liquidity feature)
        let actual_sui_balance = get_pool_sui_balance(pool);
        let current_token_reserve = pool.token_balance;

        // Ensure there's liquidity
        assert!(actual_sui_balance > 0, EZeroLiquidity);

        // Calculate SUI out using open liquidity bonding curve
        let gross_sui_out = calculate_sui_out(tokens_in, actual_sui_balance, current_token_reserve);

        // Calculate and collect sell fees
        let platform_fee = (gross_sui_out * (PLATFORM_FEE_BPS as u64)) / 10000;
        let creator_fee = (gross_sui_out * (pool.sell_fee_bps as u64)) / 10000;
        let reward_contribution = creator_fee / 2; // 50% of creator fee goes to reward pot
        let actual_creator_fee = creator_fee - reward_contribution;
        let net_sui_out = gross_sui_out - platform_fee - creator_fee;

        // Check slippage on net amount
        assert!(net_sui_out >= min_sui_out, ESlippageTooHigh);

        // Check if we have enough SUI in reserves
        assert!(gross_sui_out <= actual_sui_balance, EInsufficientBalance);

        // Update reserves - when selling, tokens return to the pool
        pool.token_balance = pool.token_balance + tokens_in;

        // Take SUI from reserves
        let mut sui_balance = balance::split(&mut pool.sui_balance, gross_sui_out);

        // Distribute fees
        if (platform_fee > 0) {
            let platform_fee_balance = balance::split(&mut sui_balance, platform_fee);
            let platform_fee_coin = coin::from_balance(platform_fee_balance, ctx);
            // For now, send to creator (in production, send to platform address)
            transfer::public_transfer(platform_fee_coin, pool.creator);
        };

        if (actual_creator_fee > 0) {
            let creator_fee_balance = balance::split(&mut sui_balance, actual_creator_fee);
            let creator_fee_coin = coin::from_balance(creator_fee_balance, ctx);
            transfer::public_transfer(creator_fee_coin, pool.fee_payout_address);
        };

        if (reward_contribution > 0) {
            let reward_balance = balance::split(&mut sui_balance, reward_contribution);
            balance::join(&mut pool.reward_pot, reward_balance);
        };

        // Transfer remaining SUI to seller
        let net_sui_coin = coin::from_balance(sui_balance, ctx);
        transfer::public_transfer(net_sui_coin, tx_context::sender(ctx));

        // Update trade count for sells
        // Sells only count after first goal is hit
        let should_count_sell = pool.first_goal_hit_post_migration;
        if (should_count_sell) {
            pool.trade_count = pool.trade_count + 1;
        };

        // Check if trade goal reached and auto-distribute reward
        if (should_count_sell && pool.trade_count >= pool.reward_goal) {
            let reward_amount = balance::value(&pool.reward_pot);

            if (reward_amount > 0) {
                // Transfer reward pot to the trader who hit the goal (current sender)
                let reward_balance = balance::withdraw_all(&mut pool.reward_pot);
                let reward_coin = coin::from_balance(reward_balance, ctx);
                transfer::public_transfer(reward_coin, tx_context::sender(ctx));

                // Reset trade count and increase goal by 50%
                pool.trade_count = 0;
                pool.reward_goal = pool.reward_goal + (pool.reward_goal / 2);
                pool.last_reward_update = tx_context::epoch_timestamp_ms(ctx);
            };
        };

        // Emit event
        event::emit(TokenSwapped {
            token_address: pool.token_address,
            pool_address: object::id_address(pool),
            token_amount: tokens_in,
            sui_amount: net_sui_out,
            is_buy: false,
            trader: tx_context::sender(ctx),
            timestamp: tx_context::epoch_timestamp_ms(ctx),
            price_scaled: if (tokens_in > 0) { (net_sui_out * 1_000_000_000) / tokens_in } else { 0 },
            pool_sui_balance: balance::value(&pool.sui_balance),
            pool_token_balance: pool.token_balance,
        });
    }

    // Add to reward pot - anyone can contribute
    public entry fun add_to_reward_pot(
        pool: &mut LiquidityPool,
        payment: Coin<SUI>,
        ctx: &mut TxContext
    ) {
        let amount = coin::value(&payment);
        assert!(amount > 0, EInvalidParameter);

        let payment_balance = coin::into_balance(payment);
        balance::join(&mut pool.reward_pot, payment_balance);

        // Emit event for reward pot contribution
        event::emit(TokenSwapped {
            token_address: pool.token_address,
            pool_address: object::id_address(pool),
            token_amount: 0,
            sui_amount: amount,
            is_buy: true, // Use true to indicate contribution
            trader: tx_context::sender(ctx),
            timestamp: tx_context::epoch_timestamp_ms(ctx),
            price_scaled: 0,
            pool_sui_balance: balance::value(&pool.sui_balance),
            pool_token_balance: pool.token_balance,
        });
    }

    // Auto-adjust reward goal based on inactivity (call this periodically)
    public entry fun adjust_reward_goal(pool: &mut LiquidityPool, ctx: &mut TxContext) {
        let current_time = tx_context::epoch_timestamp_ms(ctx);
        let time_since_update = current_time - pool.last_reward_update;

        // If no activity for 5 minutes (300,000 ms), decrease goal
        if (time_since_update >= 300000) {
            // Calculate safe decrease: max 10% of current goal
            let max_decrease = pool.reward_goal / 10;
            let max_decrease = if (max_decrease == 0) { 1 } else { max_decrease }; // At least decrease by 1

            // Never decrease below 90% of current trade count + 1
            let min_goal = (pool.trade_count * 9) / 10 + 1;
            let new_goal = if (pool.reward_goal > max_decrease) {
                pool.reward_goal - max_decrease
            } else {
                1
            };

            // Apply minimum constraint
            let new_goal = if (new_goal < min_goal) { min_goal } else { new_goal };

            pool.reward_goal = new_goal;
            pool.last_reward_update = current_time;
        };
    }

    // ========== OWNERSHIP TRANSFER FUNCTIONS ==========

    // Transfer ownership to a new address
    // Rules: Owner can transfer, Creator can transfer ONLY if they are still the owner, Platform admin can always transfer
    public entry fun transfer_pool_ownership(
        registry: &PoolRegistry,
        pool: &mut LiquidityPool,
        new_owner: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(new_owner != @0x0, EInvalidParameter);

        // Check authorization:
        // 1. Current owner can always transfer
        // 2. Creator can transfer ONLY if they are still the owner (haven't transferred yet)
        // 3. Platform admin can always transfer (override capability)
        assert!(
            sender == pool.owner ||
            (sender == pool.creator && sender == pool.owner) ||
            sender == registry.platform_admin,
            ENotOwner
        );

        pool.owner = new_owner;
        pool.fee_payout_address = new_owner; // Update fee recipient too
    }

    // Update buy/sell fees (owner only)
    public entry fun update_pool_fees(
        pool: &mut LiquidityPool,
        new_buy_fee_bps: u16,
        new_sell_fee_bps: u16,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == pool.owner, ENotOwner);
        assert!(new_buy_fee_bps <= 500, EInvalidParameter); // Max 5%
        assert!(new_sell_fee_bps <= 500, EInvalidParameter); // Max 5%

        pool.buy_fee_bps = new_buy_fee_bps;
        pool.sell_fee_bps = new_sell_fee_bps;
    }

    // Get pool info including ownership
    public fun get_pool_info(pool: &LiquidityPool): (address, String, u64, u64, u64, address, address, u16, u16, u64, u64, u64) {
        (
            pool.token_address,
            pool.token_symbol,
            pool.token_balance,
            balance::value(&pool.sui_balance),
            pool.lp_token_supply,
            pool.creator,
            pool.owner,
            pool.buy_fee_bps,
            pool.sell_fee_bps,
            balance::value(&pool.reward_pot),
            pool.trade_count,
            pool.reward_goal
        )
    }
}
