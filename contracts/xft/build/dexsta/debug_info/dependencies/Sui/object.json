{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_af5297b292c3/crates/sui-framework/packages/sui-framework/sources/object.move", "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 114, "end": 120}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "object"], "struct_map": {"0": {"definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 1986, "end": 1988}, "type_parameters": [], "fields": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 2345, "end": 2350}]}, "1": {"definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 2882, "end": 2885}, "type_parameters": [], "fields": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 2902, "end": 2904}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 2960, "end": 3036}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 2971, "end": 2982}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 2983, "end": 2985}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 2993, "end": 3003}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3025, "end": 3027}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3024, "end": 3033}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3010, "end": 3034}}, "is_native": false}, "1": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3085, "end": 3144}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3096, "end": 3109}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3110, "end": 3112}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3120, "end": 3127}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3134, "end": 3136}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3134, "end": 3142}}, "is_native": false}, "2": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3179, "end": 3269}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3190, "end": 3203}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3204, "end": 3209}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3224, "end": 3226}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3253, "end": 3258}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3233, "end": 3259}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3233, "end": 3267}}, "is_native": false}, "3": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3305, "end": 3372}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3316, "end": 3331}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3332, "end": 3337}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3349, "end": 3351}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3363, "end": 3368}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3358, "end": 3370}}, "is_native": false}, "4": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3535, "end": 3705}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3539, "end": 3555}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3556, "end": 3559}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3574, "end": 3577}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3592, "end": 3595}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3592, "end": 3604}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3608, "end": 3612}, "3": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3605, "end": 3607}, "4": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3584, "end": 3632}, "6": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3614, "end": 3631}, "7": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3584, "end": 3632}, "8": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3668, "end": 3694}, "9": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3656, "end": 3696}, "10": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3638, "end": 3703}}, "is_native": false}, "5": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3812, "end": 3911}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3832, "end": 3837}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3841, "end": 3844}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3881, "end": 3900}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3869, "end": 3902}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 3851, "end": 3909}}, "is_native": false}, "6": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4045, "end": 4165}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4065, "end": 4084}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4088, "end": 4091}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4128, "end": 4154}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4116, "end": 4156}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4098, "end": 4163}}, "is_native": false}, "7": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4274, "end": 4378}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4294, "end": 4310}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4314, "end": 4317}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4354, "end": 4367}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4342, "end": 4369}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4324, "end": 4376}}, "is_native": false}, "8": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4492, "end": 4613}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4512, "end": 4535}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4539, "end": 4542}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4579, "end": 4602}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4567, "end": 4604}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4549, "end": 4611}}, "is_native": false}, "9": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4748, "end": 4826}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4752, "end": 4758}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4762, "end": 4765}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4802, "end": 4815}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4790, "end": 4817}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4772, "end": 4824}}, "is_native": false}, "10": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4860, "end": 4915}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4871, "end": 4883}, "type_parameters": [], "parameters": [["uid#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4884, "end": 4887}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4896, "end": 4899}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4907, "end": 4910}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4906, "end": 4913}}, "is_native": false}, "11": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4963, "end": 5016}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4974, "end": 4986}, "type_parameters": [], "parameters": [["uid#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4987, "end": 4990}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 4999, "end": 5001}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5008, "end": 5011}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5008, "end": 5014}}, "is_native": false}, "12": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5051, "end": 5134}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5062, "end": 5074}, "type_parameters": [], "parameters": [["uid#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5075, "end": 5078}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5087, "end": 5097}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5119, "end": 5122}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5119, "end": 5131}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5118, "end": 5131}, "3": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5104, "end": 5132}}, "is_native": false}, "13": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5183, "end": 5249}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5194, "end": 5208}, "type_parameters": [], "parameters": [["uid#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5209, "end": 5212}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5221, "end": 5228}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5235, "end": 5238}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5235, "end": 5247}}, "is_native": false}, "14": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5397, "end": 5511}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5408, "end": 5411}, "type_parameters": [], "parameters": [["ctx#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5412, "end": 5415}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5434, "end": 5437}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5474, "end": 5477}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5474, "end": 5500}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5462, "end": 5502}, "3": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5444, "end": 5509}}, "is_native": false}, "15": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5852, "end": 5944}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5863, "end": 5869}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5870, "end": 5872}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5916, "end": 5918}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5889, "end": 5913}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5899, "end": 5911}, "3": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5924, "end": 5942}}, "is_native": false}, "16": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5983, "end": 6044}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5994, "end": 5996}, "type_parameters": [["T", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 5997, "end": 5998}]], "parameters": [["obj#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6005, "end": 6008}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6015, "end": 6017}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6035, "end": 6038}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6024, "end": 6039}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6024, "end": 6042}}, "is_native": false}, "17": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6086, "end": 6156}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6097, "end": 6106}, "type_parameters": [["T", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6107, "end": 6108}]], "parameters": [["obj#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6115, "end": 6118}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6125, "end": 6128}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6147, "end": 6150}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6136, "end": 6151}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6135, "end": 6154}}, "is_native": false}, "18": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6213, "end": 6304}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6224, "end": 6232}, "type_parameters": [["T", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6233, "end": 6234}]], "parameters": [["obj#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6241, "end": 6244}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6251, "end": 6261}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6294, "end": 6297}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6283, "end": 6298}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6282, "end": 6301}, "3": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6268, "end": 6302}}, "is_native": false}, "19": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6363, "end": 6443}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6374, "end": 6384}, "type_parameters": [["T", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6385, "end": 6386}]], "parameters": [["obj#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6393, "end": 6396}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6403, "end": 6410}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6428, "end": 6431}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6417, "end": 6432}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6417, "end": 6441}}, "is_native": false}, "20": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6753, "end": 6798}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6764, "end": 6774}, "type_parameters": [["T", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6775, "end": 6776}]], "parameters": [["obj#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6783, "end": 6786}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6793, "end": 6797}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "21": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6872, "end": 6990}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6892, "end": 6909}, "type_parameters": [], "parameters": [["bytes#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6910, "end": 6915}]], "returns": [{"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6927, "end": 6930}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6952, "end": 6957}, "1": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6937, "end": 6958}, "2": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6979, "end": 6984}, "3": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6974, "end": 6986}, "4": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 6964, "end": 6988}}, "is_native": false}, "22": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 7044, "end": 7080}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 7055, "end": 7066}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 7067, "end": 7069}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "23": {"location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 7120, "end": 7159}, "definition_location": {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 7131, "end": 7145}, "type_parameters": [], "parameters": [["id#0#0", {"file_hash": [34, 0, 28, 151, 195, 11, 255, 22, 3, 139, 84, 13, 204, 14, 94, 129, 215, 67, 1, 186, 104, 5, 185, 130, 121, 102, 179, 174, 229, 220, 207, 80], "start": 7146, "end": 7148}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"ENotSystemAddress": 6, "SUI_AUTHENTICATOR_STATE_ID": 2, "SUI_BRIDGE_ID": 5, "SUI_CLOCK_OBJECT_ID": 1, "SUI_DENY_LIST_OBJECT_ID": 4, "SUI_RANDOM_ID": 3, "SUI_SYSTEM_STATE_OBJECT_ID": 0}}