{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_af5297b292c3/crates/sui-framework/packages/sui-framework/sources/crypto/hmac.move", "definition_location": {"file_hash": [141, 72, 129, 199, 148, 159, 191, 128, 87, 83, 164, 221, 131, 173, 234, 157, 182, 164, 78, 115, 122, 183, 211, 111, 255, 178, 160, 99, 132, 17, 132, 104], "start": 87, "end": 91}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "hmac"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [141, 72, 129, 199, 148, 159, 191, 128, 87, 83, 164, 221, 131, 173, 234, 157, 182, 164, 78, 115, 122, 183, 211, 111, 255, 178, 160, 99, 132, 17, 132, 104], "start": 247, "end": 327}, "definition_location": {"file_hash": [141, 72, 129, 199, 148, 159, 191, 128, 87, 83, 164, 221, 131, 173, 234, 157, 182, 164, 78, 115, 122, 183, 211, 111, 255, 178, 160, 99, 132, 17, 132, 104], "start": 265, "end": 278}, "type_parameters": [], "parameters": [["key#0#0", {"file_hash": [141, 72, 129, 199, 148, 159, 191, 128, 87, 83, 164, 221, 131, 173, 234, 157, 182, 164, 78, 115, 122, 183, 211, 111, 255, 178, 160, 99, 132, 17, 132, 104], "start": 279, "end": 282}], ["msg#0#0", {"file_hash": [141, 72, 129, 199, 148, 159, 191, 128, 87, 83, 164, 221, 131, 173, 234, 157, 182, 164, 78, 115, 122, 183, 211, 111, 255, 178, 160, 99, 132, 17, 132, 104], "start": 297, "end": 300}]], "returns": [{"file_hash": [141, 72, 129, 199, 148, 159, 191, 128, 87, 83, 164, 221, 131, 173, 234, 157, 182, 164, 78, 115, 122, 183, 211, 111, 255, 178, 160, 99, 132, 17, 132, 104], "start": 316, "end": 326}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}