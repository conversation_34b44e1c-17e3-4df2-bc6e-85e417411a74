{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_af5297b292c3/crates/sui-framework/packages/sui-framework/sources/crypto/ecvrf.move", "definition_location": {"file_hash": [223, 167, 133, 228, 11, 21, 74, 117, 158, 103, 131, 181, 225, 204, 117, 29, 20, 37, 157, 7, 212, 122, 207, 104, 170, 217, 22, 211, 211, 189, 168, 91], "start": 87, "end": 92}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "ecvrf"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [223, 167, 133, 228, 11, 21, 74, 117, 158, 103, 131, 181, 225, 204, 117, 29, 20, 37, 157, 7, 212, 122, 207, 104, 170, 217, 22, 211, 211, 189, 168, 91], "start": 782, "end": 929}, "definition_location": {"file_hash": [223, 167, 133, 228, 11, 21, 74, 117, 158, 103, 131, 181, 225, 204, 117, 29, 20, 37, 157, 7, 212, 122, 207, 104, 170, 217, 22, 211, 211, 189, 168, 91], "start": 800, "end": 812}, "type_parameters": [], "parameters": [["hash#0#0", {"file_hash": [223, 167, 133, 228, 11, 21, 74, 117, 158, 103, 131, 181, 225, 204, 117, 29, 20, 37, 157, 7, 212, 122, 207, 104, 170, 217, 22, 211, 211, 189, 168, 91], "start": 818, "end": 822}], ["alpha_string#0#0", {"file_hash": [223, 167, 133, 228, 11, 21, 74, 117, 158, 103, 131, 181, 225, 204, 117, 29, 20, 37, 157, 7, 212, 122, 207, 104, 170, 217, 22, 211, 211, 189, 168, 91], "start": 841, "end": 853}], ["public_key#0#0", {"file_hash": [223, 167, 133, 228, 11, 21, 74, 117, 158, 103, 131, 181, 225, 204, 117, 29, 20, 37, 157, 7, 212, 122, 207, 104, 170, 217, 22, 211, 211, 189, 168, 91], "start": 872, "end": 882}], ["proof#0#0", {"file_hash": [223, 167, 133, 228, 11, 21, 74, 117, 158, 103, 131, 181, 225, 204, 117, 29, 20, 37, 157, 7, 212, 122, 207, 104, 170, 217, 22, 211, 211, 189, 168, 91], "start": 901, "end": 906}]], "returns": [{"file_hash": [223, 167, 133, 228, 11, 21, 74, 117, 158, 103, 131, 181, 225, 204, 117, 29, 20, 37, 157, 7, 212, 122, 207, 104, 170, 217, 22, 211, 211, 189, 168, 91], "start": 924, "end": 928}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidHashLength": 0, "EInvalidProofEncoding": 2, "EInvalidPublicKeyEncoding": 1}}