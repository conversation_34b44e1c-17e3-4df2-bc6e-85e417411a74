{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_af5297b292c3/crates/sui-framework/packages/sui-framework/sources/crypto/zklogin_verified_issuer.move", "definition_location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 110, "end": 133}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "zklogin_verified_issuer"], "struct_map": {"0": {"definition_location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 577, "end": 591}, "type_parameters": [], "fields": [{"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 644, "end": 646}, {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 712, "end": 717}, {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 751, "end": 757}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 835, "end": 924}, "definition_location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 846, "end": 851}, "type_parameters": [], "parameters": [["verified_issuer#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 852, "end": 867}]], "returns": [{"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 887, "end": 894}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 901, "end": 916}, "1": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 901, "end": 922}}, "is_native": false}, "1": {"location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 990, "end": 1082}, "definition_location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1001, "end": 1007}, "type_parameters": [], "parameters": [["verified_issuer#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1008, "end": 1023}]], "returns": [{"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1043, "end": 1050}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1058, "end": 1073}, "1": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1057, "end": 1080}}, "is_native": false}, "2": {"location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1112, "end": 1253}, "definition_location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1123, "end": 1129}, "type_parameters": [], "parameters": [["verified_issuer#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1130, "end": 1145}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1218, "end": 1233}, "1": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1173, "end": 1215}, "2": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1212, "end": 1213}, "3": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1201, "end": 1202}, "4": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1239, "end": 1250}, "5": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1250, "end": 1251}}, "is_native": false}, "3": {"location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1486, "end": 1854}, "definition_location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1497, "end": 1518}, "type_parameters": [], "parameters": [["address_seed#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1519, "end": 1531}], ["issuer#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1539, "end": 1545}], ["ctx#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1555, "end": 1558}]], "returns": [], "locals": [["sender#1#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1586, "end": 1592}]], "nops": {}, "code_map": {"0": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1595, "end": 1598}, "2": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1595, "end": 1607}, "3": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1586, "end": 1592}, "4": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1642, "end": 1648}, "5": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1650, "end": 1662}, "6": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1664, "end": 1671}, "7": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1621, "end": 1672}, "8": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1613, "end": 1688}, "12": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1674, "end": 1687}, "13": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1613, "end": 1688}, "14": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1767, "end": 1770}, "15": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1755, "end": 1771}, "16": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1792, "end": 1798}, "17": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1812, "end": 1818}, "18": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1722, "end": 1829}, "19": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1839, "end": 1845}, "20": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1694, "end": 1852}}, "is_native": false}, "4": {"location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1952, "end": 2124}, "definition_location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1963, "end": 1983}, "type_parameters": [], "parameters": [["address#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 1984, "end": 1991}], ["address_seed#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2002, "end": 2014}], ["issuer#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2022, "end": 2028}]], "returns": [{"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2040, "end": 2044}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2081, "end": 2088}, "1": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2090, "end": 2102}, "2": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2104, "end": 2110}, "3": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2104, "end": 2121}, "4": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2051, "end": 2122}}, "is_native": false}, "5": {"location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2306, "end": 2427}, "definition_location": {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2317, "end": 2346}, "type_parameters": [], "parameters": [["address#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2352, "end": 2359}], ["address_seed#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2374, "end": 2386}], ["issuer#0#0", {"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2398, "end": 2404}]], "returns": [{"file_hash": [17, 67, 105, 60, 163, 233, 157, 33, 28, 247, 87, 152, 230, 13, 5, 247, 224, 52, 85, 0, 220, 133, 207, 199, 37, 72, 21, 213, 235, 180, 132, 193], "start": 2422, "end": 2426}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EInvalidInput": 0, "EInvalidProof": 1}}