{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_af5297b292c3/crates/sui-framework/packages/move-stdlib/sources/hash.move", "definition_location": {"file_hash": [180, 83, 37, 169, 160, 52, 137, 9, 155, 138, 116, 210, 20, 66, 203, 205, 101, 18, 49, 34, 149, 114, 32, 163, 241, 195, 176, 82, 88, 162, 153, 189], "start": 262, "end": 266}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "hash"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [180, 83, 37, 169, 160, 52, 137, 9, 155, 138, 116, 210, 20, 66, 203, 205, 101, 18, 49, 34, 149, 114, 32, 163, 241, 195, 176, 82, 88, 162, 153, 189], "start": 269, "end": 326}, "definition_location": {"file_hash": [180, 83, 37, 169, 160, 52, 137, 9, 155, 138, 116, 210, 20, 66, 203, 205, 101, 18, 49, 34, 149, 114, 32, 163, 241, 195, 176, 82, 88, 162, 153, 189], "start": 287, "end": 295}, "type_parameters": [], "parameters": [["data#0#0", {"file_hash": [180, 83, 37, 169, 160, 52, 137, 9, 155, 138, 116, 210, 20, 66, 203, 205, 101, 18, 49, 34, 149, 114, 32, 163, 241, 195, 176, 82, 88, 162, 153, 189], "start": 296, "end": 300}]], "returns": [{"file_hash": [180, 83, 37, 169, 160, 52, 137, 9, 155, 138, 116, 210, 20, 66, 203, 205, 101, 18, 49, 34, 149, 114, 32, 163, 241, 195, 176, 82, 88, 162, 153, 189], "start": 315, "end": 325}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "1": {"location": {"file_hash": [180, 83, 37, 169, 160, 52, 137, 9, 155, 138, 116, 210, 20, 66, 203, 205, 101, 18, 49, 34, 149, 114, 32, 163, 241, 195, 176, 82, 88, 162, 153, 189], "start": 327, "end": 384}, "definition_location": {"file_hash": [180, 83, 37, 169, 160, 52, 137, 9, 155, 138, 116, 210, 20, 66, 203, 205, 101, 18, 49, 34, 149, 114, 32, 163, 241, 195, 176, 82, 88, 162, 153, 189], "start": 345, "end": 353}, "type_parameters": [], "parameters": [["data#0#0", {"file_hash": [180, 83, 37, 169, 160, 52, 137, 9, 155, 138, 116, 210, 20, 66, 203, 205, 101, 18, 49, 34, 149, 114, 32, 163, 241, 195, 176, 82, 88, 162, 153, 189], "start": 354, "end": 358}]], "returns": [{"file_hash": [180, 83, 37, 169, 160, 52, 137, 9, 155, 138, 116, 210, 20, 66, 203, 205, 101, 18, 49, 34, 149, 114, 32, 163, 241, 195, 176, 82, 88, 162, 153, 189], "start": 373, "end": 383}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}