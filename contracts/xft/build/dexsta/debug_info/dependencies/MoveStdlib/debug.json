{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_af5297b292c3/crates/sui-framework/packages/move-stdlib/sources/debug.move", "definition_location": {"file_hash": [130, 39, 194, 65, 152, 163, 85, 202, 79, 174, 118, 53, 127, 134, 21, 176, 5, 152, 60, 121, 154, 28, 164, 69, 149, 13, 81, 102, 124, 247, 230, 212], "start": 129, "end": 134}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000001", "debug"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [130, 39, 194, 65, 152, 163, 85, 202, 79, 174, 118, 53, 127, 134, 21, 176, 5, 152, 60, 121, 154, 28, 164, 69, 149, 13, 81, 102, 124, 247, 230, 212], "start": 137, "end": 171}, "definition_location": {"file_hash": [130, 39, 194, 65, 152, 163, 85, 202, 79, 174, 118, 53, 127, 134, 21, 176, 5, 152, 60, 121, 154, 28, 164, 69, 149, 13, 81, 102, 124, 247, 230, 212], "start": 155, "end": 160}, "type_parameters": [["T", {"file_hash": [130, 39, 194, 65, 152, 163, 85, 202, 79, 174, 118, 53, 127, 134, 21, 176, 5, 152, 60, 121, 154, 28, 164, 69, 149, 13, 81, 102, 124, 247, 230, 212], "start": 161, "end": 162}]], "parameters": [["x#0#0", {"file_hash": [130, 39, 194, 65, 152, 163, 85, 202, 79, 174, 118, 53, 127, 134, 21, 176, 5, 152, 60, 121, 154, 28, 164, 69, 149, 13, 81, 102, 124, 247, 230, 212], "start": 164, "end": 165}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "1": {"location": {"file_hash": [130, 39, 194, 65, 152, 163, 85, 202, 79, 174, 118, 53, 127, 134, 21, 176, 5, 152, 60, 121, 154, 28, 164, 69, 149, 13, 81, 102, 124, 247, 230, 212], "start": 173, "end": 211}, "definition_location": {"file_hash": [130, 39, 194, 65, 152, 163, 85, 202, 79, 174, 118, 53, 127, 134, 21, 176, 5, 152, 60, 121, 154, 28, 164, 69, 149, 13, 81, 102, 124, 247, 230, 212], "start": 191, "end": 208}, "type_parameters": [], "parameters": [], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}