{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_af5297b292c3/crates/sui-framework/packages/bridge/sources/crypto.move", "definition_location": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 90, "end": 96}, "module_name": ["000000000000000000000000000000000000000000000000000000000000000b", "crypto"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 145, "end": 752}, "definition_location": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 165, "end": 193}, "type_parameters": [], "parameters": [["compressed_pub_key#0#0", {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 194, "end": 212}]], "returns": [{"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 228, "end": 238}], "locals": [["address#1#0", {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 616, "end": 623}], ["decompressed#1#0", {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 275, "end": 287}], ["decompressed_64#1#0", {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 387, "end": 402}], ["hash#1#0", {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 541, "end": 545}], ["i#1#0", {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 380, "end": 381}], ["i#2#0", {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 648, "end": 649}]], "nops": {}, "code_map": {"0": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 318, "end": 336}, "1": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 290, "end": 337}, "2": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 275, "end": 287}, "3": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 407, "end": 408}, "4": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 410, "end": 418}, "5": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 383, "end": 402}, "6": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 376, "end": 381}, "7": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 432, "end": 433}, "8": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 436, "end": 438}, "9": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 434, "end": 435}, "10": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 425, "end": 518}, "12": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 450, "end": 465}, "13": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 476, "end": 491}, "14": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 489, "end": 490}, "15": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 476, "end": 491}, "17": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 450, "end": 492}, "18": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 506, "end": 507}, "19": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 510, "end": 511}, "20": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 508, "end": 509}, "21": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 502, "end": 503}, "22": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 425, "end": 518}, "23": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 558, "end": 574}, "24": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 548, "end": 575}, "25": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 541, "end": 545}, "26": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 626, "end": 634}, "27": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 612, "end": 623}, "28": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 652, "end": 654}, "29": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 644, "end": 649}, "30": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 667, "end": 668}, "31": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 671, "end": 673}, "32": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 669, "end": 670}, "33": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 660, "end": 737}, "35": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 685, "end": 692}, "36": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 703, "end": 710}, "37": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 708, "end": 709}, "38": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 703, "end": 710}, "40": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 685, "end": 711}, "41": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 725, "end": 726}, "42": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 729, "end": 730}, "43": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 727, "end": 728}, "44": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 721, "end": 722}, "45": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 660, "end": 737}, "46": {"file_hash": [11, 65, 248, 206, 174, 10, 114, 170, 59, 20, 110, 250, 191, 107, 46, 168, 56, 170, 131, 114, 95, 144, 214, 185, 15, 159, 66, 209, 43, 246, 148, 110], "start": 743, "end": 750}}, "is_native": false}}, "constant_map": {}}