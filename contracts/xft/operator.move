module dexsta::operator {
    use std::string::{Self, String};
    use std::option::{Self, Option};
    use std::vector;
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::event;
    use sui::package;
    use sui::display;
    use sui::clock::{Self, Clock};
    use sui::table::{Self, Table};
    use sui::dynamic_field as df;
    use sui::coin::{Self, Coin};
    use sui::sui::SUI;
    use dexsta::label::{Self, Label};

    // ===== Constants =====
    const DEFAULT_LICENSE_DURATION: u64 = **********; // 30 days in milliseconds
    const LICENSE_FEE: u64 = 500000000; // 0.5 SUI per month

    // ===== Errors =====
    const EInvalidOwner: u64 = 0;
    const EInvalidOperator: u64 = 1;
    const EInsufficientFunds: u64 = 2;
    const ELicenseExpired: u64 = 3;
    const EInvalidRole: u64 = 4;

    // ===== Events =====
    struct LicenseCreated has copy, drop {
        license_id: ID,
        label_id: ID,
        operator: address,
        role: u64,
        expiration_time: u64,
    }

    struct LicenseRenewed has copy, drop {
        license_id: ID,
        new_expiration_time: u64,
    }

    struct LicenseTransferred has copy, drop {
        license_id: ID,
        from: address,
        to: address,
    }

    struct LicenseRemoved has copy, drop {
        license_id: ID,
        label_id: ID,
        operator: address,
        removed_by: address,
    }

    struct WithdrawalPermissionAdded has copy, drop {
        license_id: ID,
        label_id: ID,
        operator: address,
        object_id: ID,
        object_type: String,
        max_amount: Option<u64>,
        updated_by: address,
    }

    struct WithdrawalPermissionRemoved has copy, drop {
        license_id: ID,
        label_id: ID,
        operator: address,
        object_id: ID,
        updated_by: address,
    }

    // ===== One-Time Witness for the package =====
    struct OPERATOR has drop {}

    // ===== Dynamic Field Keys =====
    struct OperatorLicenseKey has store, copy, drop {
        label_id: ID,
        operator: address,
    }

    // ===== Objects =====
    struct OperatorRegistry has key {
        id: UID,
        owner: address,
        fee_address: address,
        license_count: u64,
        fire_registry_id: ID, // Reference to Fire Registry for inter-contract communication
    }

    /// Withdrawal permission for a specific object
    struct WithdrawalPermission has store, drop {
        object_id: ID, // Specific object ID this permission applies to
        object_type: String, // Type of object (Coin<SUI>, Coin<USDC>, XFT, etc.)
        max_amount: Option<u64>, // For coins: max amount, for NFTs: None
        allowed: bool, // Whether withdrawal is allowed for this object
    }

    struct License has key, store {
        id: UID,
        label_id: ID,
        operator: address,
        role: u64, // 1 = super operator (special permissions), any other number = custom role
        creation_time: u64,
        expiration_time: u64,
        withdrawal_cooldown_days: u64, // How often they can withdraw (in days)
        next_withdrawal_date: u64, // Timestamp when next withdrawal is allowed
        withdrawal_permissions: vector<WithdrawalPermission>, // Specific object permissions
    }

    // ===== Functions =====
    fun init(witness: OPERATOR, ctx: &mut TxContext) {
        let publisher = package::claim(witness, ctx);
        
        let keys = vector[
            string::utf8(b"name"),
            string::utf8(b"description"),
            string::utf8(b"image_url"),
            string::utf8(b"operator"),
            string::utf8(b"project_url"),
        ];
        
        let values = vector[
            string::utf8(b"Operator License"),
            string::utf8(b"License for operating label {label_id}"),
            string::utf8(b"https://xft.red/licenses/{id}.png"),
            string::utf8(b"{operator}"),
            string::utf8(b"https://xft.red"),
        ];
        
        let display_info = display::new_with_fields<License>(
            &publisher, keys, values, ctx
        );
        
        display::update_version(&mut display_info);
        
        // Create operator registry
        let registry = OperatorRegistry {
            id: object::new(ctx),
            owner: tx_context::sender(ctx),
            fee_address: tx_context::sender(ctx),
            licenses: table::new(ctx),
            license_count: 0,
            fire_registry_id: object::id_from_address(@0x0), // Placeholder, set via admin
        };
        
        transfer::public_transfer(publisher, tx_context::sender(ctx));
        transfer::public_transfer(display_info, tx_context::sender(ctx));
        transfer::share_object(registry);
    }

    /// Admin function to set Fire Registry ID (called after Fire Registry is deployed)
    public fun set_fire_registry(
        registry: &mut OperatorRegistry,
        fire_registry_id: ID,
        ctx: &mut TxContext
    ) {
        // Only owner can set Fire Registry
        assert!(tx_context::sender(ctx) == registry.owner, 0);
        registry.fire_registry_id = fire_registry_id;
    }

    /// Add an operator to a label
    /// Caller must be label owner OR super operator for the label
    public fun add_operator(
        registry: &mut OperatorRegistry,
        label: &Label,
        operator_address: address,
        role: u64, // 1 = super operator (special permissions), any other number = custom role
        duration_days: u64,
        withdrawal_cooldown_days: u64, // How often they can withdraw (in days)
        clock: &Clock,
        ctx: &mut TxContext
    ): License {
        let caller = tx_context::sender(ctx);

        // Check if caller is label owner
        let is_label_owner = dexsta::label::is_owner(label, caller, clock);

        if (!is_label_owner) {
            // Check if caller is super operator for this label
            let label_global_id = dexsta::label::global_id(label);
            let (is_operator_result, operator_role, _license_global_id) = is_operator(
                registry,
                label_global_id,
                caller,
                clock
            );

            // Must be super operator (role 1) to add operators
            assert!(is_operator_result && operator_role == 1, 1); // EInvalidOperator
        };

        // No role validation needed - only role 1 (super operator) has special meaning
        // Label owners can assign any role number for their own internal use

        // Calculate expiration time
        let current_time = clock::timestamp_ms(clock);
        let duration_ms = duration_days * 24 * 60 * 60 * 1000; // Convert days to milliseconds
        let expiration_time = current_time + duration_ms;

        // Create operator license
        let license_id = object::new(ctx);
        let license_object_id = object::uid_to_inner(&license_id);
        let label_object_id = object::uid_to_inner(&label.id);
        
        // Calculate next withdrawal date (now + cooldown days)
        let cooldown_ms = withdrawal_cooldown_days * 24 * 60 * 60 * 1000; // Convert days to milliseconds
        let next_withdrawal_date = current_time + cooldown_ms;

        let license = License {
            id: license_id,
            label_id: label_object_id,
            operator: operator_address,
            role,
            creation_time: current_time,
            expiration_time,
            withdrawal_cooldown_days,
            next_withdrawal_date, // Scheduled for future based on cooldown
            withdrawal_permissions: vector::empty<WithdrawalPermission>(), // Start with no permissions
        };

        // Store license in dynamic field using (label_id, operator) as key
        let license_key = OperatorLicenseKey {
            label_id: label_object_id,
            operator: operator_address
        };
        df::add(&mut registry.id, license_key, license_object_id);
        registry.license_count = registry.license_count + 1;
        
        
        // Emit event
        event::emit(LicenseCreated {
            license_id: license_object_id,
            label_id: label_object_id,
            operator: operator_address,
            role,
            expiration_time,
        });

        license
    }

    /// Remove an operator from a label by setting expiration in past and role to 0
    /// Caller must be label owner OR super operator for the label
    public fun remove_operator(
        registry: &mut OperatorRegistry,
        label: &Label,
        license: &mut License,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let caller = tx_context::sender(ctx);

        // Check if caller is label owner
        let is_label_owner = dexsta::label::is_owner(label, caller, clock);

        if (!is_label_owner) {
            // Check if caller is super operator for this label
            let label_global_id = dexsta::label::global_id(label);
            let (is_operator_result, operator_role, _license_global_id) = is_operator(
                registry,
                label_global_id,
                caller,
                clock
            );

            // Must be super operator (role 1) to remove operators
            assert!(is_operator_result && operator_role == 1, 1); // EInvalidOperator
        };

        // Verify this license belongs to the label
        let label_object_id = object::uid_to_inner(&label.id);
        assert!(license.label_id == label_object_id, 0); // EInvalidOwner

        // Deactivate the license by setting expiration in past and role to 0
        let current_time = clock::timestamp_ms(clock);
        license.expiration_time = current_time - 1; // Set to past
        license.role = 0; // Remove all permissions

        // Emit event
        event::emit(LicenseRemoved {
            license_id: object::uid_to_inner(&license.id),
            label_id: label_object_id,
            operator: license.operator,
            removed_by: caller,
        });
    }

    /// Add withdrawal permission for a specific object
    /// Caller must be label owner OR super operator for the label
    public fun add_withdrawal_permission(
        registry: &mut OperatorRegistry,
        label: &Label,
        license: &mut License,
        object_id: ID, // Specific object ID to grant permission for
        object_type: String, // Type of object (e.g., "Coin<SUI>", "XFT", etc.)
        max_amount: Option<u64>, // For coins: max amount, for NFTs: None
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let caller = tx_context::sender(ctx);

        // Check if caller is label owner
        let is_label_owner = dexsta::label::is_owner(label, caller, clock);

        if (!is_label_owner) {
            // Check if caller is super operator for this label
            let label_global_id = dexsta::label::global_id(label);
            let (is_operator_result, operator_role, _license_global_id) = is_operator(
                registry,
                label_global_id,
                caller,
                clock
            );

            // Must be super operator (role 1) to modify withdrawal permissions
            assert!(is_operator_result && operator_role == 1, 1); // EInvalidOperator
        };

        // Verify this license belongs to the label
        let label_object_id = object::uid_to_inner(&label.id);
        assert!(license.label_id == label_object_id, 0); // EInvalidOwner

        // Check if permission for this object already exists
        let permissions = &mut license.withdrawal_permissions;
        let mut found_index = option::none<u64>();
        let mut i = 0;
        while (i < vector::length(permissions)) {
            let permission = vector::borrow(permissions, i);
            if (permission.object_id == object_id) {
                found_index = option::some(i);
                break
            };
            i = i + 1;
        };

        // Create new permission
        let new_permission = WithdrawalPermission {
            object_id,
            object_type,
            max_amount,
            allowed: true,
        };

        if (option::is_some(&found_index)) {
            // Update existing permission
            let index = option::extract(&mut found_index);
            *vector::borrow_mut(permissions, index) = new_permission;
        } else {
            // Add new permission
            vector::push_back(permissions, new_permission);
        };

        // Emit event
        event::emit(WithdrawalPermissionAdded {
            license_id: object::uid_to_inner(&license.id),
            label_id: label_object_id,
            operator: license.operator,
            object_id,
            object_type,
            max_amount,
            updated_by: caller,
        });
    }

    /// Edit withdrawal permission for a specific object (must already exist)
    /// Caller must be label owner OR super operator for the label
    public fun edit_withdrawal_permission(
        registry: &mut OperatorRegistry,
        label: &Label,
        license: &mut License,
        object_id: ID, // Specific object ID to edit permission for
        new_object_type: String, // Updated object type
        new_max_amount: Option<u64>, // Updated max amount
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let caller = tx_context::sender(ctx);

        // Check if caller is label owner
        let is_label_owner = dexsta::label::is_owner(label, caller, clock);

        if (!is_label_owner) {
            // Check if caller is super operator for this label
            let label_global_id = dexsta::label::global_id(label);
            let (is_operator_result, operator_role, _license_global_id) = is_operator(
                registry,
                label_global_id,
                caller,
                clock
            );

            // Must be super operator (role 1) to modify withdrawal permissions
            assert!(is_operator_result && operator_role == 1, 1); // EInvalidOperator
        };

        // Verify this license belongs to the label
        let label_object_id = object::uid_to_inner(&label.id);
        assert!(license.label_id == label_object_id, 0); // EInvalidOwner

        // Find and update permission for this object
        let permissions = &mut license.withdrawal_permissions;
        let mut found = false;
        let mut i = 0;
        while (i < vector::length(permissions)) {
            let permission = vector::borrow_mut(permissions, i);
            if (permission.object_id == object_id) {
                // Update the existing permission
                permission.object_type = new_object_type;
                permission.max_amount = new_max_amount;
                permission.allowed = true; // Ensure it's still allowed
                found = true;
                break
            };
            i = i + 1;
        };

        // Assert that permission was found and updated
        assert!(found, 3); // Permission not found

        // Emit event
        event::emit(WithdrawalPermissionAdded {
            license_id: object::uid_to_inner(&license.id),
            label_id: label_object_id,
            operator: license.operator,
            object_id,
            object_type: new_object_type,
            max_amount: new_max_amount,
            updated_by: caller,
        });
    }

    /// Remove withdrawal permission for a specific object
    /// Caller must be label owner OR super operator for the label
    public fun remove_withdrawal_permission(
        registry: &mut OperatorRegistry,
        label: &Label,
        license: &mut License,
        object_id: ID, // Specific object ID to remove permission for
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let caller = tx_context::sender(ctx);

        // Check if caller is label owner
        let is_label_owner = dexsta::label::is_owner(label, caller, clock);

        if (!is_label_owner) {
            // Check if caller is super operator for this label
            let label_global_id = dexsta::label::global_id(label);
            let (is_operator_result, operator_role, _license_global_id) = is_operator(
                registry,
                label_global_id,
                caller,
                clock
            );

            // Must be super operator (role 1) to modify withdrawal permissions
            assert!(is_operator_result && operator_role == 1, 1); // EInvalidOperator
        };

        // Verify this license belongs to the label
        let label_object_id = object::uid_to_inner(&label.id);
        assert!(license.label_id == label_object_id, 0); // EInvalidOwner

        // Find and remove permission for this object
        let permissions = &mut license.withdrawal_permissions;
        let mut found_index = option::none<u64>();
        let mut i = 0;
        while (i < vector::length(permissions)) {
            let permission = vector::borrow(permissions, i);
            if (permission.object_id == object_id) {
                found_index = option::some(i);
                break
            };
            i = i + 1;
        };

        if (option::is_some(&found_index)) {
            let index = option::extract(&mut found_index);
            vector::remove(permissions, index);

            // Emit event
            event::emit(WithdrawalPermissionRemoved {
                license_id: object::uid_to_inner(&license.id),
                label_id: label_object_id,
                operator: license.operator,
                object_id,
                updated_by: caller,
            });
        };
    }

    /// Update withdrawal cooldown for an operator
    /// Caller must be label owner OR super operator for the label
    public fun set_withdrawal_cooldown(
        registry: &mut OperatorRegistry,
        label: &Label,
        license: &mut License,
        withdrawal_cooldown_days: u64, // How often they can withdraw (in days)
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let caller = tx_context::sender(ctx);

        // Check if caller is label owner
        let is_label_owner = dexsta::label::is_owner(label, caller, clock);

        if (!is_label_owner) {
            // Check if caller is super operator for this label
            let label_global_id = dexsta::label::global_id(label);
            let (is_operator_result, operator_role, _license_global_id) = is_operator(
                registry,
                label_global_id,
                caller,
                clock
            );

            // Must be super operator (role 1) to modify withdrawal permissions
            assert!(is_operator_result && operator_role == 1, 1); // EInvalidOperator
        };

        // Verify this license belongs to the label
        let label_object_id = object::uid_to_inner(&label.id);
        assert!(license.label_id == label_object_id, 0); // EInvalidOwner

        // Update withdrawal cooldown
        license.withdrawal_cooldown_days = withdrawal_cooldown_days;

        // Set next withdrawal date based on new cooldown period
        let current_time = clock::timestamp_ms(clock);
        let cooldown_ms = withdrawal_cooldown_days * 24 * 60 * 60 * 1000; // Convert days to milliseconds
        license.next_withdrawal_date = current_time + cooldown_ms;
    }
    
    

    /// Check if an address is an operator for a specific label by object ID
    /// Returns (is_operator: bool, role: u64, license_object_id: ID)
    /// - is_operator: true if user has valid, active, non-expired license
    /// - role: 0=no license, 1=super operator (special permissions), other numbers=custom roles
    /// - license_object_id: object ID of the operator license
    public fun is_operator_by_object_id(
        registry: &OperatorRegistry,
        label_object_id: ID,
        operator_address: address,
        clock: &Clock
    ): (bool, u64, ID) {
        // Direct lookup using (label_id, operator) key
        let license_key = OperatorLicenseKey {
            label_id: label_object_id,
            operator: operator_address
        };

        // Check if license exists for this operator and label
        if (!df::exists_(&registry.id, license_key)) {
            return (false, 0, object::id_from_address(@0x0))
        };

        // Get the license object ID
        let license_object_id = *df::borrow<OperatorLicenseKey, ID>(&registry.id, license_key);

        // Note: To fully validate the license (expiration, role), we need the License object
        // This function returns the license ID, and the caller should validate the License object
        // For now, we return that a license exists
        (true, 0, license_object_id) // Role 0 as placeholder - need License object to get actual role
    }

    /// Complete operator check with License object validation
    /// Returns (is_operator: bool, role: u64, license_object_id: ID)
    public fun is_operator_with_license(
        registry: &OperatorRegistry,
        label_object_id: ID,
        operator_address: address,
        license: &License,
        clock: &Clock
    ): (bool, u64, ID) {
        // First check if license mapping exists
        let license_key = OperatorLicenseKey {
            label_id: label_object_id,
            operator: operator_address
        };

        if (!df::exists_(&registry.id, license_key)) {
            return (false, 0, object::id_from_address(@0x0))
        };

        // Get stored license ID and verify it matches the provided license
        let stored_license_id = *df::borrow<OperatorLicenseKey, ID>(&registry.id, license_key);
        let provided_license_id = object::uid_to_inner(&license.id);

        if (stored_license_id != provided_license_id) {
            return (false, 0, object::id_from_address(@0x0))
        };

        // Validate the license
        let (is_valid, role) = is_license_valid(license, operator_address, clock);

        (is_valid, role, stored_license_id)
    }

    

    /// Complete operator check with License object
    /// This is the full implementation that validates the actual license
    public fun is_operator_complete(
        registry: &OperatorRegistry,
        global_registry: &dexsta::registry::GlobalRegistry,
        license: &License,
        label_global_id: u64,
        operator_address: address,
        clock: &Clock
    ): (bool, u64, u64) {
        // Get label object ID from global registry
        let label_object_id_opt = dexsta::registry::get_object_id(global_registry, label_global_id);
        if (option::is_none(&label_object_id_opt)) {
            return (false, 0, 0) // Label doesn't exist
        };

        let label_object_id = option::extract(&mut label_object_id_opt);

        // Validate the license using the actual License object
        let (is_valid, actual_role) = is_license_valid(license, operator_address, clock);
        if (!is_valid) {
            return (false, 0, 0) // License is invalid/expired
        };

        // Verify this license is stored in our registry for this label/operator
        let license_key = OperatorLicenseKey {
            label_id: label_object_id,
            operator: operator_address
        };

        if (!df::exists_(&registry.id, license_key)) {
            return (false, 0, 0) // License not registered in our system
        };

        // Verify the stored license ID matches the provided license
        let stored_license_id = *df::borrow<OperatorLicenseKey, ID>(&registry.id, license_key);
        let provided_license_id = object::uid_to_inner(&license.id);

        if (stored_license_id != provided_license_id) {
            return (false, 0, 0) // License ID mismatch
        };

        // Convert license object ID to global ID
        let license_global_id_opt = dexsta::registry::get_global_id_by_object(global_registry, provided_license_id);
        let license_global_id = if (option::is_some(&license_global_id_opt)) {
            option::extract(&mut license_global_id_opt)
        } else {
            0 // License not registered in global registry
        };

        // Return actual role from the license
        (true, actual_role, license_global_id)
    }

    /// Check if a specific license is valid for an operator
    /// Returns (is_valid: bool, role: u64)
    public fun is_license_valid(
        license: &License,
        operator_address: address,
        clock: &Clock
    ): (bool, u64) {
        // Check if license belongs to the operator
        if (license.operator != operator_address) {
            return (false, 0)
        };

        // Check if license is expired
        let current_time = clock::timestamp_ms(clock);
        if (current_time > license.expiration_time) {
            return (false, 0)
        };

        // Check if license is deactivated (role 0)
        if (license.role == 0) {
            return (false, 0)
        };

        // License is valid
        (true, license.role)
    }

    /// Check if operator has withdrawal permission for a specific object
    /// Returns (has_permission: bool, max_amount: Option<u64>)
    public fun has_withdrawal_permission(
        license: &License,
        object_id: ID
    ): (bool, Option<u64>) {
        let permissions = &license.withdrawal_permissions;
        let mut i = 0;

        while (i < vector::length(permissions)) {
            let permission = vector::borrow(permissions, i);
            if (permission.object_id == object_id && permission.allowed) {
                return (true, permission.max_amount)
            };
            i = i + 1;
        };

        // No permission found for this object
        (false, option::none<u64>())
    }

    /// Check if operator can withdraw now (cooldown check)
    public fun can_withdraw_now(
        license: &License,
        clock: &Clock
    ): bool {
        let current_time = clock::timestamp_ms(clock);
        current_time >= license.next_withdrawal_date
    }

    /// Get license owner (accessor function)
    public fun license_owner(license: &License): address {
        license.operator
    }

    /// Get license label ID (accessor function)
    public fun license_label_id(license: &License): ID {
        license.label_id
    }

    /// Check if license is expired
    public fun is_license_expired(license: &License, clock: &Clock): bool {
        let current_time = clock::timestamp_ms(clock);
        current_time > license.expiration_time
    }

    /// Get license settings (placeholder - would return operator settings array)
    public fun license_settings(license: &License): vector<u64> {
        // TODO: Implement license settings array
        // For now return empty vector
        vector::empty()
    }

}
