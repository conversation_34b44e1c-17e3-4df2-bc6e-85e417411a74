module dexsta::operator {
    use std::string::{Self, String};
    use std::option::{Self, Option};
    use std::vector;
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::event;
    use sui::package;
    use sui::display;
    use sui::clock::{Self, Clock};
    use sui::table::{Self, Table};
    use sui::coin::{Self, Coin};
    use sui::sui::SUI;
    use dexsta::label::{Self, Label};

    // ===== Constants =====
    const DEFAULT_LICENSE_DURATION: u64 = **********; // 30 days in milliseconds
    const LICENSE_FEE: u64 = 500000000; // 0.5 SUI per month

    // ===== Errors =====
    const EInvalidOwner: u64 = 0;
    const EInvalidOperator: u64 = 1;
    const EInsufficientFunds: u64 = 2;
    const ELicenseExpired: u64 = 3;
    const EInvalidRole: u64 = 4;

    // ===== Events =====
    struct LicenseCreated has copy, drop {
        license_id: ID,
        label_id: ID,
        operator: address,
        role: u64,
        expiration_time: u64,
    }

    struct LicenseRenewed has copy, drop {
        license_id: ID,
        new_expiration_time: u64,
    }

    struct LicenseTransferred has copy, drop {
        license_id: ID,
        from: address,
        to: address,
    }

    // ===== One-Time Witness for the package =====
    struct OPERATOR has drop {}

    // ===== Objects =====
    struct OperatorRegistry has key {
        id: UID,
        owner: address,
        fee_address: address,
        licenses: Table<ID, ID>, // label_id -> license_id
        license_count: u64,
        fire_registry_id: ID, // Reference to Fire Registry for inter-contract communication
    }

    struct License has key, store {
        id: UID,
        label_id: ID,
        operator: address,
        role: u64, // 1 = super operator (special permissions), any other number = custom role
        creation_time: u64,
        expiration_time: u64,
        transferable: bool,
    }

    // ===== Functions =====
    fun init(witness: OPERATOR, ctx: &mut TxContext) {
        let publisher = package::claim(witness, ctx);
        
        let keys = vector[
            string::utf8(b"name"),
            string::utf8(b"description"),
            string::utf8(b"image_url"),
            string::utf8(b"operator"),
            string::utf8(b"project_url"),
        ];
        
        let values = vector[
            string::utf8(b"Operator License"),
            string::utf8(b"License for operating label {label_id}"),
            string::utf8(b"https://xft.red/licenses/{id}.png"),
            string::utf8(b"{operator}"),
            string::utf8(b"https://xft.red"),
        ];
        
        let display_info = display::new_with_fields<License>(
            &publisher, keys, values, ctx
        );
        
        display::update_version(&mut display_info);
        
        // Create operator registry
        let registry = OperatorRegistry {
            id: object::new(ctx),
            owner: tx_context::sender(ctx),
            fee_address: tx_context::sender(ctx),
            licenses: table::new(ctx),
            license_count: 0,
            fire_registry_id: object::id_from_address(@0x0), // Placeholder, set via admin
        };
        
        transfer::public_transfer(publisher, tx_context::sender(ctx));
        transfer::public_transfer(display_info, tx_context::sender(ctx));
        transfer::share_object(registry);
    }

    /// Admin function to set Fire Registry ID (called after Fire Registry is deployed)
    public fun set_fire_registry(
        registry: &mut OperatorRegistry,
        fire_registry_id: ID,
        ctx: &mut TxContext
    ) {
        // Only owner can set Fire Registry
        assert!(tx_context::sender(ctx) == registry.owner, 0);
        registry.fire_registry_id = fire_registry_id;
    }

    /// Add an operator to a label
    /// Caller must be label owner OR super operator for the label
    public fun add_operator(
        registry: &mut OperatorRegistry,
        label: &Label,
        operator_address: address,
        role: u64, // 1 = super operator (special permissions), any other number = custom role
        duration_days: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ): License {
        let caller = tx_context::sender(ctx);

        // Check if caller is label owner
        let is_label_owner = dexsta::label::is_owner(label, caller, clock);

        if (!is_label_owner) {
            // Check if caller is super operator for this label
            let label_global_id = dexsta::label::global_id(label);
            let (is_operator_result, operator_role, _license_global_id) = is_operator(
                registry,
                label_global_id,
                caller,
                clock
            );

            // Must be super operator (role 1) to add operators
            assert!(is_operator_result && operator_role == 1, 4); // Not authorized
        };

        // No role validation needed - only role 1 (super operator) has special meaning
        // Label owners can assign any role number for their own internal use

        // Calculate expiration time
        let current_time = clock::timestamp_ms(clock);
        let duration_ms = duration_days * 24 * 60 * 60 * 1000; // Convert days to milliseconds
        let expiration_time = current_time + duration_ms;

        // Create operator license
        let license_id = object::new(ctx);
        let license_object_id = object::uid_to_inner(&license_id);
        let label_object_id = object::uid_to_inner(&label.id);

        let license = License {
            id: license_id,
            label_id: label_object_id,
            operator: operator_address,
            role,
            creation_time: current_time,
            expiration_time,
            transferable: true, // Operator licenses are transferable
        };

        // Store license mapping in registry
        table::add(&mut registry.licenses, label_object_id, license_object_id);
        registry.license_count = registry.license_count + 1;

        // Emit event
        event::emit(LicenseCreated {
            license_id: license_object_id,
            label_id: label_object_id,
            operator: operator_address,
            role,
            expiration_time,
        });

        license
    }
    
    

    /// Check if an address is an operator for a specific label
    /// Returns (is_operator: bool, role: u64, license_global_id: u64)
    /// - is_operator: true if user has valid, active, non-expired license
    /// - role: 0=no license, 1=super operator, 2=regular operator
    /// - license_global_id: global ID of the operator license (0 if no license)
    public fun is_operator(
        registry: &OperatorRegistry,
        label_global_id: u64,
        operator_address: address,
        clock: &Clock
    ): (bool, u64, u64) {
        // TODO: Implement full license checking logic
        // This should:
        // 1. Check if operator_address has a license for label_global_id
        // 2. Verify license is active
        // 3. Check license is not expired
        // 4. Return the role from the license
        // 5. Return the global ID of the operator license

        // For now, return (false, 0, 0) as placeholder
        (false, 0, 0)
    }

    /// Get license owner (accessor function)
    public fun license_owner(license: &License): address {
        license.operator
    }

    /// Get license label ID (accessor function)
    public fun license_label_id(license: &License): ID {
        license.label_id
    }

    /// Check if license is expired
    public fun is_license_expired(license: &License, clock: &Clock): bool {
        let current_time = clock::timestamp_ms(clock);
        current_time > license.expiration_time
    }

    /// Get license settings (placeholder - would return operator settings array)
    public fun license_settings(license: &License): vector<u64> {
        // TODO: Implement license settings array
        // For now return empty vector
        vector::empty()
    }

}
