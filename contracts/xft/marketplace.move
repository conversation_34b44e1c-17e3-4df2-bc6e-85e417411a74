///
/// # Marketplace Module
///
/// This module implements the core marketplace functionality for buying and selling NFTs.
/// The marketplace supports:
/// - Listing NFTs for sale with fixed or incremental pricing
/// - Buying NFTs with automatic fee distribution
/// - Cancelling listings
/// - Searching for listings by various criteria
///
/// ## Key Features
///
/// * Marketplace directly owns its SUI balance
/// * Support for incremental pricing (price increases after certain number of sales)
/// * Multi-level fee distribution (platform, creator, label)
/// * Listing expiration and cancellation
/// * Advanced search functionality for listings
///
/// ## Dynamic Fields
///
/// The marketplace has the following dynamic fields:
/// * `BalanceKey` - Stores the marketplace's SUI balance
/// * `OperatorsKey` - Stores the marketplace's operators
/// * `ListingsKey` - Stores all listings
/// * `ActiveListingsKey` - Stores active listings for quick access
/// * `ListingsByPriceKey` - Stores listings sorted by price
/// * `ListingsByNFTKey` - Stores listings by NFT ID
/// * `ListingsBySellerKey` - Stores listings by seller address
            
            sell settings
            // settings.push(parseInt(_xft)); //0
            // settings.push(0);//1 price
            // settings.push(parseInt(priceType_)); //2 price type
            // settings.push(parseInt(supply_)); //3 quantity
            // settings.push(parseInt(marketLicense_)); //4 marketplacelicense
            // settings.push(0);//5 label license royaltea %
            // settings.push(parseInt(marketplace));///6 marketplace
            // settings.push(0);///7 opid
            // settings.push(parseInt(_display.type));/// 8 nftea type
            // settings.push(0);/// 9 fees
            // settings.push(0);/// 10 sale expire
///
module dexsta::marketplace {
    use std::string::{Self, String};
    use std::option::{Self, Option};
    use std::vector;
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::coin::{Self, Coin};
    use sui::sui::SUI;
    use sui::event;
    use sui::table::{Self, Table};
    use sui::clock::{Self, Clock};
    use sui::dynamic_field as df;
    use sui::vec_set::{Self, VecSet};
    use sui::balance::{Self, Balance};
    use dexsta::xft::{Self, XFT};
    use dexsta::label::{Self, Label};
    use dexsta::fire;
    use dexsta::registry;
    use dexsta::operator;

    // ===== Constants =====
    const DEFAULT_LISTING_DURATION: u64 = 7776000000; // 90 days in milliseconds
    const PLATFORM_FEE_BPS: u64 = 300; // 3% platform fee

    // ===== Errors =====
    const EInvalidPrice: u64 = 0;
    const EInvalidQuantity: u64 = 1;
    const EInvalidSeller: u64 = 2;
    const EInvalidBuyer: u64 = 3;
    const EListingNotActive: u64 = 4;
    const EListingExpired: u64 = 5;
    const EInsufficientFunds: u64 = 6;
    const EInvalidOperator: u64 = 7;
    const EInvalidListing: u64 = 8;
    const ENotAuthorized: u64 = 9;
    const EInsufficientBalance: u64 = 10;
    const EInvalidOwner: u64 = 11;

    // ===== Events =====
    struct ListingCreated has copy, drop {
        listing_id: ID,
        xft_id: u64,                    // XFT global ID (consistent with other events)
        seller: address,
        price: u64,
        quantity: u64,
        expiration_time: u64,
        label_global_id: u64,           // Label the item is being sold under
        marketplace_license: u64,       // Marketplace license used (0 if none)
        xft_type: u64,                  // XFT type for reference
    }

    struct ListingCancelled has copy, drop {
        listing_id: ID,
        xft_id: u64,                    // XFT global ID (consistent with other events)
        seller: address,
    }

    struct Sale has copy, drop {
        listing_id: ID,
        xft_id: u64,                    // XFT global ID (consistent with other events)
        seller: address,
        buyer: address,
        price: u64,
        quantity: u64,
        platform_fee: u64,
        royalty_fee: u64,
        label_fee: u64,
    }

    struct DepositMade has copy, drop {
        marketplace_id: ID,
        depositor: address,
        amount: u64,
    }

    struct WithdrawalMade has copy, drop {
        marketplace_id: ID,
        recipient: address,
        amount: u64,
    }

    // ===== Dynamic Field Keys =====
    struct BalanceKey has store, copy, drop {}
    struct OperatorsKey has store, copy, drop {}
    struct ListingsKey has store, copy, drop {}
    struct ActiveListingsKey has store, copy, drop {}
    struct ListingsByPriceKey has store, copy, drop {}
    struct ListingsByNFTKey has store, copy, drop {}
    struct ListingsBySellerKey has store, copy, drop {}
    struct PurchaseHistoryKey has store, copy, drop {}

    // ===== Objects =====
    struct Marketplace has key {
        id: UID,
        owner: address,
        fee_address: address,
        fire_registry_id: ID, // Reference to Fire Registry for inter-contract communication
    }

    /// Marketplace listing with settings array
    ///
    /// Settings array structure:
    /// * 0: XFT global ID
    /// * 1: Price (base price in SUI)
    /// * 2: Price type (1=fixed, 2=dynamic +5% per sale)
    /// * 3: Quantity/supply
    /// * 4: Marketplace license global ID
    /// * 5: Label marketplace fee (basis points)
    /// * 6: Label ID (global ID)
    /// * 7: Operator ID
    /// * 8: XFT type
    /// * 9: Additional fees
    /// * 10: Sale expiration timestamp
    struct Listing has store {
        id: ID,
        nft_id: ID,
        seller: address,
        price: u64, // Base price from settings[1]
        quantity: u64, // Available quantity from settings[3]
        expiration_time: u64,
        active: bool,
        settings: vector<u64>, // Full settings array as defined above
        label_id: Option<ID>,
        incremental_price: bool, // Deprecated - use settings[2] instead
        buy_count: u64, // Number of sales for dynamic pricing
    }

    struct Operators has store {
        operators: VecSet<address>,
    }

    struct Listings has store {
        listings: Table<ID, Listing>,
    }

    struct ActiveListings has store {
        active_listings: VecSet<ID>,
    }

    /// Stores listings sorted by price for efficient searching
    struct ListingsByPrice has store {
        // Price range -> Set of listing IDs
        // Price ranges are in increments of 100000000 (0.1 SUI)
        price_ranges: Table<u64, vector<ID>>,
    }

    /// Stores listings by NFT ID for efficient searching
    struct ListingsByNFT has store {
        // NFT ID -> Set of listing IDs
        listings_by_nft: Table<ID, vector<ID>>,
    }

    /// Stores listings by seller for efficient searching
    struct ListingsBySeller has store {
        // Seller address -> Set of listing IDs
        listings_by_seller: Table<address, vector<ID>>,
    }

    /// Purchase history tracking - maps buyer to labels they've purchased from
    struct PurchaseHistory has store {
        // Buyer address -> vector of label global IDs they've purchased from
        buyer_to_labels: Table<address, vector<u64>>,
        // Label global ID -> vector of buyers who purchased from this label
        label_to_buyers: Table<u64, vector<address>>,
        // XFT global ID -> (buyer, label_purchased_from) for item tracking
        xft_purchase_history: Table<u64, PurchaseRecord>,
    }

    struct PurchaseRecord has store {
        buyer: address,
        label_purchased_from: u64,  // 0 if open market
        marketplace_license_used: u64, // 0 if none
        purchase_timestamp: u64,
    }

    // ===== Functions =====
    // Initialize the marketplace
    fun init(ctx: &mut TxContext) {
        let sender = tx_context::sender(ctx);
        let marketplace_id = object::new(ctx);

        let marketplace = Marketplace {
            id: marketplace_id,
            owner: sender,
            fee_address: sender,
            fire_registry_id: object::id_from_address(@0x0), // Placeholder, set via admin
        };

        // Initialize dynamic fields
        // 1. Balance for holding SUI
        df::add(&mut marketplace.id, BalanceKey {}, balance::zero<SUI>());

        // 2. Operators list with owner as initial operator
        let operators_set = vec_set::empty<address>();
        vec_set::insert(&mut operators_set, sender);
        let operators = Operators { operators: operators_set };
        df::add(&mut marketplace.id, OperatorsKey {}, operators);

        // 3. Listings table
        df::add(&mut marketplace.id, ListingsKey {}, Listings {
            listings: table::new(ctx)
        });

        // 4. Active listings set
        df::add(&mut marketplace.id, ActiveListingsKey {}, ActiveListings {
            active_listings: vec_set::empty()
        });

        // 5. Listings by price index
        df::add(&mut marketplace.id, ListingsByPriceKey {}, ListingsByPrice {
            price_ranges: table::new(ctx)
        });

        // 6. Listings by NFT index
        df::add(&mut marketplace.id, ListingsByNFTKey {}, ListingsByNFT {
            listings_by_nft: table::new(ctx)
        });

        // 7. Listings by seller index
        df::add(&mut marketplace.id, ListingsBySellerKey {}, ListingsBySeller {
            listings_by_seller: table::new(ctx)
        });

        // 8. Purchase history tracking
        df::add(&mut marketplace.id, PurchaseHistoryKey {}, PurchaseHistory {
            buyer_to_labels: table::new(ctx),
            label_to_buyers: table::new(ctx),
            xft_purchase_history: table::new(ctx),
        });

        transfer::share_object(marketplace);
    }

    /// Admin function to set Fire Registry ID (called after Fire Registry is deployed)
    public fun set_fire_registry(
        marketplace: &mut Marketplace,
        fire_registry_id: ID,
        ctx: &mut TxContext
    ) {
        // Only owner can set Fire Registry
        assert!(tx_context::sender(ctx) == marketplace.owner, EInvalidOwner);
        marketplace.fire_registry_id = fire_registry_id;
    }

    // Create a new listing
    public fun create_listing(
        marketplace: &mut Marketplace,
        xft: XFT,                       // Take ownership for transfer to marketplace
        settings: vector<u64>,          // Full settings array with all listing parameters
        label_id: Option<ID>,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let seller = tx_context::sender(ctx);

        // Validate settings array has minimum required elements
        assert!(vector::length(&settings) >= 11, EInvalidQuantity);

        // Extend settings array if needed to store operator license usage
        while (vector::length(&settings) < 12) {
            vector::push_back(&mut settings, 0);
        };

        // Extract values from settings array
        let xft_global_id = *vector::borrow(&settings, 0);
        let price = *vector::borrow(&settings, 1);
        let price_type = *vector::borrow(&settings, 2);
        let quantity = *vector::borrow(&settings, 3);
        let marketplace_license = *vector::borrow(&settings, 4);
        let label_marketplace_fee = *vector::borrow(&settings, 5);
        let label_global_id = *vector::borrow(&settings, 6);
        let operator_id = *vector::borrow(&settings, 7);
        let xft_type = *vector::borrow(&settings, 8);
        let additional_fees = *vector::borrow(&settings, 9);
        let sale_expire = *vector::borrow(&settings, 10);
        
        // Validate inputs
        assert!(price > 0, EInvalidPrice);
        assert!(quantity > 0 && quantity <= xft::quantity(&xft), EInvalidQuantity);
        assert!(price_type == 1 || price_type == 2, EInvalidPrice); // 1=fixed, 2=dynamic
        assert!(xft::owner(&xft) == seller, EInvalidSeller);
        
        
        // Validate label ownership if label_global_id > 0
        let using_operator_license = false; // Track if seller is using operator license
        if (label_global_id > 0) {
            // Get Fire Registry to access other contracts
            let fire_registry_opt = dexsta::fire::get_fire_registry(marketplace.fire_registry_id);
            assert!(option::is_some(&fire_registry_opt), ENotAuthorized);
            let fire_registry = option::extract(&mut fire_registry_opt);

            // Get global registry to convert global ID to object ID
            let global_registry_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"global_registry"));
            assert!(option::is_some(&global_registry_opt), ENotAuthorized);

            // Get label object ID from global registry
            let label_object_id_opt = dexsta::registry::get_object_id_by_contract(global_registry_opt, label_global_id);
            assert!(option::is_some(&label_object_id_opt), ENotAuthorized);

            // Check if seller is label owner
            let is_label_owner = label::is_owner_by_global_id(
                global_registry_opt,
                label_global_id,
                seller,
                clock
            );
            
            // Check if item was last purchased from this label
            let purchase_history = df::borrow<PurchaseHistoryKey, PurchaseHistory>(&marketplace.id, PurchaseHistoryKey {});
            let was_purchased_from_label = if (table::contains(&purchase_history.xft_purchase_history, xft_global_id)) {
                let record = table::borrow(&purchase_history.xft_purchase_history, xft_global_id);
                record.buyer == seller && record.label_purchased_from == label_global_id
            } else {
                false
            };

            // Check if XFT is linked to this label
            let is_xft_linked_to_label = label::is_xft_linked_to_label(
                global_registry_opt,
                xft_global_id,
                label_global_id
            );

            let has_label_access = if (is_label_owner) {
                true
            } else if (was_purchased_from_label) {
                // Seller bought this item from this label - can sell under it without license
                true
            } else if (is_xft_linked_to_label) {
                // XFT is linked to this label - can sell under it without license
                true
            } else {
                // Check if seller has operator license for this label
                let operator_registry_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"operator"));
                if (option::is_some(&operator_registry_opt)) {
                    // Direct call to operator contract using IDs
                    let operator_registry_id = option::extract(&mut operator_registry_opt);
                    let global_registry_id = option::extract(&mut global_registry_opt);
                    let (has_license, operator_role, _license_global_id) = operator::check_operator_by_id(
                        operator_registry_id,
                        global_registry_id,
                        label_global_id,
                        seller,
                        clock
                    );
                    using_operator_license = has_license; // Track operator license usage
                    has_license // Any operator role works for marketplace listing
                } else {
                    false
                }
            };

            assert!(has_label_access, ENotAuthorized);

            // Store operator license usage in settings[11] for payment routing
            *vector::borrow_mut(&mut settings, 11) = if (using_operator_license) { 1 } else { 0 };
        };

        // Validate marketplace license if marketplace_license > 0
        if (marketplace_license > 0) {
            // Get Fire Registry to access label contract
            let fire_registry_opt = dexsta::fire::get_fire_registry(marketplace.fire_registry_id);
            assert!(option::is_some(&fire_registry_opt), ENotAuthorized);
            let fire_registry = option::extract(&mut fire_registry_opt);

            // Get global registry to validate license
            let global_registry_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"global_registry"));
            assert!(option::is_some(&global_registry_opt), ENotAuthorized);

            // Get label registry to validate marketplace license
            let label_registry_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"label"));
            assert!(option::is_some(&label_registry_opt), ENotAuthorized);

            // Validate marketplace license
            let (is_valid, parent_label_id, license_expiration, marketplace_fee_bps) = label::validate_marketplace_license(
                label_registry_opt,
                global_registry_opt,
                marketplace_license,
                clock
            );
            assert!(is_valid, EInvalidListing);

            // Get license object ID to check ownership
            let license_object_id_opt = registry::get_object_id_by_contract(global_registry_opt, marketplace_license);
            assert!(option::is_some(&license_object_id_opt), ENotAuthorized);

            // Check if seller owns the marketplace license
            let license_object_id = option::extract(&mut license_object_id_opt);
            let owns_license = label::owns_marketplace_license_by_id(
                label_registry_opt,
                license_object_id,
                seller
            );
            assert!(owns_license, ENotAuthorized);

            // Update label marketplace fee in settings if provided by license
            if (marketplace_fee_bps > 0) {
                *vector::borrow_mut(&mut settings, 5) = marketplace_fee_bps;
            };
        };
        
        

        // Create listing
        let listing_id = object::new(ctx);

        // Set expiration time - use settings[10] if provided, otherwise default
        let expiration_time = if (sale_expire > 0) {
            sale_expire
        } else {
            clock::timestamp_ms(clock) + DEFAULT_LISTING_DURATION
        };

        let listing = Listing {
            id: object::uid_to_inner(&listing_id),
            nft_id: object::uid_to_inner(xft::id(&xft)),
            seller,
            price, // Base price from settings[1]
            quantity, // Quantity from settings[3]
            expiration_time,
            active: true,
            settings,
            label_id,
            incremental_price: *vector::borrow(&settings, 2) == 2, // Deprecated - use settings[2] for pricing type
            buy_count: 0,
        };

        // Add listing to marketplace
        let listing_id_inner = object::uid_to_inner(&listing_id);
        let listings = df::borrow_mut<ListingsKey, Listings>(&mut marketplace.id, ListingsKey {});
        table::add(&mut listings.listings, listing_id_inner, listing);

        // Add to active listings
        let active_listings = df::borrow_mut<ActiveListingsKey, ActiveListings>(&mut marketplace.id, ActiveListingsKey {});
        vec_set::insert(&mut active_listings.active_listings, listing_id_inner);

        // Add to listings by price index
        let price_range = price / 100000000; // Group by 0.1 SUI increments
        let listings_by_price = df::borrow_mut<ListingsByPriceKey, ListingsByPrice>(&mut marketplace.id, ListingsByPriceKey {});

        if (!table::contains(&listings_by_price.price_ranges, price_range)) {
            table::add(&mut listings_by_price.price_ranges, price_range, vector::empty<ID>());
        };

        let price_listings = table::borrow_mut(&mut listings_by_price.price_ranges, price_range);
        vector::push_back(price_listings, listing_id_inner);

        // Add to listings by XFT index
        let xft_id = object::uid_to_inner(xft::id(&xft));
        let listings_by_nft = df::borrow_mut<ListingsByNFTKey, ListingsByNFT>(&mut marketplace.id, ListingsByNFTKey {});

        if (!table::contains(&listings_by_nft.listings_by_nft, xft_id)) {
            table::add(&mut listings_by_nft.listings_by_nft, xft_id, vector::empty<ID>());
        };

        let nft_listings = table::borrow_mut(&mut listings_by_nft.listings_by_nft, xft_id);
        vector::push_back(nft_listings, listing_id_inner);

        // Add to listings by seller index
        let listings_by_seller = df::borrow_mut<ListingsBySellerKey, ListingsBySeller>(&mut marketplace.id, ListingsBySellerKey {});

        if (!table::contains(&listings_by_seller.listings_by_seller, seller)) {
            table::add(&mut listings_by_seller.listings_by_seller, seller, vector::empty<ID>());
        };

        let seller_listings = table::borrow_mut(&mut listings_by_seller.listings_by_seller, seller);
        vector::push_back(seller_listings, listing_id_inner);

        // Transfer XFT to marketplace (escrow)
        // For marketplace listings, we transfer the entire XFT to marketplace for escrow
        transfer::transfer(xft, object::id_to_address(&object::uid_to_inner(&marketplace.id)));

        // Emit event with complete listing information
        event::emit(ListingCreated {
            listing_id: object::uid_to_inner(&listing_id),
            xft_id: xft_global_id,          // XFT global ID from settings[0]
            seller,
            price,
            quantity,
            expiration_time,
            label_global_id,        // Label the item is being sold under
            marketplace_license,    // Marketplace license used (0 if none)
            xft_type,              // XFT type for reference
        });

        object::delete(listing_id);
    }

    // Cancel a listing
    public fun cancel_listing(
        marketplace: &mut Marketplace,
        listing_id: ID,
        ctx: &mut TxContext
    ): XFT {
        let sender = tx_context::sender(ctx);

        // Get listing info first
        let (listing_seller, listing_quantity, listing_xft_id, xft_global_id) = {
            let listings = df::borrow<ListingsKey, Listings>(&marketplace.id, ListingsKey {});
            assert!(table::contains(&listings.listings, listing_id), EInvalidListing);
            let listing = table::borrow(&listings.listings, listing_id);

            // Validate
            assert!(listing.seller == sender, EInvalidSeller);
            assert!(listing.active, EListingNotActive);

            let xft_global_id = *vector::borrow(&listing.settings, 0); // XFT global ID from settings[0]
            (listing.seller, listing.quantity, listing.nft_id, xft_global_id)
        };

        // Update listing
        let listings = df::borrow_mut<ListingsKey, Listings>(&mut marketplace.id, ListingsKey {});
        let listing = table::borrow_mut(&mut listings.listings, listing_id);
        listing.active = false;

        // Remove from active listings
        let active_listings = df::borrow_mut<ActiveListingsKey, ActiveListings>(&mut marketplace.id, ActiveListingsKey {});
        if (vec_set::contains(&active_listings.active_listings, &listing_id)) {
            vec_set::remove(&mut active_listings.active_listings, &listing_id);
        };

        // Get XFT from marketplace escrow
        let xft = df::remove<ID, XFT>(&mut marketplace.id, listing_xft_id);

        // Emit event
        event::emit(ListingCancelled {
            listing_id,
            xft_id: xft_global_id,
            seller: listing_seller,
        });

        // Return XFT to caller (who will transfer to seller)
        xft
    }

    // Buy XFT from a listing
    public fun buy(
        marketplace: &mut Marketplace,
        listing_id: ID,
        payment: &mut Coin<SUI>,
        quantity: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ): XFT {
        let buyer = tx_context::sender(ctx);

        // Get listing info and validate
        let (listing_seller, listing_nft_id, listing_price, listing_incremental_price) = {
            let listings = df::borrow<ListingsKey, Listings>(&marketplace.id, ListingsKey {});
            assert!(table::contains(&listings.listings, listing_id), EInvalidListing);
            let listing = table::borrow(&listings.listings, listing_id);

            // Validate
            assert!(listing.active, EListingNotActive);
            assert!(clock::timestamp_ms(clock) <= listing.expiration_time, EListingExpired);
            assert!(quantity > 0 && quantity <= listing.quantity, EInvalidQuantity);
            assert!(buyer != listing.seller, EInvalidBuyer);

            (listing.seller, listing.nft_id, listing.price, listing.incremental_price)
        };

        // Update buy count and calculate price
        let listings = df::borrow_mut<ListingsKey, Listings>(&mut marketplace.id, ListingsKey {});
        let listing = table::borrow_mut(&mut listings.listings, listing_id);
        listing.buy_count = listing.buy_count + 1;

        // Calculate price based on pricing type from settings
        // settings[2] = price type: 1=fixed, 2=dynamic (5% increase per sale)
        let price_type = *vector::borrow(&listing.settings, 2);
        let price = if (price_type == 2) {
            // Dynamic pricing: increase by 5% for each previous sale
            let price_multiplier = 100 + (listing.buy_count * 5); // 5% per sale
            (listing_price * price_multiplier) / 100
        } else {
            // Fixed pricing (type 1 or default)
            listing_price
        };

        // Get listing details for fee calculation
        let (label_global_id, marketplace_license, label_marketplace_fee_bps, xft_global_id, using_operator_license) = {
            let listings = df::borrow<ListingsKey, Listings>(&marketplace.id, ListingsKey {});
            let listing = table::borrow(&listings.listings, listing_id);

            let label_global_id = *vector::borrow(&listing.settings, 6);
            let marketplace_license = *vector::borrow(&listing.settings, 4);
            let label_marketplace_fee_bps = *vector::borrow(&listing.settings, 5);
            let xft_global_id = *vector::borrow(&listing.settings, 0);
            let using_operator_license = if (vector::length(&listing.settings) > 11) {
                *vector::borrow(&listing.settings, 11) == 1
            } else {
                false
            };

            (label_global_id, marketplace_license, label_marketplace_fee_bps, xft_global_id, using_operator_license)
        };

        // Calculate fees
        let total_price = price * quantity;
        let platform_fee = (total_price * PLATFORM_FEE_BPS) / 10000;

        // Calculate label fee based on marketplace usage
        let label_fee = if (label_global_id > 0 && marketplace_license == 0) {
            // Case 1: Selling under a label (seller is label owner/operator), no marketplace license
            // Label gets the marketplace fee percentage
            (total_price * label_marketplace_fee_bps) / 10000
        } else if (label_global_id == 0 && marketplace_license > 0) {
            // Case 3: Using marketplace license, no direct label
            // Marketplace license fee goes to the label the license is linked to
            (total_price * label_marketplace_fee_bps) / 10000
        } else {
            // Case 2: Open market (no label, no marketplace license)
            0
        };

        let seller_amount = total_price - platform_fee - label_fee;

        // Check if buyer has enough funds
        assert!(coin::value(payment) >= total_price, EInsufficientFunds);

        // Process payment splits
        let platform_payment = coin::split(payment, platform_fee, ctx);
        let seller_payment = coin::split(payment, seller_amount, ctx);

        // Process label payment if applicable
        let label_payment_opt = if (label_fee > 0) {
            option::some(coin::split(payment, label_fee, ctx))
        } else {
            option::none()
        };

        // Get Fire Registry for contract lookups
        let fire_registry_opt = fire::get_fire_registry(marketplace.fire_registry_id);
        assert!(option::is_some(&fire_registry_opt), ENotAuthorized);
        let fire_registry = option::extract(&mut fire_registry_opt);

        // Get platform label ID for platform fees
        let platform_label_id_opt = fire::get_platform_label_id(fire_registry);

        // Transfer platform fees to platform label
        if (option::is_some(&platform_label_id_opt)) {
            let platform_label_id = option::extract(&mut platform_label_id_opt);
            // Deposit platform fee to platform label
            label::deposit_to_label_by_id(platform_label_id, platform_payment, ctx);
        } else {
            // Fallback to marketplace fee address
            transfer::public_transfer(platform_payment, marketplace.fee_address);
        };

        // Transfer seller payment - route based on operator license usage
        if (label_global_id > 0 && using_operator_license) {
            // If seller used operator license, payment goes to the label (not the seller)
            label::deposit_to_label_by_global_id(label_global_id, seller_payment, ctx);
        } else {
            // Normal case - payment goes to seller
            transfer::public_transfer(seller_payment, listing_seller);
        };

        // Handle label fee distribution
        if (option::is_some(&label_payment_opt)) {
            let label_payment = option::extract(&mut label_payment_opt);

            if (label_global_id > 0 && marketplace_license == 0) {
                // Case 1: Direct label sale - fee goes to the label being sold under
                label::deposit_to_label_by_global_id(label_global_id, label_payment, ctx);
            } else if (label_global_id == 0 && marketplace_license > 0) {
                // Case 3: Marketplace license - fee goes to parent label of the license
                let global_registry_opt = fire::get_contract_address(fire_registry, string::utf8(b"global_registry"));
                assert!(option::is_some(&global_registry_opt), ENotAuthorized);

                let label_registry_opt = fire::get_contract_address(fire_registry, string::utf8(b"label"));
                assert!(option::is_some(&label_registry_opt), ENotAuthorized);

                // Get parent label ID from marketplace license
                let (is_valid, parent_label_id, _, _) = label::validate_marketplace_license(
                    label_registry_opt,
                    global_registry_opt,
                    marketplace_license,
                    clock
                );

                if (is_valid && parent_label_id > 0) {
                    label::deposit_to_label_by_global_id(parent_label_id, label_payment, ctx);
                } else {
                    // Fallback to seller if license validation fails
                    transfer::public_transfer(label_payment, listing_seller);
                };
            };
        };

        // Update listing and check if it should be deactivated
        listing.quantity = listing.quantity - quantity;
        let should_deactivate = listing.quantity == 0;
        if (should_deactivate) {
            listing.active = false;
        };

        // Remove from active listings if needed
        if (should_deactivate) {
            let active_listings = df::borrow_mut<ActiveListingsKey, ActiveListings>(&mut marketplace.id, ActiveListingsKey {});
            if (vec_set::contains(&active_listings.active_listings, &listing_id)) {
                vec_set::remove(&mut active_listings.active_listings, &listing_id);
            };
        };

        // Record purchase history - VERY IMPORTANT for tracking
        let purchase_history = df::borrow_mut<PurchaseHistoryKey, PurchaseHistory>(&mut marketplace.id, PurchaseHistoryKey {});
        let current_time = clock::timestamp_ms(clock);

        // Determine which label this was purchased from
        let label_purchased_from = if (label_global_id > 0) {
            label_global_id  // Direct label sale
        } else if (marketplace_license > 0) {
            // Get parent label from marketplace license
            let global_registry_opt = fire::get_contract_address(fire_registry, string::utf8(b"global_registry"));
            let label_registry_opt = fire::get_contract_address(fire_registry, string::utf8(b"label"));

            if (option::is_some(&global_registry_opt) && option::is_some(&label_registry_opt)) {
                let (is_valid, parent_label_id, _, _) = label::validate_marketplace_license(
                    label_registry_opt,
                    global_registry_opt,
                    marketplace_license,
                    clock
                );
                if (is_valid) { parent_label_id } else { 0 }
            } else { 0 }
        } else {
            0  // Open market
        };

        // Record XFT purchase history
        let purchase_record = PurchaseRecord {
            buyer,
            label_purchased_from,
            marketplace_license_used: marketplace_license,
            purchase_timestamp: current_time,
        };
        table::add(&mut purchase_history.xft_purchase_history, xft_global_id, purchase_record);

        // Update buyer -> labels mapping
        if (label_purchased_from > 0) {
            if (!table::contains(&purchase_history.buyer_to_labels, buyer)) {
                table::add(&mut purchase_history.buyer_to_labels, buyer, vector::empty<u64>());
            };
            let buyer_labels = table::borrow_mut(&mut purchase_history.buyer_to_labels, buyer);

            // Add label if not already present
            let (already_purchased, _) = vector::index_of(buyer_labels, &label_purchased_from);
            if (!already_purchased) {
                vector::push_back(buyer_labels, label_purchased_from);
            };

            // Update label -> buyers mapping
            if (!table::contains(&purchase_history.label_to_buyers, label_purchased_from)) {
                table::add(&mut purchase_history.label_to_buyers, label_purchased_from, vector::empty<address>());
            };
            let label_buyers = table::borrow_mut(&mut purchase_history.label_to_buyers, label_purchased_from);

            // Add buyer if not already present
            let (already_recorded, _) = vector::index_of(label_buyers, &buyer);
            if (!already_recorded) {
                vector::push_back(label_buyers, buyer);
            };
        };

        // Get XFT from marketplace escrow and transfer to buyer
        let xft = df::remove<ID, XFT>(&mut marketplace.id, listing_nft_id);

        // Emit event
        event::emit(Sale {
            listing_id,
            xft_id: xft_global_id,
            seller: listing_seller,
            buyer,
            price,
            quantity,
            platform_fee,
            label_fee,
        });

        // Return XFT to buyer
        xft
    }
    

    // Get all active listings
    public fun get_active_listings(marketplace: &Marketplace): vector<ID> {
        let active_listings = df::borrow<ActiveListingsKey, ActiveListings>(&marketplace.id, ActiveListingsKey {});
        vec_set::into_keys(active_listings.active_listings)
    }

    // Get listing details
    public fun get_listing(marketplace: &Marketplace, listing_id: ID): &Listing {
        let listings = df::borrow<ListingsKey, Listings>(&marketplace.id, ListingsKey {});
        table::borrow(&listings.listings, listing_id)
    }

    /// Get listings by price range
    /// Returns listings with prices in the specified range (inclusive)
    /// Prices are in SUI (1 SUI = 1,000,000,000 units)
    public fun get_listings_by_price_range(
        marketplace: &Marketplace,
        min_price: u64,
        max_price: u64,
        max_results: u64
    ): vector<ID> {
        let min_range = min_price / 100000000; // Convert to 0.1 SUI increments
        let max_range = max_price / 100000000;

        let listings_by_price = df::borrow<ListingsByPriceKey, ListingsByPrice>(&marketplace.id, ListingsByPriceKey {});
        let results = vector::empty<ID>();
        let result_count = 0;

        let current_range = min_range;
        while (current_range <= max_range && result_count < max_results) {
            if (table::contains(&listings_by_price.price_ranges, current_range)) {
                let range_listings = table::borrow(&listings_by_price.price_ranges, current_range);

                let i = 0;
                while (i < vector::length(range_listings) && result_count < max_results) {
                    let listing_id = *vector::borrow(range_listings, i);

                    // Verify the listing is still active
                    let listings = df::borrow<ListingsKey, Listings>(&marketplace.id, ListingsKey {});
                    let listing = table::borrow(&listings.listings, listing_id);

                    if (listing.active) {
                        vector::push_back(&mut results, listing_id);
                        result_count = result_count + 1;
                    };

                    i = i + 1;
                };
            };

            current_range = current_range + 1;
        };

        results
    }

    /// Get listings for a specific NFT
    public fun get_listings_by_nft(
        marketplace: &Marketplace,
        nft_id: ID,
        max_results: u64
    ): vector<ID> {
        let listings_by_nft = df::borrow<ListingsByNFTKey, ListingsByNFT>(&marketplace.id, ListingsByNFTKey {});

        if (table::contains(&listings_by_nft.listings_by_nft, nft_id)) {
            let nft_listings = table::borrow(&listings_by_nft.listings_by_nft, nft_id);
            let results = vector::empty<ID>();
            let result_count = 0;

            let i = 0;
            while (i < vector::length(nft_listings) && result_count < max_results) {
                let listing_id = *vector::borrow(nft_listings, i);

                // Verify the listing is still active
                let listings = df::borrow<ListingsKey, Listings>(&marketplace.id, ListingsKey {});
                let listing = table::borrow(&listings.listings, listing_id);

                if (listing.active) {
                    vector::push_back(&mut results, listing_id);
                    result_count = result_count + 1;
                };

                i = i + 1;
            };

            results
        } else {
            vector::empty()
        }
    }

    /// Get listings by seller
    public fun get_listings_by_seller(
        marketplace: &Marketplace,
        seller: address,
        max_results: u64
    ): vector<ID> {
        let listings_by_seller = df::borrow<ListingsBySellerKey, ListingsBySeller>(&marketplace.id, ListingsBySellerKey {});

        if (table::contains(&listings_by_seller.listings_by_seller, seller)) {
            let seller_listings = table::borrow(&listings_by_seller.listings_by_seller, seller);
            let results = vector::empty<ID>();
            let result_count = 0;

            let i = 0;
            while (i < vector::length(seller_listings) && result_count < max_results) {
                let listing_id = *vector::borrow(seller_listings, i);

                // Verify the listing is still active
                let listings = df::borrow<ListingsKey, Listings>(&marketplace.id, ListingsKey {});
                let listing = table::borrow(&listings.listings, listing_id);

                if (listing.active) {
                    vector::push_back(&mut results, listing_id);
                    result_count = result_count + 1;
                };

                i = i + 1;
            };

            results
        } else {
            vector::empty()
        }
    }

    // Get marketplace balance
    public fun balance(marketplace: &Marketplace): u64 {
        let marketplace_balance = df::borrow<BalanceKey, Balance<SUI>>(&marketplace.id, BalanceKey {});
        balance::value(marketplace_balance)
    }

    /// Get labels a buyer has purchased from
    public fun get_buyer_labels(marketplace: &Marketplace, buyer: address): vector<u64> {
        let purchase_history = df::borrow<PurchaseHistoryKey, PurchaseHistory>(&marketplace.id, PurchaseHistoryKey {});

        if (table::contains(&purchase_history.buyer_to_labels, buyer)) {
            *table::borrow(&purchase_history.buyer_to_labels, buyer)
        } else {
            vector::empty()
        }
    }

    /// Get buyers who have purchased from a label
    public fun get_label_buyers(marketplace: &Marketplace, label_global_id: u64): vector<address> {
        let purchase_history = df::borrow<PurchaseHistoryKey, PurchaseHistory>(&marketplace.id, PurchaseHistoryKey {});

        if (table::contains(&purchase_history.label_to_buyers, label_global_id)) {
            *table::borrow(&purchase_history.label_to_buyers, label_global_id)
        } else {
            vector::empty()
        }
    }

    /// Get purchase record for a specific XFT
    public fun get_xft_purchase_history(marketplace: &Marketplace, xft_global_id: u64): (bool, address, u64, u64, u64) {
        let purchase_history = df::borrow<PurchaseHistoryKey, PurchaseHistory>(&marketplace.id, PurchaseHistoryKey {});

        if (table::contains(&purchase_history.xft_purchase_history, xft_global_id)) {
            let record = table::borrow(&purchase_history.xft_purchase_history, xft_global_id);
            (true, record.buyer, record.label_purchased_from, record.marketplace_license_used, record.purchase_timestamp)
        } else {
            (false, @0x0, 0, 0, 0)
        }
    }
}
