///
/// # NFT Module
///
/// This module implements the XFT (Extended Fungible Token) system.
/// XFTs are advanced NFTs that can:
/// - Store SUI and other assets as "asset baskets"
/// - Link to parent labels for organization
/// - Support time-locking functionality
/// - Store custom metadata attributes

///
/// ## Key Features
///
/// * XFTs can store multiple asset types (SUI, tokens, other NFTs)
/// * XFTs link to labels for hierarchical organization
/// * XFTs support time-locking for secure asset storage
/// * XFTs are always transferable (unlike labels)
/// * XFTs don't expire (unlike labels)
///
/// ## Dynamic Fields
///
/// Each XFT has the following dynamic fields:
/// * `BalanceKey` - Stores the XFT's SUI balance
/// * `MetadataKey` - Stores the XFT's metadata attributes
///
/// ## Label Settings Vector (settings: vector<u64>)
///
/// The settings vector contains configuration parameters for labels:
/// * 0: link to label
/// * 1: registration in years
/// * 2: operator license
/// * 3: xft type
/// * 4: if type is license, license term
/// * 5: 0 false, 1 true // formerly mint pass
/// * 6: quantity
/// * 7: label registration expire
/// * 8: unused.. use to be redeem days
/// * 9: transferable
/// * 10: wrapto
/// * 11: label split for marketplace license
/// * 12: label vault locked
/// * 13: label vault unlock date
///
/// ## Label Types (via settings[3] - xft type)
///
/// * Type 1: Lead Label
/// * Type 2: Profile Label
/// * Type 3: Tag Label
/// * Type 4: Chapter Label
/// * Type 5: Operator License
/// * Type 6: Marketplace License
/// * Type 7: Art/tickets/gaming
/// * Type 8: wrappedTo
/// * Type 9: open

module dexsta::xft {
    use std::string::{Self, String};
    use std::vector;
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::url::{Self, Url};
    use sui::event;
    use sui::package;
    use sui::display;
    use sui::balance::{Self, Balance};
    use sui::sui::SUI;
    use sui::coin::{Self, Coin};
    use sui::table::{Self, Table};
    use sui::dynamic_field as df;
    use sui::vec_set::{Self, VecSet};
    use sui::clock::{Self, Clock};
    use sui::type_name::{Self, TypeName};
    use std::option::{Self, Option};

    // ===== Constants =====
    const MAX_NAME_LENGTH: u64 = 32;
    const MAX_DESCRIPTION_LENGTH: u64 = 512;
    const MAX_URL_LENGTH: u64 = 512;

    // ===== Errors =====
    const ENameTooLong: u64 = 0;
    const EDescriptionTooLong: u64 = 1;
    const EUrlTooLong: u64 = 2;
    const EInvalidOwner: u64 = 3;
    const EInvalidQuantity: u64 = 5;
    const EInsufficientBalance: u64 = 6;
    const ENotAuthorized: u64 = 7;

    // ===== Events =====
    struct XFTCreated has copy, drop {
        xft_id: ID,
        global_id: u64,
        name: String,
        owner: address,
        expiration_time: u64,
    }

    struct XFTTransferred has copy, drop {
        xft_id: ID,
        global_id: u64,
        name: String,
        from: address,
        to: address,
    }





    struct VaultLocked has copy, drop {
        xft_id: ID,
        owner: address,
        unlock_timestamp: u64,
    }

    struct VaultUnlocked has copy, drop {
        xft_id: ID,
        owner: address,
    }

    struct XFTLinkedToLabel has copy, drop {
        xft_global_id: u64,
        label_id: ID,
        linked_by: address,
    }

    struct XFTWrapped has copy, drop {
        wrapped_xft_id: u64,
        target_xft_id: u64,
        wrapped_by: address,
    }

    struct XFTUnwrapped has copy, drop {
        unwrapped_xft_id: u64,
        from_xft_id: u64,
        unwrapped_by: address,
    }

    struct ObjectWithdrawn has copy, drop {
        xft_id: ID,
        xft_global_id: u64,
        object_id: ID,
        object_type: String,
        amount: Option<u64>,  // For coins/tokens, none for NFTs
        withdrawn_by: address,
        withdrawal_timestamp: u64,
    }

    // ===== One-Time Witness for the package =====
    struct NFT has drop {}

    // ===== Dynamic Field Keys =====
    struct BalanceKey has store, copy, drop {}
    struct MetadataKey has store, copy, drop {}
    struct WrappedXFTsKey has store, copy, drop {}

    // ===== Objects =====

    /// Global XFT Registry for auto-incrementing IDs and search functionality
    struct XFTRegistry has key {
        id: UID,
        next_global_id: u64, // Auto-incrementing global ID counter (starts at 2)
        // Mapping from global ID to object ID
        global_to_object: Table<u64, ID>,
        // Mapping from object ID to global ID
        object_to_global: Table<ID, u64>,
        // Total XFTs created
        total_xfts: u64,
        // Fee configuration
        fee_address: address,
        fire_registry_id: ID, // Reference to Fire Registry for fees and global IDs
    }

    struct XFT has key, store {
        id: UID,
        global_id: u64, // Auto-incrementing global ID (#2, #3, #4, etc.)
        name: String,
        owner: address,
        creation_time: u64,
        expiration_time: u64,
        settings: vector<u64>, // Same settings structure as labels
        ipfs_hash: String,
    }



    struct Metadata has store {
        attributes: Table<String, String>,
    }

    /// Stores XFTs that are wrapped to this XFT
    struct WrappedXFTs has store {
        // Array of XFT global IDs wrapped to this XFT
        wrapped_xft_global_ids: vector<u64>,
    }

    // ===== Functions =====
    fun init(witness: NFT, ctx: &mut TxContext) {
        let publisher = package::claim(witness, ctx);

        let keys = vector[
            string::utf8(b"name"),
            string::utf8(b"description"),
            string::utf8(b"image_url"),
            string::utf8(b"creator"),
            string::utf8(b"global_id"),
            string::utf8(b"project_url"),
        ];

        let values = vector[
            string::utf8(b"{name}"),
            string::utf8(b"{description}"),
            string::utf8(b"{url}"),
            string::utf8(b"{creator}"),
            string::utf8(b"#{global_id}"),
            string::utf8(b"https://xft.red"),
        ];

        let display_info = display::new_with_fields<XFT>(
            &publisher, keys, values, ctx
        );

        display::update_version(&mut display_info);

        // Create global XFT registry
        let registry_id = object::new(ctx);
        let registry = XFTRegistry {
            id: registry_id,
            next_global_id: 2, // Start at #2 (reserve #1 for special use)
            global_to_object: table::new(ctx),
            object_to_global: table::new(ctx),
            total_xfts: 0,
            fee_address: tx_context::sender(ctx), // Default to deployer
            fire_registry_id: object::id_from_address(@0x0), // Placeholder, set via admin
        };



        transfer::public_transfer(publisher, tx_context::sender(ctx));
        transfer::public_transfer(display_info, tx_context::sender(ctx));
        transfer::share_object(registry);
    }

    // Create a new XFT
    public fun mint(
        registry: &mut XFTRegistry,
        fire_registry: &dexsta::fire::FireRegistry,
        parent_label: Option<&mut dexsta::label::Label>, // Optional parent label for linking
        name: String,
        settings: vector<u64>,
        ipfs_hash: String,
        clock: &Clock,
        ctx: &mut TxContext
    ): XFT {
        // Validate inputs
        assert!(string::length(&name) <= MAX_NAME_LENGTH, ENameTooLong);
        assert!(string::length(&ipfs_hash) <= MAX_URL_LENGTH, EUrlTooLong);

        // Validate settings array has minimum required elements
        assert!(vector::length(&settings) >= 10, EInvalidQuantity);

        // Validate XFT-specific settings
        // settings[3] must be 8 (XFT type)
        assert!(*vector::borrow(&settings, 3) == 8, EInvalidQuantity);

        // settings[9] must be 1 (transferable - all XFTs are transferable)
        assert!(*vector::borrow(&settings, 9) == 1, EInvalidQuantity);

        let creator = tx_context::sender(ctx);
        let xft_id = object::new(ctx);

        // Get incremental global ID from registry
        let global_id = registry.next_global_id;
        registry.next_global_id = registry.next_global_id + 1;
        registry.total_xfts = registry.total_xfts + 1;

        // Store global ID mappings
        let xft_object_id = object::uid_to_inner(&xft_id);
        table::add(&mut registry.global_to_object, global_id, xft_object_id);
        table::add(&mut registry.object_to_global, xft_object_id, global_id);

        // Calculate timestamps
        let current_time = clock::timestamp_ms(clock);

        // Create XFT (no expiration time for XFTs)
        let xft = XFT {
            id: xft_id,
            global_id,
            name,
            owner: creator,
            creation_time: current_time,
            expiration_time: 0, // XFTs don't expire
            settings,
            ipfs_hash,
        };
        
        // Initialize dynamic fields
        // 1. Balance for holding SUI
        df::add(&mut xft.id, BalanceKey {}, balance::zero<SUI>());

        // 2. Metadata table
        df::add(&mut xft.id, MetadataKey {}, Metadata {
            attributes: table::new(ctx)
        });

        // 3. Wrapped XFTs array
        df::add(&mut xft.id, WrappedXFTsKey {}, WrappedXFTs {
            wrapped_xft_global_ids: vector::empty<u64>(),
        });
        

        // Link to parent label if settings[0] > 0
        let linked_label_id = *vector::borrow(&settings, 0);
        if (linked_label_id > 0) {
            // Parent label must be provided for linking
            assert!(option::is_some(&parent_label), ENotAuthorized);
            let parent_label_ref = option::borrow_mut(&mut parent_label);

            // Verify the parent label global ID matches
            assert!(dexsta::label::global_id(parent_label_ref) == linked_label_id, EInvalidOwner);

            // Check if parent label is expired
            assert!(!dexsta::label::is_expired(parent_label_ref, clock), ENotAuthorized);

            // Check authorization - caller must be label owner OR super operator
            let is_label_owner = (creator == dexsta::label::owner(parent_label_ref));
            let is_super_operator = false;

            if (!is_label_owner) {
                // Check if caller has super operator license for this label
                let operator_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"operator"));
                if (option::is_some(&operator_contract_opt)) {
                    // Note: This is a placeholder - in practice we need the actual OperatorRegistry object
                    // For now, we'll assume no operator license exists
                    let is_operator_result = false;
                    let operator_role = 0;
                    is_super_operator = (is_operator_result && operator_role == 1);
                };
            };

            assert!(is_label_owner || is_super_operator, ENotAuthorized);

            // Add this XFT to parent label's linked array
            dexsta::label::add_linked_xft(
                parent_label_ref,
                global_id,
                fire_registry,
                option::none(), // No operator license needed since we already validated
                clock,
                ctx
            );
        };
        
        // Emit event
        event::emit(XFTCreated {
            xft_id: xft_object_id,
            global_id,
            name,
            owner: creator,
            expiration_time: 0, // XFTs don't expire
        });

        xft
    }
    
    
    
    // ===== Asset Storage =====
    // Note: Users can directly transfer any assets (tokens, NFTs, etc.) to the XFT object ID.
    // No deposit function is needed - Sui handles direct transfers to object IDs automatically.
    // Use RPC methods to query stored assets:
    // - sui_getOwnedObjects(xft_object_id) - Get all objects owned by this XFT
    // - sui_getDynamicFields(xft_object_id) - Get all dynamic fields attached to this XFT
    
    /// Withdraw any coin (SUI, USDC, etc.) from XFT vault with amount tracking
    public fun withdraw_coin<T>(
        xft: &mut XFT,
        object_id: ID,
        clock: &Clock,
        ctx: &mut TxContext
    ): Coin<T> {
        let sender = tx_context::sender(ctx);

        // Check time lock
        let is_locked = *vector::borrow(&xft.settings, 12) == 1;
        if (is_locked) {
            let unlock_date = *vector::borrow(&xft.settings, 13);
            let current_time = clock::timestamp_ms(clock);
            assert!(current_time >= unlock_date, ENotAuthorized);
        };

        // Get current timestamp for event
        let withdrawal_timestamp = clock::timestamp_ms(clock);

        // Remove the coin from dynamic fields
        let withdrawn_coin = df::remove<ID, Coin<T>>(&mut xft.id, object_id);

        // Get coin amount for detailed tracking
        let coin_amount = coin::value(&withdrawn_coin);

        // Get coin type information
        let coin_type_name = type_name::get<Coin<T>>();
        let coin_type_string = type_name::into_string(coin_type_name);

        // Emit detailed withdrawal event
        event::emit(ObjectWithdrawn {
            xft_id: object::uid_to_inner(&xft.id),
            xft_global_id: xft.global_id,
            object_id,
            object_type: coin_type_string,
            amount: option::some(coin_amount),
            withdrawn_by: sender,
            withdrawal_timestamp,
        });

        withdrawn_coin
    }

    /// Withdraw any object (XFTs, NFTs, etc.) from XFT vault
    public fun withdraw_object<T: key + store>(
        xft: &mut XFT,
        object_id: ID,
        clock: &Clock,
        ctx: &mut TxContext
    ): T {
        let sender = tx_context::sender(ctx);

        // Check time lock
        let is_locked = *vector::borrow(&xft.settings, 12) == 1;
        if (is_locked) {
            let unlock_date = *vector::borrow(&xft.settings, 13);
            let current_time = clock::timestamp_ms(clock);
            assert!(current_time >= unlock_date, ENotAuthorized);
        };

        // Get current timestamp for event
        let withdrawal_timestamp = clock::timestamp_ms(clock);

        // Get object type information
        let object_type_name = type_name::get<T>();
        let object_type_string = type_name::into_string(object_type_name);

        // Remove the object from dynamic fields
        let withdrawn_object = df::remove<ID, T>(&mut xft.id, object_id);

        // Emit detailed withdrawal event (no amount for non-coin objects)
        event::emit(ObjectWithdrawn {
            xft_id: object::uid_to_inner(&xft.id),
            xft_global_id: xft.global_id,
            object_id,
            object_type: object_type_string,
            amount: option::none<u64>(),
            withdrawn_by: sender,
            withdrawal_timestamp,
        });

        withdrawn_object
    }

    // ===== Wrap/Unwrap Functions =====

    /// Wrap an XFT into a new 1-of-1 XFT (frontend calls with global ID)
    /// Creates a new wrapped XFT and stores the original in escrow
    public fun wrap_xft(
        registry: &mut XFTRegistry,
        fire_registry: &dexsta::fire::FireRegistry,
        source_xft: XFT, // The XFT being wrapped (user transfers to function)
        clock: &Clock,
        ctx: &mut TxContext
    ): XFT {
        let sender = tx_context::sender(ctx);

        // Get source XFT details before storing in escrow
        let source_global_id = source_xft.global_id;
        let source_name = source_xft.name;
        let source_settings = source_xft.settings;
        let source_ipfs_hash = source_xft.ipfs_hash;
        
        // Check if source XFT is linked to a label
        let linked_label_id = *vector::borrow(&source_settings, 0);

        // Create new wrapped XFT with new global ID
        let new_global_id = registry.next_global_id;
        registry.next_global_id = registry.next_global_id + 1;
        registry.total_xfts = registry.total_xfts + 1;

        let wrapped_xft_id = object::new(ctx);
        let wrapped_object_id = object::uid_to_inner(&wrapped_xft_id);

        // Store new XFT in registry mappings
        table::add(&mut registry.global_to_object, new_global_id, wrapped_object_id);
        table::add(&mut registry.object_to_global, wrapped_object_id, new_global_id);

        // Create wrapped XFT settings (mark as 1-of-1)
        let wrapped_settings = source_settings;
        *vector::borrow_mut(&mut wrapped_settings, 10) = source_global_id; // Store original global ID in wrapto field

        let current_time = clock::timestamp_ms(clock);
        let wrapped_xft = XFT {
            id: wrapped_xft_id,
            global_id: new_global_id,
            name: string::utf8(b"Wrapped "),
            owner: sender,
            creation_time: current_time,
            expiration_time: 0,
            settings: wrapped_settings,
            ipfs_hash: source_ipfs_hash,
        };
        string::append(&mut wrapped_xft.name, source_name);

        // Initialize dynamic fields for wrapped XFT
        df::add(&mut wrapped_xft.id, BalanceKey {}, balance::zero<SUI>());
        df::add(&mut wrapped_xft.id, MetadataKey {}, Metadata {
            attributes: table::new(ctx)
        });
        df::add(&mut wrapped_xft.id, WrappedXFTsKey {}, WrappedXFTs {
            wrapped_xft_global_ids: vector::empty<u64>(),
        });

        // Store original XFT in escrow (as dynamic field of wrapped XFT)
        df::add(&mut wrapped_xft.id, source_global_id, source_xft);

        // If original was linked to a label, update label to link to new wrapped XFT
        if (linked_label_id > 0) {
            // Get label contract and update linking
            let label_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"label"));
            if (option::is_some(&label_contract_opt)) {
                // Note: Would need to call label contract to update linking
                // This requires the label contract to have an update_linked_xft function
            };
        };

        // Emit event
        event::emit(XFTWrapped {
            wrapped_xft_id: new_global_id,
            target_xft_id: source_global_id,
            wrapped_by: sender,
        });

        wrapped_xft
    }

    /// Unwrap a wrapped XFT back to its original form
    /// Checks balance first - if empty, burns wrapped XFT and returns original
    public fun unwrap_xft(
        registry: &mut XFTRegistry,
        fire_registry: &dexsta::fire::FireRegistry,
        wrapped_xft: XFT, // The wrapped XFT to unwrap (user transfers to function)
        clock: &Clock,
        ctx: &mut TxContext
    ): XFT {
        let sender = tx_context::sender(ctx);

        // Get original XFT global ID from settings[10]
        let original_global_id = *vector::borrow(&wrapped_xft.settings, 10);
        assert!(original_global_id > 0, ENotAuthorized); // Must be a wrapped XFT

        // Check if wrapped XFT has SUI balance
        let wrapped_balance = df::borrow<BalanceKey, Balance<SUI>>(&wrapped_xft.id, BalanceKey {});
        let has_balance = balance::value(wrapped_balance) > 0;

        if (has_balance) {
            // Wrapped XFT has balance - cannot unwrap, return it back
            // User needs to withdraw balance first
            assert!(false, EInsufficientBalance); // Custom error for "has balance"
        };

        // Remove wrapped XFT from registry
        let wrapped_global_id = wrapped_xft.global_id;
        let wrapped_object_id = object::uid_to_inner(&wrapped_xft.id);
        table::remove(&mut registry.global_to_object, wrapped_global_id);
        table::remove(&mut registry.object_to_global, wrapped_object_id);

        // Retrieve original XFT from escrow
        let original_xft = df::remove<u64, XFT>(&mut wrapped_xft.id, original_global_id);

        // Update original XFT owner to current sender
        original_xft.owner = sender;

        // Re-add original XFT to registry mappings
        let original_object_id = object::uid_to_inner(&original_xft.id);
        table::add(&mut registry.global_to_object, original_global_id, original_object_id);
        table::add(&mut registry.object_to_global, original_object_id, original_global_id);

        // Check if original was linked to a label, update label linking
        let linked_label_id = *vector::borrow(&original_xft.settings, 0);
        if (linked_label_id > 0) {
            // Get label contract and update linking back to original
            let label_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"label"));
            if (option::is_some(&label_contract_opt)) {
                // Note: Would need to call label contract to update linking
                // Remove wrapped XFT global ID and add back original global ID
            };
        };

        // Burn the wrapped XFT (destroy all remaining dynamic fields)
        let XFT { id, global_id: _, name: _, owner: _, creation_time: _, expiration_time: _, settings: _, ipfs_hash: _ } = wrapped_xft;
        object::delete(id);

        // Emit event
        event::emit(XFTUnwrapped {
            unwrapped_xft_id: original_global_id,
            from_xft_id: wrapped_global_id,
            unwrapped_by: sender,
        });

        original_xft
    }
    
    // ===== Enhanced Getter Functions =====

    /// Get XFT details by global ID
    /// Frontend calls this with global ID, function handles the lookup
    /// If XFT doesn't exist, frontend should call label.get_label(global_id)
    public fun get_xft(
        registry: &XFTRegistry,
        global_id: u64
    ): Option<ID> {
        if (table::contains(&registry.global_to_object, global_id)) {
            // Return the object ID so frontend can fetch the object
            let object_id = *table::borrow(&registry.global_to_object, global_id);
            option::some(object_id)
        } else {
            // XFT doesn't exist with this global ID
            option::none()
        }
    }

    /// Get complete XFT details from XFT object reference
    public fun get_xft_details(xft: &XFT): (u64, String, address, u64, u64, vector<u64>, String) {
        (
            xft.global_id,
            xft.name,
            xft.owner,
            xft.creation_time,
            xft.expiration_time,
            xft.settings,
            xft.ipfs_hash
        )
    }

    /// Standardized function for wallets and 3rd party websites to get NFT metadata
    /// This is the function wallets will call to get IPFS metadata
    public fun tokenURI(xft: &XFT): String {
        xft.ipfs_hash
    }

    /// Alternative standardized name (some wallets may expect this)
    public fun uri(xft: &XFT): String {
        xft.ipfs_hash
    }

    /// Check if a global ID exists in XFT registry
    public fun xft_exists(registry: &XFTRegistry, global_id: u64): bool {
        table::contains(&registry.global_to_object, global_id)
    }

    /// Get XFT object ID from global ID
    public fun get_xft_object_id(registry: &XFTRegistry, global_id: u64): Option<ID> {
        if (table::contains(&registry.global_to_object, global_id)) {
            option::some(*table::borrow(&registry.global_to_object, global_id))
        } else {
            option::none()
        }
    }
    
    /// Get array of XFT global IDs wrapped to this XFT
    public fun get_xfts_wrapped_to(xft: &XFT): vector<u64> {
        let wrapped_xfts = df::borrow<WrappedXFTsKey, WrappedXFTs>(&xft.id, WrappedXFTsKey {});
        wrapped_xfts.wrapped_xft_global_ids
    }

}
