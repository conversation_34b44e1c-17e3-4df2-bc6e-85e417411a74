///
/// # NFT Module
///
/// This module implements the core NFT functionality for the marketplace.
/// NFTs in this system are more than just tokens - they are smart assets that can:
/// - Own their own SUI balance
/// - Manage their own display status
/// - Track their own operators/permissions
/// - Store their own metadata
///
/// ## Key Features
///
/// * NFTs directly own their SUI balance (no separate bag contract needed)
/// * NFTs have built-in operator permissions
/// * NFTs manage their own display status
/// * NFTs store metadata as dynamic fields
/// * NFTs support royalty fees for creators
///
/// ## Dynamic Fields
///
/// Each NFT has the following dynamic fields:
/// * `BalanceKey` - Stores the NFT's SUI balance
/// * `OperatorsKey` - Stores the NFT's operators
/// * `DisplayKey` - Stores the NFT's display status
/// * `MetadataKey` - Stores the NFT's metadata attributes
///
/// ## Label Settings Vector (settings: vector<u64>)
///
/// The settings vector contains configuration parameters for labels:
/// * 0: link to label
/// * 1: registration in years
/// * 2: operator license
/// * 3: xft type
/// * 4: if type is license, license term
/// * 5: 0 false, 1 true // formerly mint pass
/// * 6: quantity
/// * 7: label registration expire
/// * 8: unused.. use to be redeem days
/// * 9: transferable
/// * 10: wrapto
/// * 11: label split for marketplace license
/// * 12: label vault locked
/// * 13: label vault unlock date
///
/// ## Label Types (via settings[3] - xft type)
///
/// * Type 1: Lead Label
/// * Type 2: Profile Label
/// * Type 3: Tag Label
/// * Type 4: Chapter Label
/// * Type 5: Operator License
/// * Type 6: Marketplace License
/// * Type 7: Art/tickets/gaming
/// * Type 8: wrappedTo
/// * Type 9: open

module dexsta::xft {
    use std::string::{Self, String};
    use std::vector;
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::url::{Self, Url};
    use sui::event;
    use sui::package;
    use sui::display;
    use sui::balance::{Self, Balance};
    use sui::sui::SUI;
    use sui::coin::{Self, Coin};
    use sui::table::{Self, Table};
    use sui::dynamic_field as df;
    use sui::vec_set::{Self, VecSet};
    use sui::clock::{Self, Clock};
    use std::option::{Self, Option};

    // ===== Constants =====
    const MAX_NAME_LENGTH: u64 = 32;
    const MAX_DESCRIPTION_LENGTH: u64 = 512;
    const MAX_URL_LENGTH: u64 = 512;

    // ===== Errors =====
    const ENameTooLong: u64 = 0;
    const EDescriptionTooLong: u64 = 1;
    const EUrlTooLong: u64 = 2;
    const EInvalidOwner: u64 = 3;
    const EInvalidOperator: u64 = 4;
    const EInvalidQuantity: u64 = 5;
    const EInsufficientBalance: u64 = 6;
    const ENotAuthorized: u64 = 7;
    const EDisplayAlreadyActive: u64 = 8;
    const EDisplayNotActive: u64 = 9;

    // ===== Events =====
    struct XFTCreated has copy, drop {
        xft_id: ID,
        global_id: u64,
        name: String,
        owner: address,
        expiration_time: u64,
    }

    struct XFTTransferred has copy, drop {
        xft_id: ID,
        global_id: u64,
        name: String,
        from: address,
        to: address,
    }

    struct DepositMade has copy, drop {
        xft_id: ID,
        depositor: address,
        amount: u64,
    }

    struct WithdrawalMade has copy, drop {
        xft_id: ID,
        recipient: address,
        amount: u64,
    }

    struct VaultLocked has copy, drop {
        xft_id: ID,
        owner: address,
        unlock_timestamp: u64,
    }

    struct VaultUnlocked has copy, drop {
        xft_id: ID,
        owner: address,
    }

    struct XFTLinkedToLabel has copy, drop {
        xft_global_id: u64,
        label_id: ID,
        linked_by: address,
    }

    // ===== One-Time Witness for the package =====
    struct NFT has drop {}

    // ===== Dynamic Field Keys =====
    struct BalanceKey has store, copy, drop {}
    struct MetadataKey has store, copy, drop {}
    struct LinkedXFTsKey has store, copy, drop {}

    // ===== Objects =====

    /// Global XFT Registry for auto-incrementing IDs and search functionality
    struct XFTRegistry has key {
        id: UID,
        next_global_id: u64, // Auto-incrementing global ID counter (starts at 2)
        // Mapping from global ID to object ID
        global_to_object: Table<u64, ID>,
        // Mapping from object ID to global ID
        object_to_global: Table<ID, u64>,
        // Total XFTs created
        total_xfts: u64,
        // Fee configuration
        fee_address: address,
        fire_registry_id: ID, // Reference to Fire Registry for fees and global IDs
    }

    struct XFT has key, store {
        id: UID,
        global_id: u64, // Auto-incrementing global ID (#2, #3, #4, etc.)
        name: String,
        owner: address,
        creation_time: u64,
        expiration_time: u64,
        settings: vector<u64>, // Same settings structure as labels
        ipfs_hash: String,
    }

    /// Stores XFTs linked to this XFT (for 1-of-1 XFTs that can contain other assets)
    struct LinkedXFTs has store {
        // Array of XFT global IDs linked to this XFT
        xft_global_ids: vector<u64>,
    }

    struct Metadata has store {
        attributes: Table<String, String>,
    }

    // ===== Functions =====
    fun init(witness: NFT, ctx: &mut TxContext) {
        let publisher = package::claim(witness, ctx);

        let keys = vector[
            string::utf8(b"name"),
            string::utf8(b"description"),
            string::utf8(b"image_url"),
            string::utf8(b"creator"),
            string::utf8(b"global_id"),
            string::utf8(b"project_url"),
        ];

        let values = vector[
            string::utf8(b"{name}"),
            string::utf8(b"{description}"),
            string::utf8(b"{url}"),
            string::utf8(b"{creator}"),
            string::utf8(b"#{global_id}"),
            string::utf8(b"https://xft.red"),
        ];

        let display_info = display::new_with_fields<NFTData>(
            &publisher, keys, values, ctx
        );

        display::update_version(&mut display_info);

        // Create global XFT registry
        let registry_id = object::new(ctx);
        let registry = XFTRegistry {
            id: registry_id,
            next_global_id: 2, // Start at #2 (reserve #1 for special use)
            global_to_object: table::new(ctx),
            object_to_global: table::new(ctx),
            total_xfts: 0,
            fee_address: tx_context::sender(ctx), // Default to deployer
            fire_registry_id: object::id_from_address(@0x0), // Placeholder, set via admin
        };



        transfer::public_transfer(publisher, tx_context::sender(ctx));
        transfer::public_transfer(display_info, tx_context::sender(ctx));
        transfer::share_object(registry);
    }

    // Create a new XFT
    public fun mint(
        registry: &mut XFTRegistry,
        fire_registry: &dexsta::fire::FireRegistry,
        parent_label: Option<&mut dexsta::label::Label>, // Optional parent label for linking
        name: String,
        settings: vector<u64>,
        ipfs_hash: String,
        clock: &Clock,
        ctx: &mut TxContext
    ): XFT {
        // Validate inputs
        assert!(string::length(&name) <= MAX_NAME_LENGTH, ENameTooLong);
        assert!(string::length(&ipfs_hash) <= MAX_URL_LENGTH, EUrlTooLong);

        // Validate settings array has minimum required elements
        assert!(vector::length(&settings) >= 10, EInvalidQuantity);

        // Validate XFT-specific settings
        // settings[3] must be 8 (XFT type)
        assert!(*vector::borrow(&settings, 3) == 8, EInvalidQuantity);

        // settings[9] must be 1 (transferable - all XFTs are transferable)
        assert!(*vector::borrow(&settings, 9) == 1, EInvalidQuantity);

        let creator = tx_context::sender(ctx);
        let xft_id = object::new(ctx);

        // Get incremental global ID from registry
        let global_id = registry.next_global_id;
        registry.next_global_id = registry.next_global_id + 1;
        registry.total_xfts = registry.total_xfts + 1;

        // Store global ID mappings
        let xft_object_id = object::uid_to_inner(&xft_id);
        table::add(&mut registry.global_to_object, global_id, xft_object_id);
        table::add(&mut registry.object_to_global, xft_object_id, global_id);

        // Calculate timestamps
        let current_time = clock::timestamp_ms(clock);

        // Create XFT (no expiration time for XFTs)
        let xft = XFT {
            id: xft_id,
            global_id,
            name,
            owner: creator,
            creation_time: current_time,
            expiration_time: 0, // XFTs don't expire
            settings,
            ipfs_hash,
        };
        
        // Initialize dynamic fields
        // 1. Balance for holding SUI
        df::add(&mut xft.id, BalanceKey {}, balance::zero<SUI>());
        
        // 2. Metadata table
        df::add(&mut xft.id, MetadataKey {}, Metadata {
            attributes: table::new(ctx)
        });
        
        // 3. Linked XFTs array
        df::add(&mut xft.id, LinkedXFTsKey {}, LinkedXFTs {
            xft_global_ids: vector::empty<u64>(),
        });
        
        

        // Link to parent label if settings[0] > 0
        let linked_label_id = *vector::borrow(&settings, 0);
        if (linked_label_id > 0) {
            // Parent label must be provided for linking
            assert!(option::is_some(&parent_label), ENotAuthorized);
            let parent_label_ref = option::borrow_mut(&mut parent_label);

            // Verify the parent label global ID matches
            assert!(dexsta::label::global_id(parent_label_ref) == linked_label_id, EInvalidOwner);

            // Check if parent label is expired
            assert!(!dexsta::label::is_expired(parent_label_ref, clock), ENotAuthorized);

            // Check authorization - caller must be label owner OR super operator
            let is_label_owner = (creator == dexsta::label::owner(parent_label_ref));
            let is_super_operator = false;

            if (!is_label_owner) {
                // Check if caller has super operator license for this label
                let operator_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"operator"));
                if (option::is_some(&operator_contract_opt)) {
                    let operator_contract_id = option::extract(&mut operator_contract_opt);
                    let (is_operator_result, operator_role, _license_global_id) = dexsta::operator::is_operator(
                        operator_contract_id,
                        linked_label_id,
                        creator,
                        clock
                    );
                    is_super_operator = (is_operator_result && operator_role == 1);
                };
            };

            assert!(is_label_owner || is_super_operator, ENotAuthorized);

            // Add this XFT to parent label's linked array
            dexsta::label::add_linked_xft(
                parent_label_ref,
                global_id,
                fire_registry,
                option::none(), // No operator license needed since we already validated
                clock,
                ctx
            );
        };
        
        // Emit event
        event::emit(XFTCreated {
            xft_id: xft_object_id,
            global_id,
            name,
            owner: creator,
            expiration_time: 0, // XFTs don't expire
        });

        xft
    }
    


    // Deposit SUI into the NFT
    public fun deposit(
        nft: &mut NFTData,
        payment: &mut Coin<SUI>,
        amount: u64,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Split the payment
        let payment_coin = coin::split(payment, amount, ctx);
        let payment_balance = coin::into_balance(payment_coin);

        // Get the NFT's balance
        let nft_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut nft.id, BalanceKey {});

        // Add to NFT balance
        balance::join(nft_balance, payment_balance);

        // Emit event
        event::emit(DepositMade {
            nft_id: object::uid_to_inner(&nft.id),
            depositor: sender,
            amount,
        });
    }

    // Withdraw SUI from the NFT
    public fun withdraw(
        nft: &mut NFTData,
        amount: u64,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(is_operator(nft, sender), ENotAuthorized);

        // Get the NFT's balance
        let nft_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut nft.id, BalanceKey {});

        // Check if there's enough balance
        assert!(balance::value(nft_balance) >= amount, EInsufficientBalance);

        // Withdraw from NFT balance
        let withdrawn_balance = balance::split(nft_balance, amount);
        let withdrawn_coin = coin::from_balance(withdrawn_balance, ctx);

        // Transfer to sender
        transfer::public_transfer(withdrawn_coin, sender);

        // Emit event
        event::emit(WithdrawalMade {
            nft_id: object::uid_to_inner(&nft.id),
            recipient: sender,
            amount,
        });
    }

    // Add an operator to the NFT
    public fun add_operator(
        nft: &mut NFTData,
        operator: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(sender == nft.creator, ENotAuthorized);

        // Get operators
        let operators = df::borrow_mut<OperatorsKey, Operators>(&mut nft.id, OperatorsKey {});

        // Add operator if not already present
        if (!vec_set::contains(&operators.operators, &operator)) {
            vec_set::insert(&mut operators.operators, operator);

            // Emit event
            event::emit(OperatorAdded {
                nft_id: object::uid_to_inner(&nft.id),
                operator,
            });
        };
    }

    // Remove an operator from the NFT
    public fun remove_operator(
        nft: &mut NFTData,
        operator: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(sender == nft.creator, ENotAuthorized);
        assert!(operator != nft.creator, ENotAuthorized); // Can't remove creator

        // Get operators
        let operators = df::borrow_mut<OperatorsKey, Operators>(&mut nft.id, OperatorsKey {});

        // Remove operator if present
        if (vec_set::contains(&operators.operators, &operator)) {
            vec_set::remove(&mut operators.operators, &operator);

            // Emit event
            event::emit(OperatorRemoved {
                nft_id: object::uid_to_inner(&nft.id),
                operator,
            });
        };
    }

    // Set display status
    public fun set_display_status(
        nft: &mut NFTData,
        is_displayed: bool,
        duration_hours: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(is_operator(nft, sender), ENotAuthorized);

        // Get NFT ID first
        let nft_id = object::uid_to_inner(&nft.id);

        // Get display info
        let display_info = df::borrow_mut<DisplayKey, DisplayInfo>(&mut nft.id, DisplayKey {});

        if (is_displayed) {
            // Can't display if already displayed
            assert!(!display_info.is_displayed, EDisplayAlreadyActive);
            
            // Set display info
            display_info.is_displayed = true;
            display_info.expiration_time = clock::timestamp_ms(clock) + (duration_hours * 3600000); // hours to ms
        } else {
            // Can't undisplay if not displayed
            assert!(display_info.is_displayed, EDisplayNotActive);

            // Set display info
            display_info.is_displayed = false;
            display_info.expiration_time = 0;
        };

        // Store values for event
        let final_is_displayed = display_info.is_displayed;
        let final_expiration_time = display_info.expiration_time;

        // Emit event
        event::emit(DisplayStatusChanged {
            nft_id,
            is_displayed: final_is_displayed,
            expiration_time: final_expiration_time,
        });
    }

    // Add metadata attribute
    public fun add_attribute(
        nft: &mut NFTData,
        key: String,
        value: String,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(is_operator(nft, sender), ENotAuthorized);

        // Get metadata
        let metadata = df::borrow_mut<MetadataKey, Metadata>(&mut nft.id, MetadataKey {});

        // Add or update attribute
        if (table::contains(&metadata.attributes, key)) {
            let old_value = table::remove(&mut metadata.attributes, key);
            let _ = old_value; // Drop old value
        };

        table::add(&mut metadata.attributes, key, value);
    }

    // Check if display is expired
    public fun is_display_expired(nft: &NFTData, clock: &Clock): bool {
        let display_info = df::borrow<DisplayKey, DisplayInfo>(&nft.id, DisplayKey {});
        display_info.is_displayed && clock::timestamp_ms(clock) > display_info.expiration_time
    }

    // Check if an address is an operator
    public fun is_operator(nft: &NFTData, addr: address): bool {
        let operators = df::borrow<OperatorsKey, Operators>(&nft.id, OperatorsKey {});
        vec_set::contains(&operators.operators, &addr)
    }

    // Get NFT balance
    public fun balance(nft: &NFTData): u64 {
        let nft_balance = df::borrow<BalanceKey, Balance<SUI>>(&nft.id, BalanceKey {});
        balance::value(nft_balance)
    }



    // Get display status
    public fun is_displayed(nft: &NFTData): bool {
        let display_info = df::borrow<DisplayKey, DisplayInfo>(&nft.id, DisplayKey {});
        display_info.is_displayed
    }

    // Get display expiration time
    public fun display_expiration(nft: &NFTData): u64 {
        let display_info = df::borrow<DisplayKey, DisplayInfo>(&nft.id, DisplayKey {});
        display_info.expiration_time
    }

    // Get attribute value
    public fun get_attribute(nft: &NFTData, key: String): Option<String> {
        let metadata = df::borrow<MetadataKey, Metadata>(&nft.id, MetadataKey {});
        if (table::contains(&metadata.attributes, key)) {
            option::some(*table::borrow(&metadata.attributes, key))
        } else {
            option::none()
        }
    }

    // ===== Accessor Functions =====
    public fun id(nft: &NFTData): &UID {
        &nft.id
    }

    public fun name(nft: &NFTData): &String {
        &nft.name
    }

    public fun description(nft: &NFTData): &String {
        &nft.description
    }

    public fun url(nft: &NFTData): &Url {
        &nft.url
    }

    public fun creator(nft: &NFTData): address {
        nft.creator
    }

    public fun royalty_fee_bps(nft: &NFTData): u64 {
        nft.royalty_fee_bps
    }

    public fun quantity(nft: &NFTData): u64 {
        nft.quantity
    }



    public fun settings(nft: &NFTData): &vector<u64> {
        &nft.settings
    }

    public fun addresses(nft: &NFTData): &vector<address> {
        &nft.addresses
    }

    public fun label_id(nft: &NFTData): &Option<ID> {
        &nft.label_id
    }
}
