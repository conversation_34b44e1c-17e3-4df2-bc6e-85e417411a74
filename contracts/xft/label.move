///
/// # Label Module
///
/// This module implements the label system for the NFT marketplace.
/// Labels are like domain names or namespaces for NFTs, allowing for organization
/// and categorization of NFTs under specific labels.
///
/// ## Key Features
///
/// * Labels have expiration dates and need to be renewed
/// * Labels can be owned and transferred
/// * Labels have role-based operator permissions
/// * Labels directly own their SUI balance
/// * Labels can collect royalties from NFT sales
///
/// ## Label Settings Vector (settings: vector<u64>)
///
/// The settings vector contains configuration parameters for labels:
/// * 0: link to label
/// * 1: registration in years
/// * 2: operator license
/// * 3: xft type
/// * 4: if type is license, license term
/// * 5: 0 false, 1 true // formerly mint pass
/// * 6: quantity
/// * 7: label registration expire
/// * 8: unused.. use to be redeem days
/// * 9: transferable
/// * 10: wrapto
/// * 11: label split for marketplace license
/// * 12: label vault locked
/// * 13: label vault unlock date
///
/// ## Label Types (via settings[3] - xft type)
///
/// * Type 1: Lead Label
/// * Type 2: Profile Label
/// * Type 3: Tag Label
/// * Type 4: Chapter Label
/// * Type 5: Operator License
/// * Type 6: Marketplace License
/// * Type 7: Art/tickets/gaming
/// * Type 8: wrappedTo
/// * Type 9: open

///
/// ## Operator Roles
///
/// * Role 1: Super Operator - Can perform all actions
/// * Role 2: Regular Operator - Limited to basic operations
///
module dexsta::label {
    use std::string::{Self, String};
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::url::{Self, Url};
    use sui::event;
    use sui::package;
    use sui::display;
    use sui::clock::{Self, Clock};
    use sui::table::{Self, Table};
    use sui::coin::{Self, Coin};
    use sui::sui::SUI;
    use sui::balance::{Self, Balance};
    use sui::dynamic_field as df;
    use sui::vec_set::{Self, VecSet};
    use std::option::{Self, Option};
    use std::vector;

    // ===== Constants =====
    const MAX_LABEL_LENGTH: u64 = 32;
    const DEFAULT_LABEL_DURATION: u64 = 31536000; // 1 year in seconds
    const LABEL_FEE: u64 = **********; // 1 SUI per year

    // ===== Errors =====
    const ELabelTooLong: u64 = 0;
    const ELabelAlreadyExists: u64 = 1;
    const EInvalidOwner: u64 = 2;
    const ELabelExpired: u64 = 3;
    const EInsufficientFunds: u64 = 4;
    const EInvalidOperator: u64 = 5;
    const ELabelNotFound: u64 = 6;
    const ENotAuthorized: u64 = 7;
    const EInsufficientBalance: u64 = 8;
    const EInvalidLabelType: u64 = 9;
    const EInvalidQuantity: u64 = 10;
    const EInvalidMarketplaceLicense: u64 = 11;
    const EOperatorLicenseExpired: u64 = 12;
    const EInvalidLabelLink: u64 = 13;

    // ===== Events =====
    struct LabelCreated has copy, drop {
        label_id: ID,
        name: String,
        owner: address,
        expiration_time: u64,
    }

    struct LabelRenewed has copy, drop {
        label_id: ID,
        name: String,
        owner: address,
        new_expiration_time: u64,
    }

    struct LabelTransferred has copy, drop {
        label_id: ID,
        global_id: u64,
        name: String,
        from: address,
        to: address,
    }



    struct DepositMade has copy, drop {
        label_id: ID,
        depositor: address,
        amount: u64,
    }

    struct ObjectWithdrawn has copy, drop {
        label_id: ID,
        label_global_id: u64,
        object_id: ID,
        object_type: String,
        amount: Option<u64>,  // For coins/tokens, none for NFTs
        withdrawn_by: address,
        operator_license_used: Option<u64>, // Global ID of operator license used, if any
        withdrawal_timestamp: u64,
    }

    struct VaultLocked has copy, drop {
        label_id: ID,
        owner: address,
        unlock_timestamp: u64,
    }

    struct VaultUnlocked has copy, drop {
        label_id: ID,
        owner: address,
    }

    struct XFTLinkedLabelUpdated has copy, drop {
        xft_global_id: u64,
        old_label_id: ID,
        new_label_id: ID,
        updated_by: address,
    }

    struct XFTLinkedToLabel has copy, drop {
        xft_global_id: u64,
        label_id: ID,
        linked_by: address,
    }

    struct XFTUnlinkedFromLabel has copy, drop {
        xft_global_id: u64,
        label_id: ID,
        unlinked_by: address,
    }

    struct LabelLinkedToLabel has copy, drop {
        new_label_global_id: u64,
        linked_to_label_global_id: u64,
        creator: address,
    }

    struct MarketplaceFeeUpdated has copy, drop {
        label_id: ID,
        global_id: u64,
        old_fee_bps: u64,
        new_fee_bps: u64,
        updated_by: address,
    }

    // ===== One-Time Witness for the package =====
    struct LABEL has drop {}

    // ===== Dynamic Field Keys =====
    struct BalanceKey has store, copy, drop {}
    struct OperatorsKey has store, copy, drop {}
    struct MetadataKey has store, copy, drop {}
    struct LabelsByTypeKey has store, copy, drop {}
    struct LabelsByOwnerKey has store, copy, drop {}
    struct LinkedXFTsKey has store, copy, drop {}
    struct ExpirationLookupKey has store, copy, drop {}


    // ===== Objects =====

    /// Central registry for all labels in the Dexsta platform
    ///
    /// This shared object serves as the main hub for label management and connects
    /// labels to the broader Dexsta ecosystem (Fire Registry, Global Registry, fees).
    ///
    /// Fields:
    /// - `id`: Unique Sui object identifier for this registry
    /// - `owner`: Admin address that controls registry settings and updates
    /// - `fee_address`: Fallback wallet address for label registration fees
    /// - `labels`: Hash table mapping label names to their object IDs ("name" → object_id)
    /// - `label_count`: Running counter of total labels created (for statistics)
    /// - `fire_registry_id`: Reference to Fire Registry contract for dynamic fees and platform config
    ///
    /// Usage:
    /// - Label creation: Check name availability, store name→ID mapping, increment counter
    /// - Label lookup: Find label object ID by name for frontend queries
    /// - Fee calculation: Use Fire Registry reference to get current registration fees
    /// - Admin functions: Update settings, manage registry configuration
    struct LabelRegistry has key {
        id: UID,
        owner: address,
        fee_address: address,
        labels: Table<String, ID>,
        label_count: u64,
        fire_registry_id: ID, // Reference to Fire Registry for fees and global IDs
    }
    
    /// Stores labels by type for efficient searching
    struct LabelsByType has store {
        // Type ID -> Set of label IDs
        labels_by_type: Table<u64, vector<ID>>,
    }

    /// Stores labels by owner for efficient searching
    struct LabelsByOwner has store {
        // Owner address -> Set of label IDs
        labels_by_owner: Table<address, vector<ID>>,
    }

    /// Stores XFTs linked to this label
    struct LinkedXFTs has store {
        // Array of XFT global IDs linked to this label
        xft_global_ids: vector<u64>,
    }

    /// Stores expiration times for quick lookup
    struct ExpirationLookup has store {
        // Global ID -> Expiration timestamp
        expiration_times: Table<u64, u64>,
    }



    struct Label has key {  // Removed 'store' - forces use of custom transfer function
        id: UID,
        global_id: u64,        // Auto-incrementing global ID from registry
        name: String,
        owner: address,
        creation_time: u64,
        expiration_time: u64,
        settings: vector<u64>, // settings[9] = transferable (0=false, 1=true)
        ipfs_hash: String,
    }

    struct Operator has store, drop {
        address: address,
        role: u64, // 1 = super operator, 2 = regular operator
        expiration_time: u64,
    }
    
    struct Operators has store {
        operators: Table<address, Operator>,
    }

    struct Metadata has store {
        attributes: Table<String, String>,
    }

    /// Basic label information for queries
    public struct LabelInfo has copy, drop {
        object_id: ID,
        global_id: u64,
        name: String,
        creator: address,
    }

    /// Complete label details including all metadata
    public struct LabelDetails has copy, drop {
        object_id: ID,
        global_id: u64,
        name: String,
        owner: address,
        creator: address,
        creation_time: u64,
        expiration_time: u64,
        settings: vector<u64>,
        transferable: bool,
        ipfs_hash: String,
        balance: u64,
    }

    // ===== Functions =====
    fun init(witness: LABEL, ctx: &mut TxContext) {
        let publisher = package::claim(witness, ctx);

        let keys = vector[
            string::utf8(b"name"),
            string::utf8(b"description"),
            string::utf8(b"image_url"),
            string::utf8(b"owner"),
            string::utf8(b"global_id"),
            string::utf8(b"project_url"),
        ];

        let values = vector[
            string::utf8(b"{name}"),
            string::utf8(b"Label: {name} (#{global_id})"),
            string::utf8(b"https://ipfs.io/ipfs/{ipfs_hash}"),
            string::utf8(b"{owner}"),
            string::utf8(b"#{global_id}"),
            string::utf8(b"https://dexsta.xyz"),
        ];

        let display_info = display::new_with_fields<Label>(
            &publisher, keys, values, ctx
        );

        display::update_version(&mut display_info);

        // Create label registry (Fire Registry ID will be set later via admin function)
        let registry_id = object::new(ctx);
        let registry = LabelRegistry {
            id: registry_id,
            owner: tx_context::sender(ctx),
            fee_address: tx_context::sender(ctx),
            labels: table::new(ctx),
            label_count: 0,
            fire_registry_id: object::id_from_address(@0x0), // Placeholder, set via admin
        };

        // Initialize search indexes
        df::add(&mut registry.id, LabelsByTypeKey {}, LabelsByType {
            labels_by_type: table::new(ctx),
        });

        df::add(&mut registry.id, LabelsByOwnerKey {}, LabelsByOwner {
            labels_by_owner: table::new(ctx),
        });

        // Initialize expiration lookup table
        df::add(&mut registry.id, ExpirationLookupKey {}, ExpirationLookup {
            expiration_times: table::new(ctx),
        });



        transfer::public_transfer(publisher, tx_context::sender(ctx));
        transfer::public_transfer(display_info, tx_context::sender(ctx));
        transfer::share_object(registry);
    }

    // Create a new label
    public fun create_label(
        registry: &mut LabelRegistry,
        fire_registry: &dexsta::fire::FireRegistry,
        global_registry: &mut dexsta::registry::GlobalRegistry,
        parent_label: Option<&mut Label>,  // Optional parent label for linking
        name: String,
        settings: vector<u64>,
        ipfs_hash: String,
        payment: &mut Coin<SUI>,
        clock: &Clock,
        ctx: &mut TxContext
    ): ID {
        let sender = tx_context::sender(ctx);

        // Validate inputs
        assert!(string::length(&name) <= MAX_LABEL_LENGTH, ELabelTooLong);
        assert!(!table::contains(&registry.labels, name), ELabelAlreadyExists);

        // Get fee from Fire Registry
        let fee_per_year = dexsta::fire::get_label_fee_per_year(fire_registry);
        let registration_years = *vector::borrow(&settings, 1);
        let total_fee = fee_per_year * registration_years;
        assert!(coin::value(payment) >= total_fee, EInsufficientFunds);
        
        // Validate settings array has minimum required elements
        assert!(vector::length(&settings) >= 12, EInvalidLabelType);

        // Get key settings values
        let linked_label_id = *vector::borrow(&settings, 0);
        let registration_years = *vector::borrow(&settings, 1);
        let operator_license_id = *vector::borrow(&settings, 2);
        let xft_type = *vector::borrow(&settings, 3);
        let quantity = *vector::borrow(&settings, 6);
        let transferable_setting = *vector::borrow(&settings, 9);
        let marketplace_split = *vector::borrow(&settings, 11);

        // Validate XFT type constraints
        // Type 1 (Lead Label) must be 1 of 1
        if (xft_type == 1) {
            assert!(quantity == 1, EInvalidQuantity);
        };

        // Type 5 (Operator License) and Type 6 (Marketplace License) must be more than 1
        if (xft_type == 5 || xft_type == 6) {
            assert!(quantity > 1, EInvalidQuantity);
        };

        // If marketplace split is set, XFT type must be 6 (Marketplace License)
        if (marketplace_split > 0) {
            assert!(xft_type == 6, EInvalidMarketplaceLicense);
        };

        // If operator license is being used (settings[2] > 0)
        if (operator_license_id > 0) {
            // Verify caller has valid operator license for the linked label
            // Get linked label object ID from global registry
            let linked_label_object_id_opt = dexsta::registry::get_object_id(global_registry, linked_label_id);
            assert!(option::is_some(&linked_label_object_id_opt), EInvalidOwner);

            // Create a temporary label reference to check operator status
            // Note: This requires the linked label to be accessible
            // For now, we'll call the operator contract directly
            let operator_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"operator"));
            assert!(option::is_some(&operator_contract_opt), ENotAuthorized);

            let operator_contract_id = option::extract(&mut operator_contract_opt);
            let (is_operator_result, operator_role, license_global_id) = dexsta::operator::is_operator(
                operator_contract_id,
                linked_label_id,  // Use linked label global ID
                sender,           // Caller address
                clock
            );

            // Must be super operator (role 1) to create labels linked to other labels
            assert!(is_operator_result && operator_role == 1, ENotAuthorized);
        };

        // If linked to another label (settings[0] > 0)
        if (linked_label_id > 0) {
            // Validate that caller owns the linked label OR has super operator license

            // First, check if caller has super operator license for the linked label
            let has_operator_license = false;
            let operator_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"operator"));

            if (option::is_some(&operator_contract_opt)) {
                let operator_contract_id = option::extract(&mut operator_contract_opt);
                let (is_operator_result, operator_role, license_global_id) = dexsta::operator::is_operator(
                    operator_contract_id,
                    linked_label_id,  // Global ID of linked label
                    sender,           // Caller address
                    clock
                );

                // Check if caller has super operator license (role 1)
                has_operator_license = (is_operator_result && operator_role == 1);
            };
            
            // If no operator license, must check if caller owns the linked label
            if (!has_operator_license) {
                // Check ownership via global registry creator field
                let linked_label_creator_opt = dexsta::registry::get_item_creator(global_registry, linked_label_id);
                assert!(option::is_some(&linked_label_creator_opt), EInvalidOwner);
                let linked_label_creator = option::extract(&mut linked_label_creator_opt);
                
                // Caller must be the original creator/owner of the linked label
                assert!(sender == linked_label_creator, ENotAuthorized);
            };
        };
        
        // Process payment - route fees to platform label
        let fee_payment = coin::split(payment, total_fee, ctx);
        let platform_label_id_opt = dexsta::fire::get_platform_label_id(fire_registry);

        if (option::is_some(&platform_label_id_opt)) {
            // Send fees to platform label object ID
            let platform_label_id = option::extract(&mut platform_label_id_opt);
            // Transfer coin to the platform label object
            transfer::public_transfer(fee_payment, object::id_to_address(&platform_label_id));
        } else {
            // Fallback to fee address if no platform label configured
            transfer::public_transfer(fee_payment, registry.fee_address);
        };
        
        // Create label
        let current_time = clock::timestamp_ms(clock);
        let expiration_time = current_time + DEFAULT_LABEL_DURATION;

        let label_uid = object::new(ctx);
        let label_object_id = object::uid_to_inner(&label_uid);

        // Register with global registry to get global ID
        let global_id = dexsta::registry::register_item(
            global_registry,
            label_object_id,
            1, // ITEM_TYPE_LABEL
            name,
            ctx
        );

        // Store expiration time in lookup table for easy access
        let expiration_lookup = df::borrow_mut<ExpirationLookupKey, ExpirationLookup>(&mut registry.id, ExpirationLookupKey {});
        table::add(&mut expiration_lookup.expiration_times, global_id, expiration_time);
         
         if (linked_label_id > 0) {
           // Add this label's global ID to the parent label's linked array
           if (option::is_some(&parent_label)) {
               let parent_label_ref = option::borrow_mut(&mut parent_label);

               // Verify the parent label's global ID matches the linked_label_id
               assert!(parent_label_ref.global_id == linked_label_id, EInvalidOwner);

               // Add this label to parent's linked array
               let linked_items = df::borrow_mut<LinkedXFTsKey, LinkedXFTs>(&mut parent_label_ref.id, LinkedXFTsKey {});

               // Add child label to linked array (avoid duplicates)
               let (already_linked, _) = vector::index_of(&linked_items.xft_global_ids, &global_id);
               if (!already_linked) {
                   vector::push_back(&mut linked_items.xft_global_ids, global_id);
               };

               // Emit event
               event::emit(LabelLinkedToLabel {
                   new_label_global_id: global_id,
                   linked_to_label_global_id: linked_label_id,
                   creator: sender,
               });
           };
         }

        let label = Label {
            id: label_uid,
            global_id,
            name,
            owner: sender,
            creation_time: current_time,
            expiration_time,
            settings,
            ipfs_hash,
        };

        // Initialize dynamic fields
        // 1. Balance for holding SUI
        df::add(&mut label.id, BalanceKey {}, balance::zero<SUI>());

        // 2. Operators table
        let operators_table = table::new<address, Operator>(ctx);
        // Add owner as super operator
        table::add(&mut operators_table, sender, Operator {
            address: sender,
            role: 1, // Super operator
            expiration_time: 0, // Never expires for owner
        });
        df::add(&mut label.id, OperatorsKey {}, Operators { operators: operators_table });

        // 3. Metadata table
        df::add(&mut label.id, MetadataKey {}, Metadata {
            attributes: table::new(ctx)
        });

        // 4. Linked XFTs array
        df::add(&mut label.id, LinkedXFTsKey {}, LinkedXFTs {
            xft_global_ids: vector::empty()
        });

        // Register label
        let label_id_inner = object::uid_to_inner(&label.id);
        table::add(&mut registry.labels, name, label_id_inner);
        registry.label_count = registry.label_count + 1;

        // Add to labels by type index (use settings[3] for XFT type)
        let label_type = if (vector::length(&settings) > 3) {
            *vector::borrow(&settings, 3) // XFT type is at index 3
        } else {
            1 // Default to Lead Label
        };

        let labels_by_type = df::borrow_mut<LabelsByTypeKey, LabelsByType>(&mut registry.id, LabelsByTypeKey {});
        if (!table::contains(&labels_by_type.labels_by_type, label_type)) {
            table::add(&mut labels_by_type.labels_by_type, label_type, vector::empty<ID>());
        };

        let type_labels = table::borrow_mut(&mut labels_by_type.labels_by_type, label_type);
        vector::push_back(type_labels, label_id_inner);

        // Add to labels by owner index
        let labels_by_owner = df::borrow_mut<LabelsByOwnerKey, LabelsByOwner>(&mut registry.id, LabelsByOwnerKey {});
        if (!table::contains(&labels_by_owner.labels_by_owner, sender)) {
            table::add(&mut labels_by_owner.labels_by_owner, sender, vector::empty<ID>());
        };

        let owner_labels = table::borrow_mut(&mut labels_by_owner.labels_by_owner, sender);
        vector::push_back(owner_labels, label_id_inner);

        

        // Emit event
        event::emit(LabelCreated {
            label_id: label_id_inner,
            name,
            owner: sender,
            expiration_time,
        });

        // Transfer label to owner
        transfer::transfer(label, sender);

        label_id_inner
    }

    // Renew a label
    public fun renew_label(
        registry: &mut LabelRegistry,
        fire_registry: &dexsta::fire::FireRegistry,
        label: &mut Label,
        duration_years: u64,
        payment: &mut Coin<SUI>,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Validate
        assert!(sender == label.owner, EInvalidOwner);

        // Calculate fee from Fire Registry
        let fee_per_year = dexsta::fire::get_label_fee_per_year(fire_registry);
        let fee = fee_per_year * duration_years;
        assert!(coin::value(payment) >= fee, EInsufficientFunds);

        // Process payment - route fees to platform label
        let fee_payment = coin::split(payment, fee, ctx);
        let platform_label_id_opt = dexsta::fire::get_platform_label_id(fire_registry);

        if (option::is_some(&platform_label_id_opt)) {
            // Send fees to platform label object ID
            let platform_label_id = option::extract(&mut platform_label_id_opt);
            transfer::public_transfer(fee_payment, object::id_to_address(&platform_label_id));
        } else {
            // Fallback to fee address if no platform label configured
            transfer::public_transfer(fee_payment, registry.fee_address);
        };

        // Update expiration
        let current_time = clock::timestamp_ms(clock);
        let new_expiration = if (label.expiration_time > current_time) {
            label.expiration_time + (DEFAULT_LABEL_DURATION * duration_years)
        } else {
            current_time + (DEFAULT_LABEL_DURATION * duration_years)
        };

        label.expiration_time = new_expiration;

        // Emit event
        event::emit(LabelRenewed {
            label_id: object::uid_to_inner(&label.id),
            name: label.name,
            owner: sender,
            new_expiration_time: new_expiration,
        });
    }

    // Transfer a label (only for 1-of-1 labels)
    public fun transfer_label(
        registry: &mut LabelRegistry,           // Need registry to update labels_by_owner
        label: Label,                           // Take ownership to transfer
        global_registry: &mut dexsta::registry::GlobalRegistry,
        fire_registry: &dexsta::fire::FireRegistry, // Need for contract lookups
        recipient: address,
        clock: &Clock,                          // Need for operator validation
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Validate
        // Get label type and transferable setting
        let label_type = *vector::borrow(&label.settings, 3); // settings[3] = label type
        let transferable_setting = *vector::borrow(&label.settings, 9); // settings[9] = transferable (0=limited, 1=full)

        // Validate transfer based on transferable setting and label type
        if (transferable_setting == 1) {
            // Fully transferable - anyone who owns it can transfer
            assert!(sender == label.owner, EInvalidOwner);
        } else {
            // Limited transferable (transferable_setting == 0)
            // Check who can initiate transfers

            let is_label_owner = (sender == label.owner);
            let is_super_operator = false;
            let is_marketplace_contract = false;

            // Check if sender is super operator for this label
            if (!is_label_owner) {
                let operator_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"operator"));
                if (option::is_some(&operator_contract_opt)) {
                    let operator_contract_id = option::extract(&mut operator_contract_opt);
                    let (is_operator_result, operator_role, _license_global_id) = dexsta::operator::is_operator(
                        operator_contract_id,
                        label.global_id,
                        sender,
                        clock
                    );
                    is_super_operator = (is_operator_result && operator_role == 1);
                };
            };

            // Check if sender is marketplace contract (for marketplace licenses)
            if (!is_label_owner && !is_super_operator && label_type == 6) { // 6 = marketplace license
                let marketplace_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"marketplace"));
                if (option::is_some(&marketplace_contract_opt)) {
                    let marketplace_contract_id = option::extract(&mut marketplace_contract_opt);
                    is_marketplace_contract = (sender == object::id_to_address(&marketplace_contract_id));
                };
            };

            // For marketplace licenses, check if both license and parent label are not expired
            if (label_type == 6) {
                let current_time = clock::timestamp_ms(clock);

                // Check 1: Marketplace license itself is not expired (settings[7])
                let license_expiration = *vector::borrow(&label.settings, 7);
                assert!(current_time <= license_expiration, ENotAuthorized); // License must not be expired

                // Check 2: Parent label is not expired (get parent from settings[0])
                let parent_label_global_id = *vector::borrow(&label.settings, 0); // settings[0] = linked label ID
                if (parent_label_global_id > 0) {
                    // Get parent label expiration from lookup table
                    let parent_expiration_opt = get_label_expiration_by_global_id(registry, parent_label_global_id);
                    assert!(option::is_some(&parent_expiration_opt), EInvalidOwner); // Parent label must exist

                    let parent_expiration = option::extract(&mut parent_expiration_opt);
                    assert!(current_time <= parent_expiration, ENotAuthorized); // Parent label must not be expired
                };
            };

            // Validate sender authorization for limited transfer
            assert!(is_label_owner || is_super_operator || is_marketplace_contract, ENotAuthorized);

            // Validate recipient for limited transfer
            let burn_address = @0x0;
            let marketplace_contract_address = if (label_type == 6) {
                let marketplace_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"marketplace"));
                if (option::is_some(&marketplace_contract_opt)) {
                    let marketplace_contract_id = option::extract(&mut marketplace_contract_opt);
                    option::some(object::id_to_address(&marketplace_contract_id))
                } else {
                    option::none()
                }
            } else {
                option::none()
            };

            let is_valid_recipient = (
                recipient == label.owner ||                    // Back to owner
                recipient == burn_address ||                   // Burn address
                (label_type == 6 && option::is_some(&marketplace_contract_address) &&
                 recipient == option::extract(&mut marketplace_contract_address)) // To marketplace contract for marketplace licenses
            );

            // If sender is not owner/super operator, additional recipient restrictions apply
            if (!is_label_owner && !is_super_operator) {
                assert!(is_valid_recipient, ENotAuthorized);
            };
        };
        
        // Additional validation for 1-of-1 labels
        let quantity = *vector::borrow(&label.settings, 6); // settings[6] = quantity
        assert!(quantity == 1, EInvalidQuantity); // Only 1-of-1 labels can be transferred

        // Validate recipient is not zero address
        assert!(recipient != @0x0, EInvalidOwner);

        // Store values before consuming the label
        let old_owner = label.owner;
        let label_id = object::uid_to_inner(&label.id);
        let global_id = label.global_id;
        let name = label.name;

        // Update labels_by_owner index
        let labels_by_owner = df::borrow_mut<LabelsByOwnerKey, LabelsByOwner>(&mut registry.id, LabelsByOwnerKey {});

        // Remove from old owner's list
        if (table::contains(&labels_by_owner.labels_by_owner, old_owner)) {
            let old_owner_labels = table::borrow_mut(&mut labels_by_owner.labels_by_owner, old_owner);
            let (found, index) = vector::index_of(old_owner_labels, &label_id);
            if (found) {
                vector::remove(old_owner_labels, index);
            };
        };

        // Add to new owner's list
        if (!table::contains(&labels_by_owner.labels_by_owner, recipient)) {
            table::add(&mut labels_by_owner.labels_by_owner, recipient, vector::empty<ID>());
        };
        let new_owner_labels = table::borrow_mut(&mut labels_by_owner.labels_by_owner, recipient);
        vector::push_back(new_owner_labels, label_id);

        // Update global registry creator (optional - tracks current owner)
        // Note: This updates the "creator" field to current owner for tracking purposes
        // The original creator history is maintained in events

        // Emit enhanced transfer event
        event::emit(LabelTransferred {
            label_id,
            global_id,
            name,
            from: old_owner,
            to: recipient,
        });

        // Transfer the label object to the new owner
        transfer::transfer(label, recipient);
    }
    
    

    // Deposit SUI into the label
    public fun deposit(
        label: &mut Label,
        payment: &mut Coin<SUI>,
        amount: u64,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Split the payment
        let payment_coin = coin::split(payment, amount, ctx);
        let payment_balance = coin::into_balance(payment_coin);

        // Get the label's balance
        let label_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut label.id, BalanceKey {});

        // Add to label balance
        balance::join(label_balance, payment_balance);

        // Emit event
        event::emit(DepositMade {
            label_id: object::uid_to_inner(&label.id),
            depositor: sender,
            amount,
        });
    }

    // Deposit balance directly into a label (for fee routing)
    public fun deposit_balance(
        label: &mut Label,
        fee_balance: Balance<SUI>,
        depositor: address
    ) {
        let amount = balance::value(&fee_balance);
        
        // Get the label's balance
        let label_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut label.id, BalanceKey {});
        
        // Add to label balance
        balance::join(label_balance, fee_balance);

        // Emit event
        event::emit(DepositMade {
            label_id: object::uid_to_inner(&label.id),
            depositor,
            amount,
        });
    }

    /// Withdraw any coin (SUI, USDC, etc.) from label vault with amount tracking
    public fun withdraw_coin<T>(
        label: &mut Label,
        object_id: ID,
        operator_license: Option<&dexsta::operator::OperatorLicense>, // Optional operator license
        clock: &Clock,
        ctx: &mut TxContext
    ): Coin<T> {
        let sender = tx_context::sender(ctx);
        let operator_license_global_id = option::none<u64>();

        // Check if label vault is locked (settings[12]: 0=unlocked, 1=locked)
        let vault_locked = *vector::borrow(&label.settings, 12);
        if (vault_locked == 1) {
            // Check unlock date (settings[13]: unlock timestamp)
            let unlock_date = *vector::borrow(&label.settings, 13);
            let current_time = clock::timestamp_ms(clock);
            assert!(current_time >= unlock_date, ENotAuthorized);
        };

        // Check authorization - either owner or valid operator license holder
        if (sender == label.owner) {
            // Owner can always withdraw (no limits)
        } else {
            // Must have valid operator license
            assert!(option::is_some(&operator_license), ENotAuthorized);
            let license = option::borrow(&operator_license);

            // Verify license holder
            assert!(dexsta::operator::license_owner(license) == sender, ENotAuthorized);

            // Verify license is for this label
            let license_label_id = dexsta::operator::license_label_id(license);
            assert!(license_label_id == object::uid_to_inner(&label.id), ENotAuthorized);

            // Check if license is expired
            assert!(!dexsta::operator::is_license_expired(license, clock), ENotAuthorized);

            // Store operator license global ID for event
            operator_license_global_id = option::some(dexsta::operator::license_global_id(license));

            // Get operator settings from license
            let operator_settings = dexsta::operator::license_settings(license);
            let max_withdrawal = *vector::borrow(&operator_settings, 0); // settings[0] = max withdrawal amount
            let withdrawal_cooldown = *vector::borrow(&operator_settings, 1); // settings[1] = cooldown period (ms)
            let last_withdrawal_time = *vector::borrow(&operator_settings, 2); // settings[2] = last withdrawal timestamp

            // Check cooldown period
            let current_time = clock::timestamp_ms(clock);
            let next_allowed_withdrawal = last_withdrawal_time + withdrawal_cooldown;
            assert!(current_time >= next_allowed_withdrawal, ENotAuthorized);

            // Note: Amount limit check will be done after we get the coin amount
        };

        // Get current timestamp for event
        let withdrawal_timestamp = clock::timestamp_ms(clock);

        // Remove the coin from dynamic fields
        let withdrawn_coin = df::remove<ID, Coin<T>>(&mut label.id, object_id);

        // Get coin amount for detailed tracking and operator limit check
        let coin_amount = coin::value(&withdrawn_coin);

        // If using operator license, check withdrawal amount limit
        if (option::is_some(&operator_license)) {
            let license = option::borrow(&operator_license);
            let operator_settings = dexsta::operator::license_settings(license);
            let max_withdrawal = *vector::borrow(&operator_settings, 0);
            assert!(coin_amount <= max_withdrawal, EInsufficientBalance);
        };

        // Get coin type information
        let coin_type_name = type_name::get<Coin<T>>();
        let coin_type_string = type_name::into_string(coin_type_name);

        // Emit detailed withdrawal event
        event::emit(ObjectWithdrawn {
            label_id: object::uid_to_inner(&label.id),
            label_global_id: label.global_id,
            object_id,
            object_type: coin_type_string,
            amount: option::some(coin_amount),
            withdrawn_by: sender,
            operator_license_used: operator_license_global_id,
            withdrawal_timestamp,
        });

        withdrawn_coin
    }

    /// Withdraw any object (XFTs, NFTs, etc.) from label vault
    public fun withdraw_object<T: key + store>(
        label: &mut Label,
        object_id: ID,
        operator_license: Option<&dexsta::operator::OperatorLicense>, // Optional operator license
        clock: &Clock,
        ctx: &mut TxContext
    ): T {
        let sender = tx_context::sender(ctx);
        let operator_license_global_id = option::none<u64>();

        // Check if label vault is locked (settings[12]: 0=unlocked, 1=locked)
        let vault_locked = *vector::borrow(&label.settings, 12);
        if (vault_locked == 1) {
            // Check unlock date (settings[13]: unlock timestamp)
            let unlock_date = *vector::borrow(&label.settings, 13);
            let current_time = clock::timestamp_ms(clock);
            assert!(current_time >= unlock_date, ENotAuthorized);
        };

        // Check authorization - either owner or valid operator license holder
        if (sender == label.owner) {
            // Owner can always withdraw (no limits)
        } else {
            // Must have valid operator license
            assert!(option::is_some(&operator_license), ENotAuthorized);
            let license = option::borrow(&operator_license);

            // Verify license holder
            assert!(dexsta::operator::license_owner(license) == sender, ENotAuthorized);

            // Verify license is for this label
            let license_label_id = dexsta::operator::license_label_id(license);
            assert!(license_label_id == object::uid_to_inner(&label.id), ENotAuthorized);

            // Check if license is expired
            assert!(!dexsta::operator::is_license_expired(license, clock), ENotAuthorized);

            // Store operator license global ID for event
            operator_license_global_id = option::some(dexsta::operator::license_global_id(license));
        };

        // Get current timestamp for event
        let withdrawal_timestamp = clock::timestamp_ms(clock);

        // Get object type information
        let object_type_name = type_name::get<T>();
        let object_type_string = type_name::into_string(object_type_name);

        // Remove the object from dynamic fields
        let withdrawn_object = df::remove<ID, T>(&mut label.id, object_id);

        // Emit detailed withdrawal event (no amount for non-coin objects)
        event::emit(ObjectWithdrawn {
            label_id: object::uid_to_inner(&label.id),
            label_global_id: label.global_id,
            object_id,
            object_type: object_type_string,
            amount: option::none<u64>(),
            withdrawn_by: sender,
            operator_license_used: operator_license_global_id,
            withdrawal_timestamp,
        });

        withdrawn_object
    }

    // Lock label vault until specific date
    public fun lock_vault(
        label: &mut Label,
        unlock_timestamp: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Only owner can lock the vault
        assert!(sender == label.owner, EInvalidOwner);

        // Validate unlock timestamp is in the future
        let current_time = clock::timestamp_ms(clock);
        assert!(unlock_timestamp > current_time, EInvalidOwner);

        // Update settings to lock vault
        let settings = &mut label.settings;
        *vector::borrow_mut(settings, 12) = 1; // Lock vault (settings[12] = 1)
        *vector::borrow_mut(settings, 13) = unlock_timestamp; // Set unlock date (settings[13] = timestamp)

        // Emit event
        event::emit(VaultLocked {
            label_id: object::uid_to_inner(&label.id),
            owner: sender,
            unlock_timestamp,
        });
    }

    // Unlock label vault (can only be called after unlock date)
    public fun unlock_vault(
        label: &mut Label,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Only owner can unlock the vault
        assert!(sender == label.owner, EInvalidOwner);

        // Check if vault is currently locked
        let vault_locked = *vector::borrow(&label.settings, 12);
        assert!(vault_locked == 1, ENotAuthorized); // Vault must be locked to unlock

        // Check if unlock date has passed
        let unlock_date = *vector::borrow(&label.settings, 13);
        let current_time = clock::timestamp_ms(clock);
        assert!(current_time >= unlock_date, ENotAuthorized); // Cannot unlock before unlock date

        // Update settings to unlock vault
        let settings = &mut label.settings;
        *vector::borrow_mut(settings, 12) = 0; // Unlock vault (settings[12] = 0)
        *vector::borrow_mut(settings, 13) = 0; // Clear unlock date (settings[13] = 0)

        // Emit event
        event::emit(VaultUnlocked {
            label_id: object::uid_to_inner(&label.id),
            owner: sender,
        });
    }
    
    // Check if a label is expired
    public fun is_expired(label: &Label, clock: &Clock): bool {
        clock::timestamp_ms(clock) > label.expiration_time
    }

    // Update linked label for an XFT (fix incorrect linkages)
    public fun update_linked_label(
        old_label: &mut Label,                                    // Label to unlink from
        new_label: &mut Label,                                    // Label to link to
        xft_global_id: u64,                                       // XFT global ID to move
        operator_license_old: Option<&dexsta::operator::OperatorLicense>, // License for old label
        operator_license_new: Option<&dexsta::operator::OperatorLicense>, // License for new label
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Validate authorization for old label (to unlink from)
        if (sender != old_label.owner) {
            // Must have valid operator license for old label
            assert!(option::is_some(&operator_license_old), ENotAuthorized);
            let license = option::borrow(&operator_license_old);

            // Verify license holder and scope
            assert!(dexsta::operator::license_owner(license) == sender, ENotAuthorized);
            assert!(dexsta::operator::license_label_id(license) == object::uid_to_inner(&old_label.id), ENotAuthorized);
            assert!(!dexsta::operator::is_license_expired(license, clock), ENotAuthorized);
        };

        // Validate authorization for new label (to link to)
        if (sender != new_label.owner) {
            // Must have valid operator license for new label
            assert!(option::is_some(&operator_license_new), ENotAuthorized);
            let license = option::borrow(&operator_license_new);

            // Verify license holder and scope
            assert!(dexsta::operator::license_owner(license) == sender, ENotAuthorized);
            assert!(dexsta::operator::license_label_id(license) == object::uid_to_inner(&new_label.id), ENotAuthorized);
            assert!(!dexsta::operator::is_license_expired(license, clock), ENotAuthorized);
        };

        // Get linked XFTs arrays from both labels
        let old_linked_xfts = df::borrow_mut<LinkedXFTsKey, LinkedXFTs>(&mut old_label.id, LinkedXFTsKey {});
        let new_linked_xfts = df::borrow_mut<LinkedXFTsKey, LinkedXFTs>(&mut new_label.id, LinkedXFTsKey {});

        // Remove XFT from old label's linked array
        let (found, index) = vector::index_of(&old_linked_xfts.xft_global_ids, &xft_global_id);
        assert!(found, EInvalidOwner); // XFT must be linked to old label
        vector::remove(&mut old_linked_xfts.xft_global_ids, index);

        // Add XFT to new label's linked array (avoid duplicates)
        let (already_linked, _) = vector::index_of(&new_linked_xfts.xft_global_ids, &xft_global_id);
        if (!already_linked) {
            vector::push_back(&mut new_linked_xfts.xft_global_ids, xft_global_id);
        };

        // Emit event
        event::emit(XFTLinkedLabelUpdated {
            xft_global_id,
            old_label_id: object::uid_to_inner(&old_label.id),
            new_label_id: object::uid_to_inner(&new_label.id),
            updated_by: sender,
        });
    }

    // Add XFT to label's linked array (for initial linking)
    public fun add_linked_xft(
        label: &mut Label,
        xft_global_id: u64,
        fire_registry: &dexsta::fire::FireRegistry,
        operator_license: Option<&dexsta::operator::OperatorLicense>,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Validate authorization
        if (sender != label.owner) {
            // Check if sender is a registered contract in Fire Registry
            let is_registered_contract = dexsta::fire::is_contract_registered(fire_registry, sender);

            if (is_registered_contract) {
                // Registered contracts can call this function (they've done their own authorization)
            } else {
                // Must have valid operator license
                assert!(option::is_some(&operator_license), ENotAuthorized);
                let license = option::borrow(&operator_license);

                assert!(dexsta::operator::license_owner(license) == sender, ENotAuthorized);
                assert!(dexsta::operator::license_label_id(license) == object::uid_to_inner(&label.id), ENotAuthorized);
                assert!(!dexsta::operator::is_license_expired(license, clock), ENotAuthorized);
            };
        };

        // Get linked XFTs array
        let linked_xfts = df::borrow_mut<LinkedXFTsKey, LinkedXFTs>(&mut label.id, LinkedXFTsKey {});

        // Add XFT to linked array (avoid duplicates)
        let (already_linked, _) = vector::index_of(&linked_xfts.xft_global_ids, &xft_global_id);
        if (!already_linked) {
            vector::push_back(&mut linked_xfts.xft_global_ids, xft_global_id);
        };

        // Emit event
        event::emit(XFTLinkedToLabel {
            xft_global_id,
            label_id: object::uid_to_inner(&label.id),
            linked_by: sender,
        });
    }

    // Add label to label's linked array (for label-to-label linking)
    public fun add_linked_label(
        parent_label: &mut Label,
        child_label_global_id: u64,
        fire_registry: &dexsta::fire::FireRegistry,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Validate authorization - caller must own parent label OR have super operator license
        let is_owner = (sender == parent_label.owner);
        let has_super_operator = false;

        if (!is_owner) {
            // Check for super operator license
            let operator_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"operator"));
            if (option::is_some(&operator_contract_opt)) {
                let operator_contract_id = option::extract(&mut operator_contract_opt);
                let (is_operator_result, operator_role) = dexsta::operator::is_operator(
                    operator_contract_id,
                    parent_label.global_id,
                    sender,
                    clock
                );
                has_super_operator = (is_operator_result && operator_role == 1);
            };
        };

        assert!(is_owner || has_super_operator, ENotAuthorized);

        // Get linked XFTs array (reuse same structure for labels)
        let linked_items = df::borrow_mut<LinkedXFTsKey, LinkedXFTs>(&mut parent_label.id, LinkedXFTsKey {});

        // Add child label to linked array (avoid duplicates)
        let (already_linked, _) = vector::index_of(&linked_items.xft_global_ids, &child_label_global_id);
        if (!already_linked) {
            vector::push_back(&mut linked_items.xft_global_ids, child_label_global_id);
        };

        // Emit event
        event::emit(LabelLinkedToLabel {
            new_label_global_id: child_label_global_id,
            linked_to_label_global_id: parent_label.global_id,
            creator: sender,
        });
    }

    // Remove XFT from label's linked array (for unlinking)
    public fun remove_linked_xft(
        label: &mut Label,
        xft_global_id: u64,
        operator_license: Option<&dexsta::operator::OperatorLicense>,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Validate authorization
        if (sender != label.owner) {
            // Must have valid operator license
            assert!(option::is_some(&operator_license), ENotAuthorized);
            let license = option::borrow(&operator_license);

            assert!(dexsta::operator::license_owner(license) == sender, ENotAuthorized);
            assert!(dexsta::operator::license_label_id(license) == object::uid_to_inner(&label.id), ENotAuthorized);
            assert!(!dexsta::operator::is_license_expired(license, clock), ENotAuthorized);
        };

        // Get linked XFTs array
        let linked_xfts = df::borrow_mut<LinkedXFTsKey, LinkedXFTs>(&mut label.id, LinkedXFTsKey {});

        // Remove XFT from linked array
        let (found, index) = vector::index_of(&linked_xfts.xft_global_ids, &xft_global_id);
        assert!(found, EInvalidOwner); // XFT must be linked to remove
        vector::remove(&mut linked_xfts.xft_global_ids, index);

        // Emit event
        event::emit(XFTUnlinkedFromLabel {
            xft_global_id,
            label_id: object::uid_to_inner(&label.id),
            unlinked_by: sender,
        });
    }
    
    // ===== GET FUNCTIONS =====

    /// Get label by global ID (standardized function matching XFT contract)
    /// Frontend calls this with global ID, function handles the lookup
    /// If label doesn't exist, returns none
    public fun get_label(
        global_registry: &dexsta::registry::GlobalRegistry,
        global_id: u64
    ): Option<ID> {
        // Get object ID from global registry
        let object_id_opt = dexsta::registry::get_object_id(global_registry, global_id);
        if (option::is_some(&object_id_opt)) {
            let object_id = option::extract(&mut object_id_opt);
            option::some(object_id)
        } else {
            option::none()
        }
    }

    /// Get label ID by exact name match (simple version)
    public fun get_label_id(registry: &LabelRegistry, name: String): Option<ID> {
        if (table::contains(&registry.labels, name)) {
            option::some(*table::borrow(&registry.labels, name))
        } else {
            option::none()
        }
    }

    /// Get comprehensive label information by name
    public fun get_label_info(
        registry: &LabelRegistry,
        global_registry: &dexsta::registry::GlobalRegistry,
        name: String
    ): Option<LabelInfo> {
        if (table::contains(&registry.labels, name)) {
            let object_id = *table::borrow(&registry.labels, name);

            // Get global ID from global registry
            let global_id_opt = dexsta::registry::get_global_id(global_registry, object_id);

            if (option::is_some(&global_id_opt)) {
                let global_id = option::extract(&mut global_id_opt);

                // Get additional info from global registry
                let item_name_opt = dexsta::registry::get_item_name(global_registry, global_id);
                let creator_opt = dexsta::registry::get_item_creator(global_registry, global_id);

                option::some(LabelInfo {
                    object_id,
                    global_id,
                    name: if (option::is_some(&item_name_opt)) {
                        option::extract(&mut item_name_opt)
                    } else {
                        name
                    },
                    creator: if (option::is_some(&creator_opt)) {
                        option::extract(&mut creator_opt)
                    } else {
                        @0x0
                    },
                })
            } else {
                option::none()
            }
        } else {
            option::none()
        }
    }

    /// Get complete label details (when you have the label object)
    public fun get_label_details(
        label: &Label,
        global_registry: &dexsta::registry::GlobalRegistry
    ): LabelDetails {
        // Get creator from global registry
        let creator_opt = dexsta::registry::get_item_creator(global_registry, label.global_id);
        let creator = if (option::is_some(&creator_opt)) {
            option::extract(&mut creator_opt)
        } else {
            @0x0
        };

        // Get label balance
        let label_balance = df::borrow<BalanceKey, Balance<SUI>>(&label.id, BalanceKey {});
        let balance_value = balance::value(label_balance);

        // Get transferable from settings[9]
        let transferable_setting = *vector::borrow(&label.settings, 9);
        let transferable = transferable_setting == 1;

        LabelDetails {
            object_id: object::uid_to_inner(&label.id),
            global_id: label.global_id,
            name: label.name,
            owner: label.owner,
            creator,
            creation_time: label.creation_time,
            expiration_time: label.expiration_time,
            settings: label.settings,
            transferable,
            ipfs_hash: label.ipfs_hash,
            balance: balance_value,
        }
    }
    
    /// Get all labels of a specific type
    public fun get_labels_by_type(registry: &LabelRegistry, label_type: u64): vector<ID> {
        let labels_by_type = df::borrow<LabelsByTypeKey, LabelsByType>(&registry.id, LabelsByTypeKey {});
        
        if (table::contains(&labels_by_type.labels_by_type, label_type)) {
            *table::borrow(&labels_by_type.labels_by_type, label_type)
        } else {
            vector::empty()
        }
    }

    /// Get all labels owned by a specific address
    public fun get_labels_by_owner(registry: &LabelRegistry, owner: address): vector<ID> {
        let labels_by_owner = df::borrow<LabelsByOwnerKey, LabelsByOwner>(&registry.id, LabelsByOwnerKey {});
        
        if (table::contains(&labels_by_owner.labels_by_owner, owner)) {
            *table::borrow(&labels_by_owner.labels_by_owner, owner)
        } else {
            vector::empty()
        }
    }

    /// Get all XFTs linked to a label
    public fun get_xfts_linked_to_label(label: &Label): vector<u64> {
        let linked_xfts = df::borrow<LinkedXFTsKey, LinkedXFTs>(&label.id, LinkedXFTsKey {});
        linked_xfts.xft_global_ids
    }

    /// Get all labels linked to a label (child labels)
    public fun get_labels_linked_to_label(label: &Label): vector<u64> {
        // Reuse the same LinkedXFTs structure for labels
        let linked_items = df::borrow<LinkedXFTsKey, LinkedXFTs>(&label.id, LinkedXFTsKey {});
        linked_items.xft_global_ids
    }

    /// Get count of XFTs linked to a label
    public fun get_linked_xft_count(label: &Label): u64 {
        let linked_xfts = df::borrow<LinkedXFTsKey, LinkedXFTs>(&label.id, LinkedXFTsKey {});
        vector::length(&linked_xfts.xft_global_ids)
    }

    /// Get count of labels linked to a label
    public fun get_linked_label_count(label: &Label): u64 {
        let linked_items = df::borrow<LinkedXFTsKey, LinkedXFTs>(&label.id, LinkedXFTsKey {});
        vector::length(&linked_items.xft_global_ids)
    }

    /// Get label expiration time by global ID (internal helper function)
    fun get_label_expiration_by_global_id(
        registry: &LabelRegistry,
        label_global_id: u64
    ): Option<u64> {
        let expiration_lookup = df::borrow<ExpirationLookupKey, ExpirationLookup>(&registry.id, ExpirationLookupKey {});

        if (table::contains(&expiration_lookup.expiration_times, label_global_id)) {
            option::some(*table::borrow(&expiration_lookup.expiration_times, label_global_id))
        } else {
            option::none()
        }
    }

    // Get label balance
    public fun balance(label: &Label): u64 {
        let label_balance = df::borrow<BalanceKey, Balance<SUI>>(&label.id, BalanceKey {});
        balance::value(label_balance)
    }
    
    // ===== Accessor Functions =====
    public fun id(label: &Label): &UID {
        &label.id
    }

    public fun name(label: &Label): &String {
        &label.name
    }

    public fun owner(label: &Label): address {
        label.owner
    }

    public fun creation_time(label: &Label): u64 {
        label.creation_time
    }

    public fun expiration_time(label: &Label): u64 {
        label.expiration_time
    }

    public fun settings(label: &Label): &vector<u64> {
        &label.settings
    }
    
    public fun transferable(label: &Label): bool {
        let transferable_setting = *vector::borrow(&label.settings, 9);
        transferable_setting == 1
    }

    public fun ipfs_hash(label: &Label): &String {
        &label.ipfs_hash
    }

    public fun image_url(label: &Label): String {
        let ipfs_prefix = string::utf8(b"https://ipfs.io/ipfs/");
        string::append(&mut ipfs_prefix, label.ipfs_hash);
        ipfs_prefix
    }

    public fun global_id(label: &Label): u64 {
        label.global_id
    }

    /// Standardized function for wallets and 3rd party websites to get NFT metadata
    /// This is the function wallets will call to get IPFS metadata
    public fun tokenURI(label: &Label): String {
        label.ipfs_hash
    }

    /// Alternative standardized name (some wallets may expect this)
    public fun uri(label: &Label): String {
        label.ipfs_hash
    }
    
    // ===== Admin Functions =====

    /// Set Fire Registry ID (admin only)
    public fun set_fire_registry_id(
        registry: &mut LabelRegistry,
        fire_registry_id: ID,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.owner, ENotAuthorized);
        registry.fire_registry_id = fire_registry_id;
    }

    public fun marketplace_fee_bps(label: &Label): u64 {
        *vector::borrow(&label.settings, 11) // settings[11] = marketplace commission %
    }

    // Update marketplace fee (label owner or super operator only)
    public fun update_marketplace_fee_bps(
        label: &mut Label,
        new_fee_bps: u64,
        fire_registry: &dexsta::fire::FireRegistry,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check authorization - owner or super operator
        let is_authorized = if (sender == label.owner) {
            true
        } else {
            // Check if sender has super operator license
            let operator_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"operator"));
            if (option::is_some(&operator_contract_opt)) {
                let operator_contract_id = option::extract(&mut operator_contract_opt);
                let (is_operator_result, operator_role, _license_global_id) = dexsta::operator::is_operator(
                    operator_contract_id,
                    label.global_id,
                    sender,
                    clock
                );
                is_operator_result && operator_role == 1 // Must be super operator
            } else {
                false
            }
        };

        assert!(is_authorized, ENotAuthorized);

        // Validate fee (max 10% = 1000 basis points)
        assert!(new_fee_bps <= 1000, EInvalidOwner);

        // Update marketplace fee in settings[11]
        let settings = &mut label.settings;
        *vector::borrow_mut(settings, 11) = new_fee_bps;

        // Emit event
        event::emit(MarketplaceFeeUpdated {
            label_id: object::uid_to_inner(&label.id),
            global_id: label.global_id,
            old_fee_bps: *vector::borrow(&label.settings, 11),
            new_fee_bps,
            updated_by: sender,
        });
    }

    // Check if an address is an operator (calls operator contract)
    public fun is_operator(
        label: &Label,
        addr: address,
        fire_registry: &dexsta::fire::FireRegistry,
        clock: &Clock
    ): bool {
        // Get operator contract address from fire registry
        let operator_contract_opt = dexsta::fire::get_contract_address(fire_registry, string::utf8(b"operator"));

        if (option::is_some(&operator_contract_opt)) {
            let operator_contract_id = option::extract(&mut operator_contract_opt);

            // Call operator contract to check if address is operator for this label
            let (is_operator_result, _role, _license_global_id) = dexsta::operator::is_operator(
                operator_contract_id,
                label.global_id,  // Use global ID of label
                addr,             // Operator address
                clock
            );

            is_operator_result
        } else {
            false // No operator contract registered
        }
    }


}
