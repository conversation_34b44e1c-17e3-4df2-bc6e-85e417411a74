///
/// # Bank Module
///
/// This module implements a banking system that allows NFT owners to take loans
/// against the SUI balance stored in their NFTs. Similar to the teapot contract
/// in the BSC implementation, this bank:
///
/// - Allows users to get loans based on the SUI locked in their NFT's vault
/// - Implements a loan-to-value (LTV) system that increases with successful repayments
/// - <PERSON>les defaulted loans and allows others to purchase defaulted assets
/// - Includes a "love" system for rewarding NFT creators
///
/// ## Key Features
///
/// * Loans are issued based on a percentage of the NFT's SUI balance
/// * LTV increases with successful repayments (up to a maximum)
/// * Defaulted loans can be purchased by other users (with a fee)
/// * Includes gas refund system for certain operations
///
module nft_marketplace::bank {
    use std::string::{Self, String};
    use std::option::{Self, Option};
    use std::vector;
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::coin::{Self, Coin};
    use sui::sui::SUI;
    use sui::event;
    use sui::table::{Self, Table};
    use sui::clock::{Self, Clock};
    use sui::dynamic_field as df;
    use sui::vec_set::{Self, VecSet};
    use sui::balance::{Self, Balance};
    use nft_marketplace::nft::{Self, NFTData};
    use nft_marketplace::label::{Self, Label};

    // ===== Constants =====
    // Initial loan to value (45%)
    const INITIAL_LOAN_TO_VALUE: u64 = 45;
    // Increment for successful loan repayment (3%)
    const LOAN_TO_VALUE_INCREMENT: u64 = 3;
    // Maximum loan to value (85%)
    const MAX_LOAN_TO_VALUE: u64 = 85;
    // Default service fee (5%)
    const DEFAULT_SERVICE_FEE: u64 = 5;
    // Loan duration in seconds (30 days)
    const LOAN_DURATION: u64 = **********; // 30 days in milliseconds
    // Love reward amount (default 10,000,000 SUI units)
    const LOVE_REWARD: u64 = 10000000000; // 10 SUI
    // Minimum wallet balance to show love (100,000,000 SUI units)
    const MIN_WALLET_BALANCE: u64 = ************; // 100 SUI

    // ===== Errors =====
    const EInvalidOwner: u64 = 0;
    const EInvalidOperator: u64 = 1;
    const EInsufficientBalance: u64 = 2;
    const ELoanAlreadyActive: u64 = 3;
    const ELoanNotActive: u64 = 4;
    const ELoanNotDefaulted: u64 = 5;
    const ELoanDefaulted: u64 = 6;
    const EInsufficientPayment: u64 = 7;
    const ELoveAlreadyShown: u64 = 8;
    const EInsufficientLoveBalance: u64 = 9;
    const ENoGasRefund: u64 = 10;
    const ELowBankBalance: u64 = 11;
    const ENotAuthorized: u64 = 12;
    const ETransferFailed: u64 = 13;

    // ===== Events =====
    struct LoanIssued has copy, drop {
        loan_id: ID,
        nft_id: ID,
        loan_amount: u64,
        borrower: address,
        expiration_time: u64,
    }

    struct LoanRepaid has copy, drop {
        loan_id: ID,
        nft_id: ID,
        repay_amount: u64,
        borrower: address,
    }

    struct AssetBought has copy, drop {
        loan_id: ID,
        nft_id: ID,
        purchase_amount: u64,
        buyer: address,
    }

    struct LoveShownEvent has copy, drop {
        nft_id: ID,
        from: address,
        amount: u64,
    }

    struct GasRefunded has copy, drop {
        recipient: address,
        amount: u64,
    }

    struct GasRefundAdded has copy, drop {
        recipient: address,
        amount: u64,
    }

    // ===== Dynamic Field Keys =====
    struct BalanceKey has store, copy, drop {}
    struct OperatorsKey has store, copy, drop {}
    struct ActiveLoansKey has store, copy, drop {}
    struct UserLoansKey has store, copy, drop {}
    struct LoanToValueKey has store, copy, drop {}
    struct GasRefundsKey has store, copy, drop {}
    struct LoveShownKey has store, copy, drop {}
    struct DisplayEarningsKey has store, copy, drop {}

    // ===== Objects =====
    struct Bank has key {
        id: UID,
        owner: address,
        fee_address: address,
        marketing_address: address,
        love_reward: u64,
        min_wallet_balance: u64,
        loan_count: u64,
    }

    struct Loan has key, store {
        id: UID,
        nft_id: ID,
        borrower: address,
        collateral_amount: u64,
        expiration_time: u64,
        active: bool,
        defaulted: bool,
    }

    struct Operators has store {
        operators: VecSet<address>,
    }

    struct ActiveLoans has store {
        loans: vector<ID>,
    }

    struct UserLoans has store {
        loans: Table<address, vector<ID>>,
    }

    struct LoanToValue has store {
        values: Table<address, u64>,
    }

    struct GasRefunds has store {
        refunds: Table<address, u64>,
    }

    struct LoveShownTracker has store {
        shown: Table<address, Table<ID, bool>>,
    }

    struct DisplayEarnings has store {
        earnings: Table<ID, u64>,
    }

    // ===== Functions =====
    // Initialize the bank
    fun init(ctx: &mut TxContext) {
        let sender = tx_context::sender(ctx);
        let bank_id = object::new(ctx);

        let bank = Bank {
            id: bank_id,
            owner: sender,
            fee_address: sender,
            marketing_address: sender,
            love_reward: LOVE_REWARD,
            min_wallet_balance: MIN_WALLET_BALANCE,
            loan_count: 0,
        };

        // Initialize dynamic fields
        // 1. Balance for holding SUI
        df::add(&mut bank.id, BalanceKey {}, balance::zero<SUI>());

        // 2. Operators list with owner as initial operator
        let operators_set = vec_set::empty<address>();
        vec_set::insert(&mut operators_set, sender);
        let operators = Operators { operators: operators_set };
        df::add(&mut bank.id, OperatorsKey {}, operators);

        // 3. Active loans
        df::add(&mut bank.id, ActiveLoansKey {}, ActiveLoans {
            loans: vector::empty<ID>()
        });

        // 4. User loans
        df::add(&mut bank.id, UserLoansKey {}, UserLoans {
            loans: table::new(ctx)
        });

        // 5. Loan to value
        df::add(&mut bank.id, LoanToValueKey {}, LoanToValue {
            values: table::new(ctx)
        });

        // 6. Gas refunds
        df::add(&mut bank.id, GasRefundsKey {}, GasRefunds {
            refunds: table::new(ctx)
        });

        // 7. Love shown
        df::add(&mut bank.id, LoveShownKey {}, LoveShownTracker {
            shown: table::new<address, Table<ID, bool>>(ctx)
        });

        // 8. Display earnings
        df::add(&mut bank.id, DisplayEarningsKey {}, DisplayEarnings {
            earnings: table::new(ctx)
        });

        transfer::share_object(bank);
    }

    // Issue a loan based on the NFT's SUI balance
    public fun issue_loan(
        bank: &mut Bank,
        nft: &mut NFTData,
        clock: &Clock,
        ctx: &mut TxContext
    ): Coin<SUI> {
        let sender = tx_context::sender(ctx);

        // Check if NFT is owned by sender
        assert!(nft::is_operator(nft, sender), EInvalidOwner);

        // Get NFT ID
        let nft_id = object::uid_to_inner(nft::id(nft));

        // Check if there's already an active loan for this NFT
        let active_loans = df::borrow<ActiveLoansKey, ActiveLoans>(&bank.id, ActiveLoansKey {});
        let i = 0;
        while (i < vector::length(&active_loans.loans)) {
            let loan_id = *vector::borrow(&active_loans.loans, i);
            let loan = df::borrow<ID, Loan>(&bank.id, loan_id);
            if (loan.nft_id == nft_id) {
                assert!(false, ELoanAlreadyActive);
            };
            i = i + 1;
        };

        // Get user's loan-to-value ratio
        let ltv = get_user_ltv(bank, sender);

        // Get NFT's SUI balance
        let nft_balance = nft::balance(nft);

        // Calculate loan amount based on LTV
        let loan_amount = (nft_balance * ltv) / 100;

        // Check if bank has enough balance
        let bank_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut bank.id, BalanceKey {});
        assert!(balance::value(bank_balance) >= loan_amount, EInsufficientBalance);

        // Transfer loan amount to user first
        let loan_coin = coin::take(bank_balance, loan_amount, ctx);

        // Create loan object
        let loan_id = object::new(ctx);
        let loan_id_inner = object::uid_to_inner(&loan_id);
        let loan = Loan {
            id: loan_id,
            nft_id,
            borrower: sender,
            collateral_amount: loan_amount,
            expiration_time: clock::timestamp_ms(clock) + LOAN_DURATION,
            active: true,
            defaulted: false,
        };

        // Store loan in bank
        df::add(&mut bank.id, loan_id_inner, loan);

        // Add to active loans
        let active_loans = df::borrow_mut<ActiveLoansKey, ActiveLoans>(&mut bank.id, ActiveLoansKey {});
        vector::push_back(&mut active_loans.loans, loan_id_inner);

        // Add to user loans
        let user_loans = df::borrow_mut<UserLoansKey, UserLoans>(&mut bank.id, UserLoansKey {});
        if (!table::contains(&user_loans.loans, sender)) {
            table::add(&mut user_loans.loans, sender, vector::empty<ID>());
        };

        let user_loans_vec = table::borrow_mut(&mut user_loans.loans, sender);
        vector::push_back(user_loans_vec, loan_id_inner);

        // Transfer NFT to bank (escrow) - simplified for now
        // nft::transfer(nft, 1, tx_context::sender(ctx), ctx);

        // Increment loan count
        bank.loan_count = bank.loan_count + 1;

        // Emit event
        event::emit(LoanIssued {
            loan_id: loan_id_inner,
            nft_id,
            loan_amount,
            borrower: sender,
            expiration_time: clock::timestamp_ms(clock) + LOAN_DURATION,
        });

        loan_coin
    }

    // Repay a loan
    public fun repay_loan(
        bank: &mut Bank,
        nft: &mut NFTData,
        payment: &mut Coin<SUI>,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        let nft_id = object::uid_to_inner(nft::id(nft));

        // Find the loan for this NFT
        let loan_id_opt = find_loan_by_nft(bank, nft_id);
        assert!(option::is_some(&loan_id_opt), ELoanNotActive);

        let loan_id = option::extract(&mut loan_id_opt);

        // Get loan info before borrowing mutably
        let loan_collateral_amount = {
            let loan = df::borrow<ID, Loan>(&bank.id, loan_id);
            // Check if loan is not defaulted
            assert!(clock::timestamp_ms(clock) <= loan.expiration_time, ELoanDefaulted);
            loan.collateral_amount
        };

        // Check if payment is sufficient
        assert!(coin::value(payment) >= loan_collateral_amount, EInsufficientPayment);

        // Process payment
        let repay_coin = coin::split(payment, loan_collateral_amount, ctx);
        let bank_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut bank.id, BalanceKey {});
        balance::join(bank_balance, coin::into_balance(repay_coin));

        // Update loan status
        let loan = df::borrow_mut<ID, Loan>(&mut bank.id, loan_id);
        loan.active = false;

        // Transfer NFT back to borrower - simplified for now
        // nft::transfer(nft, 1, sender, ctx);

        // Remove from active loans
        remove_from_active_loans(bank, loan_id);

        // Remove from user loans
        remove_from_user_loans(bank, sender, loan_id);

        // Increase user's LTV if not at max
        increase_user_ltv(bank, sender);

        // Emit event
        event::emit(LoanRepaid {
            loan_id,
            nft_id,
            repay_amount: loan_collateral_amount,
            borrower: sender,
        });
    }

    // Buy a defaulted asset
    public fun buy_defaulted_asset(
        bank: &mut Bank,
        nft: &mut NFTData,
        payment: &mut Coin<SUI>,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        let nft_id = object::uid_to_inner(nft::id(nft));

        // Find the loan for this NFT
        let loan_id_opt = find_loan_by_nft(bank, nft_id);
        assert!(option::is_some(&loan_id_opt), ELoanNotActive);

        let loan_id = option::extract(&mut loan_id_opt);

        // Get loan info before borrowing mutably
        let (loan_collateral_amount, loan_borrower) = {
            let loan = df::borrow<ID, Loan>(&bank.id, loan_id);
            // Check if loan is defaulted
            assert!(clock::timestamp_ms(clock) > loan.expiration_time, ELoanNotDefaulted);
            (loan.collateral_amount, loan.borrower)
        };

        // Calculate purchase amount (loan amount + service fee)
        let fee = (loan_collateral_amount * DEFAULT_SERVICE_FEE) / 100;
        let fee_split = fee / 2;
        let purchase_amount = loan_collateral_amount + fee;

        // Check if payment is sufficient
        assert!(coin::value(payment) >= purchase_amount, EInsufficientPayment);

        // Process payment
        let purchase_coin = coin::split(payment, purchase_amount, ctx);
        let bank_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut bank.id, BalanceKey {});
        balance::join(bank_balance, coin::into_balance(purchase_coin));

        // Transfer fees
        let marketing_fee = coin::take(bank_balance, fee_split, ctx);
        transfer::public_transfer(marketing_fee, bank.marketing_address);

        let owner_fee = coin::take(bank_balance, fee_split, ctx);
        transfer::public_transfer(owner_fee, bank.fee_address);

        // Transfer NFT to buyer - simplified for now
        // nft::transfer(nft, 1, sender, ctx);

        // Update loan status
        let loan = df::borrow_mut<ID, Loan>(&mut bank.id, loan_id);
        loan.active = false;
        loan.defaulted = true;

        // Remove from active loans
        remove_from_active_loans(bank, loan_id);

        // Remove from user loans
        remove_from_user_loans(bank, loan_borrower, loan_id);

        // Emit event
        event::emit(AssetBought {
            loan_id,
            nft_id,
            purchase_amount,
            buyer: sender,
        });
    }

    // Show love to an NFT (reward the creator)
    public fun show_love(
        bank: &mut Bank,
        nft: &mut NFTData,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        let nft_id = object::uid_to_inner(nft::id(nft));

        // Check if love already shown and get required info
        let (sender_balance, ltv) = {
            let love_shown = df::borrow<LoveShownKey, LoveShownTracker>(&bank.id, LoveShownKey {});
            if (table::contains(&love_shown.shown, sender)) {
                let user_love_shown = table::borrow(&love_shown.shown, sender);
                assert!(!table::contains(user_love_shown, nft_id), ELoveAlreadyShown);
            };

            let sender_balance = balance::value(df::borrow<BalanceKey, Balance<SUI>>(&bank.id, BalanceKey {}));
            let ltv = get_user_ltv(bank, sender);
            (sender_balance, ltv)
        };

        assert!(sender_balance >= bank.min_wallet_balance, EInsufficientLoveBalance);

        // Check if NFT has enough balance to receive love
        let nft_balance = nft::balance(nft);
        assert!(nft_balance > bank.love_reward, EInsufficientLoveBalance);

        // Calculate love bonus based on user's LTV
        let love_bonus = if (ltv > 0) {
            (bank.love_reward * ltv) / 100
        } else {
            0
        };

        let total_love = bank.love_reward + love_bonus;

        // Transfer love reward to NFT
        let bank_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut bank.id, BalanceKey {});
        let love_coin = coin::take(bank_balance, total_love, ctx);
        nft::deposit(nft, &mut love_coin, total_love, ctx);
        coin::destroy_zero(love_coin);

        // Mark love as shown
        let love_shown = df::borrow_mut<LoveShownKey, LoveShownTracker>(&mut bank.id, LoveShownKey {});
        if (!table::contains(&love_shown.shown, sender)) {
            table::add(&mut love_shown.shown, sender, table::new<ID, bool>(ctx));
        };
        let user_love_shown = table::borrow_mut(&mut love_shown.shown, sender);
        table::add(user_love_shown, nft_id, true);

        // Update display earnings
        let display_earnings = df::borrow_mut<DisplayEarningsKey, DisplayEarnings>(&mut bank.id, DisplayEarningsKey {});
        if (!table::contains(&display_earnings.earnings, nft_id)) {
            table::add(&mut display_earnings.earnings, nft_id, 0);
        };

        let earnings = table::borrow_mut(&mut display_earnings.earnings, nft_id);
        *earnings = *earnings + total_love;

        // Emit event
        event::emit(LoveShownEvent {
            nft_id,
            from: sender,
            amount: total_love,
        });
    }

    // Add gas refund for a user
    public fun add_gas_refund(
        bank: &mut Bank,
        recipient: address,
        amount: u64,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(is_operator(bank, sender), ENotAuthorized);

        // Add gas refund
        let gas_refunds = df::borrow_mut<GasRefundsKey, GasRefunds>(&mut bank.id, GasRefundsKey {});
        if (!table::contains(&gas_refunds.refunds, recipient)) {
            table::add(&mut gas_refunds.refunds, recipient, 0);
        };

        let refund = table::borrow_mut(&mut gas_refunds.refunds, recipient);
        *refund = *refund + amount;

        // Emit event
        event::emit(GasRefundAdded {
            recipient,
            amount,
        });
    }

    // Claim gas refund
    public fun claim_gas_refund(
        bank: &mut Bank,
        ctx: &mut TxContext
    ): Coin<SUI> {
        let sender = tx_context::sender(ctx);

        // Get refund amount first
        let refund_amount = {
            let gas_refunds = df::borrow<GasRefundsKey, GasRefunds>(&bank.id, GasRefundsKey {});
            assert!(table::contains(&gas_refunds.refunds, sender), ENoGasRefund);
            let refund = table::borrow(&gas_refunds.refunds, sender);
            assert!(*refund > 0, ENoGasRefund);
            *refund
        };

        // Check if bank has enough balance and transfer
        let bank_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut bank.id, BalanceKey {});
        assert!(balance::value(bank_balance) >= refund_amount, ELowBankBalance);
        let refund_coin = coin::take(bank_balance, refund_amount, ctx);

        // Reset refund amount
        let gas_refunds = df::borrow_mut<GasRefundsKey, GasRefunds>(&mut bank.id, GasRefundsKey {});
        let refund = table::borrow_mut(&mut gas_refunds.refunds, sender);
        *refund = 0;

        // Emit event
        event::emit(GasRefunded {
            recipient: sender,
            amount: refund_amount,
        });

        refund_coin
    }

    // Set bank parameters
    public fun set_parameters(
        bank: &mut Bank,
        love_reward: Option<u64>,
        min_wallet_balance: Option<u64>,
        fee_address: Option<address>,
        marketing_address: Option<address>,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(sender == bank.owner, ENotAuthorized);

        // Update parameters
        if (option::is_some(&love_reward)) {
            bank.love_reward = option::extract(&mut love_reward);
        };

        if (option::is_some(&min_wallet_balance)) {
            bank.min_wallet_balance = option::extract(&mut min_wallet_balance);
        };

        if (option::is_some(&fee_address)) {
            bank.fee_address = option::extract(&mut fee_address);
        };

        if (option::is_some(&marketing_address)) {
            bank.marketing_address = option::extract(&mut marketing_address);
        };
    }

    // Add an operator to the bank
    public fun add_operator(
        bank: &mut Bank,
        operator: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(sender == bank.owner, ENotAuthorized);

        // Add operator
        let operators = df::borrow_mut<OperatorsKey, Operators>(&mut bank.id, OperatorsKey {});
        if (!vec_set::contains(&operators.operators, &operator)) {
            vec_set::insert(&mut operators.operators, operator);
        };
    }

    // Remove an operator from the bank
    public fun remove_operator(
        bank: &mut Bank,
        operator: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(sender == bank.owner, ENotAuthorized);
        assert!(operator != bank.owner, EInvalidOperator); // Can't remove owner

        // Remove operator
        let operators = df::borrow_mut<OperatorsKey, Operators>(&mut bank.id, OperatorsKey {});
        if (vec_set::contains(&operators.operators, &operator)) {
            vec_set::remove(&mut operators.operators, &operator);
        };
    }

    // Withdraw SUI from the bank
    public fun withdraw(
        bank: &mut Bank,
        amount: u64,
        ctx: &mut TxContext
    ): Coin<SUI> {
        let sender = tx_context::sender(ctx);

        // Check if sender is authorized
        assert!(sender == bank.owner, ENotAuthorized);

        // Check if bank has enough balance
        let bank_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut bank.id, BalanceKey {});
        assert!(balance::value(bank_balance) >= amount, EInsufficientBalance);

        // Transfer amount to sender
        coin::take(bank_balance, amount, ctx)
    }

    // Deposit SUI to the bank
    public fun deposit(
        bank: &mut Bank,
        payment: &mut Coin<SUI>,
        amount: u64,
        ctx: &mut TxContext
    ) {
        // Check if payment is sufficient
        assert!(coin::value(payment) >= amount, EInsufficientPayment);

        // Process payment
        let deposit_coin = coin::split(payment, amount, ctx);
        let bank_balance = df::borrow_mut<BalanceKey, Balance<SUI>>(&mut bank.id, BalanceKey {});
        balance::join(bank_balance, coin::into_balance(deposit_coin));
    }

    // ===== Helper Functions =====

    // Find a loan by NFT ID
    fun find_loan_by_nft(bank: &Bank, nft_id: ID): Option<ID> {
        let active_loans = df::borrow<ActiveLoansKey, ActiveLoans>(&bank.id, ActiveLoansKey {});
        let i = 0;
        while (i < vector::length(&active_loans.loans)) {
            let loan_id = *vector::borrow(&active_loans.loans, i);
            let loan = df::borrow<ID, Loan>(&bank.id, loan_id);
            if (loan.nft_id == nft_id) {
                return option::some(loan_id)
            };
            i = i + 1;
        };
        option::none()
    }

    // Remove a loan from active loans
    fun remove_from_active_loans(bank: &mut Bank, loan_id: ID) {
        let active_loans = df::borrow_mut<ActiveLoansKey, ActiveLoans>(&mut bank.id, ActiveLoansKey {});
        let i = 0;
        let found = false;
        let index = 0;

        while (i < vector::length(&active_loans.loans) && !found) {
            if (*vector::borrow(&active_loans.loans, i) == loan_id) {
                found = true;
                index = i;
            };
            i = i + 1;
        };

        if (found) {
            vector::swap_remove(&mut active_loans.loans, index);
        };
    }

    // Remove a loan from user loans
    fun remove_from_user_loans(bank: &mut Bank, user: address, loan_id: ID) {
        let user_loans = df::borrow_mut<UserLoansKey, UserLoans>(&mut bank.id, UserLoansKey {});
        if (table::contains(&user_loans.loans, user)) {
            let user_loans_vec = table::borrow_mut(&mut user_loans.loans, user);
            let i = 0;
            let found = false;
            let index = 0;

            while (i < vector::length(user_loans_vec) && !found) {
                if (*vector::borrow(user_loans_vec, i) == loan_id) {
                    found = true;
                    index = i;
                };
                i = i + 1;
            };

            if (found) {
                vector::swap_remove(user_loans_vec, index);
            };
        };
    }

    // Get user's loan-to-value ratio
    fun get_user_ltv(bank: &Bank, user: address): u64 {
        let loan_to_value = df::borrow<LoanToValueKey, LoanToValue>(&bank.id, LoanToValueKey {});
        if (table::contains(&loan_to_value.values, user)) {
            *table::borrow(&loan_to_value.values, user)
        } else {
            INITIAL_LOAN_TO_VALUE
        }
    }

    // Increase user's loan-to-value ratio
    fun increase_user_ltv(bank: &mut Bank, user: address) {
        let loan_to_value = df::borrow_mut<LoanToValueKey, LoanToValue>(&mut bank.id, LoanToValueKey {});
        if (!table::contains(&loan_to_value.values, user)) {
            table::add(&mut loan_to_value.values, user, INITIAL_LOAN_TO_VALUE);
        };

        let ltv = table::borrow_mut(&mut loan_to_value.values, user);
        if (*ltv < MAX_LOAN_TO_VALUE) {
            *ltv = *ltv + LOAN_TO_VALUE_INCREMENT;
        };
    }

    // Check if an address is an operator
    fun is_operator(bank: &Bank, addr: address): bool {
        if (addr == bank.owner) {
            return true
        };

        let operators = df::borrow<OperatorsKey, Operators>(&bank.id, OperatorsKey {});
        vec_set::contains(&operators.operators, &addr)
    }

    // ===== View Functions =====

    // Get active loans
    public fun get_active_loans(bank: &Bank): vector<ID> {
        let active_loans = df::borrow<ActiveLoansKey, ActiveLoans>(&bank.id, ActiveLoansKey {});
        active_loans.loans
    }

    // Get user loans
    public fun get_user_loans(bank: &Bank, user: address): vector<ID> {
        let user_loans = df::borrow<UserLoansKey, UserLoans>(&bank.id, UserLoansKey {});
        if (table::contains(&user_loans.loans, user)) {
            *table::borrow(&user_loans.loans, user)
        } else {
            vector::empty<ID>()
        }
    }

    // Get loan details
    public fun get_loan_details(bank: &Bank, loan_id: ID): (ID, address, u64, u64, bool, bool) {
        let loan = df::borrow<ID, Loan>(&bank.id, loan_id);
        (
            loan.nft_id,
            loan.borrower,
            loan.collateral_amount,
            loan.expiration_time,
            loan.active,
            loan.defaulted
        )
    }

    // Get display earnings for an NFT
    public fun get_display_earnings(bank: &Bank, nft_id: ID): u64 {
        let display_earnings = df::borrow<DisplayEarningsKey, DisplayEarnings>(&bank.id, DisplayEarningsKey {});
        if (table::contains(&display_earnings.earnings, nft_id)) {
            *table::borrow(&display_earnings.earnings, nft_id)
        } else {
            0
        }
    }

    // Get gas refund for a user
    public fun get_gas_refund(bank: &Bank, user: address): u64 {
        let gas_refunds = df::borrow<GasRefundsKey, GasRefunds>(&bank.id, GasRefundsKey {});
        if (table::contains(&gas_refunds.refunds, user)) {
            *table::borrow(&gas_refunds.refunds, user)
        } else {
            0
        }
    }

    // Get bank balance
    public fun get_balance(bank: &Bank): u64 {
        let bank_balance = df::borrow<BalanceKey, Balance<SUI>>(&bank.id, BalanceKey {});
        balance::value(bank_balance)
    }
}
