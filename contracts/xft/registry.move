///
/// # Global Registry Module
///
/// This module implements a global auto-incrementing registry for all minted items
/// in the XFT ecosystem. It ensures sequential numbering across all item types:
/// labels, XFTs, operator licenses, etc.
///
/// ## Key Features
///
/// * Auto-incrementing global IDs starting from #2 (reserve #1 for special use)
/// * No overlapping numbers between different item types
/// * Bidirectional mapping: Global ID ↔ Object ID
/// * Search by global ID or object ID
/// * Item type tracking (label, xft, operator license, etc.)
/// * Total count tracking for all minted items
///
/// ## Item Types
///
/// * Type 1: Label
/// * Type 2: XFT
/// * Type 3: Operator License
/// * Type 4: Marketplace License
/// * Type 5: Reserved for future use
///
module nft_marketplace::registry {
    use sui::object::{Self, UID, ID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::table::{Self, Table};
    use sui::event;
    use std::string::{Self, String};
    use std::option::{Self, Option};

    // ===== Constants =====
    const ITEM_TYPE_LABEL: u64 = 1;
    const ITEM_TYPE_XFT: u64 = 2;
    const ITEM_TYPE_OPERATOR_LICENSE: u64 = 3;
    const ITEM_TYPE_MARKETPLACE_LICENSE: u64 = 4;

    // ===== Errors =====
    const ENotAuthorized: u64 = 0;
    const EItemNotFound: u64 = 1;
    const EInvalidItemType: u64 = 2;
    const EItemAlreadyRegistered: u64 = 3;

    // ===== Events =====
    public struct ItemRegistered has copy, drop {
        global_id: u64,
        object_id: ID,
        item_type: u64,
        creator: address,
        name: String,
    }

    public struct ItemUpdated has copy, drop {
        global_id: u64,
        object_id: ID,
        old_name: String,
        new_name: String,
    }

    // ===== One-Time Witness =====
    public struct REGISTRY has drop {}

    // ===== Objects =====
    
    /// Global registry for all minted items with auto-incrementing IDs
    public struct GlobalRegistry has key {
        id: UID,
        admin: address,
        // Auto-incrementing counter (starts at 2, reserve #1)
        next_global_id: u64,
        // Bidirectional mappings
        global_to_object: Table<u64, ID>,      // Global ID -> Object ID
        object_to_global: Table<ID, u64>,      // Object ID -> Global ID
        // Item metadata
        global_to_type: Table<u64, u64>,       // Global ID -> Item Type
        global_to_name: Table<u64, String>,    // Global ID -> Item Name
        global_to_creator: Table<u64, address>, // Global ID -> Creator
        // Statistics
        total_items: u64,
        items_by_type: Table<u64, u64>,        // Item Type -> Count
    }

    // ===== Functions =====
    fun init(witness: REGISTRY, ctx: &mut TxContext) {
        let admin = tx_context::sender(ctx);
        
        let registry = GlobalRegistry {
            id: object::new(ctx),
            admin,
            next_global_id: 2, // Start at #2, reserve #1 for special use
            global_to_object: table::new(ctx),
            object_to_global: table::new(ctx),
            global_to_type: table::new(ctx),
            global_to_name: table::new(ctx),
            global_to_creator: table::new(ctx),
            total_items: 0,
            items_by_type: table::new(ctx),
        };

        transfer::share_object(registry);
    }

    /// Register a new item and get its global ID
    public fun register_item(
        registry: &mut GlobalRegistry,
        object_id: ID,
        item_type: u64,
        name: String,
        ctx: &mut TxContext
    ): u64 {
        let creator = tx_context::sender(ctx);
        
        // Validate item type
        assert!(item_type >= 1 && item_type <= 4, EInvalidItemType);
        
        // Ensure object isn't already registered
        assert!(!table::contains(&registry.object_to_global, object_id), EItemAlreadyRegistered);
        
        // Get next global ID
        let global_id = registry.next_global_id;
        registry.next_global_id = registry.next_global_id + 1;
        
        // Add bidirectional mappings
        table::add(&mut registry.global_to_object, global_id, object_id);
        table::add(&mut registry.object_to_global, object_id, global_id);
        
        // Add metadata
        table::add(&mut registry.global_to_type, global_id, item_type);
        table::add(&mut registry.global_to_name, global_id, name);
        table::add(&mut registry.global_to_creator, global_id, creator);
        
        // Update statistics
        registry.total_items = registry.total_items + 1;
        
        // Update type count
        if (table::contains(&registry.items_by_type, item_type)) {
            let current_count = table::remove(&mut registry.items_by_type, item_type);
            table::add(&mut registry.items_by_type, item_type, current_count + 1);
        } else {
            table::add(&mut registry.items_by_type, item_type, 1);
        };
        
        // Emit event
        event::emit(ItemRegistered {
            global_id,
            object_id,
            item_type,
            creator,
            name,
        });
        
        global_id
    }

    /// Update item name
    public fun update_item_name(
        registry: &mut GlobalRegistry,
        object_id: ID,
        new_name: String,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        
        // Get global ID from object ID
        assert!(table::contains(&registry.object_to_global, object_id), EItemNotFound);
        let global_id = *table::borrow(&registry.object_to_global, object_id);
        
        // Verify sender is creator or admin
        let creator = *table::borrow(&registry.global_to_creator, global_id);
        assert!(sender == creator || sender == registry.admin, ENotAuthorized);
        
        // Update name
        let old_name = table::remove(&mut registry.global_to_name, global_id);
        table::add(&mut registry.global_to_name, global_id, new_name);
        
        // Emit event
        event::emit(ItemUpdated {
            global_id,
            object_id,
            old_name,
            new_name,
        });
    }

    // ===== View Functions =====
    
    /// Get object ID from global ID
    public fun get_object_id(registry: &GlobalRegistry, global_id: u64): Option<ID> {
        if (table::contains(&registry.global_to_object, global_id)) {
            option::some(*table::borrow(&registry.global_to_object, global_id))
        } else {
            option::none()
        }
    }
    
    /// Get global ID from object ID
    public fun get_global_id(registry: &GlobalRegistry, object_id: ID): Option<u64> {
        if (table::contains(&registry.object_to_global, object_id)) {
            option::some(*table::borrow(&registry.object_to_global, object_id))
        } else {
            option::none()
        }
    }
    
    /// Get item type from global ID
    public fun get_item_type(registry: &GlobalRegistry, global_id: u64): Option<u64> {
        if (table::contains(&registry.global_to_type, global_id)) {
            option::some(*table::borrow(&registry.global_to_type, global_id))
        } else {
            option::none()
        }
    }
    
    /// Get item name from global ID
    public fun get_item_name(registry: &GlobalRegistry, global_id: u64): Option<String> {
        if (table::contains(&registry.global_to_name, global_id)) {
            option::some(*table::borrow(&registry.global_to_name, global_id))
        } else {
            option::none()
        }
    }
    
    /// Get item creator from global ID
    public fun get_item_creator(registry: &GlobalRegistry, global_id: u64): Option<address> {
        if (table::contains(&registry.global_to_creator, global_id)) {
            option::some(*table::borrow(&registry.global_to_creator, global_id))
        } else {
            option::none()
        }
    }
    
    /// Get total items count
    public fun get_total_items(registry: &GlobalRegistry): u64 {
        registry.total_items
    }
    
    /// Get count by item type
    public fun get_count_by_type(registry: &GlobalRegistry, item_type: u64): u64 {
        if (table::contains(&registry.items_by_type, item_type)) {
            *table::borrow(&registry.items_by_type, item_type)
        } else {
            0
        }
    }
    
    /// Get next global ID (for preview)
    public fun get_next_global_id(registry: &GlobalRegistry): u64 {
        registry.next_global_id
    }
    
    /// Check if item exists by global ID
    public fun item_exists_by_global_id(registry: &GlobalRegistry, global_id: u64): bool {
        table::contains(&registry.global_to_object, global_id)
    }
    
    /// Check if item exists by object ID
    public fun item_exists_by_object_id(registry: &GlobalRegistry, object_id: ID): bool {
        table::contains(&registry.object_to_global, object_id)
    }

    // ===== Admin Functions =====
    
    /// Update admin (only current admin can do this)
    public fun update_admin(
        registry: &mut GlobalRegistry,
        new_admin: address,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        assert!(sender == registry.admin, ENotAuthorized);
        registry.admin = new_admin;
    }
}
