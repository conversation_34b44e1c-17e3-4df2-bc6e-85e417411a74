/**
 * Test Script for Module Deployment System
 * Tests the complete template-based token creation flow
 */

const { SuiClient, getFullnodeUrl } = require('@mysten/sui/client')
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519')
const { fromB64 } = require('@mysten/sui/utils')

// Configuration
const NETWORK = 'devnet'
const PACKAGE_ID = '0x75da1bbe12c141aa2269396420b17355847063e55f0211fe97b3969c49618bcf'
const TOKEN_REGISTRY = '0xe25b786282469e3e78571b9fed5cfe9c81203eac20bfaa82bae79611dd6cba68'
const PRIVATE_KEY = 'suiprivkey1qq96u4z3h5ze7ptnm69d45fugmutr499mwddfqt6009jzfgqypge6fmrzws'

async function testDeploymentSystem() {
  console.log('🧪 Testing Module Deployment System')
  console.log('=====================================')

  // Initialize client and keypair
  const client = new SuiClient({ url: getFullnodeUrl(NETWORK) })
  const keypair = Ed25519Keypair.fromSecretKey(fromB64(PRIVATE_KEY).slice(1))
  const address = keypair.getPublicKey().toSuiAddress()

  console.log('📋 Configuration:')
  console.log('  Network:', NETWORK)
  console.log('  Package ID:', PACKAGE_ID)
  console.log('  Registry:', TOKEN_REGISTRY)
  console.log('  Address:', address)
  console.log('')

  try {
    // Step 1: Check for existing deployment events
    console.log('🔍 Step 1: Checking for existing deployment events...')
    
    const eventQuery = {
      MoveEventType: `${PACKAGE_ID}::dexsta_token::TokenModuleDeploymentRequested`
    }

    const events = await client.queryEvents({
      query: eventQuery,
      limit: 10,
      order: 'descending'
    })

    console.log(`Found ${events.data?.length || 0} deployment events`)

    if (events.data && events.data.length > 0) {
      console.log('📋 Recent deployment events:')
      events.data.forEach((event, index) => {
        const eventData = event.parsedJson
        console.log(`  ${index + 1}. Token ID: ${eventData.token_id}, Symbol: ${eventData.symbol}, Name: ${eventData.name}`)
      })

      // Test deployment of the most recent event
      const latestEvent = events.data[0]
      const eventData = latestEvent.parsedJson
      
      console.log('')
      console.log(`🚀 Step 2: Testing deployment of Token ID ${eventData.token_id} (${eventData.symbol})...`)
      
      // Make API call to deploy
      const response = await fetch('http://localhost:3000/api/deploy-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tokenId: eventData.token_id }),
      })

      const result = await response.json()

      if (response.ok) {
        console.log('✅ Deployment successful!')
        console.log('📦 Package ID:', result.packageId)
        console.log('🏛️ Treasury Cap:', result.treasuryCapId)
        console.log('📋 Coin Metadata:', result.coinMetadataId)
      } else {
        console.log('❌ Deployment failed:', result.error)
      }

    } else {
      console.log('⚠️ No deployment events found. Create a token first using the frontend.')
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testDeploymentSystem().catch(console.error)
