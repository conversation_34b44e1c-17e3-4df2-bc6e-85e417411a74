/**
 * Test RPC-based Token Deployment
 * Tests the new RPC deployment system without CLI dependency
 */

const { rpcDeployer } = require('./src/lib/rpcDeployment.ts');

async function testRPCDeployment() {
  console.log('🧪 Testing RPC-based Token Deployment');
  console.log('=====================================');

  try {
    // Test deployment request
    const testRequest = {
      symbol: 'TESTCOIN',
      name: 'Test Coin',
      description: 'A test token deployed via RPC',
      iconUrl: '',
      decimals: 9,
      creator: '0x107850bf3302019d71a699210d8fcf1789065700d3a53f2d6bd7322733c4d7fe'
    };

    console.log('📋 Test request:', testRequest);

    // Deploy the token
    const result = await rpcDeployer.deployTokenModule(testRequest);

    if (result.success) {
      console.log('🎉 RPC deployment test PASSED!');
      console.log('✅ Results:', {
        packageId: result.packageId,
        treasuryCapId: result.treasuryCapId,
        coinMetadataId: result.coinMetadataId,
        transactionDigest: result.transactionDigest
      });
      
      console.log('🌐 View on explorer:', `https://suiexplorer.com/txblock/${result.transactionDigest}?network=devnet`);
      
    } else {
      console.log('❌ RPC deployment test FAILED!');
      console.log('Error:', result.error);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testRPCDeployment().catch(console.error);
