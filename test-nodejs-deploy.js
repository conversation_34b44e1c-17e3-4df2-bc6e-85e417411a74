/**
 * Test Script for Node.js Token Deployment
 * Based on example/nodejs_deploy.js but integrated with our event system
 */

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");
const { SuiClient, getFullnodeUrl } = require('@mysten/sui/client');

// Configuration
const NETWORK = 'devnet';
const PACKAGE_ID = '0x75da1bbe12c141aa2269396420b17355847063e55f0211fe97b3969c49618bcf';

async function testTokenDeployment() {
  console.log('🧪 Testing Node.js Token Deployment');
  console.log('====================================');

  // Initialize Sui client
  const client = new SuiClient({ url: getFullnodeUrl(NETWORK) });

  try {
    // Step 1: Check for deployment events
    console.log('🔍 Step 1: Checking for deployment events...');
    
    const eventQuery = {
      MoveEventType: `${PACKAGE_ID}::dexsta_token::TokenModuleDeploymentRequested`
    };

    const events = await client.queryEvents({
      query: eventQuery,
      limit: 10,
      order: 'descending'
    });

    console.log(`Found ${events.data?.length || 0} deployment events`);

    if (!events.data || events.data.length === 0) {
      console.log('⚠️ No deployment events found. Create a token first using the frontend.');
      return;
    }

    // Use the latest event
    const latestEvent = events.data[0];
    const eventData = latestEvent.parsedJson;
    
    console.log('📋 Latest deployment event:');
    console.log('  Symbol:', eventData.token_symbol);
    console.log('  Name:', eventData.token_name);
    console.log('  Creator:', eventData.creator);

    // Step 2: Generate token module (like nodejs_deploy.js)
    console.log('\n🔧 Step 2: Generating token module...');

    const inputs = {
      tokenName: eventData.token_name,
      symbol: eventData.token_symbol,
      description: "Token created via Dexsta platform",
      iconURL: "https://example.com/icon.png",
      decimals: 9,
      structName: `${eventData.token_symbol.replace(/[^A-Za-z0-9]/g, '')}Coin`,
      moduleName: eventData.token_symbol.toLowerCase().replace(/[^a-z0-9_]/g, '_'),
      publishAddress: "0x0" // Will be replaced during publish
    };

    console.log('📋 Token inputs:', inputs);

    // Generate Move module content
    const coinTemplate = `
module ${inputs.publishAddress}::${inputs.moduleName} {
    use sui::coin;
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use std::option;

    struct ${inputs.structName} has drop {}

    fun init(ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<${inputs.structName}>(
            ${inputs.structName} {},
            ${inputs.decimals}, // decimals
            b"${inputs.symbol}", // symbol
            b"${inputs.tokenName}", // name
            b"${inputs.description}", // description
            option::none(), // icon_url
            ctx
        );

        // Transfer the treasury cap to the deployer
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));

        // Share the metadata object
        transfer::public_share_object(metadata);
    }
}`.trim();

    // Generate Move.toml
    const moveToml = `
[package]
name = "${inputs.moduleName}"
version = "0.0.1"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
${inputs.moduleName} = "0x0"
`.trim();

    // Step 3: Create project directory
    console.log('\n📁 Step 3: Creating project directory...');
    
    const projectDir = path.join(__dirname, "generated_tokens", inputs.moduleName);
    const sourcesDir = path.join(projectDir, "sources");

    fs.mkdirSync(sourcesDir, { recursive: true });
    fs.writeFileSync(path.join(projectDir, "Move.toml"), moveToml);
    fs.writeFileSync(path.join(sourcesDir, "coin.move"), coinTemplate);

    console.log("✅ Move package generated at:", projectDir);

    // Step 4: Build the module
    console.log('\n🔨 Step 4: Building module...');
    
    try {
      execSync(`cd ${projectDir} && sui move build`, { stdio: "inherit" });
      console.log("✅ Build successful.");
    } catch (err) {
      console.error("❌ Build failed:", err.message);
      return;
    }

    // Step 5: Publish the module (commented out for safety)
    console.log('\n🚀 Step 5: Publishing module...');
    console.log('⚠️ Publishing is commented out for safety. Uncomment the lines below to actually publish:');
    console.log(`   cd ${projectDir} && sui client publish --gas-budget 100000000`);
    
    /*
    try {
      const publishResult = execSync(`cd ${projectDir} && sui client publish --gas-budget 100000000 --json`, { encoding: 'utf8' });
      const publishData = JSON.parse(publishResult);
      
      console.log("✅ Module published successfully!");
      console.log("📦 Transaction digest:", publishData.digest);
      
      // Extract package ID and object IDs
      const packageChange = publishData.objectChanges?.find(change => change.type === 'published');
      if (packageChange) {
        console.log("📦 Package ID:", packageChange.packageId);
      }
      
      // Find TreasuryCap and CoinMetadata
      publishData.objectChanges?.forEach(change => {
        if (change.type === 'created') {
          if (change.objectType?.includes('TreasuryCap')) {
            console.log("🏛️ Treasury Cap ID:", change.objectId);
          } else if (change.objectType?.includes('CoinMetadata')) {
            console.log("📋 Coin Metadata ID:", change.objectId);
          }
        }
      });
      
    } catch (err) {
      console.error("❌ Publishing failed:", err.message);
    }
    */

    console.log('\n🎉 Token deployment test completed!');
    console.log('📁 Generated files are in:', projectDir);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testTokenDeployment().catch(console.error);
