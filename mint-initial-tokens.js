/**
 * Mint Initial Tokens Script
 * Gives the creator their tokens for the initial purchase
 */

const { SuiClient, getFullnodeUrl } = require('@mysten/sui/client');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { Transaction } = require('@mysten/sui/transactions');
const { fromB64 } = require('@mysten/sui/utils');

// Configuration
const NETWORK = 'devnet';
const PACKAGE_ID = '0x75da1bbe12c141aa2269396420b17355847063e55f0211fe97b3969c49618bcf';
const TOKEN_REGISTRY = '0xe25b786282469e3e78571b9fed5cfe9c81203eac20bfaa82bae79611dd6cba68';
const PRIVATE_KEY = 'suiprivkey1qq96u4z3h5ze7ptnm69d45fugmutr499mwddfqt6009jzfgqypge6fmrzws';

async function mintInitialTokens() {
  console.log('🪙 Minting Initial Tokens to Creator');
  console.log('===================================');

  // Initialize client and keypair
  const client = new SuiClient({ url: getFullnodeUrl(NETWORK) });
  const keypair = Ed25519Keypair.fromSecretKey(PRIVATE_KEY);
  const creatorAddress = keypair.getPublicKey().toSuiAddress();

  try {
    // Step 1: Find the token pool for LETSGO237642
    console.log('🔍 Finding token pool...');
    
    const tokenSymbol = 'LETSGO237642'; // Your token
    
    // Query for TokenCreated events to find the pool
    const eventQuery = {
      MoveEventType: `${PACKAGE_ID}::dexsta_token::TokenCreated`
    };

    const events = await client.queryEvents({
      query: eventQuery,
      limit: 10,
      order: 'descending'
    });

    const tokenEvent = events.data?.find(event => {
      const eventData = event.parsedJson;
      return eventData?.symbol === tokenSymbol;
    });

    if (!tokenEvent) {
      console.log('❌ Token pool not found for:', tokenSymbol);
      return;
    }

    const tokenData = tokenEvent.parsedJson;
    const tokenPoolAddress = tokenData.token_pool_address;
    
    console.log('✅ Found token pool:', tokenPoolAddress);
    console.log('📋 Token data:', {
      symbol: tokenData.symbol,
      creator: tokenData.creator,
      total_supply: tokenData.total_supply
    });

    // Step 2: Calculate initial tokens (user paid 0.1 SUI)
    const initialSuiPaid = 100_000_000; // 0.1 SUI in MIST
    const initialTokens = (initialSuiPaid * 100) / 1_000_000_000; // 1 SUI = 100 tokens
    
    console.log('💰 Initial purchase calculation:');
    console.log('  SUI paid:', initialSuiPaid / 1_000_000_000, 'SUI');
    console.log('  Tokens owed:', initialTokens);

    // Step 3: Simulate buying tokens for the creator
    console.log('🛒 Simulating token purchase for creator...');

    // Create SUI coin for the purchase
    const tx = new Transaction();
    
    // Split SUI for the purchase
    const [suiCoin] = tx.splitCoins(tx.gas, [initialSuiPaid]);

    // Buy tokens (this will update virtual balances)
    tx.moveCall({
      target: `${PACKAGE_ID}::dexsta_token::buy_tokens`,
      arguments: [
        tx.object(TOKEN_REGISTRY),
        tx.object(tokenPoolAddress),
        suiCoin,
        tx.pure.u64(Math.floor(initialTokens * 0.95)), // 5% slippage tolerance
      ],
    });

    const result = await client.signAndExecuteTransaction({
      transaction: tx,
      signer: keypair,
      options: {
        showEffects: true,
        showEvents: true,
      }
    });

    console.log('✅ Token purchase completed!');
    console.log('📦 Transaction:', result.digest);
    console.log('🌐 View on explorer:', `https://suiexplorer.com/txblock/${result.digest}?network=devnet`);

    // Check for swap events
    if (result.events) {
      console.log('📋 Events emitted:');
      result.events.forEach((event, index) => {
        console.log(`  ${index + 1}. ${event.type}`);
        if (event.parsedJson && event.type.includes('TokenSwapped')) {
          const swapData = event.parsedJson;
          console.log('     🔄 Swap details:');
          console.log('       SUI in:', swapData.sui_amount / 1_000_000_000, 'SUI');
          console.log('       Tokens out:', swapData.token_amount);
          console.log('       Price:', swapData.price_scaled / 1_000_000, 'SUI per token');
        }
      });
    }

    console.log('\n🎉 Creator now has their initial tokens!');
    console.log('💡 Note: These are virtual tokens in the bonding curve system');
    console.log('🔄 For actual wallet tokens, the template module deployment is still needed');

  } catch (error) {
    console.error('❌ Token minting failed:', error);
  }
}

// Run the minting
mintInitialTokens().catch(console.error);
