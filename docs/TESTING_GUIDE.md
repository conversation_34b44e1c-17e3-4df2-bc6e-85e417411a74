# Dexsta Contract Testing Guide

## 🎯 Testing Overview

This guide covers comprehensive testing of all Dexsta platform features including token creation, trading mechanics, migration, fees, trading game, and advanced features.

## 📋 Quick Test Checklist

### ✅ Prerequisites
- [ ] Admin contracts initialized
- [ ] Super admin wallet connected: `0x51ba62cb2f1c1c142dac8a8bf4cec1046084c88003b1a8c294e6da478c7258dc`
- [ ] Test SUI available for trading
- [ ] Frontend running at http://localhost:3000

### 🔧 Admin Testing (Start Here)
- [ ] Update platform fees through admin panel
- [ ] Add authorized admin addresses
- [ ] Test platform pause/unpause functionality
- [ ] Transfer super admin powers

### 🪙 Token Creation Testing
- [ ] Create meme tokens with different parameters
- [ ] Create startup tokens with custom settings
- [ ] Create personalized tokens for artists
- [ ] Verify token metadata and initial state

### 📈 Pre-Migration Trading
- [ ] Make initial token purchases (test bonding curve)
- [ ] Execute sell orders (test price impact)
- [ ] Verify fee collection (platform + creator fees)
- [ ] Test slippage protection mechanisms

### 🎮 Trading Game Testing
- [ ] Monitor reward pot accumulation (50% of creator fees)
- [ ] Make trades to trigger goal adjustments
- [ ] Hit exact goal to win trading pot
- [ ] Test community contributions to pot
- [ ] Verify goal proximity thresholds

### 🚀 Migration Testing
- [ ] Trade tokens to 60 SUI threshold
- [ ] Trigger automatic migration to AMM
- [ ] Verify AMM pool creation
- [ ] Test post-migration trading
- [ ] Confirm migration fee collection

### 💧 Liquidity Testing
- [ ] Send SUI directly to migrated pool addresses
- [ ] Verify liquidity pool growth
- [ ] Test pool ownership transfer
- [ ] Simulate artist revenue injection

### 🔒 NFT-Gated Pools
- [ ] Create private NFT-gated pools
- [ ] Test NFT access control
- [ ] Verify private trading games
- [ ] Test private pool liquidity

### 🤖 AI Bot Testing
- [ ] Deploy AI trading bots
- [ ] Test bot negotiation and swarms
- [ ] Verify bribe mechanics
- [ ] Test aggressive behavior scaling
- [ ] Confirm profit distribution

## 🧪 Test Scenarios

### Scenario 1: Complete Token Lifecycle
1. Create token → 2. Initial trades → 3. Trading game → 4. Migration → 5. AMM trading

### Scenario 2: High-Volume Trading
1. Multiple concurrent trades → 2. Stress test bonding curve → 3. Test fee accumulation

### Scenario 3: Artist Revenue Flow
1. Create artist token → 2. Build community → 3. Migrate → 4. Direct revenue injection

### Scenario 4: Private Community
1. Create NFT collection → 2. Create gated pool → 3. Exclusive trading → 4. Private rewards

## 📊 Key Metrics to Monitor

### Financial Metrics
- Platform fee collection accuracy
- Creator fee distribution
- Trading game pot accumulation
- Migration fee collection
- Liquidity pool growth

### Performance Metrics
- Transaction gas costs
- Response times
- Concurrent user handling
- Memory usage

### Game Mechanics
- Goal adjustment accuracy
- Winner detection
- Pot distribution
- Community engagement

## 🚨 Critical Test Cases

### Security Tests
- [ ] Unauthorized admin access attempts
- [ ] Zero/negative amount trades
- [ ] Insufficient balance scenarios
- [ ] Concurrent trading race conditions

### Edge Cases
- [ ] Maximum trade size limits
- [ ] Minimum trade thresholds
- [ ] Contract pause scenarios
- [ ] Network congestion handling

## 📝 Test Data Collection

### For Each Test:
- Transaction hashes
- Gas costs
- Execution times
- Error messages (if any)
- Before/after balances
- Event emissions

### Success Criteria:
- All transactions execute successfully
- Fees collected correctly
- Game mechanics work as designed
- UI updates reflect contract state
- No security vulnerabilities found

## 🎉 Testing Completion

Once all tests pass:
1. Document any issues found
2. Verify all features work end-to-end
3. Confirm platform ready for production
4. Create user documentation
5. Plan mainnet deployment

---

**Note**: This testing should be done systematically, starting with admin functions and progressing through the complete user journey. Each major feature should be tested independently and then as part of integrated workflows.
