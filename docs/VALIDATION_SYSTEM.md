# Dexsta Token Creation Validation System

## Overview

The Dexsta platform implements a **dual-layer validation system** to ensure secure token creation while providing excellent user experience:

1. **Frontend Validation** - Real-time user feedback and UX
2. **Contract Validation** - Bulletproof security enforcement

## 🔒 Security Philosophy

> **Frontend validation is for UX only - smart contracts must be bulletproof**

Since smart contract functions can be called directly by anyone (bypassing the frontend), all security validation MUST be implemented in the smart contracts themselves.

## 📋 Validation Scenarios

### 1. Standard Token Creation
- **Requirements**: Basic token information only
- **Validation**: Standard input validation
- **Contract Function**: `create_token_simple`

### 2. Token with Label Linking
- **Requirements**: User must own the label
- **Frontend Validation**: 
  - Label exists in registry
  - Label is not expired
  - User owns the label
- **Contract Validation**: Same checks enforced in smart contract
- **Contract Function**: `create_token_with_label`

### 3. Token with Operator License
- **Requirements**: Valid operator license for the target label
- **Frontend Validation**:
  - Operator license exists
  - License is linked to specified label
  - User owns the license
  - License is not expired
  - Operator is active (status == ACTIVE)
  - Operator has MINT role (role == 1)
- **Contract Validation**: Same checks enforced in smart contract
- **Contract Function**: `create_token_with_operator_license`

### 4. Private Pool (NFT-Gated)
- **Requirements**: NFT must be linked to the same label as token
- **Frontend Validation**:
  - NFT exists in XFT registry
  - NFT is linked to the specified label
  - Valid NFT object ID format
- **Contract Validation**: Same checks enforced in smart contract
- **Contract Function**: `create_token_with_nft_gating`

## 🔧 Implementation Details

### Frontend Validation Functions

Located in `src/app/create/page.tsx`:

```typescript
// Real-time validation for user feedback
const validateLabel = async (labelId: string) => {
  // Calls contract getter functions to validate:
  // 1. Label exists (getLabel function)
  // 2. Label not expired (label.expiration > current_time)
  // 3. User owns label (label.owner == tx.sender)
}

const validateOperatorLicense = async (licenseId: string, labelId: string) => {
  // Validates all operator license requirements
}

const validateNFT = async (nftId: string, labelId: string) => {
  // Validates NFT exists and is linked to label
}
```

### Contract Validation Requirements

Detailed in `src/services/contractValidation.ts`:

```move
// Example contract validation (Move pseudocode)
public fun create_token_with_label(
    label_id: String,
    ctx: &mut TxContext
) {
    let label = label_registry::get_label(label_id);
    assert!(option::is_some(&label), ELabelNotFound);
    
    let label_obj = option::extract(&mut label);
    assert!(label_obj.expiration > tx_context::epoch(ctx), ELabelExpired);
    assert!(label_obj.owner == tx_context::sender(ctx), ENotLabelOwner);
    
    // Proceed with token creation...
}
```

## 🚀 Migration & Ownership Transfer

### Migration to External DEX
When migrating tokens to external DEX (like Raydium), all validation data must be passed:

```typescript
struct MigrationData {
  linked_label_id: Option<u64>,
  required_nft_id: Option<address>,
  operator_license_id: Option<u64>,
  original_creator: address,
  buy_fee_bps: u64,
  sell_fee_bps: u64
}
```

### Ownership Transfer Re-validation
When transferring token ownership:
- If linking to new label: validate new owner owns the new label
- If swapping NFT: validate new NFT is linked to label
- If using new operator license: validate all requirements
- Emit audit trail events

## 📁 File Structure

```
src/
├── app/create/page.tsx              # Frontend validation & UI
├── services/contractValidation.ts   # Validation specifications
├── hooks/useContracts.ts            # Contract interaction logic
├── constants/contracts.ts           # Contract addresses & functions
└── docs/VALIDATION_SYSTEM.md        # This documentation
```

## ⚠️ Critical Security Notes

1. **Never trust frontend data** - Always re-validate in contracts
2. **Use assert! statements** for all validation in Move contracts
3. **Implement getter functions** for frontend real-time validation
4. **Emit events** for all ownership changes (audit trail)
5. **Check expiration dates** for time-sensitive objects
6. **Validate linking relationships** between objects
7. **Use proper error codes** for different validation failures

## 🧪 Testing Strategy

### Frontend Testing
- Test all validation scenarios with mock data
- Test error handling and user feedback
- Test form state management

### Contract Testing
- Test all validation edge cases
- Test unauthorized access attempts
- Test expired objects handling
- Test ownership validation
- Test linking validation

## 🔄 Next Steps

1. **Deploy contracts** with validation functions
2. **Implement getter functions** for real-time validation
3. **Test validation scenarios** thoroughly
4. **Update CLI version** to resolve deployment issues
5. **Integrate with XFT contracts** for label/NFT validation

---

This validation system ensures that Dexsta tokens can only be created with proper authorization while providing users with immediate feedback about validation status.
