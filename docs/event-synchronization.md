# Event Synchronization & Database Schema

## Overview
All Solana contract actions that emit events must be synchronized with the Supabase database to maintain consistency between on-chain state and off-chain data. This ensures the UI can display real-time information without constantly querying the blockchain.

## Event Synchronization Architecture

### 1. Event Listening Service
```typescript
// Event listener service that monitors contract events
class EventSynchronizationService {
  private connection: Connection
  private supabaseClient: SupabaseClient
  
  // Listen for specific program events
  async startEventListening() {
    // Monitor Pool Admin events
    this.connection.onLogs(POOL_ADMIN_PROGRAM_ID, this.handlePoolAdminEvent)
    
    // Monitor Token Admin events  
    this.connection.onLogs(TOKEN_ADMIN_PROGRAM_ID, this.handleTokenAdminEvent)
    
    // Monitor Pool events
    this.connection.onLogs(POOL_PROGRAM_ID, this.handlePoolEvent)
    
    // Monitor Token events
    this.connection.onLogs(TOKEN_PROGRAM_ID, this.handleTokenEvent)
  }
}
```

### 2. Event Processing Pipeline
1. **Event Detection**: Monitor Solana logs for contract events
2. **Event Parsing**: Extract event data from transaction logs
3. **Database Update**: Sync event data to Supabase tables
4. **Error Handling**: Retry failed syncs and handle conflicts
5. **Real-time Updates**: Notify UI components of changes

## Admin Table Schema

### admin_events Table
```sql
CREATE TABLE admin_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type VARCHAR(50) NOT NULL,
  contract_type VARCHAR(20) NOT NULL, -- 'pool' or 'token'
  transaction_signature VARCHAR(88) NOT NULL UNIQUE,
  block_time TIMESTAMP WITH TIME ZONE NOT NULL,
  slot BIGINT NOT NULL,
  admin_wallet VARCHAR(44) NOT NULL,
  event_data JSONB NOT NULL,
  processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for efficient querying
CREATE INDEX idx_admin_events_type ON admin_events(event_type);
CREATE INDEX idx_admin_events_contract ON admin_events(contract_type);
CREATE INDEX idx_admin_events_wallet ON admin_events(admin_wallet);
CREATE INDEX idx_admin_events_block_time ON admin_events(block_time);
CREATE INDEX idx_admin_events_signature ON admin_events(transaction_signature);
```

### platform_settings Table
```sql
CREATE TABLE platform_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contract_type VARCHAR(20) NOT NULL, -- 'pool' or 'token'
  is_initialized BOOLEAN NOT NULL DEFAULT FALSE,
  super_admin VARCHAR(44) NOT NULL,
  platform_fee_bps INTEGER NOT NULL,
  reward_fee_bps INTEGER, -- Only for token contract
  lp_fee_bps INTEGER, -- Only for pool contract
  mint_fee BIGINT NOT NULL,
  min_reward_trade_amount BIGINT NOT NULL,
  initial_reward_goal BIGINT NOT NULL,
  bonding_curve_goal BIGINT NOT NULL,
  reward_goal_increase BIGINT NOT NULL,
  reward_goal_decrease_amount BIGINT NOT NULL,
  reward_goal_decrease_threshold INTEGER NOT NULL,
  reward_goal_proximity_threshold INTEGER NOT NULL,
  migration_fee_percentage INTEGER NOT NULL,
  migration_gas_fee BIGINT NOT NULL,
  platform_fee_address VARCHAR(44) NOT NULL,
  last_updated_signature VARCHAR(88),
  last_updated_slot BIGINT,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(contract_type)
);
```

### authorized_admins Table
```sql
CREATE TABLE authorized_admins (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contract_type VARCHAR(20) NOT NULL, -- 'pool' or 'token'
  admin_wallet VARCHAR(44) NOT NULL,
  added_by VARCHAR(44) NOT NULL, -- Super admin who added this admin
  added_at_signature VARCHAR(88) NOT NULL,
  added_at_slot BIGINT NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  removed_at_signature VARCHAR(88),
  removed_at_slot BIGINT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(contract_type, admin_wallet)
);

CREATE INDEX idx_authorized_admins_wallet ON authorized_admins(admin_wallet);
CREATE INDEX idx_authorized_admins_contract ON authorized_admins(contract_type);
CREATE INDEX idx_authorized_admins_active ON authorized_admins(is_active);
```

## Event Types & Data Structures

### Pool Admin Events
```typescript
interface PoolAdminEvents {
  'platform_initialized': {
    super_admin: string
    platform_fee_bps: number
    lp_fee_bps: number
    mint_fee: number
    platform_fee_address: string
    // ... other settings
  }
  
  'settings_updated': {
    admin: string
    old_settings: PlatformSettings
    new_settings: PlatformSettings
    updated_fields: string[]
  }
  
  'admin_added': {
    super_admin: string
    new_admin: string
  }
  
  'admin_removed': {
    super_admin: string
    removed_admin: string
  }
  
  'super_admin_transferred': {
    old_super_admin: string
    new_super_admin: string
  }
}
```

### Token Admin Events
```typescript
interface TokenAdminEvents {
  'platform_initialized': {
    super_admin: string
    platform_fee_bps: number
    reward_fee_bps: number
    mint_fee: number
    platform_fee_address: string
    // ... other settings
  }
  
  'settings_updated': {
    admin: string
    old_settings: PlatformSettings
    new_settings: PlatformSettings
    updated_fields: string[]
  }
  
  'admin_added': {
    super_admin: string
    new_admin: string
  }
  
  'admin_removed': {
    super_admin: string
    removed_admin: string
  }
}
```

## Event Synchronization Implementation

### 1. Event Handler Functions
```typescript
class AdminEventHandler {
  async handlePlatformInitialized(
    contractType: 'pool' | 'token',
    eventData: any,
    signature: string,
    slot: number,
    blockTime: number
  ) {
    // Insert into platform_settings table
    await this.supabase
      .from('platform_settings')
      .insert({
        contract_type: contractType,
        is_initialized: true,
        super_admin: eventData.super_admin,
        platform_fee_bps: eventData.platform_fee_bps,
        // ... other fields
        last_updated_signature: signature,
        last_updated_slot: slot
      })
    
    // Log event
    await this.logAdminEvent('platform_initialized', contractType, eventData, signature, slot, blockTime)
  }
  
  async handleSettingsUpdated(
    contractType: 'pool' | 'token',
    eventData: any,
    signature: string,
    slot: number,
    blockTime: number
  ) {
    // Update platform_settings table
    await this.supabase
      .from('platform_settings')
      .update({
        platform_fee_bps: eventData.new_settings.platform_fee_bps,
        // ... other updated fields
        last_updated_signature: signature,
        last_updated_slot: slot,
        updated_at: new Date().toISOString()
      })
      .eq('contract_type', contractType)
    
    // Log event
    await this.logAdminEvent('settings_updated', contractType, eventData, signature, slot, blockTime)
  }
  
  async handleAdminAdded(
    contractType: 'pool' | 'token',
    eventData: any,
    signature: string,
    slot: number,
    blockTime: number
  ) {
    // Insert into authorized_admins table
    await this.supabase
      .from('authorized_admins')
      .insert({
        contract_type: contractType,
        admin_wallet: eventData.new_admin,
        added_by: eventData.super_admin,
        added_at_signature: signature,
        added_at_slot: slot,
        is_active: true
      })
    
    // Log event
    await this.logAdminEvent('admin_added', contractType, eventData, signature, slot, blockTime)
  }
}
```

### 2. Database Sync Service
```typescript
class DatabaseSyncService {
  async syncAdminEvent(
    eventType: string,
    contractType: 'pool' | 'token',
    eventData: any,
    signature: string,
    slot: number,
    blockTime: number,
    adminWallet: string
  ) {
    try {
      // Insert event log
      await this.supabase
        .from('admin_events')
        .insert({
          event_type: eventType,
          contract_type: contractType,
          transaction_signature: signature,
          block_time: new Date(blockTime * 1000).toISOString(),
          slot: slot,
          admin_wallet: adminWallet,
          event_data: eventData
        })
      
      // Update relevant tables based on event type
      await this.updateRelatedTables(eventType, contractType, eventData, signature, slot)
      
    } catch (error) {
      console.error('Failed to sync admin event:', error)
      // Add to retry queue
      await this.addToRetryQueue(eventType, contractType, eventData, signature, slot, blockTime, adminWallet)
    }
  }
}
```

## Real-time Updates

### 1. Supabase Realtime Subscriptions
```typescript
// Subscribe to admin events for real-time UI updates
const subscription = supabase
  .channel('admin_events')
  .on('postgres_changes', 
    { event: 'INSERT', schema: 'public', table: 'admin_events' },
    (payload) => {
      // Update UI state based on new admin event
      handleAdminEventUpdate(payload.new)
    }
  )
  .subscribe()
```

### 2. UI State Management
```typescript
// React hook for real-time admin status
export const useAdminStatus = () => {
  const [adminStatus, setAdminStatus] = useState<AdminStatus>()
  
  useEffect(() => {
    // Subscribe to platform_settings changes
    const subscription = supabase
      .channel('platform_settings')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'platform_settings' },
        (payload) => {
          // Update admin status when settings change
          updateAdminStatus(payload.new)
        }
      )
      .subscribe()
    
    return () => subscription.unsubscribe()
  }, [])
  
  return adminStatus
}
```

## Error Handling & Retry Logic

### 1. Failed Event Processing
- **Retry Queue**: Store failed events for retry processing
- **Exponential Backoff**: Implement retry delays
- **Dead Letter Queue**: Handle permanently failed events
- **Manual Recovery**: Admin interface for resolving sync issues

### 2. Conflict Resolution
- **Slot-based Ordering**: Use slot numbers to determine event order
- **Idempotency**: Ensure events can be processed multiple times safely
- **State Reconciliation**: Periodic sync between on-chain and database state

This event synchronization system ensures that all admin contract actions are properly tracked and synchronized with the Supabase database, providing real-time updates to the UI while maintaining data consistency.
