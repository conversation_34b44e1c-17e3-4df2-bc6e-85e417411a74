# Contract Updates for Validation System

## Overview

Updated the token and pool contracts to implement proper validation using the Fire Registry for cross-contract communication. This ensures secure token creation and trading with proper authorization checks.

## 🔥 Fire Registry Updates

### Added Contract Types
- **TOKEN**: Token contract addresses
- **POOL**: Pool contract addresses

### New Getter Functions
```move
public fun get_token_addresses(registry: &FireRegistry): (address, address)
public fun get_pool_addresses(registry: &FireRegistry): (address, address)
```

## 🪙 Token Contract Updates (`simple_token.move`)

### New Error Codes
```move
const ELabelNotFound: u64 = 10;
const ELabelExpired: u64 = 11;
const ENotLabelOwner: u64 = 12;
const EOperatorLicenseNotFound: u64 = 13;
const EOperatorLicenseExpired: u64 = 14;
const ENotLicenseOwner: u64 = 15;
const EOperatorInactive: u64 = 16;
const EInsufficientPermissions: u64 = 17;
const ENFTNotFound: u64 = 18;
const ENFTNotLinkedToLabel: u64 = 19;
const EFireRegistryNotSet: u64 = 20;
```

### Enhanced TokenRegistry
```move
struct TokenRegistry has key {
    // ... existing fields
    fire_registry_address: Option<address>, // For cross-contract validation
}
```

### New Admin Function
```move
public entry fun set_fire_registry_address(
    registry: &mut TokenRegistry,
    fire_registry_address: address,
    ctx: &mut TxContext
)
```

### New Token Creation Functions

#### 1. Create Token with Label Linking
```move
public entry fun create_token_with_label(
    registry: &mut TokenRegistry,
    // ... token parameters
    label_id: vector<u8>, // Required label ID
    payment: &mut Coin<SUI>,
    ctx: &mut TxContext
)
```
**Validation**: Checks label ownership before creation

#### 2. Create Token with Operator License
```move
public entry fun create_token_with_operator_license(
    registry: &mut TokenRegistry,
    // ... token parameters
    label_id: vector<u8>, // Target label ID
    operator_license_id: vector<u8>, // Operator license ID
    payment: &mut Coin<SUI>,
    ctx: &mut TxContext
)
```
**Validation**: Checks operator license validity and permissions

### Enhanced Validation Functions

#### Label Ownership Validation
```move
fun validate_label_ownership(
    registry: &TokenRegistry,
    label_id: String,
    user_address: address
): bool
```
**Checks**:
- Label exists in registry
- Label is not expired
- User owns the label

#### Operator License Validation
```move
fun validate_operator_license(
    registry: &TokenRegistry,
    license_id: String,
    label_id: String,
    user_address: address
): bool
```
**Checks**:
- License exists
- License is linked to target label
- User owns the license
- License is not expired
- Operator is active
- Operator has MINT role (role == 1)

#### NFT Ownership and Linking Validation
```move
fun validate_nft_ownership_and_linking(
    registry: &TokenRegistry,
    required_nft_id: Option<u64>,
    linked_label_id: Option<String>,
    user_address: address
): bool
```
**Checks**:
- NFT exists in XFT registry
- User owns the NFT
- NFT is linked to the same label as token

### Updated Trading Functions
- **`swap_sui_for_tokens`**: Enhanced NFT validation for private pools
- **`swap_tokens_for_sui`**: Enhanced NFT validation for private pools

## 🏊 Pool Contract Updates (`pool.move`)

### New Error Codes
```move
const ENFTNotFound: u64 = 18;
const ENFTNotLinkedToLabel: u64 = 19;
const EFireRegistryNotSet: u64 = 20;
```

### Enhanced PoolRegistry
```move
struct PoolRegistry has key {
    // ... existing fields
    fire_registry_address: Option<address>, // For cross-contract validation
}
```

### New Admin Function
```move
public entry fun set_fire_registry_address(
    registry: &PoolRegistry,
    fire_registry_address: address,
    ctx: &mut TxContext
)
```

### Enhanced Validation Function
```move
fun validate_nft_ownership_and_linking(
    registry: &PoolRegistry,
    required_nft_id: Option<u64>,
    linked_label_id: Option<String>,
    user_address: address
): bool
```

### Updated Trading Functions
- **`swap_sui_for_tokens`**: Added registry parameter for validation
- **`swap_tokens_for_sui`**: Added registry parameter for validation

## 🔗 Cross-Contract Integration

### Fire Registry Integration
Both token and pool contracts now:
1. Store Fire Registry address in their registries
2. Use Fire Registry to get other contract addresses
3. Make direct cross-contract calls for validation

### Validation Flow
```
1. User calls token creation function
2. Contract gets Fire Registry address
3. Contract calls Fire Registry to get label/operator/NFT contract addresses
4. Contract makes direct calls to validate:
   - Label ownership
   - Operator license validity
   - NFT ownership and linking
5. If validation passes, token/pool is created
6. If validation fails, transaction reverts with specific error
```

## 🚀 Deployment Requirements

### 1. Deploy Fire Registry First
```bash
sui client publish fresh_fire_deploy/
```

### 2. Deploy Token and Pool Contracts
```bash
sui client publish contracts_sui/
```

### 3. Initialize Fire Registry
```bash
# Set token contract addresses
sui client call --function set_contract_addresses --module fire --package <FIRE_PACKAGE> \
  --args <FIRE_REGISTRY> "token" <TOKEN_PACKAGE> <TOKEN_REGISTRY>

# Set pool contract addresses  
sui client call --function set_contract_addresses --module fire --package <FIRE_PACKAGE> \
  --args <FIRE_REGISTRY> "pool" <POOL_PACKAGE> <POOL_REGISTRY>
```

### 4. Configure Token and Pool Contracts
```bash
# Set Fire Registry address in token contract
sui client call --function set_fire_registry_address --module simple_token --package <TOKEN_PACKAGE> \
  --args <TOKEN_REGISTRY> <FIRE_REGISTRY>

# Set Fire Registry address in pool contract
sui client call --function set_fire_registry_address --module pool --package <POOL_PACKAGE> \
  --args <POOL_REGISTRY> <FIRE_REGISTRY>
```

## 🔒 Security Features

### Bulletproof Validation
- All validation happens in smart contracts
- Frontend validation is for UX only
- Direct cross-contract calls prevent bypassing
- Proper error codes for different failure scenarios

### Authorization Checks
- Label ownership validation
- Operator license validation with role checking
- NFT ownership and linking validation
- Expiration date validation for time-sensitive objects

### Migration Support
- All validation data preserved during migration
- Ownership transfer re-validation
- Audit trail with events

## 📝 TODO: Implement Cross-Contract Calls

The validation functions currently have placeholder implementations. Next steps:

1. **Implement actual cross-contract calls** in validation functions
2. **Add getter functions** to XFT contracts for validation
3. **Test all validation scenarios** thoroughly
4. **Deploy and configure** all contracts with Fire Registry

This architecture ensures secure, validated token creation while maintaining excellent user experience through the dual-layer validation system.
