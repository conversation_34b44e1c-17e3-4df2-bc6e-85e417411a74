# Manual XFT Contracts Deployment Guide

Since we know Sui CLI is installed on your system (from previous deployments), here's a step-by-step manual deployment guide.

## 🔍 Step 1: Find Sui CLI

First, let's locate where Sui CLI is installed:

```bash
# Try common locations
which sui
/opt/homebrew/bin/sui --version
/usr/local/bin/sui --version
~/.cargo/bin/sui --version

# Or search for it
find /usr -name "sui" 2>/dev/null
find /opt -name "sui" 2>/dev/null
find ~ -name "sui" 2>/dev/null
```

## 🔧 Step 2: Setup Environment

Once you find Sui CLI, add it to your PATH or use the full path:

```bash
# If found in /opt/homebrew/bin/
export PATH="/opt/homebrew/bin:$PATH"

# Or if found elsewhere, use full path like:
# /full/path/to/sui --version
```

## 🚀 Step 3: Deploy XFT Contracts

### 3.1 Check Current Setup
```bash
sui client active-env
sui client active-address
sui client balance
```

### 3.2 Switch to Devnet (if needed)
```bash
sui client switch --env devnet
```

### 3.3 Get SUI (if balance is low)
```bash
sui client faucet
```

### 3.4 Build Contracts
```bash
cd contracts/xft
sui move build
```

### 3.5 Deploy Contracts
```bash
# Deploy with high gas budget
sui client publish --gas-budget 300000000 --json > ../../deployment_output.json

# Check if deployment succeeded
echo "Deployment status: $?"
```

## 📋 Step 4: Extract Contract Addresses

```bash
cd ../..

# Extract package ID
PACKAGE_ID=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.type == "published") | .packageId')
echo "Package ID: $PACKAGE_ID"

# Extract contract object IDs
FIRE_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("fire::FireRegistry")) | .objectId')
GLOBAL_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("registry::GlobalRegistry")) | .objectId')
LABEL_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("label::LabelRegistry")) | .objectId')
XFT_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("xft::XFTRegistry")) | .objectId')
OPERATOR_REGISTRY=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("operator::OperatorRegistry")) | .objectId')
MARKETPLACE=$(cat deployment_output.json | jq -r '.objectChanges[] | select(.objectType | contains("marketplace::Marketplace")) | .objectId')

echo "🔥 Fire Registry: $FIRE_REGISTRY"
echo "🌐 Global Registry: $GLOBAL_REGISTRY"
echo "🏷️  Label Registry: $LABEL_REGISTRY"
echo "🎨 XFT Registry: $XFT_REGISTRY"
echo "👥 Operator Registry: $OPERATOR_REGISTRY"
echo "🛒 Marketplace: $MARKETPLACE"
```

## 📝 Step 5: Create Environment File

```bash
cat > .env.deployment << EOF
# Dexsta XFT Contracts - Devnet Deployment
# Generated on $(date)
NEXT_PUBLIC_SUI_NETWORK=devnet
NEXT_PUBLIC_PACKAGE_ID=$PACKAGE_ID
NEXT_PUBLIC_FIRE_REGISTRY_ID=$FIRE_REGISTRY
NEXT_PUBLIC_GLOBAL_REGISTRY_ID=$GLOBAL_REGISTRY
NEXT_PUBLIC_LABEL_REGISTRY_ID=$LABEL_REGISTRY
NEXT_PUBLIC_XFT_REGISTRY_ID=$XFT_REGISTRY
NEXT_PUBLIC_OPERATOR_REGISTRY_ID=$OPERATOR_REGISTRY
NEXT_PUBLIC_MARKETPLACE_ID=$MARKETPLACE
EOF

echo "✅ Environment file created!"
cat .env.deployment
```

## 🔧 Step 6: Initialize Fire Registry

```bash
# Initialize Fire Registry with all contract addresses
sui client call \
    --package $PACKAGE_ID \
    --module fire \
    --function initialize_contracts \
    --args $FIRE_REGISTRY \
        $GLOBAL_REGISTRY \
        $LABEL_REGISTRY \
        $XFT_REGISTRY \
        $OPERATOR_REGISTRY \
        $MARKETPLACE \
    --gas-budget 100000000
```

## 🧪 Step 7: Test Basic Functionality

### 7.1 Create Test Label
```bash
sui client call \
    --package $PACKAGE_ID \
    --module label \
    --function create_label \
    --args $LABEL_REGISTRY \
        $GLOBAL_REGISTRY \
        $FIRE_REGISTRY \
        '"test-label-$(date +%s)"' \
        '[1, 1000000000, 1, 1, 0, 0, 1, 0, 0, 1, 0, 250, 0, 0]' \
        'true' \
        '0x6' \
    --gas-budget 100000000 \
    --json > label_creation.json

# Extract label ID
LABEL_ID=$(cat label_creation.json | jq -r '.objectChanges[] | select(.objectType | contains("label::Label")) | .objectId')
echo "✅ Test label created: $LABEL_ID"
```

### 7.2 Create Test XFT
```bash
sui client call \
    --package $PACKAGE_ID \
    --module xft \
    --function mint_xft \
    --args $XFT_REGISTRY \
        $GLOBAL_REGISTRY \
        $FIRE_REGISTRY \
        '"Test XFT"' \
        '"A test XFT for deployment verification"' \
        '"https://xft.red/test.png"' \
        '[1, 1000000000, 1, 1, 0, 0, 1, 0, 0, 1, 0, 250, 0, 0]' \
        'true' \
        '0x6' \
    --gas-budget 100000000 \
    --json > xft_creation.json

# Extract XFT ID
XFT_ID=$(cat xft_creation.json | jq -r '.objectChanges[] | select(.objectType | contains("xft::XFT")) | .objectId')
echo "✅ Test XFT created: $XFT_ID"
```

## 🎉 Step 8: Deployment Complete!

If all steps completed successfully, you now have:

✅ **Complete XFT contract system deployed**
✅ **Environment file with all contract addresses**
✅ **Fire Registry initialized**
✅ **Basic functionality tested**

## 📊 Contract Addresses Summary

Your deployed contracts:
- **Package ID**: `$PACKAGE_ID`
- **Fire Registry**: `$FIRE_REGISTRY`
- **Global Registry**: `$GLOBAL_REGISTRY`
- **Label Registry**: `$LABEL_REGISTRY`
- **XFT Registry**: `$XFT_REGISTRY`
- **Operator Registry**: `$OPERATOR_REGISTRY`
- **Marketplace**: `$MARKETPLACE`

## 🔄 Next Steps

1. **Copy addresses** from `.env.deployment` to your `.env.local`
2. **Update frontend** constants with new contract addresses
3. **Test frontend integration** with deployed contracts
4. **Initialize platform settings** through admin interface

## 🚨 Troubleshooting

### If deployment fails:
- **Increase gas budget**: Try `--gas-budget 500000000`
- **Check balance**: Run `sui client balance` and get more SUI if needed
- **Check network**: Ensure you're on devnet with `sui client active-env`

### If contract calls fail:
- **Check object IDs**: Verify all extracted IDs are valid
- **Check permissions**: Ensure you're using the correct wallet address
- **Check gas**: Make sure you have enough SUI for transactions

### If jq is not available:
- **Install jq**: `brew install jq` (macOS) or `apt-get install jq` (Linux)
- **Manual extraction**: Look for object IDs in the JSON output manually

## 🎯 Success Indicators

You'll know the deployment succeeded when:
- ✅ All contract addresses are extracted successfully
- ✅ Fire Registry initialization completes without errors
- ✅ Test label and XFT creation work
- ✅ `.env.deployment` file contains all required addresses

The XFT contract system is now ready for production use on Sui devnet!
