2025/06/17-12:16:27.410595 ********** RocksDB version: 7.4.4
2025/06/17-12:16:27.413508 ********** Compile date 2022-07-19 08:49:59
2025/06/17-12:16:27.413510 ********** DB SUMMARY
2025/06/17-12:16:27.413510 ********** DB Session ID:  FT86ZSFLU7EFXV85QU6X
2025/06/17-12:16:27.413555 ********** SST files in test-ledger/rocksdb dir, Total Num: 0, files: 
2025/06/17-12:16:27.413556 ********** Write Ahead Log file in test-ledger/rocksdb: 
2025/06/17-12:16:27.413557 **********                         Options.error_if_exists: 0
2025/06/17-12:16:27.413558 **********                       Options.create_if_missing: 1
2025/06/17-12:16:27.413559 **********                         Options.paranoid_checks: 1
2025/06/17-12:16:27.413560 **********             Options.flush_verify_memtable_count: 1
2025/06/17-12:16:27.413560 **********                               Options.track_and_verify_wals_in_manifest: 0
2025/06/17-12:16:27.413561 **********        Options.verify_sst_unique_id_in_manifest: 0
2025/06/17-12:16:27.413561 **********                                     Options.env: 0x104af4f30
2025/06/17-12:16:27.413562 **********                                      Options.fs: PosixFileSystem
2025/06/17-12:16:27.413563 **********                                Options.info_log: 0x158e4f320
2025/06/17-12:16:27.413563 **********                Options.max_file_opening_threads: 16
2025/06/17-12:16:27.413564 **********                              Options.statistics: 0x0
2025/06/17-12:16:27.413565 **********                               Options.use_fsync: 0
2025/06/17-12:16:27.413565 **********                       Options.max_log_file_size: 0
2025/06/17-12:16:27.413566 **********                  Options.max_manifest_file_size: 1073741824
2025/06/17-12:16:27.413567 **********                   Options.log_file_time_to_roll: 0
2025/06/17-12:16:27.413567 **********                       Options.keep_log_file_num: 1000
2025/06/17-12:16:27.413568 **********                    Options.recycle_log_file_num: 0
2025/06/17-12:16:27.413568 **********                         Options.allow_fallocate: 1
2025/06/17-12:16:27.413569 **********                        Options.allow_mmap_reads: 0
2025/06/17-12:16:27.413569 **********                       Options.allow_mmap_writes: 0
2025/06/17-12:16:27.413570 **********                        Options.use_direct_reads: 0
2025/06/17-12:16:27.413571 **********                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/17-12:16:27.413571 **********          Options.create_missing_column_families: 1
2025/06/17-12:16:27.413572 **********                              Options.db_log_dir: 
2025/06/17-12:16:27.413572 **********                                 Options.wal_dir: 
2025/06/17-12:16:27.413573 **********                Options.table_cache_numshardbits: 6
2025/06/17-12:16:27.413573 **********                         Options.WAL_ttl_seconds: 0
2025/06/17-12:16:27.413574 **********                       Options.WAL_size_limit_MB: 0
2025/06/17-12:16:27.413575 **********                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/17-12:16:27.413575 **********             Options.manifest_preallocation_size: 4194304
2025/06/17-12:16:27.413576 **********                     Options.is_fd_close_on_exec: 1
2025/06/17-12:16:27.413576 **********                   Options.advise_random_on_open: 1
2025/06/17-12:16:27.413577 **********                   Options.experimental_mempurge_threshold: 0.000000
2025/06/17-12:16:27.413579 **********                    Options.db_write_buffer_size: 0
2025/06/17-12:16:27.413580 **********                    Options.write_buffer_manager: 0x158e4f370
2025/06/17-12:16:27.413580 **********         Options.access_hint_on_compaction_start: 1
2025/06/17-12:16:27.413581 **********           Options.random_access_max_buffer_size: 1048576
2025/06/17-12:16:27.413581 **********                      Options.use_adaptive_mutex: 0
2025/06/17-12:16:27.413582 **********                            Options.rate_limiter: 0x0
2025/06/17-12:16:27.413583 **********     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/17-12:16:27.413583 **********                       Options.wal_recovery_mode: 2
2025/06/17-12:16:27.413628 **********                  Options.enable_thread_tracking: 0
2025/06/17-12:16:27.413630 **********                  Options.enable_pipelined_write: 0
2025/06/17-12:16:27.413631 **********                  Options.unordered_write: 0
2025/06/17-12:16:27.413632 **********         Options.allow_concurrent_memtable_write: 1
2025/06/17-12:16:27.413633 **********      Options.enable_write_thread_adaptive_yield: 1
2025/06/17-12:16:27.413633 **********             Options.write_thread_max_yield_usec: 100
2025/06/17-12:16:27.413634 **********            Options.write_thread_slow_yield_usec: 3
2025/06/17-12:16:27.413634 **********                               Options.row_cache: None
2025/06/17-12:16:27.413635 **********                              Options.wal_filter: None
2025/06/17-12:16:27.413636 **********             Options.avoid_flush_during_recovery: 0
2025/06/17-12:16:27.413637 **********             Options.allow_ingest_behind: 0
2025/06/17-12:16:27.413637 **********             Options.two_write_queues: 0
2025/06/17-12:16:27.413638 **********             Options.manual_wal_flush: 0
2025/06/17-12:16:27.413638 **********             Options.wal_compression: 0
2025/06/17-12:16:27.413639 **********             Options.atomic_flush: 0
2025/06/17-12:16:27.413639 **********             Options.avoid_unnecessary_blocking_io: 0
2025/06/17-12:16:27.413640 **********                 Options.persist_stats_to_disk: 0
2025/06/17-12:16:27.413640 **********                 Options.write_dbid_to_manifest: 0
2025/06/17-12:16:27.413641 **********                 Options.log_readahead_size: 0
2025/06/17-12:16:27.413642 **********                 Options.file_checksum_gen_factory: Unknown
2025/06/17-12:16:27.413642 **********                 Options.best_efforts_recovery: 0
2025/06/17-12:16:27.413643 **********                Options.max_bgerror_resume_count: 2147483647
2025/06/17-12:16:27.413644 **********            Options.bgerror_resume_retry_interval: 1000000
2025/06/17-12:16:27.413644 **********             Options.allow_data_in_errors: 0
2025/06/17-12:16:27.413645 **********             Options.db_host_id: __hostname__
2025/06/17-12:16:27.413645 **********             Options.enforce_single_del_contracts: true
2025/06/17-12:16:27.413646 **********             Options.max_background_jobs: 8
2025/06/17-12:16:27.413646 **********             Options.max_background_compactions: -1
2025/06/17-12:16:27.413647 **********             Options.max_subcompactions: 1
2025/06/17-12:16:27.413648 **********             Options.avoid_flush_during_shutdown: 0
2025/06/17-12:16:27.413648 **********           Options.writable_file_max_buffer_size: 1048576
2025/06/17-12:16:27.413649 **********             Options.delayed_write_rate : 16777216
2025/06/17-12:16:27.413649 **********             Options.max_total_wal_size: 4294967296
2025/06/17-12:16:27.413650 **********             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/17-12:16:27.413651 **********                   Options.stats_dump_period_sec: 600
2025/06/17-12:16:27.413651 **********                 Options.stats_persist_period_sec: 600
2025/06/17-12:16:27.413652 **********                 Options.stats_history_buffer_size: 1048576
2025/06/17-12:16:27.413652 **********                          Options.max_open_files: -1
2025/06/17-12:16:27.413653 **********                          Options.bytes_per_sync: 0
2025/06/17-12:16:27.413654 **********                      Options.wal_bytes_per_sync: 0
2025/06/17-12:16:27.413654 **********                   Options.strict_bytes_per_sync: 0
2025/06/17-12:16:27.413655 **********       Options.compaction_readahead_size: 0
2025/06/17-12:16:27.413656 **********                  Options.max_background_flushes: -1
2025/06/17-12:16:27.413656 ********** Compression algorithms supported:
2025/06/17-12:16:27.413657 ********** 	kZSTD supported: 0
2025/06/17-12:16:27.413658 ********** 	kZlibCompression supported: 0
2025/06/17-12:16:27.413659 ********** 	kXpressCompression supported: 0
2025/06/17-12:16:27.413682 ********** 	kSnappyCompression supported: 0
2025/06/17-12:16:27.413683 ********** 	kZSTDNotFinalCompression supported: 0
2025/06/17-12:16:27.413684 ********** 	kLZ4HCCompression supported: 1
2025/06/17-12:16:27.413685 ********** 	kLZ4Compression supported: 1
2025/06/17-12:16:27.413685 ********** 	kBZip2Compression supported: 0
2025/06/17-12:16:27.413695 ********** Fast CRC32 supported: Supported on Arm64
2025/06/17-12:16:27.413696 ********** DMutex implementation: pthread_mutex_t
2025/06/17-12:16:27.414231 ********** [db/db_impl/db_impl_open.cc:313] Creating manifest 1 
2025/06/17-12:16:27.414606 ********** [db/version_set.cc:4948] Recovering from manifest file: test-ledger/rocksdb/MANIFEST-000001
2025/06/17-12:16:27.414771 ********** [db/column_family.cc:614] --------------- Options for column family [default]:
2025/06/17-12:16:27.414772 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.414773 **********           Options.merge_operator: None
2025/06/17-12:16:27.414773 **********        Options.compaction_filter: None
2025/06/17-12:16:27.414774 **********        Options.compaction_filter_factory: None
2025/06/17-12:16:27.414775 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.414775 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.414776 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.414794 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e4dfb0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e4e008
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.414795 **********        Options.write_buffer_size: 67108864
2025/06/17-12:16:27.414796 **********  Options.max_write_buffer_number: 2
2025/06/17-12:16:27.414797 **********          Options.compression: NoCompression
2025/06/17-12:16:27.414797 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.414798 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.414799 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.414799 **********             Options.num_levels: 7
2025/06/17-12:16:27.414800 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.414801 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.414801 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.414802 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.414802 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.414803 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.414804 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.414804 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.414805 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.414815 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.414816 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.414817 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.414817 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.414818 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.414819 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.414819 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.414820 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.414820 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.414821 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.414822 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.414822 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.414823 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.414823 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.414824 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.414824 **********                   Options.target_file_size_base: 67108864
2025/06/17-12:16:27.414825 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.414826 **********                Options.max_bytes_for_level_base: *********
2025/06/17-12:16:27.414826 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.414827 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.414828 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.414828 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.414829 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.414830 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.414830 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.414831 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.414831 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.414832 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.414832 **********                    Options.max_compaction_bytes: 1677721600
2025/06/17-12:16:27.414833 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.414834 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.414834 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.414835 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.414836 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.414837 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.414837 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.414838 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.414838 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.414839 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.414839 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.414840 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.414841 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.414841 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.414850 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.414851 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.414851 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.414852 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.414852 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.414853 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.414854 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.414854 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.414855 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.414855 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.414856 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.414856 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.414857 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.414858 **********          Options.periodic_compaction_seconds: 0
2025/06/17-12:16:27.414858 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.414859 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.414859 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.414860 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.414860 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.414861 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.414862 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.414862 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.414863 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.415289 ********** [db/version_set.cc:4996] Recovered from manifest file:test-ledger/rocksdb/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/06/17-12:16:27.415292 ********** [db/version_set.cc:5005] Column family [default] (ID 0), log number is 0
2025/06/17-12:16:27.415358 ********** [db/db_impl/db_impl_open.cc:532] DB ID: a2821087-a996-44b1-bc5d-2ab4020ae4ab
2025/06/17-12:16:27.415555 ********** [db/version_set.cc:4467] Creating manifest 5
2025/06/17-12:16:27.416291 ********** [db/column_family.cc:614] --------------- Options for column family [meta]:
2025/06/17-12:16:27.416294 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.416295 **********           Options.merge_operator: None
2025/06/17-12:16:27.416296 **********        Options.compaction_filter: None
2025/06/17-12:16:27.416310 **********        Options.compaction_filter_factory: purged_slot_filter_factory(meta)
2025/06/17-12:16:27.416310 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.416311 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.416311 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.416322 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e3c990)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e3c9e8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.416323 **********        Options.write_buffer_size: *********
2025/06/17-12:16:27.416324 **********  Options.max_write_buffer_number: 8
2025/06/17-12:16:27.416324 **********          Options.compression: NoCompression
2025/06/17-12:16:27.416325 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.416326 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.416326 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.416327 **********             Options.num_levels: 7
2025/06/17-12:16:27.416327 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.416328 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.416328 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.416329 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.416330 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.416330 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.416331 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.416331 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.416332 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.416333 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.416333 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.416334 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.416334 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.416335 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.416335 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.416336 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.416337 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.416348 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.416349 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.416349 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.416350 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.416351 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.416351 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.416358 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.416359 **********                   Options.target_file_size_base: 107374182
2025/06/17-12:16:27.416360 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.416361 **********                Options.max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.416361 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.416362 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.416363 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.416363 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.416364 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.416365 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.416365 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.416366 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.416366 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.416367 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.416367 **********                    Options.max_compaction_bytes: 2684354550
2025/06/17-12:16:27.416368 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.416368 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.416369 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.416370 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.416370 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.416371 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.416372 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.416372 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.416373 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.416373 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.416374 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.416375 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.416375 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.416376 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.416377 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.416405 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.416405 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.416406 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.416407 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.416407 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.416408 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.416408 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.416417 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.416417 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.416418 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.416419 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.416419 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.416420 **********          Options.periodic_compaction_seconds: 2592000
2025/06/17-12:16:27.416420 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.416421 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.416422 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.416422 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.416423 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.416423 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.416424 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.416425 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.416425 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.416482 ********** [db/db_impl/db_impl.cc:2834] Created column family [meta] (ID 1)
2025/06/17-12:16:27.418784 ********** [db/column_family.cc:614] --------------- Options for column family [dead_slots]:
2025/06/17-12:16:27.418796 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.418798 **********           Options.merge_operator: None
2025/06/17-12:16:27.418799 **********        Options.compaction_filter: None
2025/06/17-12:16:27.418801 **********        Options.compaction_filter_factory: purged_slot_filter_factory(dead_slots)
2025/06/17-12:16:27.418803 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.418804 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.418806 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.418863 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e3d8f0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e3d948
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.418868 **********        Options.write_buffer_size: *********
2025/06/17-12:16:27.418869 **********  Options.max_write_buffer_number: 8
2025/06/17-12:16:27.418871 **********          Options.compression: NoCompression
2025/06/17-12:16:27.418872 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.418873 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.418875 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.418876 **********             Options.num_levels: 7
2025/06/17-12:16:27.418877 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.418879 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.418880 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.418881 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.418883 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.418884 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.418885 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.418887 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.418888 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.418889 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.418891 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.418892 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.418893 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.418895 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.418896 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.418897 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.418898 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.418939 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.418941 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.418943 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.418944 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.418945 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.418946 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.418948 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.418949 **********                   Options.target_file_size_base: 107374182
2025/06/17-12:16:27.418950 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.418952 **********                Options.max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.418953 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.418954 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.418956 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.418958 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.418959 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.418960 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.418961 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.418963 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.418964 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.418965 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.418967 **********                    Options.max_compaction_bytes: 2684354550
2025/06/17-12:16:27.418968 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.418969 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.418971 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.418972 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.418974 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.418975 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.418977 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.418978 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.418979 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.418981 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.418982 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.418984 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.418985 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.418986 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.418988 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.418990 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.418991 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.418992 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.418994 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.418995 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.418997 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.418998 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.419029 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.419032 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.419033 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.419033 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.419034 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.419034 **********          Options.periodic_compaction_seconds: 2592000
2025/06/17-12:16:27.419035 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.419036 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.419036 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.419037 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.419038 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.419038 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.419039 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.419040 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.419041 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.419179 ********** [db/db_impl/db_impl.cc:2834] Created column family [dead_slots] (ID 2)
2025/06/17-12:16:27.422656 ********** [db/column_family.cc:614] --------------- Options for column family [duplicate_slots]:
2025/06/17-12:16:27.422660 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.422661 **********           Options.merge_operator: None
2025/06/17-12:16:27.422661 **********        Options.compaction_filter: None
2025/06/17-12:16:27.422662 **********        Options.compaction_filter_factory: purged_slot_filter_factory(duplicate_slots)
2025/06/17-12:16:27.422663 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.422663 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.422664 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.422677 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e3e850)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e3e8a8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.422679 **********        Options.write_buffer_size: *********
2025/06/17-12:16:27.422679 **********  Options.max_write_buffer_number: 8
2025/06/17-12:16:27.422680 **********          Options.compression: NoCompression
2025/06/17-12:16:27.422681 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.422681 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.422682 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.422683 **********             Options.num_levels: 7
2025/06/17-12:16:27.422683 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.422684 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.422684 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.422685 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.422686 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.422686 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.422687 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.422688 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.422688 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.422689 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.422689 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.422690 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.422691 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.422691 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.422692 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.422693 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.422693 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.422705 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.422706 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.422706 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.422707 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.422708 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.422709 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.422710 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.422710 **********                   Options.target_file_size_base: 107374182
2025/06/17-12:16:27.422711 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.422711 **********                Options.max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.422712 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.422713 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.422713 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.422714 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.422715 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.422715 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.422716 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.422716 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.422717 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.422718 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.422718 **********                    Options.max_compaction_bytes: 2684354550
2025/06/17-12:16:27.422719 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.422719 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.422720 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.422721 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.422736 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.422739 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.422739 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.422740 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.422741 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.422742 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.422742 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.422743 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.422744 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.422745 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.422746 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.422746 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.422747 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.422748 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.422749 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.422749 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.422750 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.422750 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.422770 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.422771 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.422771 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.422772 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.422773 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.422773 **********          Options.periodic_compaction_seconds: 2592000
2025/06/17-12:16:27.422774 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.422774 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.422775 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.422776 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.422777 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.422777 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.422778 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.422779 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.422779 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.422813 ********** [db/db_impl/db_impl.cc:2834] Created column family [duplicate_slots] (ID 3)
2025/06/17-12:16:27.427484 ********** [db/column_family.cc:614] --------------- Options for column family [erasure_meta]:
2025/06/17-12:16:27.427491 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.427492 **********           Options.merge_operator: None
2025/06/17-12:16:27.427492 **********        Options.compaction_filter: None
2025/06/17-12:16:27.427493 **********        Options.compaction_filter_factory: purged_slot_filter_factory(erasure_meta)
2025/06/17-12:16:27.427494 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.427495 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.427495 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.427507 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e3f7b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e3f808
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.427509 **********        Options.write_buffer_size: *********
2025/06/17-12:16:27.427509 **********  Options.max_write_buffer_number: 8
2025/06/17-12:16:27.427510 **********          Options.compression: NoCompression
2025/06/17-12:16:27.427511 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.427512 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.427512 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.427513 **********             Options.num_levels: 7
2025/06/17-12:16:27.427513 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.427514 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.427515 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.427515 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.427516 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.427517 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.427518 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.427518 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.427519 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.427520 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.427520 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.427521 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.427521 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.427522 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.427523 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.427523 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.427524 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.427537 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.427538 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.427539 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.427540 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.427540 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.427541 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.427542 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.427542 **********                   Options.target_file_size_base: 107374182
2025/06/17-12:16:27.427543 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.427544 **********                Options.max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.427544 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.427545 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.427546 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.427547 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.427547 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.427548 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.427549 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.427549 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.427550 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.427551 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.427551 **********                    Options.max_compaction_bytes: 2684354550
2025/06/17-12:16:27.427552 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.427552 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.427553 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.427554 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.427555 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.427555 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.427556 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.427557 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.427557 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.427558 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.427559 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.427560 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.427560 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.427561 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.427563 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.427563 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.427564 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.427565 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.427565 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.427566 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.427567 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.427567 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.427575 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.427576 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.427576 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.427577 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.427578 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.427578 **********          Options.periodic_compaction_seconds: 2592000
2025/06/17-12:16:27.427579 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.427580 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.427580 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.427581 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.427582 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.427582 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.427583 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.427584 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.427585 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.427625 ********** [db/db_impl/db_impl.cc:2834] Created column family [erasure_meta] (ID 4)
2025/06/17-12:16:27.430385 ********** [db/column_family.cc:614] --------------- Options for column family [orphans]:
2025/06/17-12:16:27.430390 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.430391 **********           Options.merge_operator: None
2025/06/17-12:16:27.430392 **********        Options.compaction_filter: None
2025/06/17-12:16:27.430392 **********        Options.compaction_filter_factory: purged_slot_filter_factory(orphans)
2025/06/17-12:16:27.430393 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.430394 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.430395 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.430406 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e40710)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e40768
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.430407 **********        Options.write_buffer_size: *********
2025/06/17-12:16:27.430408 **********  Options.max_write_buffer_number: 8
2025/06/17-12:16:27.430409 **********          Options.compression: NoCompression
2025/06/17-12:16:27.430410 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.430410 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.430411 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.430412 **********             Options.num_levels: 7
2025/06/17-12:16:27.430412 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.430413 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.430414 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.430414 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.430415 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.430416 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.430416 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.430417 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.430418 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.430418 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.430419 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.430420 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.430420 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.430421 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.430422 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.430422 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.430423 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.430433 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.430433 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.430434 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.430435 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.430435 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.430436 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.430437 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.430437 **********                   Options.target_file_size_base: 107374182
2025/06/17-12:16:27.430438 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.430439 **********                Options.max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.430440 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.430440 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.430441 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.430442 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.430442 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.430443 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.430444 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.430444 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.430445 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.430446 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.430446 **********                    Options.max_compaction_bytes: 2684354550
2025/06/17-12:16:27.430447 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.430448 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.430448 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.430449 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.430450 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.430451 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.430451 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.430452 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.430453 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.430453 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.430454 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.430455 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.430456 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.430456 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.430458 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.430458 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.430459 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.430460 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.430460 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.430461 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.430462 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.430462 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.430603 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.430604 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.430605 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.430605 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.430606 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.430607 **********          Options.periodic_compaction_seconds: 2592000
2025/06/17-12:16:27.430607 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.430608 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.430609 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.430609 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.430610 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.430611 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.430612 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.430612 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.430613 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.430650 ********** [db/db_impl/db_impl.cc:2834] Created column family [orphans] (ID 5)
2025/06/17-12:16:27.433094 ********** [db/column_family.cc:614] --------------- Options for column family [bank_hashes]:
2025/06/17-12:16:27.433097 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.433098 **********           Options.merge_operator: None
2025/06/17-12:16:27.433099 **********        Options.compaction_filter: None
2025/06/17-12:16:27.433099 **********        Options.compaction_filter_factory: purged_slot_filter_factory(bank_hashes)
2025/06/17-12:16:27.433100 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.433101 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.433101 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.433112 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e41670)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e416c8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.433113 **********        Options.write_buffer_size: *********
2025/06/17-12:16:27.433114 **********  Options.max_write_buffer_number: 8
2025/06/17-12:16:27.433115 **********          Options.compression: NoCompression
2025/06/17-12:16:27.433115 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.433116 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.433117 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.433117 **********             Options.num_levels: 7
2025/06/17-12:16:27.433118 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.433119 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.433119 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.433120 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.433121 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.433122 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.433122 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.433123 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.433123 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.433124 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.433125 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.433125 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.433126 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.433127 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.433127 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.433128 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.433129 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.433139 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.433140 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.433140 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.433141 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.433142 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.433142 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.433143 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.433144 **********                   Options.target_file_size_base: 107374182
2025/06/17-12:16:27.433144 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.433145 **********                Options.max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.433145 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.433146 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.433147 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.433148 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.433148 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.433149 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.433150 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.433150 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.433151 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.433151 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.433152 **********                    Options.max_compaction_bytes: 2684354550
2025/06/17-12:16:27.433153 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.433153 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.433154 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.433155 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.433156 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.433156 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.433157 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.433158 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.433158 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.433159 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.433160 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.433161 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.433161 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.433162 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.433163 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.433164 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.433164 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.433165 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.433166 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.433166 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.433167 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.433168 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.433175 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.433176 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.433177 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.433177 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.433178 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.433178 **********          Options.periodic_compaction_seconds: 2592000
2025/06/17-12:16:27.433179 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.433180 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.433180 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.433181 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.433182 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.433182 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.433183 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.433184 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.433185 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.433214 ********** [db/db_impl/db_impl.cc:2834] Created column family [bank_hashes] (ID 6)
2025/06/17-12:16:27.436575 ********** [db/column_family.cc:614] --------------- Options for column family [root]:
2025/06/17-12:16:27.436584 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.436585 **********           Options.merge_operator: None
2025/06/17-12:16:27.436586 **********        Options.compaction_filter: None
2025/06/17-12:16:27.436587 **********        Options.compaction_filter_factory: purged_slot_filter_factory(root)
2025/06/17-12:16:27.436588 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.436588 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.436589 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.436602 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e425d0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e42628
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.436618 **********        Options.write_buffer_size: *********
2025/06/17-12:16:27.436620 **********  Options.max_write_buffer_number: 8
2025/06/17-12:16:27.436621 **********          Options.compression: NoCompression
2025/06/17-12:16:27.436622 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.436623 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.436623 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.436624 **********             Options.num_levels: 7
2025/06/17-12:16:27.436625 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.436625 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.436626 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.436627 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.436627 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.436628 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.436629 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.436629 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.436630 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.436631 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.436631 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.436632 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.436633 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.436633 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.436634 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.436634 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.436635 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.436647 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.436647 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.436648 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.436649 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.436649 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.436650 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.436651 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.436651 **********                   Options.target_file_size_base: 107374182
2025/06/17-12:16:27.436652 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.436653 **********                Options.max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.436653 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.436654 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.436655 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.436655 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.436656 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.436657 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.436657 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.436658 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.436659 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.436659 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.436660 **********                    Options.max_compaction_bytes: 2684354550
2025/06/17-12:16:27.436660 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.436661 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.436662 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.436662 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.436664 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.436665 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.436665 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.436666 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.436666 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.436667 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.436668 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.436669 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.436670 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.436670 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.436672 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.436672 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.436673 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.436673 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.436674 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.436675 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.436675 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.436676 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.436687 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.436687 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.436688 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.436689 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.436690 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.436690 **********          Options.periodic_compaction_seconds: 2592000
2025/06/17-12:16:27.436691 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.436692 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.436692 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.436693 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.436694 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.436695 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.436695 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.436696 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.436697 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.436739 ********** [db/db_impl/db_impl.cc:2834] Created column family [root] (ID 7)
2025/06/17-12:16:27.439671 ********** [db/column_family.cc:614] --------------- Options for column family [index]:
2025/06/17-12:16:27.439675 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.439676 **********           Options.merge_operator: None
2025/06/17-12:16:27.439676 **********        Options.compaction_filter: None
2025/06/17-12:16:27.439677 **********        Options.compaction_filter_factory: purged_slot_filter_factory(index)
2025/06/17-12:16:27.439678 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.439678 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.439679 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.439690 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e43530)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e43588
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.439691 **********        Options.write_buffer_size: *********
2025/06/17-12:16:27.439692 **********  Options.max_write_buffer_number: 8
2025/06/17-12:16:27.439693 **********          Options.compression: NoCompression
2025/06/17-12:16:27.439693 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.439694 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.439695 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.439696 **********             Options.num_levels: 7
2025/06/17-12:16:27.439696 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.439697 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.439698 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.439698 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.439699 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.439700 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.439700 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.439701 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.439702 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.439702 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.439703 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.439704 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.439704 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.439705 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.439706 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.439706 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.439707 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.439717 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.439718 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.439719 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.439719 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.439720 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.439721 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.439721 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.439722 **********                   Options.target_file_size_base: 107374182
2025/06/17-12:16:27.439722 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.439723 **********                Options.max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.439724 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.439724 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.439725 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.439726 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.439727 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.439727 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.439728 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.439729 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.439729 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.439730 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.439730 **********                    Options.max_compaction_bytes: 2684354550
2025/06/17-12:16:27.439731 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.439732 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.439732 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.439733 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.439734 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.439735 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.439735 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.439736 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.439736 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.439737 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.439738 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.439739 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.439739 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.439740 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.439741 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.439742 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.439742 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.439743 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.439744 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.439744 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.439745 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.439746 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.439755 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.439756 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.439757 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.439757 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.439758 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.439759 **********          Options.periodic_compaction_seconds: 2592000
2025/06/17-12:16:27.439759 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.439760 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.439760 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.439761 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.439762 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.439762 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.439763 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.439764 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.439765 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.439795 ********** [db/db_impl/db_impl.cc:2834] Created column family [index] (ID 8)
2025/06/17-12:16:27.443062 ********** [db/column_family.cc:614] --------------- Options for column family [data_shred]:
2025/06/17-12:16:27.443065 **********               Options.comparator: leveldb.BytewiseComparator
2025/06/17-12:16:27.443066 **********           Options.merge_operator: None
2025/06/17-12:16:27.443067 **********        Options.compaction_filter: None
2025/06/17-12:16:27.443068 **********        Options.compaction_filter_factory: purged_slot_filter_factory(data_shred)
2025/06/17-12:16:27.443069 **********  Options.sst_partitioner_factory: None
2025/06/17-12:16:27.443070 **********         Options.memtable_factory: SkipListFactory
2025/06/17-12:16:27.443071 **********            Options.table_factory: BlockBasedTable
2025/06/17-12:16:27.443082 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x158e3aaa0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0x158e3aaf8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
  block_cache_compressed: 0x0
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
2025/06/17-12:16:27.443084 **********        Options.write_buffer_size: *********
2025/06/17-12:16:27.443084 **********  Options.max_write_buffer_number: 8
2025/06/17-12:16:27.443085 **********          Options.compression: NoCompression
2025/06/17-12:16:27.443086 **********                  Options.bottommost_compression: Disabled
2025/06/17-12:16:27.443087 **********       Options.prefix_extractor: nullptr
2025/06/17-12:16:27.443087 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/17-12:16:27.443088 **********             Options.num_levels: 7
2025/06/17-12:16:27.443089 **********        Options.min_write_buffer_number_to_merge: 1
2025/06/17-12:16:27.443089 **********     Options.max_write_buffer_number_to_maintain: 0
2025/06/17-12:16:27.443090 **********     Options.max_write_buffer_size_to_maintain: 0
2025/06/17-12:16:27.443091 **********            Options.bottommost_compression_opts.window_bits: -14
2025/06/17-12:16:27.443092 **********                  Options.bottommost_compression_opts.level: 32767
2025/06/17-12:16:27.443092 **********               Options.bottommost_compression_opts.strategy: 0
2025/06/17-12:16:27.443093 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.443094 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.443095 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/17-12:16:27.443095 **********                  Options.bottommost_compression_opts.enabled: false
2025/06/17-12:16:27.443096 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.443097 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.443097 **********            Options.compression_opts.window_bits: -14
2025/06/17-12:16:27.443098 **********                  Options.compression_opts.level: 32767
2025/06/17-12:16:27.443099 **********               Options.compression_opts.strategy: 0
2025/06/17-12:16:27.443100 **********         Options.compression_opts.max_dict_bytes: 0
2025/06/17-12:16:27.443100 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/17-12:16:27.443116 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/17-12:16:27.443117 **********         Options.compression_opts.parallel_threads: 1
2025/06/17-12:16:27.443117 **********                  Options.compression_opts.enabled: false
2025/06/17-12:16:27.443118 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/17-12:16:27.443119 **********      Options.level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.443119 **********          Options.level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.443120 **********              Options.level0_stop_writes_trigger: 36
2025/06/17-12:16:27.443121 **********                   Options.target_file_size_base: 107374182
2025/06/17-12:16:27.443122 **********             Options.target_file_size_multiplier: 1
2025/06/17-12:16:27.443122 **********                Options.max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.443123 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/06/17-12:16:27.443124 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.443125 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/17-12:16:27.443125 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/17-12:16:27.443126 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/17-12:16:27.443127 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/17-12:16:27.443128 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/17-12:16:27.443128 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/17-12:16:27.443129 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/17-12:16:27.443130 **********       Options.max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.443130 **********                    Options.max_compaction_bytes: 2684354550
2025/06/17-12:16:27.443131 **********                        Options.arena_block_size: 1048576
2025/06/17-12:16:27.443132 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.443133 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.443133 **********                Options.disable_auto_compactions: 0
2025/06/17-12:16:27.443134 **********                        Options.compaction_style: kCompactionStyleLevel
2025/06/17-12:16:27.443135 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/06/17-12:16:27.443136 ********** Options.compaction_options_universal.size_ratio: 1
2025/06/17-12:16:27.443137 ********** Options.compaction_options_universal.min_merge_width: 2
2025/06/17-12:16:27.443137 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/17-12:16:27.443138 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/17-12:16:27.443139 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/06/17-12:16:27.443140 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/17-12:16:27.443141 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/17-12:16:27.443141 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/06/17-12:16:27.443142 **********                   Options.table_properties_collectors: 
2025/06/17-12:16:27.443143 **********                   Options.inplace_update_support: 0
2025/06/17-12:16:27.443144 **********                 Options.inplace_update_num_locks: 10000
2025/06/17-12:16:27.443144 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/17-12:16:27.443145 **********               Options.memtable_whole_key_filtering: 0
2025/06/17-12:16:27.443146 **********   Options.memtable_huge_page_size: 0
2025/06/17-12:16:27.443147 **********                           Options.bloom_locality: 0
2025/06/17-12:16:27.443147 **********                    Options.max_successive_merges: 0
2025/06/17-12:16:27.443156 **********                Options.optimize_filters_for_hits: 0
2025/06/17-12:16:27.443157 **********                Options.paranoid_file_checks: 0
2025/06/17-12:16:27.443158 **********                Options.force_consistency_checks: 1
2025/06/17-12:16:27.443158 **********                Options.report_bg_io_stats: 0
2025/06/17-12:16:27.443159 **********                               Options.ttl: 2592000
2025/06/17-12:16:27.443160 **********          Options.periodic_compaction_seconds: 2592000
2025/06/17-12:16:27.443160 **********                       Options.enable_blob_files: false
2025/06/17-12:16:27.443161 **********                           Options.min_blob_size: 0
2025/06/17-12:16:27.443162 **********                          Options.blob_file_size: *********
2025/06/17-12:16:27.443163 **********                   Options.blob_compression_type: NoCompression
2025/06/17-12:16:27.443163 **********          Options.enable_blob_garbage_collection: false
2025/06/17-12:16:27.443164 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.443165 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.443166 **********          Options.blob_compaction_readahead_size: 0
2025/06/17-12:16:27.443166 **********                Options.blob_file_starting_level: 0
2025/06/17-12:16:27.443196 ********** [db/db_impl/db_impl.cc:2834] Created column family [data_shred] (ID 9)
2025/06/17-12:16:27.446779 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.446805 ********** [db/db_impl/db_impl.cc:2834] Created column family [code_shred] (ID 10)
2025/06/17-12:16:27.451059 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.451127 ********** [db/db_impl/db_impl.cc:2834] Created column family [transaction_status] (ID 11)
2025/06/17-12:16:27.456084 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.456123 ********** [db/db_impl/db_impl.cc:2834] Created column family [address_signatures] (ID 12)
2025/06/17-12:16:27.460170 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.460196 ********** [db/db_impl/db_impl.cc:2834] Created column family [transaction_memos] (ID 13)
2025/06/17-12:16:27.464521 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.464541 ********** [db/db_impl/db_impl.cc:2834] Created column family [transaction_status_index] (ID 14)
2025/06/17-12:16:27.468984 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.469022 ********** [db/db_impl/db_impl.cc:2834] Created column family [rewards] (ID 15)
2025/06/17-12:16:27.473450 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.473472 ********** [db/db_impl/db_impl.cc:2834] Created column family [blocktime] (ID 16)
2025/06/17-12:16:27.477965 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.477987 ********** [db/db_impl/db_impl.cc:2834] Created column family [perf_samples] (ID 17)
2025/06/17-12:16:27.482532 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.482548 ********** [db/db_impl/db_impl.cc:2834] Created column family [block_height] (ID 18)
2025/06/17-12:16:27.487385 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.487414 ********** [db/db_impl/db_impl.cc:2834] Created column family [program_costs] (ID 19)
2025/06/17-12:16:27.492096 ********** [db/column_family.cc:617] 	(skipping printing options)
2025/06/17-12:16:27.492113 ********** [db/db_impl/db_impl.cc:2834] Created column family [optimistic_slots] (ID 20)
2025/06/17-12:16:27.503600 ********** [db/db_impl/db_impl_open.cc:1965] SstFileManager instance 0x158e4f5a0
2025/06/17-12:16:27.503636 ********** DB pointer 0x159057400
2025/06/17-12:16:27.504163 6166441984 [db/db_impl/db_impl.cc:1043] ------- DUMPING STATS -------
2025/06/17-12:16:27.504184 6166441984 [db/db_impl/db_impl.cc:1044] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e4e008#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 2e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e3c9e8#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [dead_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [dead_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e3d948#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [duplicate_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [duplicate_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e3e8a8#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [erasure_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [erasure_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e3f808#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 5e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [orphans] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [orphans] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e40768#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [bank_hashes] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [bank_hashes] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e416c8#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [root] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [root] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e42628#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e43588#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [data_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [data_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e3aaf8#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 5e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [code_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [code_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e3ba88#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [transaction_status] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e444e8#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [address_signatures] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [address_signatures] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e45468#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [transaction_memos] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_memos] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e463e8#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [transaction_status_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e472a8#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [rewards] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [rewards] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e48168#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [blocktime] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [blocktime] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e490c8#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 5e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [perf_samples] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [perf_samples] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e4a028#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [block_height] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [block_height] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e4af88#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [program_costs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [program_costs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e4bee8#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [optimistic_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [optimistic_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x158e4cd88#74951 capacity: 8.00 MB collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [meta] **

** File Read Latency Histogram By Level [dead_slots] **

** File Read Latency Histogram By Level [duplicate_slots] **

** File Read Latency Histogram By Level [erasure_meta] **

** File Read Latency Histogram By Level [orphans] **

** File Read Latency Histogram By Level [bank_hashes] **

** File Read Latency Histogram By Level [root] **

** File Read Latency Histogram By Level [index] **

** File Read Latency Histogram By Level [data_shred] **

** File Read Latency Histogram By Level [code_shred] **

** File Read Latency Histogram By Level [transaction_status] **

** File Read Latency Histogram By Level [address_signatures] **

** File Read Latency Histogram By Level [transaction_memos] **

** File Read Latency Histogram By Level [transaction_status_index] **

** File Read Latency Histogram By Level [rewards] **

** File Read Latency Histogram By Level [blocktime] **

** File Read Latency Histogram By Level [perf_samples] **

** File Read Latency Histogram By Level [block_height] **

** File Read Latency Histogram By Level [program_costs] **

** File Read Latency Histogram By Level [optimistic_slots] **
2025/06/17-12:16:27.510019 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [erasure_meta], inputs:
2025/06/17-12:16:27.510026 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.510027 ********** [db/db_impl/db_impl.cc:1153] [erasure_meta] SetOptions() succeeded
2025/06/17-12:16:27.510027 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.510028 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.510029 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.510029 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.510030 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.510031 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.510031 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.510032 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.510032 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.510033 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.510033 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.510034 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.510035 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.510035 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.510036 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.510036 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.510037 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.510037 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.510038 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.510038 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.510039 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.510040 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.510041 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.510041 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.510042 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.510042 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.510043 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.510043 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.510044 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.510044 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.510045 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.510045 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.510046 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.510064 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.510065 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.510065 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.510066 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.510067 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.510067 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.510068 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.510068 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.510069 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.510069 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.510070 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.510071 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.510071 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.510072 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.510072 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.518860 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [dead_slots], inputs:
2025/06/17-12:16:27.518866 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.518867 ********** [db/db_impl/db_impl.cc:1153] [dead_slots] SetOptions() succeeded
2025/06/17-12:16:27.518868 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.518869 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.518870 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.518870 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.518871 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.518872 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.518872 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.518873 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.518873 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.518874 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.518875 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.518875 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.518876 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.518876 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.518877 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.518877 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.518878 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.518878 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.518879 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.518880 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.518880 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.518881 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.518882 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.518882 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.518883 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.518884 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.518884 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.518885 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.518885 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.518886 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.518886 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.518887 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.518887 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.518908 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.518908 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.518909 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.518910 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.518910 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.518911 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.518912 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.518912 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.518913 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.518914 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.518914 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.518915 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.518915 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.518916 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.518917 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.527498 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [duplicate_slots], inputs:
2025/06/17-12:16:27.527503 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.527504 ********** [db/db_impl/db_impl.cc:1153] [duplicate_slots] SetOptions() succeeded
2025/06/17-12:16:27.527505 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.527506 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.527507 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.527507 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.527508 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.527509 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.527509 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.527510 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.527510 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.527511 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.527512 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.527512 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.527513 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.527513 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.527514 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.527514 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.527515 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.527515 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.527516 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.527517 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.527517 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.527518 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.527519 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.527519 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.527520 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.527521 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.527521 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.527522 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.527522 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.527523 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.527523 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.527524 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.527524 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.527544 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.527544 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.527545 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.527546 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.527546 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.527547 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.527547 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.527548 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.527548 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.527549 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.527550 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.527550 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.527551 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.527551 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.527552 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.533624 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [index], inputs:
2025/06/17-12:16:27.533629 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.533630 ********** [db/db_impl/db_impl.cc:1153] [index] SetOptions() succeeded
2025/06/17-12:16:27.533630 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.533631 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.533632 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.533632 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.533633 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.533634 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.533634 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.533635 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.533635 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.533636 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.533637 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.533637 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.533638 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.533638 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.533639 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.533639 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.533644 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.533645 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.533645 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.533646 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.533646 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.533647 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.533648 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.533649 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.533649 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.533650 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.533650 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.533651 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.533651 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.533652 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.533652 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.533653 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.533653 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.533671 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.533672 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.533673 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.533673 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.533674 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.533674 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.533675 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.533675 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.533676 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.533677 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.533677 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.533678 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.533678 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.533679 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.533680 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.539197 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [orphans], inputs:
2025/06/17-12:16:27.539205 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.539206 ********** [db/db_impl/db_impl.cc:1153] [orphans] SetOptions() succeeded
2025/06/17-12:16:27.539207 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.539208 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.539209 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.539209 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.539210 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.539211 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.539211 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.539212 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.539212 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.539213 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.539213 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.539214 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.539215 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.539215 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.539216 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.539216 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.539217 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.539217 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.539218 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.539218 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.539219 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.539219 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.539221 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.539221 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.539222 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.539222 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.539223 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.539223 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.539224 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.539224 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.539225 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.539225 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.539226 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.539241 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.539242 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.539242 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.539243 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.539244 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.539244 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.539245 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.539245 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.539246 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.539246 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.539247 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.539248 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.539248 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.539249 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.539249 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.545157 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [bank_hashes], inputs:
2025/06/17-12:16:27.545162 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.545163 ********** [db/db_impl/db_impl.cc:1153] [bank_hashes] SetOptions() succeeded
2025/06/17-12:16:27.545163 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.545164 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.545165 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.545165 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.545166 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.545167 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.545167 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.545168 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.545168 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.545169 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.545169 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.545170 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.545171 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.545171 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.545172 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.545172 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.545173 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.545173 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.545174 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.545174 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.545175 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.545175 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.545176 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.545177 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.545178 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.545178 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.545179 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.545179 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.545180 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.545180 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.545181 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.545181 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.545182 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.545199 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.545200 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.545200 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.545201 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.545202 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.545202 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.545203 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.545203 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.545204 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.545205 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.545205 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.545206 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.545206 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.545207 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.545207 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.550611 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [root], inputs:
2025/06/17-12:16:27.550616 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.550617 ********** [db/db_impl/db_impl.cc:1153] [root] SetOptions() succeeded
2025/06/17-12:16:27.550618 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.550619 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.550619 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.550620 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.550621 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.550621 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.550622 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.550622 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.550623 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.550624 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.550624 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.550625 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.550625 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.550626 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.550626 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.550627 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.550627 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.550628 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.550629 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.550629 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.550630 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.550630 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.550632 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.550632 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.550633 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.550633 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.550634 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.550634 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.550635 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.550635 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.550636 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.550636 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.550637 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.550654 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.550654 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.550655 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.550656 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.550656 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.550657 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.550657 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.550658 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.550659 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.550659 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.550660 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.550660 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.550661 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.550661 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.550662 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.556360 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [meta], inputs:
2025/06/17-12:16:27.556364 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.556365 ********** [db/db_impl/db_impl.cc:1153] [meta] SetOptions() succeeded
2025/06/17-12:16:27.556365 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.556366 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.556367 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.556367 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.556368 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.556369 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.556369 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.556370 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.556371 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.556371 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.556372 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.556372 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.556373 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.556373 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.556374 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.556375 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.556375 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.556376 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.556376 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.556377 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.556377 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.556378 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.556379 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.556380 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.556380 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.556381 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.556381 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.556382 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.556382 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.556383 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.556383 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.556384 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.556384 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.556401 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.556402 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.556402 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.556403 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.556404 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.556404 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.556405 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.556405 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.556406 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.556407 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.556407 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.556408 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.556408 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.556409 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.556409 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.561542 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [data_shred], inputs:
2025/06/17-12:16:27.561546 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.561547 ********** [db/db_impl/db_impl.cc:1153] [data_shred] SetOptions() succeeded
2025/06/17-12:16:27.561548 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.561548 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.561549 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.561550 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.561550 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.561551 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.561551 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.561552 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.561552 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.561553 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.561554 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.561554 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.561555 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.561555 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.561556 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.561556 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.561557 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.561557 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.561558 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.561559 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.561559 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.561560 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.561561 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.561561 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.561562 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.561562 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.561563 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.561563 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.561564 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.561564 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.561565 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.561566 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.561566 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.561581 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.561582 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.561583 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.561583 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.561584 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.561584 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.561585 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.561585 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.561586 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.561587 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.561587 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.561588 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.561588 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.561589 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.561589 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.566615 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [code_shred], inputs:
2025/06/17-12:16:27.566617 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.566618 ********** [db/db_impl/db_impl.cc:1153] [code_shred] SetOptions() succeeded
2025/06/17-12:16:27.566619 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.566620 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.566620 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.566621 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.566622 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.566622 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.566623 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.566623 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.566624 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.566625 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.566625 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.566626 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.566626 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.566627 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.566627 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.566628 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.566628 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.566629 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.566629 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.566630 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.566631 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.566631 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.566632 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.566633 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.566633 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.566634 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.566634 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.566635 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.566635 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.566636 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.566636 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.566637 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.566638 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.566649 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.566650 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.566650 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.566651 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.566651 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.566652 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.566652 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.566653 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.566654 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.566654 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.566655 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.566655 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.566656 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.566656 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.566657 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.572029 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [transaction_status], inputs:
2025/06/17-12:16:27.572033 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.572034 ********** [db/db_impl/db_impl.cc:1153] [transaction_status] SetOptions() succeeded
2025/06/17-12:16:27.572034 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.572035 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.572036 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.572036 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.572037 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.572038 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.572038 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.572039 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.572039 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.572040 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.572040 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.572041 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.572041 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.572042 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.572043 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.572043 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.572044 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.572044 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.572045 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.572045 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.572046 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.572046 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.572047 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.572048 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.572049 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.572049 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.572050 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.572050 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.572051 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.572051 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.572052 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.572052 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.572053 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.572074 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.572074 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.572075 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.572076 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.572076 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.572077 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.572077 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.572078 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.572079 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.572079 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.572080 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.572080 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.572081 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.572082 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.572082 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.577012 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [address_signatures], inputs:
2025/06/17-12:16:27.577015 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.577016 ********** [db/db_impl/db_impl.cc:1153] [address_signatures] SetOptions() succeeded
2025/06/17-12:16:27.577016 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.577017 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.577018 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.577018 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.577019 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.577019 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.577020 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.577020 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.577021 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.577022 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.577022 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.577023 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.577023 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.577024 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.577024 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.577025 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.577025 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.577026 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.577027 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.577027 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.577028 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.577028 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.577029 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.577030 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.577030 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.577031 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.577032 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.577032 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.577033 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.577033 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.577034 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.577034 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.577035 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.577045 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.577046 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.577047 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.577047 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.577048 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.577048 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.577049 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.577049 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.577050 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.577050 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.577051 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.577052 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.577052 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.577053 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.577053 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.582146 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [rewards], inputs:
2025/06/17-12:16:27.582154 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.582155 ********** [db/db_impl/db_impl.cc:1153] [rewards] SetOptions() succeeded
2025/06/17-12:16:27.582155 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.582156 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.582157 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.582158 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.582158 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.582159 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.582159 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.582160 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.582161 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.582161 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.582162 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.582162 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.582163 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.582163 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.582164 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.582164 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.582165 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.582166 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.582166 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.582167 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.582167 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.582168 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.582169 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.582170 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.582170 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.582171 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.582171 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.582172 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.582172 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.582173 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.582173 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.582174 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.582174 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.582196 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.582196 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.582197 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.582197 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.582198 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.582199 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.582199 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.582200 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.582200 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.582201 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.582201 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.582202 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.582202 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.582203 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.582204 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.587820 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [blocktime], inputs:
2025/06/17-12:16:27.587825 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.587826 ********** [db/db_impl/db_impl.cc:1153] [blocktime] SetOptions() succeeded
2025/06/17-12:16:27.587826 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.587827 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.587828 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.587828 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.587829 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.587829 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.587830 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.587830 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.587831 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.587832 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.587832 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.587833 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.587833 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.587834 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.587834 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.587835 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.587836 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.587836 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.587837 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.587837 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.587838 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.587839 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.587840 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.587840 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.587841 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.587841 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.587842 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.587842 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.587843 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.587843 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.587844 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.587844 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.587845 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.587859 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.587859 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.587860 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.587860 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.587861 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.587861 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.587862 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.587863 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.587863 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.587864 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.587864 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.587865 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.587866 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.587866 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.587867 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.592832 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [perf_samples], inputs:
2025/06/17-12:16:27.592835 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.592835 ********** [db/db_impl/db_impl.cc:1153] [perf_samples] SetOptions() succeeded
2025/06/17-12:16:27.592836 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.592837 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.592837 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.592838 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.592839 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.592839 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.592840 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.592840 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.592841 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.592841 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.592842 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.592842 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.592843 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.592843 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.592844 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.592844 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.592845 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.592846 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.592846 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.592847 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.592847 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.592848 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.592849 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.592849 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.592850 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.592850 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.592851 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.592851 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.592852 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.592853 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.592853 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.592854 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.592854 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.592864 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.592865 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.592866 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.592866 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.592867 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.592867 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.592868 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.592868 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.592869 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.592869 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.592870 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.592871 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.592871 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.592872 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.592872 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.598122 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [block_height], inputs:
2025/06/17-12:16:27.598127 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.598128 ********** [db/db_impl/db_impl.cc:1153] [block_height] SetOptions() succeeded
2025/06/17-12:16:27.598129 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.598130 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.598130 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.598131 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.598132 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.598132 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.598133 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.598134 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.598134 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.598135 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.598135 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.598136 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.598136 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.598137 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.598137 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.598138 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.598139 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.598139 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.598140 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.598140 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.598141 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.598141 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.598142 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.598143 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.598144 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.598144 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.598145 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.598145 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.598146 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.598146 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.598147 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.598147 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.598148 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.598164 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.598165 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.598166 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.598166 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.598167 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.598167 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.598168 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.598168 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.598169 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.598170 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.598170 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.598171 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.598171 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.598172 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.598172 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.603351 ********** [db/db_impl/db_impl.cc:1146] SetOptions() on column family [optimistic_slots], inputs:
2025/06/17-12:16:27.603358 ********** [db/db_impl/db_impl.cc:1149] periodic_compaction_seconds: 86400
2025/06/17-12:16:27.603359 ********** [db/db_impl/db_impl.cc:1153] [optimistic_slots] SetOptions() succeeded
2025/06/17-12:16:27.603359 ********** [options/cf_options.cc:967]                         write_buffer_size: *********
2025/06/17-12:16:27.603360 ********** [options/cf_options.cc:969]                   max_write_buffer_number: 8
2025/06/17-12:16:27.603361 ********** [options/cf_options.cc:972]                          arena_block_size: 1048576
2025/06/17-12:16:27.603361 ********** [options/cf_options.cc:974]               memtable_prefix_bloom_ratio: 0.000000
2025/06/17-12:16:27.603362 ********** [options/cf_options.cc:976]               memtable_whole_key_filtering: 0
2025/06/17-12:16:27.603363 ********** [options/cf_options.cc:979]                   memtable_huge_page_size: 0
2025/06/17-12:16:27.603363 ********** [options/cf_options.cc:982]                     max_successive_merges: 0
2025/06/17-12:16:27.603364 ********** [options/cf_options.cc:985]                  inplace_update_num_locks: 10000
2025/06/17-12:16:27.603364 ********** [options/cf_options.cc:989]                          prefix_extractor: nullptr
2025/06/17-12:16:27.603365 ********** [options/cf_options.cc:991]                  disable_auto_compactions: 0
2025/06/17-12:16:27.603366 ********** [options/cf_options.cc:993]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/17-12:16:27.603366 ********** [options/cf_options.cc:995]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/17-12:16:27.603367 ********** [options/cf_options.cc:997]        level0_file_num_compaction_trigger: 4
2025/06/17-12:16:27.603367 ********** [options/cf_options.cc:999]            level0_slowdown_writes_trigger: 20
2025/06/17-12:16:27.603368 ********** [options/cf_options.cc:1001]                level0_stop_writes_trigger: 36
2025/06/17-12:16:27.603368 ********** [options/cf_options.cc:1003]                      max_compaction_bytes: 2684354550
2025/06/17-12:16:27.603369 ********** [options/cf_options.cc:1005]                     target_file_size_base: 107374182
2025/06/17-12:16:27.603369 ********** [options/cf_options.cc:1007]               target_file_size_multiplier: 1
2025/06/17-12:16:27.603370 ********** [options/cf_options.cc:1009]                  max_bytes_for_level_base: 1073741824
2025/06/17-12:16:27.603370 ********** [options/cf_options.cc:1011]            max_bytes_for_level_multiplier: 10.000000
2025/06/17-12:16:27.603371 ********** [options/cf_options.cc:1013]                                       ttl: 2592000
2025/06/17-12:16:27.603372 ********** [options/cf_options.cc:1015]               periodic_compaction_seconds: 86400
2025/06/17-12:16:27.603373 ********** [options/cf_options.cc:1029] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/17-12:16:27.603373 ********** [options/cf_options.cc:1031]         max_sequential_skip_in_iterations: 8
2025/06/17-12:16:27.603374 ********** [options/cf_options.cc:1033]          check_flush_compaction_key_order: 1
2025/06/17-12:16:27.603374 ********** [options/cf_options.cc:1035]                      paranoid_file_checks: 0
2025/06/17-12:16:27.603375 ********** [options/cf_options.cc:1037]                        report_bg_io_stats: 0
2025/06/17-12:16:27.603375 ********** [options/cf_options.cc:1039]                               compression: 0
2025/06/17-12:16:27.603376 ********** [options/cf_options.cc:1043] compaction_options_universal.size_ratio : 1
2025/06/17-12:16:27.603376 ********** [options/cf_options.cc:1045] compaction_options_universal.min_merge_width : 2
2025/06/17-12:16:27.603377 ********** [options/cf_options.cc:1047] compaction_options_universal.max_merge_width : -1
2025/06/17-12:16:27.603378 ********** [options/cf_options.cc:1050] compaction_options_universal.max_size_amplification_percent : 200
2025/06/17-12:16:27.603378 ********** [options/cf_options.cc:1053] compaction_options_universal.compression_size_percent : -1
2025/06/17-12:16:27.603395 ********** [options/cf_options.cc:1055] compaction_options_universal.stop_style : 1
2025/06/17-12:16:27.603395 ********** [options/cf_options.cc:1058] compaction_options_universal.allow_trivial_move : 0
2025/06/17-12:16:27.603396 ********** [options/cf_options.cc:1060] compaction_options_universal.incremental        : 0
2025/06/17-12:16:27.603396 ********** [options/cf_options.cc:1064] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/17-12:16:27.603397 ********** [options/cf_options.cc:1066] compaction_options_fifo.allow_compaction : 0
2025/06/17-12:16:27.603398 ********** [options/cf_options.cc:1070]                         enable_blob_files: false
2025/06/17-12:16:27.603398 ********** [options/cf_options.cc:1072]                             min_blob_size: 0
2025/06/17-12:16:27.603399 ********** [options/cf_options.cc:1074]                            blob_file_size: *********
2025/06/17-12:16:27.603399 ********** [options/cf_options.cc:1076]                     blob_compression_type: NoCompression
2025/06/17-12:16:27.603400 ********** [options/cf_options.cc:1078]            enable_blob_garbage_collection: false
2025/06/17-12:16:27.603400 ********** [options/cf_options.cc:1080]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/17-12:16:27.603401 ********** [options/cf_options.cc:1082]   blob_garbage_collection_force_threshold: 1.000000
2025/06/17-12:16:27.603402 ********** [options/cf_options.cc:1084]            blob_compaction_readahead_size: 0
2025/06/17-12:16:27.603402 ********** [options/cf_options.cc:1086]                  blob_file_starting_level: 0
2025/06/17-12:16:27.603403 ********** [options/cf_options.cc:1089]                    bottommost_temperature: 0
2025/06/17-12:16:27.607537 ********** [db/db_impl/db_impl.cc:480] Shutdown: canceling all background work
2025/06/17-12:16:27.607878 ********** [db/db_impl/db_impl.cc:710] Shutdown complete
