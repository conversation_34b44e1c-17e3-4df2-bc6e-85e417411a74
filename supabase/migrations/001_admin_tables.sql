-- Admin Events Table
-- Tracks all admin-related contract events for audit trail and synchronization
CREATE TABLE admin_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type VARCHAR(50) NOT NULL,
  contract_type VARCHAR(20) NOT NULL CHECK (contract_type IN ('pool', 'token')),
  transaction_signature VARCHAR(88) NOT NULL UNIQUE,
  block_time TIMESTAMP WITH TIME ZONE NOT NULL,
  slot BIGINT NOT NULL,
  admin_wallet VARCHAR(44) NOT NULL,
  event_data JSONB NOT NULL,
  processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Platform Settings Table
-- Stores current platform configuration for both pool and token contracts
CREATE TABLE platform_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contract_type VARCHAR(20) NOT NULL CHECK (contract_type IN ('pool', 'token')),
  is_initialized BOOLEAN NOT NULL DEFAULT FALSE,
  super_admin VARCHAR(44) NOT NULL,
  platform_fee_bps INTEGER NOT NULL CHECK (platform_fee_bps >= 0 AND platform_fee_bps <= 10000),
  reward_fee_bps INTEGER CHECK (reward_fee_bps >= 0 AND reward_fee_bps <= 10000), -- Only for token contract
  lp_fee_bps INTEGER CHECK (lp_fee_bps >= 0 AND lp_fee_bps <= 100), -- Only for pool contract
  mint_fee BIGINT NOT NULL CHECK (mint_fee >= 0),
  min_reward_trade_amount BIGINT NOT NULL CHECK (min_reward_trade_amount >= 0),
  initial_reward_goal BIGINT NOT NULL CHECK (initial_reward_goal > 0),
  bonding_curve_goal BIGINT NOT NULL CHECK (bonding_curve_goal > 0),
  reward_goal_increase BIGINT NOT NULL CHECK (reward_goal_increase > 0),
  reward_goal_decrease_amount BIGINT NOT NULL CHECK (reward_goal_decrease_amount > 0),
  reward_goal_decrease_threshold INTEGER NOT NULL CHECK (reward_goal_decrease_threshold > 0),
  reward_goal_proximity_threshold INTEGER NOT NULL CHECK (reward_goal_proximity_threshold > 0 AND reward_goal_proximity_threshold <= 100),
  migration_fee_percentage INTEGER NOT NULL CHECK (migration_fee_percentage >= 0 AND migration_fee_percentage <= 100),
  migration_gas_fee BIGINT NOT NULL CHECK (migration_gas_fee >= 0),
  platform_fee_address VARCHAR(44) NOT NULL,
  last_updated_signature VARCHAR(88),
  last_updated_slot BIGINT,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT unique_contract_type UNIQUE(contract_type)
);

-- Authorized Admins Table
-- Tracks authorized administrators for each contract
CREATE TABLE authorized_admins (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contract_type VARCHAR(20) NOT NULL CHECK (contract_type IN ('pool', 'token')),
  admin_wallet VARCHAR(44) NOT NULL,
  added_by VARCHAR(44) NOT NULL, -- Super admin who added this admin
  added_at_signature VARCHAR(88) NOT NULL,
  added_at_slot BIGINT NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  removed_at_signature VARCHAR(88),
  removed_at_slot BIGINT,
  removed_by VARCHAR(44), -- Super admin who removed this admin
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT unique_contract_admin UNIQUE(contract_type, admin_wallet)
);

-- Event Processing Status Table
-- Tracks event processing status for retry logic and monitoring
CREATE TABLE event_processing_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_signature VARCHAR(88) NOT NULL UNIQUE,
  event_type VARCHAR(50) NOT NULL,
  contract_type VARCHAR(20) NOT NULL CHECK (contract_type IN ('pool', 'token')),
  processing_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed', 'retrying')),
  retry_count INTEGER NOT NULL DEFAULT 0,
  last_retry_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for efficient querying
CREATE INDEX idx_admin_events_type ON admin_events(event_type);
CREATE INDEX idx_admin_events_contract ON admin_events(contract_type);
CREATE INDEX idx_admin_events_wallet ON admin_events(admin_wallet);
CREATE INDEX idx_admin_events_block_time ON admin_events(block_time DESC);
CREATE INDEX idx_admin_events_signature ON admin_events(transaction_signature);
CREATE INDEX idx_admin_events_slot ON admin_events(slot DESC);

CREATE INDEX idx_platform_settings_contract ON platform_settings(contract_type);
CREATE INDEX idx_platform_settings_initialized ON platform_settings(is_initialized);
CREATE INDEX idx_platform_settings_super_admin ON platform_settings(super_admin);

CREATE INDEX idx_authorized_admins_wallet ON authorized_admins(admin_wallet);
CREATE INDEX idx_authorized_admins_contract ON authorized_admins(contract_type);
CREATE INDEX idx_authorized_admins_active ON authorized_admins(is_active);
CREATE INDEX idx_authorized_admins_added_by ON authorized_admins(added_by);

CREATE INDEX idx_event_processing_status ON event_processing_status(processing_status);
CREATE INDEX idx_event_processing_retry ON event_processing_status(retry_count);
CREATE INDEX idx_event_processing_signature ON event_processing_status(transaction_signature);

-- Row Level Security (RLS) Policies
ALTER TABLE admin_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE authorized_admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_processing_status ENABLE ROW LEVEL SECURITY;

-- Admin events are readable by authenticated users, writable by service role
CREATE POLICY "Admin events are viewable by authenticated users" ON admin_events
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admin events are insertable by service role" ON admin_events
  FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Platform settings are readable by authenticated users, writable by service role
CREATE POLICY "Platform settings are viewable by authenticated users" ON platform_settings
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Platform settings are writable by service role" ON platform_settings
  FOR ALL USING (auth.role() = 'service_role');

-- Authorized admins are readable by authenticated users, writable by service role
CREATE POLICY "Authorized admins are viewable by authenticated users" ON authorized_admins
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authorized admins are writable by service role" ON authorized_admins
  FOR ALL USING (auth.role() = 'service_role');

-- Event processing status is only accessible by service role
CREATE POLICY "Event processing status is service role only" ON event_processing_status
  FOR ALL USING (auth.role() = 'service_role');

-- Functions for event processing
CREATE OR REPLACE FUNCTION update_platform_settings_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_platform_settings_timestamp
  BEFORE UPDATE ON platform_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_platform_settings_timestamp();

CREATE OR REPLACE FUNCTION update_authorized_admins_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_authorized_admins_timestamp
  BEFORE UPDATE ON authorized_admins
  FOR EACH ROW
  EXECUTE FUNCTION update_authorized_admins_timestamp();

-- Function to get admin status for a wallet
CREATE OR REPLACE FUNCTION get_admin_status(wallet_address VARCHAR(44))
RETURNS TABLE (
  contract_type VARCHAR(20),
  is_super_admin BOOLEAN,
  is_authorized_admin BOOLEAN,
  is_initialized BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ps.contract_type,
    (ps.super_admin = wallet_address) as is_super_admin,
    COALESCE(aa.is_active, false) as is_authorized_admin,
    ps.is_initialized
  FROM platform_settings ps
  LEFT JOIN authorized_admins aa ON aa.contract_type = ps.contract_type 
    AND aa.admin_wallet = wallet_address 
    AND aa.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log admin events with validation
CREATE OR REPLACE FUNCTION log_admin_event(
  p_event_type VARCHAR(50),
  p_contract_type VARCHAR(20),
  p_transaction_signature VARCHAR(88),
  p_block_time TIMESTAMP WITH TIME ZONE,
  p_slot BIGINT,
  p_admin_wallet VARCHAR(44),
  p_event_data JSONB
)
RETURNS UUID AS $$
DECLARE
  event_id UUID;
BEGIN
  INSERT INTO admin_events (
    event_type,
    contract_type,
    transaction_signature,
    block_time,
    slot,
    admin_wallet,
    event_data
  ) VALUES (
    p_event_type,
    p_contract_type,
    p_transaction_signature,
    p_block_time,
    p_slot,
    p_admin_wallet,
    p_event_data
  ) RETURNING id INTO event_id;
  
  RETURN event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comments for documentation
COMMENT ON TABLE admin_events IS 'Audit trail of all admin contract events';
COMMENT ON TABLE platform_settings IS 'Current platform configuration for pool and token contracts';
COMMENT ON TABLE authorized_admins IS 'List of authorized administrators for each contract';
COMMENT ON TABLE event_processing_status IS 'Status tracking for event synchronization';

COMMENT ON COLUMN platform_settings.contract_type IS 'Either pool or token contract';
COMMENT ON COLUMN platform_settings.platform_fee_bps IS 'Platform fee in basis points (0-10000)';
COMMENT ON COLUMN platform_settings.reward_fee_bps IS 'Reward fee in basis points (token contract only)';
COMMENT ON COLUMN platform_settings.lp_fee_bps IS 'LP fee percentage of platform fee (pool contract only)';
COMMENT ON COLUMN authorized_admins.is_active IS 'Whether the admin authorization is currently active';
COMMENT ON COLUMN event_processing_status.retry_count IS 'Number of times event processing has been retried';
